"""
Marketing components for the Datagenius agent system.

This module provides components for marketing content generation tasks using MCP tools.
"""

import logging
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field

from agents.components.base import AgentComponent
from agents.components.mcp_server import MCPServerComponent

# Configure logging
logger = logging.getLogger(__name__)


class MarketingTask(BaseModel):
    """Model for marketing task request"""
    task_type: str = Field(..., description="Type of marketing task to perform")
    brand_description: str = Field("", description="Description of the brand")
    target_audience: str = Field("", description="Description of the target audience")
    products_services: str = Field("", description="Description of products or services")
    marketing_goals: str = Field("", description="Marketing goals")
    existing_content: str = Field("", description="Existing marketing content")
    keywords: str = Field("", description="Keywords to target")
    suggested_topics: str = Field("", description="Suggested topics")
    tone: str = Field("Professional", description="Tone of the content")
    file_id: Optional[str] = Field(None, description="ID of the file to process")
    is_first_conversation: bool = Field(False, description="Whether this is the first message in a conversation")
    has_data_source: bool = Field(False, description="Whether a data source is attached to the conversation")
    provider: Optional[str] = Field(None, description="AI provider to use for content generation")
    model: Optional[str] = Field(None, description="AI model to use for content generation")


class MarketingParserComponent(AgentComponent):
    """Component for parsing marketing requests."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing MarketingParserComponent")

        # Define keywords that indicate an explicit marketing request
        self.marketing_keywords = [
            "marketing strategy", "marketing plan", "marketing content",
            "campaign strategy", "campaign plan",
            "social media content", "social media strategy",
            "seo optimization", "seo strategy",
            "post composer", "create post", "write post",
            "generate marketing", "create marketing", "develop marketing",
            "marketing materials", "marketing collateral",
            "content strategy", "content creation"
        ]

    def _is_explicit_marketing_request(self, message: str, context: Dict[str, Any] = None) -> bool:
        """
        Determine if a message is explicitly requesting marketing content.

        Args:
            message: The user's message text
            context: Additional context information

        Returns:
            True if the message is an explicit marketing request, False otherwise
        """
        message_lower = message.lower()

        # Check for explicit marketing content generation phrases
        if message_lower.startswith("generate ") and any(keyword in message_lower for keyword in [
            "marketing", "campaign", "social media", "seo", "post"
        ]):
            logger.info(f"Message starts with 'generate' and contains marketing keywords")
            return True

        # Check for other explicit marketing keywords
        for keyword in self.marketing_keywords:
            if keyword in message_lower:
                logger.info(f"Message contains explicit marketing keyword: '{keyword}'")
                return True

        # Check if this is a follow-up question about previously generated content
        if context and "conversation_history" in context:
            # Look for references to "it" or "this" in the message
            if any(ref in message_lower for ref in ["improve it", "enhance it", "make it better", "refine it",
                                                   "update it", "change it", "modify it", "edit it",
                                                   "improve this", "enhance this", "refine this"]):
                # Check if there was recently generated marketing content
                conversation_history = context["conversation_history"]
                for msg in reversed(conversation_history):  # Start from most recent
                    if msg["sender"] == "ai" and msg.get("metadata", {}).get("generated_content", False):
                        logger.info(f"Message appears to be a follow-up about previously generated content")
                        return True

        # Check for action buttons
        if message_lower in ["marketing strategy", "campaign strategy", "social media content", "seo optimization"]:
            logger.info(f"Message appears to be an action button selection: {message}")
            return True

        # Check for explicit requests for marketing help
        if any(phrase in message_lower for phrase in [
            "help me with marketing",
            "create a marketing",
            "need marketing",
            "marketing help",
            "marketing assistance",
            "marketing advice",
            "marketing ideas",
            "marketing plan",
            "marketing content"
        ]):
            logger.info(f"Message contains explicit request for marketing help")
            return True

        # If no explicit marketing keywords are found, it's likely a general conversation
        logger.info("No explicit marketing keywords found in message")
        return False

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        message = context.get("message", "")

        # Create a combined context that includes metadata
        combined_ctx = {}

        # Add the regular context
        ctx = context.get("context", {})
        if ctx:
            combined_ctx.update(ctx)

        # Add metadata if available
        metadata = context.get("metadata", {})
        if metadata:
            combined_ctx["metadata"] = metadata

        # Add the original context as parent_context for reference
        combined_ctx["parent_context"] = context

        # Log the incoming context to see what we're working with
        logger.info("MARKETING PARSER COMPONENT - INCOMING CONTEXT:")
        logger.info(f"Message: {message}")
        logger.info(f"Context keys: {list(context.keys())}")
        logger.info(f"Combined context keys: {list(combined_ctx.keys())}")

        # Check for marketing_form_data in various locations
        has_marketing_form_data = False
        if "marketing_form_data" in ctx:
            logger.info(f"FOUND marketing_form_data in context")
            has_marketing_form_data = True
        elif metadata and "marketing_form_data" in metadata:
            logger.info(f"FOUND marketing_form_data in metadata")
            has_marketing_form_data = True
        else:
            logger.info("NO marketing_form_data found in context or metadata")

        # Check if this is a regeneration request
        is_regeneration = False
        if "is_regeneration" in combined_ctx and combined_ctx["is_regeneration"]:
            is_regeneration = True
            logger.info("This is a regeneration request")

        # Check if this is an explicit marketing request
        is_explicit_marketing_request = self._is_explicit_marketing_request(message, combined_ctx)

        # If this is not a form submission, not a regeneration request, and not an explicit marketing request,
        # treat it as a general conversation
        if not has_marketing_form_data and not is_regeneration and not is_explicit_marketing_request:
            logger.info("Message appears to be general conversation, not a marketing request")
            # Set a flag to skip content generation
            context["skip_marketing_content_generation"] = True
            # Return early without further processing
            return context

        # Check for provider and model in various locations
        provider = None
        model = None

        if "provider" in ctx:
            provider = ctx["provider"]
            logger.info(f"FOUND provider in context: {provider}")
        elif metadata and "provider" in metadata:
            provider = metadata["provider"]
            logger.info(f"FOUND provider in metadata: {provider}")
        else:
            logger.info("NO provider found in context or metadata")

        if "model" in ctx:
            model = ctx["model"]
            logger.info(f"FOUND model in context: {model}")
        elif metadata and "model" in metadata:
            model = metadata["model"]
            logger.info(f"FOUND model in metadata: {model}")
        else:
            logger.info("NO model found in context or metadata")

        # Check if this is a regeneration request
        is_regeneration = False
        if "is_regeneration" in combined_ctx and combined_ctx["is_regeneration"]:
            is_regeneration = True
            logger.info("This is a regeneration request")

        # Parse the marketing request using the combined context
        task = await self._parse_marketing_request(message, combined_ctx, is_regeneration)

        # Update the context with the parsed information
        context["marketing_task"] = task.model_dump()

        # Add regeneration flag to the context and metadata
        if is_regeneration:
            context["is_regeneration"] = True
            if "metadata" not in context:
                context["metadata"] = {}
            context["metadata"]["is_regeneration"] = True

        # Also add provider and model to the context if found
        if provider:
            context["provider"] = provider
        if model:
            context["model"] = model

        # Log the task that was created
        logger.info("MARKETING PARSER COMPONENT - CREATED TASK:")
        logger.info(f"Task type: {task.task_type}")
        logger.info(f"Brand description: '{task.brand_description}'")
        logger.info(f"Target audience: '{task.target_audience}'")
        logger.info(f"Products/services: '{task.products_services}'")
        logger.info(f"Marketing goals: '{task.marketing_goals}'")
        logger.info(f"Existing content: '{task.existing_content}'")
        logger.info(f"Keywords: '{task.keywords}'")
        logger.info(f"Suggested topics: '{task.suggested_topics}'")
        logger.info(f"Tone: '{task.tone}'")

        return context

    async def _parse_marketing_request(self, message: str, context: Optional[Dict[str, Any]], is_regeneration: bool = False) -> MarketingTask:
        """
        Parse the marketing request from the message and context.

        Args:
            message: The user's message text
            context: Additional context information

        Returns:
            MarketingTask object with parsed parameters
        """
        # Log the full context structure to debug
        logger.info(f"FULL CONTEXT STRUCTURE: {context}")
        logger.info(f"Is regeneration request: {is_regeneration}")

        # Check for marketing_form_data in different possible locations
        form_data = None

        # For regeneration requests, we prioritize finding the form data
        if is_regeneration:
            logger.info("Prioritizing form data for regeneration request")

            # First check if marketing_form_data is directly in the context
            if context and "marketing_form_data" in context:
                form_data = context["marketing_form_data"]
                logger.info(f"Found marketing_form_data directly in context for regeneration")
            # Then check in metadata
            elif context and "metadata" in context and "marketing_form_data" in context["metadata"]:
                form_data = context["metadata"]["marketing_form_data"]
                logger.info(f"Found marketing_form_data in metadata for regeneration")
            # Then check in parent_context
            elif context and "parent_context" in context and "context" in context["parent_context"] and "marketing_form_data" in context["parent_context"]["context"]:
                form_data = context["parent_context"]["context"]["marketing_form_data"]
                logger.info(f"Found marketing_form_data in parent_context for regeneration")

            if not form_data:
                logger.warning("No marketing_form_data found for regeneration request")
        else:
            # First check if marketing_form_data is directly in the context
            if context and "marketing_form_data" in context:
                form_data = context["marketing_form_data"]
                logger.info(f"Found marketing_form_data directly in context")
            # If not found, check if it's in the metadata
            elif context and "metadata" in context and isinstance(context["metadata"], dict) and "marketing_form_data" in context["metadata"]:
                form_data = context["metadata"]["marketing_form_data"]
                logger.info(f"Found marketing_form_data in context.metadata")
            # If still not found, check if it's in the parent context
            elif "parent_context" in context and isinstance(context["parent_context"], dict):
                parent_context = context["parent_context"]
                if "marketing_form_data" in parent_context:
                    form_data = parent_context["marketing_form_data"]
                    logger.info(f"Found marketing_form_data in parent_context")
                elif "metadata" in parent_context and isinstance(parent_context["metadata"], dict) and "marketing_form_data" in parent_context["metadata"]:
                    form_data = parent_context["metadata"]["marketing_form_data"]
                    logger.info(f"Found marketing_form_data in parent_context.metadata")

        # If form data was found in any location, use it
        if form_data:
            try:
                logger.info(f"Using marketing_form_data: {form_data}")

                # Extract provider and model from various possible locations
                provider = None
                model = None

                # Check direct context
                if context:
                    provider = context.get("provider")
                    model = context.get("model")

                    # Check metadata
                    if not provider and "metadata" in context and isinstance(context["metadata"], dict):
                        provider = context["metadata"].get("provider")
                        model = context["metadata"].get("model")

                # Check form data itself
                if not provider and "provider" in form_data:
                    provider = form_data.get("provider")
                    model = form_data.get("model")

                logger.info(f"Provider: {provider}, Model: {model}")

                # Extract file_id from various possible locations
                file_id = None
                has_data_source = False

                # Check direct context
                if context:
                    file_id = context.get("file_id")

                    # Check for data_source in context
                    if not file_id and "data_source" in context:
                        data_source = context.get("data_source", {})
                        has_data_source = True
                        if isinstance(data_source, dict) and data_source.get("type") == "file" and "id" in data_source:
                            file_id = data_source["id"]

                    # Check for data_source in metadata
                    if not file_id and "metadata" in context and isinstance(context["metadata"], dict) and "data_source" in context["metadata"]:
                        data_source = context["metadata"].get("data_source", {})
                        has_data_source = True
                        if isinstance(data_source, dict) and data_source.get("type") == "file" and "id" in data_source:
                            file_id = data_source["id"]

                logger.info(f"File ID: {file_id}, Has data source: {has_data_source}")

                # Get form values directly without adding defaults
                brand_description = form_data.get("brand_description", "")
                if not brand_description:
                    logger.warning("Brand description is empty in form data")

                target_audience = form_data.get("target_audience", "")
                if not target_audience:
                    logger.warning("Target audience is empty in form data")

                products_services = form_data.get("products_services", "")
                if not products_services:
                    logger.warning("Products/services is empty in form data")

                marketing_goals = form_data.get("marketing_goals", "")
                if not marketing_goals:
                    logger.warning("Marketing goals is empty in form data")

                existing_content = form_data.get("existing_content", "")
                if not existing_content:
                    logger.warning("Existing content is empty in form data")

                keywords = form_data.get("keywords", "")
                if not keywords:
                    logger.warning("Keywords are empty in form data")

                suggested_topics = form_data.get("suggested_topics", "")
                if not suggested_topics:
                    logger.warning("Suggested topics are empty in form data")

                tone = form_data.get("tone", "Professional")
                if not tone:
                    logger.warning("Tone is empty in form data, using default: Professional")

                # Log all extracted values
                logger.info(f"EXTRACTED FORM VALUES:")
                logger.info(f"- brand_description: '{brand_description}'")
                logger.info(f"- target_audience: '{target_audience}'")
                logger.info(f"- products_services: '{products_services}'")
                logger.info(f"- marketing_goals: '{marketing_goals}'")
                logger.info(f"- existing_content: '{existing_content}'")
                logger.info(f"- keywords: '{keywords}'")
                logger.info(f"- suggested_topics: '{suggested_topics}'")
                logger.info(f"- tone: '{tone}'")

                # Map form data fields to task fields
                return MarketingTask(
                    task_type=form_data.get("content_type", "marketing_strategy"),
                    brand_description=brand_description,
                    target_audience=target_audience,
                    products_services=products_services,
                    marketing_goals=marketing_goals,
                    existing_content=existing_content,
                    keywords=keywords,
                    suggested_topics=suggested_topics,
                    tone=tone,
                    file_id=file_id,
                    is_first_conversation=False,  # Form submission is never the first conversation
                    has_data_source=has_data_source or "data_source" in context,
                    provider=provider,
                    model=model
                )
            except Exception as e:
                logger.error(f"Error parsing marketing_form_data: {str(e)}", exc_info=True)
                # Fall back to parsing from the message
        else:
            logger.warning("No marketing_form_data found in any context location")

        # If context contains task parameters, use those
        if context and "task_params" in context:
            try:
                return MarketingTask(**context["task_params"])
            except Exception as e:
                logger.error(f"Error parsing task parameters from context: {str(e)}")
                # Fall back to parsing from the message

        # Extract file ID from context
        file_id = None
        has_data_source = False

        if context and "file_id" in context:
            file_id = context["file_id"]
            has_data_source = True
        elif context and "data_source" in context:
            data_source = context.get("data_source", {})
            has_data_source = True
            if data_source.get("type") == "file" and "id" in data_source:
                file_id = data_source["id"]

        # Check if this is the first message in the conversation
        is_first_conversation = True
        if context and "conversation_messages" in context:
            # If there are 0 or 1 messages (just the current one), it's the first conversation
            message_count = len(context["conversation_messages"])
            is_first_conversation = message_count <= 1
            logger.info(f"Conversation has {message_count} messages, is_first_conversation: {is_first_conversation}")
        else:
            # If we can't determine, assume it might be the first
            logger.info("Could not determine message count, assuming first conversation")

        # Otherwise, try to parse from the message
        task_type = "marketing_strategy"  # Default task type

        # Check for specific task types in the message
        message_lower = message.lower()
        if "campaign" in message_lower:
            task_type = "campaign_strategy"
        elif "social media" in message_lower:
            task_type = "social_media_content"
        elif "seo" in message_lower:
            task_type = "seo_optimization"
        elif "post" in message_lower:
            task_type = "post_composer"

        # Create a basic task with just the type
        # Check if we have marketing_form_data in the context but failed to parse it earlier
        if context and "marketing_form_data" in context:
            # Try one more time to use the form data
            try:
                form_data = context["marketing_form_data"]
                logger.info(f"Using marketing_form_data from fallback: {form_data}")

                # Get form values directly without adding defaults
                brand_description = form_data.get("brand_description", "")
                if not brand_description:
                    logger.warning("Brand description is empty in form data (fallback)")

                target_audience = form_data.get("target_audience", "")
                if not target_audience:
                    logger.warning("Target audience is empty in form data (fallback)")

                products_services = form_data.get("products_services", "")
                if not products_services:
                    logger.warning("Products/services is empty in form data (fallback)")

                marketing_goals = form_data.get("marketing_goals", "")
                if not marketing_goals:
                    logger.warning("Marketing goals is empty in form data (fallback)")

                existing_content = form_data.get("existing_content", "")
                if not existing_content:
                    logger.warning("Existing content is empty in form data (fallback)")

                keywords = form_data.get("keywords", "")
                if not keywords:
                    logger.warning("Keywords are empty in form data (fallback)")

                suggested_topics = form_data.get("suggested_topics", "")
                if not suggested_topics:
                    logger.warning("Suggested topics are empty in form data (fallback)")

                tone = form_data.get("tone", "Professional")
                if not tone:
                    logger.warning("Tone is empty in form data (fallback), using default: Professional")

                # Log all extracted values
                logger.info(f"EXTRACTED FORM VALUES (FALLBACK):")
                logger.info(f"- brand_description: '{brand_description}'")
                logger.info(f"- target_audience: '{target_audience}'")
                logger.info(f"- products_services: '{products_services}'")
                logger.info(f"- marketing_goals: '{marketing_goals}'")
                logger.info(f"- existing_content: '{existing_content}'")
                logger.info(f"- keywords: '{keywords}'")
                logger.info(f"- suggested_topics: '{suggested_topics}'")
                logger.info(f"- tone: '{tone}'")

                # Check for provider and model in form data
                provider = form_data.get("provider")
                model = form_data.get("model")

                task = MarketingTask(
                    task_type=form_data.get("content_type", task_type),
                    brand_description=brand_description,
                    target_audience=target_audience,
                    products_services=products_services,
                    marketing_goals=marketing_goals,
                    existing_content=existing_content,
                    keywords=keywords,
                    suggested_topics=suggested_topics,
                    tone=tone,
                    file_id=file_id,
                    is_first_conversation=is_first_conversation,
                    has_data_source=has_data_source,
                    provider=provider,
                    model=model
                )
            except Exception as e:
                logger.error(f"Error in fallback parsing of marketing_form_data: {str(e)}")
                # If we still fail, create the basic task
                # Try to extract provider and model from context
                provider = None
                model = None

                if context:
                    if "provider" in context:
                        provider = context["provider"]
                    elif "metadata" in context and isinstance(context["metadata"], dict) and "provider" in context["metadata"]:
                        provider = context["metadata"]["provider"]

                    if "model" in context:
                        model = context["model"]
                    elif "metadata" in context and isinstance(context["metadata"], dict) and "model" in context["metadata"]:
                        model = context["metadata"]["model"]

                task = MarketingTask(
                    task_type=task_type,
                    brand_description="",
                    target_audience="",
                    products_services="",
                    marketing_goals="",
                    existing_content="",
                    keywords="",
                    suggested_topics="",
                    tone="Professional",
                    file_id=file_id,
                    is_first_conversation=is_first_conversation,
                    has_data_source=has_data_source,
                    provider=provider,
                    model=model
                )
        else:
            # No form data, create basic task
            # Try to extract provider and model from context
            provider = None
            model = None

            if context:
                if "provider" in context:
                    provider = context["provider"]
                elif "metadata" in context and isinstance(context["metadata"], dict) and "provider" in context["metadata"]:
                    provider = context["metadata"]["provider"]

                if "model" in context:
                    model = context["model"]
                elif "metadata" in context and isinstance(context["metadata"], dict) and "model" in context["metadata"]:
                    model = context["metadata"]["model"]

            task = MarketingTask(
                task_type=task_type,
                brand_description="",
                target_audience="",
                products_services="",
                marketing_goals="",
                existing_content="",
                keywords="",
                suggested_topics="",
                tone="Professional",
                file_id=file_id,
                is_first_conversation=is_first_conversation,
                has_data_source=has_data_source,
                provider=provider,
                model=model
            )

        return task


class MCPContentGeneratorComponent(AgentComponent):
    """Component for generating marketing content using MCP tools."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info(f"Initializing MCPContentGeneratorComponent with config: {config}")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        # Log the incoming context to see what we're working with
        logger.info("MCP CONTENT GENERATOR COMPONENT - INCOMING CONTEXT:")
        logger.info(f"Context keys: {list(context.keys())}")

        # Check if we should skip content generation (for general conversation)
        if context.get("skip_marketing_content_generation", False):
            logger.info("Skipping marketing content generation as requested by parser component")

            # Get the message
            message = context.get("message", "").lower()

            # Get conversation context
            combined_ctx = {}
            if "context" in context and isinstance(context["context"], dict):
                combined_ctx.update(context["context"])
            if "conversation_history" in context:
                combined_ctx["conversation_history"] = context["conversation_history"]
            elif "context" in context and "conversation_history" in context["context"]:
                combined_ctx["conversation_history"] = context["context"]["conversation_history"]

            # Check if this is a follow-up question about previously generated content
            if any(ref in message for ref in ["improve it", "enhance it", "make it better", "refine it",
                                             "update it", "change it", "modify it", "edit it",
                                             "improve this", "enhance this", "refine this"]):
                # If we have conversation history, check for previously generated content
                if "conversation_history" in combined_ctx:
                    conversation_history = combined_ctx["conversation_history"]
                    for msg in reversed(conversation_history):  # Start from most recent
                        if msg["sender"] == "ai" and msg.get("metadata", {}).get("generated_content", False):
                            # Found previously generated content
                            logger.info("Found previously generated content in conversation history")

                            # Add a note to the response about follow-up questions
                            context["response"] = "I'll help you with your general questions. If you'd like me to generate new marketing content or modify the existing content, please use the marketing content form or explicitly ask for marketing content generation."
                            return context

            # For general conversation, we'll let the LLM handle it with conversation history
            # Don't modify the context, just pass it through
            return context

        # Check if marketing_form_data is in the context
        if "context" in context and isinstance(context["context"], dict):
            inner_ctx = context["context"]
            logger.info(f"Inner context keys: {list(inner_ctx.keys())}")

            if "marketing_form_data" in inner_ctx:
                logger.info(f"FOUND marketing_form_data in inner context: {inner_ctx['marketing_form_data']}")
            else:
                logger.info("NO marketing_form_data found in inner context")

        # Get the marketing task from the context
        task_dict = context.get("marketing_task", {})
        if not task_dict:
            logger.error("NO marketing_task found in context")

            # Check if this is a direct message that should be handled conversationally
            if "message" in context and context.get("message"):
                logger.info("No marketing task found, but message is present. Treating as conversational.")
                context["response"] = "I'm your marketing assistant. I can help you create marketing strategies, campaign plans, social media content, and SEO optimization strategies. What would you like me to help you with today?"
                context["metadata"] = {"conversational_response": True}
                return context
            else:
                context["response"] = "No marketing task found in the request. Please specify what type of marketing content you'd like me to generate."
                context["metadata"] = {"error": "missing_marketing_task"}
                return context

        logger.info(f"FOUND marketing_task in context: {task_dict}")

        # Convert to MarketingTask object
        task = MarketingTask(**task_dict)

        # Log the task that was loaded
        logger.info("MCP CONTENT GENERATOR COMPONENT - LOADED TASK:")
        logger.info(f"Task type: {task.task_type}")
        logger.info(f"Brand description: '{task.brand_description}'")
        logger.info(f"Target audience: '{task.target_audience}'")
        logger.info(f"Products/services: '{task.products_services}'")
        logger.info(f"Marketing goals: '{task.marketing_goals}'")
        logger.info(f"Existing content: '{task.existing_content}'")
        logger.info(f"Keywords: '{task.keywords}'")
        logger.info(f"Suggested topics: '{task.suggested_topics}'")
        logger.info(f"Tone: '{task.tone}'")

        # Find MCP server component
        mcp_server = None
        for component in context.get("agent_components", []):
            if isinstance(component, MCPServerComponent):
                mcp_server = component
                break

        if not mcp_server:
            logger.error("MCP server component not found")
            context["response"] = "MCP server component not found. Cannot execute marketing tasks."
            context["metadata"] = {"error": "mcp_server_not_found"}
            return context

        logger.info("Found MCP server component")

        # Generate the marketing content
        try:
            # Check for provider and model in various locations
            provider = None
            model = None

            # First check if provider and model are directly in the context
            provider = context.get("provider")
            model = context.get("model")

            # Check if they're in the metadata
            if (not provider or not model) and "metadata" in context:
                metadata = context.get("metadata", {})
                if not provider and "provider" in metadata:
                    provider = metadata.get("provider")
                    logger.info(f"Found provider in metadata: {provider}")
                if not model and "model" in metadata:
                    model = metadata.get("model")
                    logger.info(f"Found model in metadata: {model}")

            # Check if they're in marketing_form_data in context
            if (not provider or not model) and "marketing_form_data" in context:
                form_data = context.get("marketing_form_data", {})
                if not provider and "provider" in form_data:
                    provider = form_data.get("provider")
                    logger.info(f"Found provider in marketing_form_data: {provider}")
                if not model and "model" in form_data:
                    model = form_data.get("model")
                    logger.info(f"Found model in marketing_form_data: {model}")

            # Check if they're in marketing_form_data in metadata
            if (not provider or not model) and "metadata" in context and "marketing_form_data" in context["metadata"]:
                form_data = context["metadata"].get("marketing_form_data", {})
                if not provider and "provider" in form_data:
                    provider = form_data.get("provider")
                    logger.info(f"Found provider in metadata.marketing_form_data: {provider}")
                if not model and "model" in form_data:
                    model = form_data.get("model")
                    logger.info(f"Found model in metadata.marketing_form_data: {model}")

            # Check if they're in the task itself
            if (not provider or not model):
                if not provider and hasattr(task, 'provider') and task.provider:
                    provider = task.provider
                    logger.info(f"Found provider in task: {provider}")
                if not model and hasattr(task, 'model') and task.model:
                    model = task.model
                    logger.info(f"Found model in task: {model}")

            # Update the task with provider and model if they weren't already set
            if hasattr(task, 'provider') and not task.provider and provider:
                task.provider = provider
                logger.info(f"Updated task with provider: {provider}")
            if hasattr(task, 'model') and not task.model and model:
                task.model = model
                logger.info(f"Updated task with model: {model}")

            # If not found, fall back to agent configuration
            if not provider or not model:
                agent_config = context.get("agent_config", {})
                if not provider:
                    provider = agent_config.get("provider", "groq")
                    logger.info(f"Using default provider from agent_config: {provider}")
                if not model:
                    model = agent_config.get("model", "llama3-70b-8192")
                    logger.info(f"Using default model from agent_config: {model}")

            logger.info(f"Using provider: {provider}, model: {model} for content generation")

            # Check if this is a regeneration request
            is_regeneration = False
            if "is_regeneration" in context and context["is_regeneration"]:
                is_regeneration = True
                logger.info("This is a regeneration request in content generator")
            elif "metadata" in context and isinstance(context["metadata"], dict) and context["metadata"].get("is_regeneration"):
                is_regeneration = True
                logger.info("This is a regeneration request (from metadata) in content generator")

            # For regeneration, use a slightly higher temperature for more variation
            temperature = 0.8 if is_regeneration else 0.7

            # Prepare the tool parameters
            tool_params = {
                "content_type": task.task_type,
                "brand_description": task.brand_description,
                "target_audience": task.target_audience,
                "products_services": task.products_services,
                "marketing_goals": task.marketing_goals,
                "existing_content": task.existing_content,
                "keywords": task.keywords,
                "suggested_topics": task.suggested_topics,
                "tone": task.tone,
                "provider": provider,
                "model": model,
                "temperature": temperature,
                "is_first_conversation": task.is_first_conversation,
                "has_data_source": task.has_data_source,
                "is_regeneration": is_regeneration
            }

            # Debug log the parameters being sent to the tool
            logger.info(f"CALLING CONTENT GENERATION TOOL WITH PARAMETERS:")
            logger.info(f"- content_type: {task.task_type}")
            logger.info(f"- brand_description: {task.brand_description}")
            logger.info(f"- target_audience: {task.target_audience}")
            logger.info(f"- products_services: {task.products_services}")
            logger.info(f"- marketing_goals: {task.marketing_goals}")
            logger.info(f"- existing_content: {task.existing_content}")
            logger.info(f"- keywords: {task.keywords}")
            logger.info(f"- suggested_topics: {task.suggested_topics}")
            logger.info(f"- tone: {task.tone}")
            logger.info(f"- provider: {provider}")
            logger.info(f"- model: {model}")
            logger.info(f"- is_first_conversation: {task.is_first_conversation}")
            logger.info(f"- has_data_source: {task.has_data_source}")

            # Call the MCP content generation tool using its registered name
            tool_result = await mcp_server.call_tool("generate_content", tool_params)

            # Extract the result from the tool response
            if tool_result.get("isError", False):
                raise ValueError(tool_result.get("content", [{"text": "Unknown error"}])[0].get("text", "Unknown error"))

            # Extract the generated content
            content_items = tool_result.get("content", [])
            result = "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"])

            context["response"] = result
            context["metadata"] = {
                "task_type": task.task_type,
                "is_first_conversation": task.is_first_conversation,
                "has_data_source": task.has_data_source,
                "generated_content": True,
                "response": result,  # Include the response in metadata for visualization
                "content": result,   # Also include as content for backward compatibility
                "provider": provider,
                "model": model,
                # Include the marketing form data in the response metadata
                "marketing_form_data": {
                    "content_type": task.task_type,
                    "brand_description": task.brand_description,
                    "target_audience": task.target_audience,
                    "products_services": task.products_services,
                    "marketing_goals": task.marketing_goals,
                    "existing_content": task.existing_content,
                    "keywords": task.keywords,
                    "suggested_topics": task.suggested_topics,
                    "tone": task.tone,
                    "provider": provider,
                    "model": model
                }
            }
        except Exception as e:
            logger.error(f"Error generating marketing content: {str(e)}", exc_info=True)
            context["response"] = f"I encountered an error generating your marketing content: {str(e)}"
            context["metadata"] = {
                "error": "content_generation_error",
                "error_details": str(e),
                "error_type": e.__class__.__name__,
                "component": "MCPContentGeneratorComponent"
            }

        return context
