"""
Exceptions for the model provider system.

This module provides exception classes for the model provider system.
"""


class ModelProviderError(Exception):
    """Base exception for model provider errors."""
    pass


class ModelInitializationError(ModelProviderError):
    """Exception raised when a model cannot be initialized."""
    pass


class ModelNotFoundError(ModelProviderError):
    """Exception raised when a model is not found."""
    pass


class ProviderNotFoundError(ModelProviderError):
    """Exception raised when a provider is not found."""
    pass


class ProviderNotAvailableError(ModelProviderError):
    """Exception raised when a provider is not available."""
    pass
