"""
Data storytelling MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for generating data storytelling insights
using PandasAI.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, Optional

# PandasAI imports
import pandasai as pai

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper
from ..pandasai_v3.cache import ResponseCache
from ..pandasai_v3.error_handler import ErrorHandler

logger = logging.getLogger(__name__)


class DataStorytellingTool(BaseMCPTool):
    """Tool for generating data storytelling insights using PandasAI."""

    def __init__(self):
        """Initialize the data storytelling tool."""
        super().__init__(
            name="data_storytelling",
            description="Generates a narrative explanation (data story) about a specific topic using insights derived from the data via PandasAI.",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the data file (CSV, Excel, JSON supported)."
                    },
                    "topic": {
                        "type": "string",
                        "description": "The main topic or question the data story should address (e.g., 'sales trends', 'customer segment differences')."
                    },
                    "api_key": {
                        "type": "string",
                        "description": "API key for the LLM provider."
                    },
                    "provider": {
                        "type": "string",
                        "description": "LLM provider to use (e.g., openai, groq, anthropic).",
                        "default": "openai"
                    },
                    "model": {
                        "type": "string",
                        "description": "Model name to use for the analysis."
                    }
                },
                "required": ["file_path", "topic", "api_key"]
            }
        )
        self.pandasai = PandasAIWrapper()
        self.cache = ResponseCache()

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # No additional initialization needed
        pass

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the data storytelling tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution (following the inputSchema)

        Returns:
            Tool execution results in MCP format
        """
        file_path = arguments["file_path"]
        topic = arguments["topic"]
        api_key = arguments["api_key"]
        provider = arguments.get("provider", "openai")
        model = arguments.get("model")

        logger.info(f"PandasAI data storytelling requested for {file_path} with topic: {topic}")

        # Check if we have a cached response
        cache_key = f"{file_path}:{topic}:{provider}:{model}"
        cached_result = self.cache.get(cache_key)
        if cached_result:
            logger.info(f"Using cached result for data storytelling: {cache_key}")
            return cached_result

        # Input validation
        if not provider or not api_key:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Error: LLM Provider and API Key must be provided."
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "topic": topic,
                    "status": "error",
                    "error_type": "config_error"
                }
            }

        # Load the data
        try:
            if file_path.lower().endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.lower().endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.lower().endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ]
                }

            if df.empty:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"The dataframe loaded from {file_path} is empty."
                        }
                    ]
                }
        except Exception as e:
            logger.error(f"Error loading data from {file_path}: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error loading data from {file_path}: {str(e)}"
                    }
                ]
            }

        # Generate the story
        try:
            # Initialize PandasAI
            self.pandasai.initialize(api_key, provider)

            # Create agent with the dataframe
            if not self.pandasai.create_agent(df=df, model=model):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "Error creating PandasAI Agent"
                        }
                    ]
                }

            # Craft a prompt guiding PandasAI towards storytelling
            prompt = f"Analyze the dataframe and tell a story about '{topic}'. Focus on key insights, trends, or interesting patterns related to this topic. Explain your findings clearly."

            # Run the analysis using PandasAI wrapper
            result = self.pandasai.chat(prompt)

            # Process the result
            if isinstance(result, str):
                results_text = result
                results_metadata = {"status": "success", "result_type": "string"}
            elif result is None:
                results_text = "The storytelling analysis was processed, but did not produce a narrative."
                results_metadata = {"status": "success", "result_type": "None"}
            else:
                # Handle other potential return types (though string is expected)
                results_text = f"Storytelling analysis returned an unexpected result type: {type(result).__name__}. Result: {str(result)}"
                results_metadata = {"status": "success", "result_type": str(type(result).__name__)}

            response = {
                "content": [
                    {
                        "type": "text",
                        "text": results_text
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "topic": topic,
                    "implementation": "pandasai",
                    **results_metadata
                }
            }

            # Cache the response
            self.cache.set(cache_key, response)
            return response

        except Exception as e:
            error_handler = ErrorHandler()
            error_info = error_handler.handle_error(e, context={
                "operation": "data_storytelling",
                "file_path": file_path,
                "topic": topic
            })

            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": error_info["message"]
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "topic": topic,
                    "error_type": error_info["error_type"],
                    "details": error_info["details"]
                }
            }
