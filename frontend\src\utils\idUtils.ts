/**
 * Utility functions for generating unique IDs
 */
import { v4 as uuidv4 } from 'uuid';

/**
 * Generate a UUID v4
 * @returns A random UUID v4 string
 */
export const generateUUID = (): string => {
  return uuidv4();
};

/**
 * Generate a prefixed UUID
 * @param prefix - Optional prefix to add to the UUID
 * @returns A string in the format `${prefix}-${uuid}`
 */
export const generatePrefixedId = (prefix: string): string => {
  return `${prefix}-${uuidv4()}`;
};

/**
 * Generate a unique message ID
 * @param sender - The sender of the message ('user' or 'ai')
 * @returns A string in the format `msg-${sender}-${uuid}`
 */
export const generateMessageId = (sender: 'user' | 'ai'): string => {
  return `msg-${sender}-${uuidv4()}`;
};

/**
 * Generate a unique temporary message ID
 * @returns A string in the format `temp-${uuid}`
 */
export const generateTempMessageId = (): string => {
  return `temp-${uuidv4()}`;
};

/**
 * Generate a unique stream message ID
 * @returns A string in the format `stream-${uuid}`
 */
export const generateStreamMessageId = (): string => {
  return `stream-${uuidv4()}`;
};

/**
 * Generate a unique welcome message ID
 * @returns A string in the format `welcome-${uuid}`
 */
export const generateWelcomeMessageId = (): string => {
  return `welcome-${uuidv4()}`;
};
