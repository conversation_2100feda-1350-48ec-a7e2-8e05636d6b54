"""
Natural language query MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for querying data using natural language
with PandasAI.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, List, Optional

# PandasAI imports
import pandasai as pai

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper
from ..pandasai_v3.cache import ResponseCache
from ..pandasai_v3.error_handler import ErrorHandler

logger = logging.getLogger(__name__)


class NaturalLanguageQueryTool(BaseMCPTool):
    """Tool for querying data using natural language with PandasAI."""

    def __init__(self):
        """Initialize the natural language query tool."""
        super().__init__(
            name="natural_language_query",
            description="Processes natural language queries against a data file using the PandasAI library and a configured LLM (e.g., OpenAI). Translates the query and executes it.",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the data file (CSV, Excel, JSON supported)."
                    },
                    "query": {
                        "type": "string",
                        "description": "The user's query in natural language."
                    },
                    "conversation_history": {
                        "type": "array",
                        "description": "List of previous conversation turns.",
                        "items": {
                            "type": "object",
                            "properties": {
                                "role": {"type": "string"},
                                "content": {"type": "string"}
                            }
                        }
                    },
                    "api_key": {
                        "type": "string",
                        "description": "API key for the LLM provider."
                    },
                    "provider": {
                        "type": "string",
                        "description": "LLM provider to use (e.g., openai, groq, anthropic).",
                        "default": "openai"
                    },
                    "model": {
                        "type": "string",
                        "description": "Model name to use for the query."
                    }
                },
                "required": ["file_path", "query", "api_key"]
            }
        )
        self.pandasai = PandasAIWrapper()
        self.cache = ResponseCache()

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # No additional initialization needed
        pass

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the natural language query tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution (following the inputSchema)

        Returns:
            Tool execution results in MCP format
        """
        file_path = arguments["file_path"]
        query = arguments["query"]
        api_key = arguments["api_key"]
        provider = arguments.get("provider", "openai")
        model = arguments.get("model")
        conversation_history = arguments.get("conversation_history", [])

        logger.info(f"PandasAI NL query requested for {file_path} with query: {query}")

        # Check if we have a cached response
        cache_key = f"{file_path}:{query}:{provider}:{model}"
        cached_result = self.cache.get(cache_key)
        if cached_result:
            logger.info(f"Using cached result for natural language query: {cache_key}")
            return cached_result

        # Input validation
        if not provider or not api_key:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Error: LLM Provider and API Key must be provided."
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "query": query,
                    "status": "error",
                    "error_type": "config_error"
                }
            }

        # Load the data
        try:
            if file_path.lower().endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.lower().endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.lower().endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ]
                }

            if df.empty:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"The dataframe loaded from {file_path} is empty."
                        }
                    ]
                }
        except Exception as e:
            logger.error(f"Error loading data from {file_path}: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error loading data from {file_path}: {str(e)}"
                    }
                ]
            }

        # Execute the query
        try:
            # Initialize PandasAI
            self.pandasai.initialize(api_key, provider)

            # Create agent with the dataframe
            if not self.pandasai.create_agent(df=df, model=model):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "Error creating PandasAI Agent"
                        }
                    ]
                }

            # Run the query using PandasAI wrapper
            result = self.pandasai.chat(query)

            # Process the result
            if isinstance(result, (pd.DataFrame, pd.Series)):
                result_df = result if isinstance(result, pd.DataFrame) else result.to_frame()
                rows = len(result_df)
                results_metadata = {"status": "success", "result_type": "dataframe", "rows_returned": rows}
                if rows > 50:  # Limit output size
                    results_text = f"Query returned a table with {rows} rows. Showing the first 50:\n\n{result_df.head(50).to_string()}"
                    results_metadata["truncated"] = True
                elif rows == 0:
                    results_text = "Query executed successfully, but returned no matching rows."
                else:
                    results_text = f"Query results ({rows} rows):\n\n{result_df.to_string()}"
            elif isinstance(result, (str, int, float, bool)):
                results_text = f"Query result: {result}"
                results_metadata = {"status": "success", "result_type": str(type(result).__name__), "value": result}
            elif result is None:
                results_text = "The query was processed, but did not produce a direct textual or numerical result. It might have been an action or a query with no return value."
                results_metadata = {"status": "success", "result_type": "None"}
            else:
                # Handle unexpected result types
                results_text = f"Query returned an unexpected result type: {type(result).__name__}. Result: {str(result)}"
                results_metadata = {"status": "success", "result_type": str(type(result).__name__)}

            response = {
                "content": [
                    {
                        "type": "text",
                        "text": results_text
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "query": query,
                    "implementation": "pandasai",
                    **results_metadata
                }
            }

            # Cache the response
            self.cache.set(cache_key, response)
            return response

        except Exception as e:
            error_handler = ErrorHandler()
            error_info = error_handler.handle_error(e, context={
                "operation": "natural_language_query",
                "file_path": file_path,
                "query": query
            })

            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": error_info["message"]
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "query": query,
                    "error_type": error_info["error_type"],
                    "details": error_info["details"]
                }
            }
