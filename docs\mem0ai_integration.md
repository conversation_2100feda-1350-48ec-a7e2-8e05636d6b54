# mem0ai Integration with Datagenius

This document describes the integration of mem0ai with the Datagenius application, replacing all other vector and graph databases with a complete mem0ai integration.

## Overview

mem0ai is a powerful memory system that provides vector database and knowledge graph capabilities. It integrates with Qdrant for vector storage and offers a unified interface for memory operations.

The integration with Datagenius involves:

1. **Vector Database Integration**: Replacing FAISS with mem0ai + Qdrant
2. **Knowledge Graph Integration**: Replacing NetworkX with mem0ai
3. **MCP Tools Integration**: Creating new MCP tools that use mem0ai
4. **Component Integration**: Updating all components to use mem0ai

## Architecture

The mem0ai integration follows this architecture:

```
┌─────────────────────────────────────────────────────────────────┐
│                      Datagenius Application                      │
└───────────────────────────────┬─────────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────────┐
│                        Memory Services                           │
│                                                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  MemoryService  │  │  VectorService  │  │ KnowledgeGraph  │  │
│  │                 │  │                 │  │    Service      │  │
│  └────────┬────────┘  └────────┬────────┘  └────────┬────────┘  │
│           │                    │                    │           │
└───────────┼────────────────────┼────────────────────┼───────────┘
            │                    │                    │
┌───────────▼────────────────────▼────────────────────▼───────────┐
│                           mem0ai SDK                             │
└───────────────────────────────┬─────────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────────┐
│                      Qdrant Vector Database                      │
└─────────────────────────────────────────────────────────────────┘
```

## Components

### 1. Memory Service

The `MemoryService` class provides a unified interface for memory operations using mem0ai. It handles:

- Adding memories
- Retrieving memories
- Searching memories
- Managing memory retention policies

### 2. Vector Service

The `VectorService` class provides vector database operations using mem0ai and Qdrant. It handles:

- Document embedding
- Semantic search
- Vector storage and retrieval

### 3. Knowledge Graph Service

The `KnowledgeGraphService` class provides knowledge graph operations using mem0ai. It handles:

- Entity extraction and storage
- Relationship management
- Graph queries and traversal

### 4. MCP Tools

The following MCP tools have been created or updated to use mem0ai:

- `Mem0DocumentEmbeddingTool`: For document embedding and semantic search
- `DataAccessTool`: Updated to use mem0ai for document processing
- `KnowledgeGraphTool`: Updated to use mem0ai for knowledge graph operations
- `PandasAIAnalysisTool`: Updated to store analysis results in mem0ai
- `PandasAIVisualizationTool`: Updated to store visualizations in mem0ai
- `PandasAIQueryTool`: Updated to store query results in mem0ai

### 5. Memory Manager Component

The `MemoryManagerComponent` has been updated to use mem0ai for:

- Memory management across AI personas
- Document embedding and semantic search
- Knowledge graph operations

## Integration with AI Personas

All AI personas in Datagenius now use mem0ai for:

1. **Conversation Memory**: Storing and retrieving conversation history
2. **Document Memory**: Embedding and searching documents
3. **Knowledge Graphs**: Building and querying knowledge graphs
4. **Insights and Preferences**: Storing user insights and preferences

## Configuration

The mem0ai integration can be configured in two modes:

### Self-Hosted Mode

In self-hosted mode, Datagenius uses a local Qdrant instance for vector storage. This is configured in the `.env` file:

```
MEM0_SELF_HOSTED=True
QDRANT_HOST=localhost
QDRANT_PORT=6333
```

### Hosted Mode

In hosted mode, Datagenius uses the mem0ai service for vector storage. This is configured in the `.env` file:

```
MEM0_SELF_HOSTED=False
MEM0_API_KEY=your_api_key
```

## Testing

The integration includes a comprehensive test script (`test_mem0_integration.py`) that tests:

1. Memory Service
2. Vector Service
3. Knowledge Graph Service
4. MCP Tools
5. Data Access Tool

## Usage Examples

### Adding a Memory

```python
from agents.utils.memory_service import MemoryService

memory_service = MemoryService()
memory = memory_service.add_memory(
    content="This is a memory",
    user_id="user123",
    metadata={"category": "test"}
)
```

### Embedding a Document

```python
from agents.utils.vector_service import VectorService

vector_service = VectorService()
vector_store_id, file_info = vector_service.embed_document(
    file_path="document.pdf",
    chunk_size=1000,
    chunk_overlap=200
)
```

### Searching a Document

```python
from agents.utils.vector_service import VectorService

vector_service = VectorService()
results = vector_service.search_document(
    vector_store_id="vector_store_123",
    query="What is artificial intelligence?",
    limit=5
)
```

### Creating a Knowledge Graph

```python
from agents.utils.knowledge_graph_service import KnowledgeGraphService

kg_service = KnowledgeGraphService()
graph_id = kg_service.create_graph(
    name="My Graph",
    description="A knowledge graph",
    metadata={"category": "test"}
)
```

### Adding Entities and Relationships

```python
from agents.utils.knowledge_graph_service import KnowledgeGraphService

kg_service = KnowledgeGraphService()
entity_id = kg_service.add_entity(
    graph_id="graph_123",
    entity_type="Person",
    name="John Doe",
    properties={"age": 30}
)

relationship_id = kg_service.add_relationship(
    graph_id="graph_123",
    relationship_type="WORKS_AT",
    source_id="entity_123",
    target_id="entity_456",
    properties={"since": 2020}
)
```

## Conclusion

The mem0ai integration provides a unified memory system for the Datagenius application, replacing all other vector and graph databases. It offers improved performance, scalability, and ease of use for AI personas.
