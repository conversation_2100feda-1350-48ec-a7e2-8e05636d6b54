"""
PandasAI v3 query MCP tool.

This module provides an MCP-compatible tool for querying data using PandasAI v3.
It integrates with mem0ai for enhanced query capabilities.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, Optional
import base64

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper
from ..pandasai_v3.cache import ResponseCache
from ..pandasai_v3.error_handler import ErrorHandler
from ...utils.memory_service import MemoryService
from ...utils.vector_service import VectorService

logger = logging.getLogger(__name__)

class PandasAIQueryTool(BaseMCPTool):
    """Tool for querying data using PandasAI v3."""

    def __init__(self):
        """Initialize the PandasAI query tool."""
        super().__init__(
            name="pandasai_query",
            description="Query data using natural language with PandasAI v3 and mem0ai integration",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "data_source": {
                        "type": ["object", "string"],
                        "description": "Data source information (can be an object with id/name or a string identifier)"
                    },
                    "query": {"type": "string"},
                    "api_key": {"type": "string"},
                    "provider": {"type": "string", "default": "openai"},
                    "model": {"type": "string"},
                    "user_id": {"type": "string"},
                    "persona_id": {"type": "string"},
                    "conversation_id": {"type": "string"},
                    "store_in_memory": {"type": "boolean", "default": True}
                },
                "required": ["query", "api_key"]
            }
        )
        self.pandasai = PandasAIWrapper()
        self.cache = ResponseCache()
        self.memory_service = MemoryService()
        self.vector_service = VectorService()

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the PandasAI query tool."""
        file_path = arguments.get("file_path")
        data_source = arguments.get("data_source")
        query = arguments.get("query")
        api_key = arguments.get("api_key")
        provider = arguments.get("provider", "openai")

        # If we have a data_source but no file_path, use the data_access tool to get the file
        if data_source and not file_path:
            try:
                # Import the data access tool
                from .data_access import DataAccessTool

                # Create and initialize the tool
                data_tool = DataAccessTool()
                await data_tool.initialize({})

                # Call the tool to load the data
                data_result = await data_tool.execute({
                    "data_source": data_source,
                    "operation": "load",
                    "params": {"create_sample": True}
                })

                # Check if we got a valid result
                if not data_result.get("isError", False) and "metadata" in data_result:
                    # Extract the file path from the result
                    file_path = data_result["metadata"].get("file_path")
                    logger.info(f"Retrieved file path from data_access tool: {file_path}")
                else:
                    logger.error(f"Error retrieving file path from data_access tool: {data_result}")
                    return {
                        "isError": True,
                        "content": [{"type": "text", "text": "Could not access the data source. Please provide a valid file path or data source."}]
                    }
            except Exception as e:
                logger.error(f"Error using data_access tool: {e}", exc_info=True)
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error accessing data source: {str(e)}"}]
                }

        # Ensure we have a file path
        if not file_path:
            return {
                "isError": True,
                "content": [{"type": "text", "text": "No file path or valid data source provided"}]
            }

        # Check cache first
        cached_result = self.cache.get(file_path, query, provider)
        if cached_result:
            logger.info(f"Using cached result for query: {query}")
            return cached_result

        try:
            # Initialize PandasAI
            self.pandasai.initialize(api_key, provider)

            # Load dataframe
            if not self.pandasai.load_dataframe(file_path):
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error loading dataframe from {file_path}"}]
                }

            # Create agent with model if provided
            model = arguments.get("model")
            if not self.pandasai.create_agent(model=model):
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": "Error creating PandasAI Agent"}]
                }

            # Chat with agent
            result = self.pandasai.chat(query)

            # Handle error
            if "error" in result:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error in PandasAI query: {result['error']}"}]
                }

            # Format response based on result type
            response = self._format_response(result, query)

            # Cache the response
            self.cache.set(file_path, query, provider, response)

            # Store the query results in memory if requested
            store_in_memory = arguments.get("store_in_memory", True)
            if store_in_memory:
                user_id = arguments.get("user_id", "system")
                persona_id = arguments.get("persona_id", "unknown")
                conversation_id = arguments.get("conversation_id", "unknown")

                # Create metadata for the memory
                metadata = {
                    "type": "query",
                    "file_path": file_path,
                    "query": query,
                    "provider": provider,
                    "persona_id": persona_id,
                    "conversation_id": conversation_id,
                    "query_type": "pandasai"
                }

                # Format the content for memory storage
                if "content" in response:
                    content_text = ""
                    for content_item in response["content"]:
                        if content_item.get("type") == "text":
                            content_text += content_item.get("text", "") + "\n\n"

                    # Store in memory
                    if content_text:
                        try:
                            self.memory_service.add_memory(
                                content=f"Query on {os.path.basename(file_path)}: {query}\n\n{content_text}",
                                user_id=user_id,
                                metadata=metadata
                            )
                            logger.info(f"Stored query results in memory for user {user_id}")
                        except Exception as e:
                            logger.error(f"Error storing query results in memory: {e}")

            return response

        except Exception as e:
            logger.error(f"Error executing PandasAI query tool: {e}", exc_info=True)
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Error: {str(e)}"}]
            }

    def _format_response(self, result: Dict[str, Any], query: str) -> Dict[str, Any]:
        """Format the response for the MCP tool."""
        if result["type"] == "dataframe":
            return {
                "isError": False,
                "content": [
                    {"type": "text", "text": f"Query results for: {query}"},
                    {"type": "table", "data": result["data"], "columns": result["columns"]}
                ]
            }
        elif result["type"] == "chart":
            # Read image file and convert to base64
            try:
                with open(result["image_path"], "rb") as image_file:
                    encoded_image = base64.b64encode(image_file.read()).decode("utf-8")

                return {
                    "isError": False,
                    "content": [
                        {"type": "text", "text": f"Visualization for query: {query}"},
                        {"type": "image", "src": f"data:image/png;base64,{encoded_image}"}
                    ]
                }
            except Exception as e:
                logger.error(f"Error reading chart image: {e}", exc_info=True)
                return {
                    "isError": False,
                    "content": [
                        {"type": "text", "text": f"Visualization for query: {query}"},
                        {"type": "text", "text": f"Error displaying chart: {str(e)}"}
                    ]
                }
        elif result["type"] == "number":
            return {
                "isError": False,
                "content": [
                    {"type": "text", "text": f"Query result for: {query}"},
                    {"type": "text", "text": str(result["value"])}
                ]
            }
        else:
            return {
                "isError": False,
                "content": [{"type": "text", "text": result["text"]}]
            }
