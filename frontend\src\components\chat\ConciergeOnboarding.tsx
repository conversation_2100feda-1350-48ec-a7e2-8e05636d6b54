import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Compass, X, ArrowRight } from 'lucide-react';

interface ConciergeOnboardingProps {
  isActive: boolean;
  onDismiss: () => void;
}

export const ConciergeOnboarding: React.FC<ConciergeOnboardingProps> = ({
  isActive,
  onDismiss
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [hasShown, setHasShown] = useState(false);
  
  // Check if we've shown the onboarding before
  useEffect(() => {
    const hasSeenOnboarding = localStorage.getItem('concierge_onboarding_seen');
    if (hasSeenOnboarding === 'true') {
      setHasShown(true);
    }
  }, []);
  
  // If we've already shown the onboarding or it's not active, don't show it
  if (hasShown || !isActive) {
    return null;
  }
  
  const steps = [
    {
      title: "Welcome to the Concierge",
      description: "The Datagenius Concierge helps you navigate the platform and find the right AI personas for your tasks.",
      icon: <Compass className="h-6 w-6 text-brand-500" />
    },
    {
      title: "Follow the Workflow",
      description: "The workflow indicator at the top shows your current stage. The concierge will guide you through each step.",
      icon: <ArrowRight className="h-6 w-6 text-brand-500" />
    },
    {
      title: "Get Specialized Help",
      description: "The concierge will recommend specialized AI personas based on your needs and help you work with your data.",
      icon: <Compass className="h-6 w-6 text-brand-500" />
    }
  ];
  
  const handleDismiss = () => {
    localStorage.setItem('concierge_onboarding_seen', 'true');
    setHasShown(true);
    onDismiss();
  };
  
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleDismiss();
    }
  };
  
  return (
    <AnimatePresence>
      {!hasShown && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30
          }}
          className="fixed bottom-24 left-1/2 transform -translate-x-1/2 z-50 w-[90%] max-w-md"
        >
          <Card className="p-4 shadow-lg border-brand-100">
            <div className="flex justify-between items-start mb-2">
              <div className="flex items-center gap-2">
                {steps[currentStep].icon}
                <h3 className="font-medium text-lg">{steps[currentStep].title}</h3>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleDismiss}
                className="h-8 w-8 -mt-1 -mr-1"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <p className="text-gray-600 mb-4">
              {steps[currentStep].description}
            </p>
            
            <div className="flex justify-between items-center">
              <div className="flex gap-1">
                {steps.map((_, index) => (
                  <div
                    key={index}
                    className={`h-1.5 w-6 rounded-full ${
                      index === currentStep ? 'bg-brand-500' : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>
              
              <Button
                variant="default"
                size="sm"
                onClick={handleNext}
                className="bg-brand-500 hover:bg-brand-600"
              >
                {currentStep < steps.length - 1 ? 'Next' : 'Got it'}
              </Button>
            </div>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
