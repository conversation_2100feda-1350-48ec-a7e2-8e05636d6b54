"""
Template processor utilities for the Datagenius backend.

This module provides utilities for processing templates with actual values,
particularly for data profiles and other structured content.
"""

import logging
import re
from typing import Dict, Any, Optional, Union

logger = logging.getLogger(__name__)


def format_data_profile(template: str, metadata: Dict[str, Any]) -> str:
    """
    Format a data profile template with actual values from metadata.

    Args:
        template: The template string with placeholders
        metadata: Metadata containing actual values to replace placeholders

    Returns:
        Formatted template with actual values
    """
    try:
        # Create a dictionary of replacements from metadata
        replacements = {}

        # Basic information
        replacements["data_name"] = metadata.get("file_name", "your data")

        # Extract row and column counts
        if "row_count" in metadata:
            replacements["row_count"] = format_number(metadata["row_count"])
        elif "shape" in metadata:
            replacements["row_count"] = format_number(metadata["shape"][0])

        if "column_count" in metadata:
            replacements["column_count"] = format_number(metadata["column_count"])
        elif "shape" in metadata:
            replacements["column_count"] = format_number(metadata["shape"][1])

        # Data types
        if "data_types" in metadata:
            replacements["data_types"] = format_data_types(metadata["data_types"])
        elif "dtypes" in metadata:
            replacements["data_types"] = format_data_types(metadata["dtypes"])

        # Memory usage
        if "memory_usage" in metadata:
            replacements["memory_usage"] = format_memory_usage(metadata["memory_usage"])
        else:
            replacements["memory_usage"] = "Not available"

        # Data quality
        if "missing_values" in metadata:
            replacements["missing_values"] = format_missing_values(metadata["missing_values"])
        else:
            replacements["missing_values"] = "Not analyzed"

        # Set default values for other placeholders
        replacements.setdefault("duplicates", "Not analyzed")
        replacements.setdefault("issues", "None detected")
        replacements.setdefault("column_summary", "Column details not available")

        # Recommendations (these would typically come from the LLM)
        for i in range(1, 6):
            replacements.setdefault(f"recommendation_{i}", f"Recommendation {i} not available")

        # Format the template with the replacements
        # Use a regex-based approach to replace only the placeholders that exist in the template
        formatted_template = template
        for key, value in replacements.items():
            placeholder = "{" + key + "}"
            formatted_template = formatted_template.replace(placeholder, str(value))

        return formatted_template

    except Exception as e:
        logger.error(f"Error formatting data profile template: {str(e)}", exc_info=True)
        # Return the original template with an error note
        return template + f"\n\nError formatting template: {str(e)}"


def format_number(number: Union[int, float, str, None]) -> str:
    """
    Format a number with commas as thousands separators.

    Args:
        number: The number to format

    Returns:
        Formatted number string
    """
    # Validate the input
    if number is None:
        logger.warning("Received None value for number formatting, using 'N/A'")
        return "N/A"

    try:
        # Convert to int if it's a string or float
        if isinstance(number, str):
            # Check if the string is empty or not a valid number
            if not number.strip() or not any(c.isdigit() for c in number):
                logger.warning(f"Invalid number string: '{number}', using 'N/A'")
                return "N/A"
            number = float(number)

        # Validate the number is not NaN or infinity
        if isinstance(number, float) and (number != number or number == float('inf') or number == float('-inf')):
            logger.warning(f"Invalid number value: {number}, using 'N/A'")
            return "N/A"

        # Format as integer if it's a whole number
        if isinstance(number, float) and number.is_integer():
            return f"{int(number):,}"
        elif isinstance(number, int):
            return f"{number:,}"
        else:
            # Format with 2 decimal places for floats
            return f"{number:,.2f}"
    except (ValueError, TypeError) as e:
        # Log the error and return a fallback value
        logger.error(f"Error formatting number '{number}': {str(e)}")
        return "N/A"


def format_data_types(dtypes: Dict[str, str]) -> str:
    """
    Format data types dictionary into a readable string.

    Args:
        dtypes: Dictionary of column names to data types

    Returns:
        Formatted string of data types
    """
    if not dtypes:
        return "Not available"

    # Count occurrences of each data type
    type_counts = {}
    for dtype in dtypes.values():
        dtype_str = str(dtype)
        # Simplify pandas/numpy dtypes
        if "int" in dtype_str:
            simple_type = "numeric"
        elif "float" in dtype_str:
            simple_type = "numeric"
        elif "object" in dtype_str:
            simple_type = "categorical"
        elif "datetime" in dtype_str:
            simple_type = "datetime"
        elif "bool" in dtype_str:
            simple_type = "boolean"
        else:
            simple_type = dtype_str

        type_counts[simple_type] = type_counts.get(simple_type, 0) + 1

    # Format as a readable string
    type_strings = [f"{count} {dtype}" for dtype, count in type_counts.items()]
    return ", ".join(type_strings)


def format_missing_values(missing_values: Dict[str, int]) -> str:
    """
    Format missing values dictionary into a readable string.

    Args:
        missing_values: Dictionary of column names to missing value counts

    Returns:
        Formatted string of missing values
    """
    if not missing_values:
        return "None detected"

    # Count total missing values
    total_missing = sum(missing_values.values())
    if total_missing == 0:
        return "None detected"

    # Count columns with missing values
    cols_with_missing = sum(1 for count in missing_values.values() if count > 0)

    return f"{total_missing} missing values across {cols_with_missing} columns"


def format_memory_usage(memory_usage: Union[int, float]) -> str:
    """
    Format memory usage into a human-readable string.

    Args:
        memory_usage: Memory usage in bytes

    Returns:
        Formatted memory usage string
    """
    try:
        # Convert to int if it's a string
        if isinstance(memory_usage, str):
            memory_usage = float(memory_usage)

        # Format based on size
        if memory_usage < 1024:
            return f"{memory_usage} bytes"
        elif memory_usage < 1024 * 1024:
            return f"{memory_usage / 1024:.2f} KB"
        elif memory_usage < 1024 * 1024 * 1024:
            return f"{memory_usage / (1024 * 1024):.2f} MB"
        else:
            return f"{memory_usage / (1024 * 1024 * 1024):.2f} GB"
    except (ValueError, TypeError):
        # Return as-is if conversion fails
        return str(memory_usage)
