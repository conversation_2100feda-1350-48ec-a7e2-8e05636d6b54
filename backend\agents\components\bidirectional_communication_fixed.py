"""
Component for enabling bidirectional communication between personas.
"""

import logging
import json
import time
from typing import Dict, Any, List, Optional, Tuple

from .base import AgentComponent
from ..registry import AgentRegistry

logger = logging.getLogger(__name__)


class BidirectionalCommunicationComponent(AgentComponent):
    """
    Enables bidirectional communication between personas, allowing specialized personas
    to communicate back to the concierge or other personas.
    """

    def __init__(self):
        """Initialize the BidirectionalCommunicationComponent."""
        super().__init__()
        self.message_store = {}  # In-memory store for simplicity; could be replaced with Redis
        self.callback_registry = {}  # Store callback information

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"BidirectionalCommunicationComponent '{self.name}' initialized.")
        self.message_ttl = config.get("message_ttl", 3600)  # Default TTL: 1 hour
        self.agent_registry = AgentRegistry
        self.enable_auto_callbacks = config.get("enable_auto_callbacks", True)
        self.callback_threshold = config.get("callback_threshold", 0.7)

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the context to enable bidirectional communication.

        Args:
            context: Context dictionary containing request data.

        Returns:
            Updated context dictionary with bidirectional communication capabilities.
        """
        user_message = context.get("message", "")
        conversation_id = context.get("conversation_id")
        current_persona = context.get("persona_id", "unknown")

        logger.debug(f"BidirectionalCommunicationComponent processing for conversation {conversation_id}")

        # Initialize bidirectional communication context if not present
        if "bidirectional" not in context:
            context["bidirectional"] = {
                "callbacks": [],
                "messages": [],
                "pending_requests": []
            }

        # Check for callback commands
        if self._is_callback_command(user_message):
            # Extract callback target from command
            callback_target, callback_reason = self._extract_callback_info(user_message)
            if callback_target:
                # Register callback
                await self._register_callback(conversation_id, current_persona, callback_target, callback_reason)

                # Add callback information to the context
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["callback_request"] = {
                    "target_persona": callback_target,
                    "reason": callback_reason,
                    "message": f"I'll request assistance from the {callback_target} persona."
                }

                logger.info(f"Callback request initiated to {callback_target} for conversation {conversation_id}")
            else:
                logger.warning(f"Callback command detected but no target persona found: {user_message}")

        # Check for pending callbacks for this persona
        pending_callbacks = await self._check_pending_callbacks(conversation_id, current_persona)
        if pending_callbacks:
            # Add pending callbacks to the context
            context["bidirectional"]["pending_callbacks"] = pending_callbacks
            
            # Add callback information to the metadata
            context["metadata"] = context.get("metadata", {})
            context["metadata"]["pending_callbacks"] = {
                "count": len(pending_callbacks),
                "sources": [cb["source_persona"] for cb in pending_callbacks]
            }
            
            logger.info(f"Found {len(pending_callbacks)} pending callbacks for {current_persona}")

        # Check if this is a response to a callback
        if self._is_callback_response(user_message):
            # Extract callback response information
            response_target, response_message = self._extract_callback_response(user_message)
            if response_target:
                # Send the response
                await self._send_callback_response(conversation_id, current_persona, response_target, response_message)
                
                # Add response information to the context
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["callback_response"] = {
                    "target_persona": response_target,
                    "message": f"I've sent your response to the {response_target} persona."
                }
                
                logger.info(f"Callback response sent to {response_target} for conversation {conversation_id}")

        # Check for automatic callback detection
        if self.enable_auto_callbacks and not self._is_callback_command(user_message) and not self._is_callback_response(user_message):
            callback_needed, target_persona, reason = self._detect_callback_need(user_message, context)
            if callback_needed:
                # Add automatic callback suggestion to the context
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["auto_callback_suggestion"] = {
                    "target_persona": target_persona,
                    "reason": reason,
                    "message": f"It seems you might need assistance from the {target_persona} persona. Would you like me to request their help?"
                }
                
                logger.info(f"Automatic callback suggestion for {target_persona} in conversation {conversation_id}")

        return context

    def _is_callback_command(self, message: str) -> bool:
        """
        Determine if the message is a command to request a callback from another persona.

        Args:
            message: The user message.

        Returns:
            True if this is a callback command, False otherwise.
        """
        # Simple keyword-based detection
        callback_keywords = [
            "ask for help from", "request assistance from", "consult with", 
            "get input from", "collaborate with", "need help from",
            "check with", "verify with", "confirm with"
        ]

        return any(keyword in message.lower() for keyword in callback_keywords)

    def _extract_callback_info(self, message: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract the callback target and reason from a callback command.

        Args:
            message: The user message.

        Returns:
            A tuple containing the target persona and the reason for the callback.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Map keywords to consistent kebab-case persona IDs
        persona_mapping = {
            "analyst": "composable-analysis-ai",
            "analysis": "composable-analysis-ai",
            "marketer": "composable-marketing-ai",
            "marketing": "composable-marketing-ai",
            "classifier": "composable-classifier-ai",
            "classification": "composable-classifier-ai",
            "concierge": "concierge-agent"
        }
        target_persona = None
        
        for keyword, persona_id in persona_mapping.items():
            if keyword in message_lower:
                target_persona = persona_id
                break
        
        if not target_persona:
            return None, None
        
        # Extract reason (simple heuristic - everything after "because" or "for")
        reason = None
        if "because" in message_lower:
            reason = message_lower.split("because", 1)[1].strip()
        elif "for" in message_lower:
            reason = message_lower.split("for", 1)[1].strip()
        else:
            # Default reason
            reason = f"assistance with {message_lower}"
        
        return target_persona, reason

    async def _register_callback(self, conversation_id: str, source_persona: str, target_persona: str, reason: str) -> None:
        """
        Register a callback request.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The persona requesting the callback.
            target_persona: The persona to call back.
            reason: The reason for the callback.
        """
        callback_info = {
            "conversation_id": conversation_id,
            "source_persona": source_persona,
            "target_persona": target_persona,
            "reason": reason,
            "timestamp": time.time(),
            "status": "pending"
        }
        
        # Store the callback request
        key = f"{conversation_id}:{target_persona}:callbacks"
        if key not in self.callback_registry:
            self.callback_registry[key] = []
        
        self.callback_registry[key].append(callback_info)
        logger.debug(f"Registered callback request: {key}")

    async def _check_pending_callbacks(self, conversation_id: str, current_persona: str) -> List[Dict[str, Any]]:
        """
        Check for pending callbacks for a persona.

        Args:
            conversation_id: The ID of the conversation.
            current_persona: The current persona.

        Returns:
            A list of pending callback requests.
        """
        key = f"{conversation_id}:{current_persona}:callbacks"
        pending_callbacks = self.callback_registry.get(key, [])
        
        # Filter for pending callbacks only
        pending_callbacks = [cb for cb in pending_callbacks if cb["status"] == "pending"]
        
        if pending_callbacks:
            # Update status to "seen"
            for callback in pending_callbacks:
                callback["status"] = "seen"
            
            # Update the registry
            self.callback_registry[key] = pending_callbacks
            logger.debug(f"Found {len(pending_callbacks)} pending callbacks for {key}")
        
        return pending_callbacks

    def _is_callback_response(self, message: str) -> bool:
        """
        Determine if the message is a response to a callback request.

        Args:
            message: The user message.

        Returns:
            True if this is a callback response, False otherwise.
        """
        # Simple keyword-based detection
        response_keywords = [
            "respond to", "reply to", "answer", "tell", "inform", 
            "let know", "send back to", "report to", "update"
        ]

        return any(keyword in message.lower() for keyword in response_keywords)

    def _extract_callback_response(self, message: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract the target persona and response message from a callback response.

        Args:
            message: The user message.

        Returns:
            A tuple containing the target persona and the response message.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Map keywords to consistent kebab-case persona IDs
        persona_mapping = {
            "analyst": "composable-analysis-ai",
            "analysis": "composable-analysis-ai",
            "marketer": "composable-marketing-ai",
            "marketing": "composable-marketing-ai",
            "classifier": "composable-classifier-ai",
            "classification": "composable-classifier-ai",
            "concierge": "concierge-agent"
        }
        target_persona = None
        
        for keyword, persona_id in persona_mapping.items():
            if keyword in message_lower:
                target_persona = persona_id
                break
        
        if not target_persona:
            return None, None
        
        # Extract response message (simple heuristic - everything after "that" or ":")
        response_message = None
        if "that" in message_lower:
            response_message = message_lower.split("that", 1)[1].strip()
        elif ":" in message_lower:
            response_message = message_lower.split(":", 1)[1].strip()
        else:
            # Default response
            response_message = message_lower
        
        return target_persona, response_message

    async def _send_callback_response(self, conversation_id: str, source_persona: str, target_persona: str, response: str) -> None:
        """
        Send a response to a callback request.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The persona sending the response.
            target_persona: The persona to receive the response.
            response: The response message.
        """
        response_info = {
            "conversation_id": conversation_id,
            "source_persona": source_persona,
            "target_persona": target_persona,
            "response": response,
            "timestamp": time.time()
        }
        
        # Store the response
        key = f"{conversation_id}:{target_persona}:responses"
        if key not in self.message_store:
            self.message_store[key] = []
        
        self.message_store[key].append(response_info)
        logger.debug(f"Sent callback response: {key}")
        
        # Update the callback status
        callback_key = f"{conversation_id}:{source_persona}:callbacks"
        if callback_key in self.callback_registry:
            callbacks = self.callback_registry[callback_key]
            for callback in callbacks:
                if callback["source_persona"] == target_persona and callback["status"] in ["pending", "seen"]:
                    callback["status"] = "responded"
            
            # Update the registry
            self.callback_registry[callback_key] = callbacks

    def _detect_callback_need(self, message: str, context: Dict[str, Any]) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Detect if a callback to another persona is needed based on the message content.

        Args:
            message: The user message.
            context: The current context.

        Returns:
            A tuple containing a boolean indicating if a callback is needed, the target persona, and the reason.
        """
        # Simple rule-based detection - could be enhanced with NLP
        message_lower = message.lower()
        current_persona = context.get("persona_id", "unknown")
        
        # Check for analysis-related keywords when not in the analysis persona
        if current_persona != "composable-analysis-ai":
            analysis_keywords = ["analyze", "analysis", "data", "chart", "graph", "visualization", 
                               "statistics", "insights", "trends", "dataset", "csv", "excel"]
            if any(keyword in message_lower for keyword in analysis_keywords):
                return True, "composable-analysis-ai", "data analysis assistance"
        
        # Check for marketing-related keywords when not in the marketing persona
        if current_persona != "composable-marketing-ai":
            marketing_keywords = ["marketing", "campaign", "content", "social media", "advertisement", 
                                "copy", "seo", "brand", "promotion", "strategy"]
            if any(keyword in message_lower for keyword in marketing_keywords):
                return True, "composable-marketing-ai", "marketing expertise"
        
        # Check for classification-related keywords when not in the classification persona
        if current_persona != "composable-classifier-ai":
            classification_keywords = ["classify", "classification", "categorize", "sort", "group", 
                                     "label", "tag", "organize"]
            if any(keyword in message_lower for keyword in classification_keywords):
                return True, "composable-classifier-ai", "classification assistance"
        
        # Check for concierge-related keywords when not in the concierge persona
        if current_persona != "concierge-agent":
            concierge_keywords = ["guide", "help", "assist", "recommend", "suggest", 
                                "find", "navigate", "workflow", "process"]
            if any(keyword in message_lower for keyword in concierge_keywords):
                return True, "concierge-agent", "guidance and assistance"
        
        return False, None, None

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["bidirectional_communication"])
