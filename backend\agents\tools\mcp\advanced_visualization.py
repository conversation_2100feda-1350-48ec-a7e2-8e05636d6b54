"""
Advanced visualization MCP tool using PandasAI.

This module provides an MCP-compatible tool for creating advanced visualizations
by interpreting natural language prompts using PandasAI.
"""

import logging
import os
import io
import base64
import pandas as pd
from typing import Dict, Any, Optional

# PandasAI imports
import pandasai as pai
from pandasai import Agent


from .base import BaseMCPTool

logger = logging.getLogger(__name__)

# --- Reusing Helper Functions (Assume they are accessible or defined here/imported) ---
# If these are defined in a shared utility file, import them instead.
# For simplicity here, they are redefined.

def _try_load_dataframe(file_path: str) -> Optional[pd.DataFrame]:
    """Attempts to load a dataframe from the given path."""
    try:
        if file_path.lower().endswith(".csv"):
            df = pd.read_csv(file_path)
        elif file_path.lower().endswith((".xls", ".xlsx")):
            df = pd.read_excel(file_path)
        elif file_path.lower().endswith(".json"):
             df = pd.read_json(file_path)
        else:
            logger.warning(f"Unsupported file type for PandasAI visualization: {file_path}")
            return None
        if df.empty:
             logger.warning(f"Loaded dataframe from {file_path} is empty.")
             return None
        logger.info(f"Successfully loaded dataframe from {file_path} for PandasAI advanced visualization")
        return df
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        return None
    except pd.errors.EmptyDataError:
        logger.warning(f"File is empty: {file_path}")
        return None
    except Exception as e:
        logger.error(f"Error loading dataframe from {file_path}: {e}", exc_info=True)
        return None

def _get_pandasai_llm(provider: Optional[str], api_key: Optional[str], model: Optional[str] = None):
    """Instantiates a PandasAI LLM based on provider and API key."""
    from ..pandasai_v3.llm_providers import LLMProviderFactory

    provider = provider.lower() if provider else "openai"
    if not api_key:
        logger.error(f"API key missing for LLM provider: {provider}")
        raise ValueError(f"API key required for {provider}")

    try:
        return LLMProviderFactory.create_provider(provider=provider, api_key=api_key, model=model)
    except Exception as e:
        logger.error(f"Error instantiating LLM for {provider}: {e}", exc_info=True)
        raise ValueError(f"Could not instantiate LLM for {provider}: {e}")

# --- End Helper Functions ---


class AdvancedVisualizationTool(BaseMCPTool):
    """Tool for creating advanced visualizations using PandasAI based on natural language prompts."""

    def __init__(self):
        """Initialize the PandasAI advanced visualization tool."""
        super().__init__(
            # Match the 'type' used in the agent config
            name="advanced_visualization",
            description="Generates advanced data visualizations (e.g., heatmaps, 3D plots, network graphs, geospatial maps) based on a natural language prompt using PandasAI.",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the data file (CSV, Excel, JSON supported)."
                    },
                    "prompt": {
                        "type": "string",
                        "description": "Natural language prompt describing the desired advanced visualization (e.g., 'Heatmap of correlations', '3D scatter plot of X, Y, Z', 'Geospatial map showing locations colored by Value')."
                    },
                    "provider": {
                        "type": "string",
                        "description": "LLM provider for PandasAI (e.g., 'openai')."
                    },
                    "model": {
                        "type": "string",
                        "description": "Optional: Specific LLM model name."
                    },
                    "api_key": {
                        "type": "string",
                        "description": "API key for the selected LLM provider."
                    }
                    # Removed specific plot params like plot_type, x_column, z_column etc.
                },
                "required": ["file_path", "prompt", "provider", "api_key"]
            },
            output_schema={ # Keep output schema consistent with basic visualization tool
                 "type": "object",
                 "properties": {
                     "tool_name": {"type": "string"},
                     "status": {"type": "string"},
                     "isError": {"type": "boolean"},
                     "content": {
                         "type": "array",
                         "items": {
                             "type": "object",
                             "properties": {
                                 "type": {"type": "string", "enum": ["text", "image"]},
                                 "text": {"type": "string"},
                                 "image": {
                                     "type": "object",
                                     "properties": {
                                         "url": {"type": "string", "format": "uri", "description": "Base64 encoded PNG image data URI"}
                                     }
                                 }
                             },
                             "required": ["type"]
                         }
                     },
                     "metadata": {"type": "object"}
                 }
            },
            annotations={
                "title": "Advanced Visualize (PandasAI)",
                "readOnlyHint": True,
                "openWorldHint": False # Relies on external LLM
            }
        )
        self.data_dir = "data" # Default data directory

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration (e.g., data directory).

        Args:
            config: Configuration dictionary for the tool
        """
        if "data_dir" in config:
            self.data_dir = config["data_dir"]
            logger.info(f"Set data directory to: {self.data_dir}")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments using PandasAI.

        Args:
            arguments: Arguments for tool execution (file_path, prompt, provider, api_key, model?)

        Returns:
            Tool execution results in MCP format, potentially including a base64 image.
        """
        file_path_arg = arguments.get("file_path")
        prompt = arguments.get("prompt")
        provider = arguments.get("provider")
        api_key = arguments.get("api_key")
        model = arguments.get("model") # Optional

        # --- Input Validation ---
        if not file_path_arg or not prompt or not provider or not api_key:
             missing = [k for k, v in arguments.items() if k in ["file_path", "prompt", "provider", "api_key"] and not v]
             return {
                 "tool_name": self.name, "status": "error", "isError": True,
                 "content": [{"type": "text", "text": f"Error: Missing required arguments: {', '.join(missing)}"}],
                 "metadata": {"status": "error", "error_type": "missing_arguments"}
             }

        # --- Resolve File Path ---
        if not os.path.isabs(file_path_arg):
            file_path = os.path.join(self.data_dir, file_path_arg)
        else:
            file_path = file_path_arg

        # --- Load Data ---
        df = _try_load_dataframe(file_path)
        if df is None:
            return {
                "tool_name": self.name, "status": "error", "isError": True,
                "content": [{"type": "text", "text": f"Error: Could not load or data is empty in {file_path}."}],
                "metadata": {"file_path": file_path, "prompt": prompt, "status": "error", "error_type": "load_error"}
            }

        results_content = []
        results_metadata = {}
        is_error = False
        status = "success"

        try:
            # --- Instantiate PandasAI Agent ---
            llm = _get_pandasai_llm(provider, api_key, model)
            export_path = "exports/charts" # Same export path as basic viz
            os.makedirs(export_path, exist_ok=True)

            # Create agent with config
            config = {
                "llm": llm,
                "conversational": False,
                "verbose": False,
                "enforce_privacy": False,
                "save_charts": True,
                "save_charts_path": export_path
            }
            agent = Agent(df, config=config)

            # --- Run Query ---
            logger.info(f"Running PandasAI advanced visualization prompt: '{prompt}'")
            result = agent.chat(prompt)
            logger.info(f"PandasAI advanced visualization execution finished. Result: {result} (Type: {type(result)})")

            # --- Process Result (Identical logic to basic visualization tool) ---
            image_path = None
            if isinstance(result, str) and "exports/charts" in result and result.lower().endswith(".png"):
                 if os.path.exists(result):
                      image_path = result
                 else:
                      logger.warning(f"PandasAI returned image path '{result}' but file does not exist.")
                      results_content.append({"type": "text", "text": "Advanced chart generation was attempted, but the output file was not found."})
                      status = "partial_success"

            elif isinstance(result, str):
                 results_content.append({"type": "text", "text": f"PandasAI response: {result}"})
                 status = "success_text_response"

            elif result is None:
                 results_content.append({"type": "text", "text": "Advanced visualization request processed, but no specific chart was generated or returned."})
                 status = "success_no_output"

            else:
                 results_content.append({"type": "text", "text": f"PandasAI returned an unexpected result type: {type(result).__name__}. Result: {str(result)}"})
                 status = "unexpected_result"

            if image_path:
                try:
                    with open(image_path, "rb") as f:
                        img_bytes = f.read()
                    img_base64 = base64.b64encode(img_bytes).decode("utf-8")
                    results_content.append({
                        "type": "image",
                        "image": {"url": f"data:image/png;base64,{img_base64}"}
                    })
                    results_content.append({
                         "type": "text",
                         "text": f"Generated advanced visualization based on prompt: '{prompt}'"
                    })
                    results_metadata["image_path"] = image_path
                except Exception as img_e:
                    logger.error(f"Error reading or encoding image file {image_path}: {img_e}", exc_info=True)
                    results_content.append({"type": "text", "text": f"Error processing generated image file: {img_e}"})
                    status = "error"
                    is_error = True
                    results_metadata["error_type"] = "image_processing_error"


        except ValueError as e:
            logger.error(f"Configuration or instantiation error for PandasAI: {e}")
            results_content = [{"type": "text", "text": f"Error setting up visualization engine: {e}"}]
            results_metadata = {"status": "error", "error_type": "setup_error", "details": str(e)}
            is_error = True
            status = "error"
        except Exception as e:
            logger.error(f"Error during PandasAI advanced visualization execution: {e}", exc_info=True)
            error_detail = str(e)
            results_content = [{"type": "text", "text": f"An unexpected error occurred while generating the advanced visualization with PandasAI: {error_detail}"}]
            results_metadata = {"status": "error", "error_type": "pandasai_execution_error", "details": error_detail}
            is_error = True
            status = "error"

        if not results_content:
             results_content.append({"type": "text", "text": "No specific output generated."})
             if status == "success": status = "success_no_output"


        return {
            "tool_name": self.name,
            "status": status,
            "isError": is_error,
            "content": results_content,
            "metadata": {
                "file_path": file_path,
                "prompt": prompt,
                "implementation": "pandasai",
                **results_metadata
            }
        }

# Registration likely happens elsewhere for class-based tools in mcp/
