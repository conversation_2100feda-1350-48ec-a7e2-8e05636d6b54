"""
File models for the Datagenius backend.

This module provides Pydantic models for file-related functionality.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field


class FileBase(BaseModel):
    """Base model for file data."""
    filename: str
    file_size: int
    num_rows: Optional[int] = None
    columns: Optional[List[str]] = None


class FileCreate(FileBase):
    """Model for creating a new file."""
    file_path: str


class FileResponse(FileBase):
    """Model for file data returned to the client."""
    id: str
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class FileListResponse(BaseModel):
    """Model for file list response."""
    files: List[FileResponse]
