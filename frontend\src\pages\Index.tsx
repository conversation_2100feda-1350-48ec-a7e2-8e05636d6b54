
import { motion } from "framer-motion";
import { <PERSON> } from "react-router-dom";
import { 
  <PERSON><PERSON><PERSON>, 
  BarChart4, 
  <PERSON><PERSON>, 
  BrainCircuit, 
  Clock, 
  Database, 
  LineChart, 
  MessageSquare, 
  Share2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-brand-50 to-brand-100">
      {/* Hero Section */}
      <header className="container mx-auto px-4 py-16 md:py-24">
        <motion.div 
          className="max-w-3xl mx-auto text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <motion.h1 
            className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-brand-600 to-brand-800 bg-clip-text text-transparent"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            Welcome to DataGent
          </motion.h1>
          <motion.p 
            className="text-xl text-gray-700 mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            Your AI-powered business intelligence partner that transforms data into actionable insights.
          </motion.p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-brand-600 hover:bg-brand-700"
              asChild
            >
              <Link to="/signup">Get Started <ArrowRight className="ml-2 h-4 w-4" /></Link>
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="border-brand-600 text-brand-600 hover:bg-brand-50"
              asChild
            >
              <Link to="/login">Sign In</Link>
            </Button>
          </div>
        </motion.div>
      </header>

      {/* Feature Section */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Powerful Features</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              DataGent brings powerful AI tools to help you understand your data and make informed decisions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard 
              icon={BrainCircuit} 
              title="AI-Powered Analysis" 
              description="Our advanced AI algorithms analyze your data to uncover hidden patterns and insights."
            />
            <FeatureCard 
              icon={MessageSquare} 
              title="Conversational Interface" 
              description="Ask questions about your data in natural language and get immediate answers."
            />
            <FeatureCard 
              icon={BarChart4} 
              title="Automated Reporting" 
              description="Generate comprehensive reports with visualizations that tell the story behind your data."
            />
            <FeatureCard 
              icon={Clock} 
              title="Scheduled Updates" 
              description="Schedule recurring reports and get them delivered straight to your inbox."
            />
            <FeatureCard 
              icon={Database} 
              title="Data Integration" 
              description="Connect to your existing data sources and systems with our simple integration process."
            />
            <FeatureCard 
              icon={Share2} 
              title="Collaboration Tools" 
              description="Share insights with your team and collaborate on analysis in real-time."
            />
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-12 bg-brand-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">How It Works</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Getting started with DataGent is simple. Follow these steps to begin transforming your data into insights.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="absolute left-12 h-full w-1 bg-brand-200 hidden md:block"></div>
              
              <StepCard 
                number="1" 
                title="Sign Up for an Account" 
                description="Create your DataGent account in seconds to access all our features."
                linkText="Create Account"
                linkUrl="/signup"
              />
              
              <StepCard 
                number="2" 
                title="Select Your Industry" 
                description="Tell us about your business so we can customize our AI analysis to your needs."
                linkText="Learn More"
                linkUrl="/industry-selection"
              />
              
              <StepCard 
                number="3" 
                title="Connect Your Data" 
                description="Integrate your data sources seamlessly with our secure connectors."
                linkText="View Integrations"
                linkUrl="/data-integration"
              />
              
              <StepCard 
                number="4" 
                title="Ask Questions and Get Insights" 
                description="Use our conversational interface to query your data and generate reports."
                linkText="Try Data Chat"
                linkUrl="/data-chat"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Suggested Tasks */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Try These Tasks</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Explore what DataGent can do with these sample tasks.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <TaskCard 
              icon={MessageSquare}
              title="Analyze Customer Behavior" 
              description="Ask the AI about customer purchase patterns and get visualized insights."
              buttonText="Start Analysis"
              buttonLink="/data-chat"
            />
            <TaskCard 
              icon={LineChart}
              title="Create a Sales Report" 
              description="Generate a comprehensive sales report with key metrics and forecasts."
              buttonText="Create Report"
              buttonLink="/reports"
            />
            <TaskCard 
              icon={Clock}
              title="Set Up Weekly Reporting" 
              description="Schedule automated weekly reports to be sent to your team."
              buttonText="Schedule Reports"
              buttonLink="/reports"
            />
            <TaskCard 
              icon={Bot}
              title="Explore AI Marketplace" 
              description="Discover specialized AI models tailored for different business needs."
              buttonText="View Marketplace"
              buttonLink="/ai-marketplace"
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-brand-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Transform Your Data?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of businesses using DataGent to turn their data into actionable insights.
          </p>
          <Button 
            size="lg" 
            className="bg-white text-brand-600 hover:bg-gray-100"
            asChild
          >
            <Link to="/signup">Get Started for Free</Link>
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 bg-gray-800 text-gray-300">
        <div className="container mx-auto px-4 text-center">
          <p>© 2023 DataGent. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

// Component for Feature Cards
const FeatureCard = ({ icon: Icon, title, description }) => (
  <Card className="h-full">
    <CardHeader>
      <div className="w-12 h-12 rounded-full bg-brand-100 flex items-center justify-center mb-4">
        <Icon className="h-6 w-6 text-brand-600" />
      </div>
      <CardTitle>{title}</CardTitle>
    </CardHeader>
    <CardContent>
      <p className="text-gray-600">{description}</p>
    </CardContent>
  </Card>
);

// Component for Step Cards
const StepCard = ({ number, title, description, linkText, linkUrl }) => (
  <div className="flex mb-8 relative z-10">
    <div className="flex-shrink-0 w-24 text-center">
      <div className="w-12 h-12 rounded-full bg-brand-600 flex items-center justify-center text-white font-bold mx-auto">
        {number}
      </div>
    </div>
    <div className="ml-4 bg-white p-6 rounded-lg shadow-md flex-grow">
      <h3 className="font-bold text-xl mb-2">{title}</h3>
      <p className="text-gray-600 mb-4">{description}</p>
      <Link to={linkUrl} className="text-brand-600 hover:text-brand-700 font-medium flex items-center">
        {linkText} <ArrowRight className="ml-1 h-4 w-4" />
      </Link>
    </div>
  </div>
);

// Component for Task Cards
const TaskCard = ({ icon: Icon, title, description, buttonText, buttonLink }) => (
  <Card className="h-full">
    <CardHeader>
      <Icon className="h-6 w-6 text-brand-600 mb-2" />
      <CardTitle>{title}</CardTitle>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
    <CardFooter>
      <Button variant="outline" className="w-full border-brand-600 text-brand-600 hover:bg-brand-50" asChild>
        <Link to={buttonLink}>{buttonText}</Link>
      </Button>
    </CardFooter>
  </Card>
);

export default Index;
