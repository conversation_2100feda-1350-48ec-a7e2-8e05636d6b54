
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";

export const WelcomeScreen = () => {
  const navigate = useNavigate();

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen bg-gradient-to-b from-brand-100 to-brand-200 flex flex-col items-center justify-center p-4"
    >
      <motion.div
        initial={{ y: 20 }}
        animate={{ y: 0 }}
        className="text-center mb-8"
      >
        <motion.h1
          className="text-4xl font-bold mb-2 bg-gradient-to-r from-brand-600 to-brand-800 bg-clip-text text-transparent"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          Welcome to DataGent
        </motion.h1>
        <motion.p
          className="text-gray-600 mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          Your AI-powered business intelligence partner
        </motion.p>
      </motion.div>

      <motion.div
        className="w-full max-w-md space-y-4"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <Card className="p-6 backdrop-blur-sm bg-white/90">
          <Button
            variant="default"
            className="w-full mb-3 bg-brand-500 hover:bg-brand-600"
            onClick={() => navigate("/login")}
          >
            Login
          </Button>
          <Button
            variant="outline"
            className="w-full border-brand-500 text-brand-500 hover:bg-brand-50"
            onClick={() => navigate("/signup")}
          >
            Sign Up
          </Button>
        </Card>
      </motion.div>
    </motion.div>
  );
};
