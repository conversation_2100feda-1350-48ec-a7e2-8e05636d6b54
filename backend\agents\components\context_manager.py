"""
Component for managing shared context across agent interactions and personas.
"""

import logging
import json
import uuid
from typing import Dict, Any, List, Optional

from .base import AgentComponent
# May need access to conversation history or session storage later
# from backend.app.services.conversation_service import ConversationService

logger = logging.getLogger(__name__)


class ContextManagerComponent(AgentComponent):
    """
    Extracts, stores, and injects contextual information for conversations.
    Enables context sharing between different personas.
    """

    def __init__(self):
        """Initialize the ContextManagerComponent."""
        super().__init__()
        # self.conversation_service = ConversationService() # Placeholder
        self.context_store = {}  # In-memory store for simplicity; could be replaced with Redis

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"ContextManagerComponent '{self.name}' initialized.")
        self.context_ttl = config.get("context_ttl", 3600)  # Default TTL: 1 hour
        # Initialization logic, e.g., setting context storage strategy

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the context to extract and store relevant information.
        Manages context sharing between different personas.

        Args:
            context: Context dictionary containing request data and potentially prior context.

        Returns:
            Updated context dictionary with managed shared context.
        """
        user_message = context.get("message", "")
        conversation_id = context.get("conversation_id")
        if not conversation_id:
            # Generate a new conversation ID if none exists
            conversation_id = str(uuid.uuid4())
            context["conversation_id"] = conversation_id
            logger.debug(f"Generated new conversation ID: {conversation_id}")

        metadata = context.get("metadata", {})
        current_persona = context.get("persona_id", "unknown")

        # Initialize shared context if not present
        if "shared_context" not in context:
            context["shared_context"] = {}

        logger.debug(f"ContextManagerComponent processing for conversation {conversation_id}")

        # Check for context transfer commands
        if self._is_context_transfer_command(user_message):
            # Extract target persona from command
            target_persona = self._extract_target_persona(user_message)
            if target_persona:
                # Save current context for the target persona
                await self._save_context(conversation_id, context, target_persona)

                # Add transfer information to the context
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["context_transfer"] = {
                    "target_persona": target_persona,
                    "message": f"I'll transfer you to the {target_persona} persona with your current context."
                }

                logger.info(f"Context transfer initiated to {target_persona} for conversation {conversation_id}")
            else:
                logger.warning(f"Context transfer command detected but no target persona found: {user_message}")

        # Check if there's incoming context from another persona
        source_persona = context.get("source_persona")
        if source_persona:
            # Load context from the source persona
            shared_context = await self._load_context(conversation_id, source_persona)
            if shared_context:
                # Merge shared context into current context
                self._merge_context(context, shared_context)
                logger.info(f"Merged context from {source_persona} for conversation {conversation_id}")

        # --- Context Extraction Logic ---
        # Store the last user message
        context["shared_context"]["last_user_message"] = user_message

        # Extract entities from the message (placeholder for more sophisticated NLP)
        entities = self._extract_entities(user_message)
        if entities:
            context["shared_context"]["entities"] = entities
            logger.debug(f"Extracted entities: {entities}")

        # Track attached files
        if "attached_files" in context:
            context["shared_context"]["attached_files"] = context["attached_files"]
            logger.debug(f"Tracking attached files: {context['attached_files']}")

        # Check if persona recommendations were made by a previous component
        if "persona_recommendations" in metadata:
             context["shared_context"]["recent_recommendations"] = metadata["persona_recommendations"]
             logger.debug("Stored recent persona recommendations in shared context.")

        # Check if data guidance was given
        if "data_attachment_guidance" in metadata:
             context["shared_context"]["recent_data_guidance"] = metadata["data_attachment_guidance"]
             logger.debug("Stored recent data guidance in shared context.")

        # Store conversation history summary if available
        if "conversation_history" in context:
            # Just store the last few messages for context
            recent_history = context["conversation_history"][-5:] if len(context["conversation_history"]) > 5 else context["conversation_history"]
            context["shared_context"]["recent_history"] = recent_history
            logger.debug(f"Stored recent conversation history ({len(recent_history)} messages)")

        logger.debug(f"Updated shared context: {context['shared_context']}")

        # This component primarily modifies the context; it might not directly contribute to the response text.
        return context

    def _is_context_transfer_command(self, message: str) -> bool:
        """
        Determine if the message is a command to transfer context to another persona.

        Args:
            message: The user message.

        Returns:
            True if this is a context transfer command, False otherwise.
        """
        # Simple keyword-based detection
        transfer_keywords = [
            "transfer to", "switch to", "talk to", "use the", "change to",
            "connect me with", "let me speak with", "hand off to"
        ]

        return any(keyword in message.lower() for keyword in transfer_keywords)

    def _extract_target_persona(self, message: str) -> Optional[str]:
        """
        Extract the target persona from a context transfer command.

        Args:
            message: The user message.

        Returns:
            The name of the target persona, or None if no target persona is found.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Check for common persona names
        personas = ["analyst", "marketer", "classifier", "analysis", "marketing", "classification"]

        for persona in personas:
            if persona in message_lower:
                return persona

        return None

    def _extract_entities(self, message: str) -> Dict[str, Any]:
        """
        Extract entities from the user message.
        This is a placeholder for more sophisticated NLP-based entity extraction.

        Args:
            message: The user message.

        Returns:
            Dictionary of extracted entities.
        """
        entities = {}

        # Simple keyword-based entity extraction
        # Data types
        if any(keyword in message.lower() for keyword in ["csv", "excel", "spreadsheet"]):
            entities["data_type"] = "tabular"
        elif any(keyword in message.lower() for keyword in ["pdf", "document", "text"]):
            entities["data_type"] = "document"

        # Analysis types
        if any(keyword in message.lower() for keyword in ["chart", "graph", "plot", "visualization"]):
            entities["analysis_type"] = "visualization"
        elif any(keyword in message.lower() for keyword in ["summary", "summarize", "overview"]):
            entities["analysis_type"] = "summary"
        elif any(keyword in message.lower() for keyword in ["predict", "forecast", "model"]):
            entities["analysis_type"] = "prediction"

        return entities

    async def _save_context(self, conversation_id: str, context: Dict[str, Any], target_persona: str) -> None:
        """
        Save context for transfer to another persona.

        Args:
            conversation_id: The ID of the conversation.
            context: The current context.
            target_persona: The target persona.
        """
        # Create a simplified version of the context to share
        shared_context = {
            "conversation_id": conversation_id,
            "user_id": context.get("user_id"),
            "source_persona": context.get("persona_id", "concierge"),
            "conversation_history": context.get("conversation_history", []),
            "attached_files": context.get("attached_files", []),
            "shared_context": context.get("shared_context", {}),
            "metadata": {
                "shared_from": context.get("persona_id", "concierge"),
                "timestamp": context.get("timestamp"),
                "original_request": context.get("message")
            }
        }

        # Store the shared context
        key = f"{conversation_id}:{target_persona}"
        self.context_store[key] = shared_context
        logger.debug(f"Saved context for transfer: {key}")

    async def _load_context(self, conversation_id: str, source_persona: str) -> Optional[Dict[str, Any]]:
        """
        Load context shared from another persona.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The source persona.

        Returns:
            The shared context, or None if no shared context is found.
        """
        key = f"{conversation_id}:{source_persona}"
        shared_context = self.context_store.get(key)

        if shared_context:
            # Remove the context from the store after loading
            del self.context_store[key]
            logger.debug(f"Loaded and removed shared context: {key}")
            return shared_context

        return None

    def _merge_context(self, target_context: Dict[str, Any], shared_context: Dict[str, Any]) -> None:
        """
        Merge shared context into the target context.

        Args:
            target_context: The target context to update.
            shared_context: The shared context to merge.
        """
        # Merge conversation history
        if "conversation_history" in shared_context:
            target_context["conversation_history"] = shared_context["conversation_history"]

        # Merge attached files
        if "attached_files" in shared_context:
            target_context["attached_files"] = shared_context["attached_files"]

        # Merge shared context
        if "shared_context" in shared_context:
            target_context["shared_context"] = target_context.get("shared_context", {})
            target_context["shared_context"].update(shared_context["shared_context"])

        # Add source information to metadata
        target_context["metadata"] = target_context.get("metadata", {})
        target_context["metadata"]["context_source"] = {
            "persona": shared_context.get("source_persona"),
            "original_request": shared_context.get("metadata", {}).get("original_request")
        }

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["context_management"])
