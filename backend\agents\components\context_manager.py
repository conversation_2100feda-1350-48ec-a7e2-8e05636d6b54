"""
Component for managing shared context across agent interactions and personas.
"""

import logging
import json
import uuid
from typing import Dict, Any, List, Optional

from .base import AgentComponent
from backend.schemas.agent_config_schemas import AgentProcessingContext # Added
# May need access to conversation history or session storage later
# from backend.app.services.conversation_service import ConversationService

logger = logging.getLogger(__name__)


class ContextManagerComponent(AgentComponent):
    """
    Extracts, stores, and injects contextual information for conversations.
    Enables context sharing between different personas.
    """

    def __init__(self):
        """Initialize the ContextManagerComponent."""
        super().__init__()
        # self.conversation_service = ConversationService() # Placeholder
        self.context_store = {}  # In-memory store for simplicity; could be replaced with Redis

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"ContextManagerComponent '{self.name}' initialized.")
        self.context_ttl = config.get("context_ttl", 3600)  # Default TTL: 1 hour
        # Initialization logic, e.g., setting context storage strategy

    async def process(self, context: AgentProcessingContext) -> AgentProcessingContext:
        """
        Process the context to extract and store relevant information.
        Manages context sharing between different personas.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object with managed shared context.
        """
        user_message = context.message
        conversation_id = context.conversation_id # Already a string (UUID)

        # current_persona_id = context.agent_config.id if context.agent_config else "unknown"
        # Using component_data for shared context storage within this component's scope for the conversation
        # This avoids polluting the top-level context fields directly unless explicitly merged.
        # 'shared_context_store' could be a key within context.component_data specific to this manager.
        
        # For simplicity, we'll use a sub-dictionary in component_data for this component's managed shared state.
        # This component will manage a dict at `context.component_data[self.name]['shared_across_personas']`
        if self.name not in context.component_data:
            context.component_data[self.name] = {}
        
        component_managed_shared_context = context.component_data[self.name].setdefault("shared_across_personas", {})

        logger.debug(f"ContextManagerComponent processing for conversation {conversation_id}")

        # Check for context transfer commands
        if self._is_context_transfer_command(user_message):
            target_persona = self._extract_target_persona(user_message)
            if target_persona:
                await self._save_context_for_transfer(conversation_id, context, target_persona)
                context.metadata["context_transfer"] = {
                    "target_persona": target_persona,
                    "message": f"I'll transfer you to the {target_persona} persona with your current context."
                }
                logger.info(f"Context transfer initiated to {target_persona} for conversation {conversation_id}")
            else:
                logger.warning(f"Context transfer command detected but no target persona found: {user_message}")

        # Check if there's incoming context from another persona (e.g., via initial_context)
        source_persona_info = context.initial_context.get("source_persona_context")
        if source_persona_info and isinstance(source_persona_info, dict):
            source_persona_id = source_persona_info.get("persona_id")
            shared_context_data = source_persona_info.get("shared_data")
            if source_persona_id and shared_context_data:
                self._merge_external_shared_context(component_managed_shared_context, shared_context_data)
                logger.info(f"Merged context from {source_persona_id} for conversation {conversation_id}")
                # Clear it from initial_context after processing to avoid re-processing
                del context.initial_context["source_persona_context"]


        # --- Context Extraction Logic into component_managed_shared_context ---
        component_managed_shared_context["last_user_message"] = user_message

        entities = self._extract_entities(user_message)
        if entities:
            component_managed_shared_context["entities"] = entities
            logger.debug(f"Extracted entities: {entities}")

        if "attached_files" in context.initial_context: # Assuming files are passed in initial_context
            component_managed_shared_context["attached_files"] = context.initial_context["attached_files"]
            logger.debug(f"Tracking attached files: {context.initial_context['attached_files']}")

        if "persona_recommendations" in context.metadata:
             component_managed_shared_context["recent_recommendations"] = context.metadata["persona_recommendations"]
             logger.debug("Stored recent persona recommendations in shared context.")

        if "data_attachment_guidance" in context.metadata:
             component_managed_shared_context["recent_data_guidance"] = context.metadata["data_attachment_guidance"]
             logger.debug("Stored recent data guidance in shared context.")

        if "conversation_history" in context.initial_context:
            history = context.initial_context["conversation_history"]
            recent_history = history[-5:] if len(history) > 5 else history
            component_managed_shared_context["recent_history"] = recent_history
            logger.debug(f"Stored recent conversation history ({len(recent_history)} messages)")

        logger.debug(f"Updated component-managed shared context: {component_managed_shared_context}")
        
        # The component_managed_shared_context is now updated within context.component_data[self.name]
        # Other components can access it via context.get_component_data(this_component_name) if needed,
        # or this component can explicitly place parts of it into context.metadata or context.response if required by downstream.
        return context

    def _is_context_transfer_command(self, message: str) -> bool:
        """
        Determine if the message is a command to transfer context to another persona.

        Args:
            message: The user message.

        Returns:
            True if this is a context transfer command, False otherwise.
        """
        # Simple keyword-based detection
        transfer_keywords = [
            "transfer to", "switch to", "talk to", "use the", "change to",
            "connect me with", "let me speak with", "hand off to"
        ]

        return any(keyword in message.lower() for keyword in transfer_keywords)

    def _extract_target_persona(self, message: str) -> Optional[str]:
        """
        Extract the target persona from a context transfer command.

        Args:
            message: The user message.

        Returns:
            The name of the target persona, or None if no target persona is found.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Check for common persona names
        personas = ["analyst", "marketer", "classifier", "analysis", "marketing", "classification"]

        for persona in personas:
            if persona in message_lower:
                return persona

        return None

    def _extract_entities(self, message: str) -> Dict[str, Any]:
        """
        Extract entities from the user message.
        This is a placeholder for more sophisticated NLP-based entity extraction.

        Args:
            message: The user message.

        Returns:
            Dictionary of extracted entities.
        """
        entities = {}

        # Simple keyword-based entity extraction
        # Data types
        if any(keyword in message.lower() for keyword in ["csv", "excel", "spreadsheet"]):
            entities["data_type"] = "tabular"
        elif any(keyword in message.lower() for keyword in ["pdf", "document", "text"]):
            entities["data_type"] = "document"

        # Analysis types
        if any(keyword in message.lower() for keyword in ["chart", "graph", "plot", "visualization"]):
            entities["analysis_type"] = "visualization"
        elif any(keyword in message.lower() for keyword in ["summary", "summarize", "overview"]):
            entities["analysis_type"] = "summary"
        elif any(keyword in message.lower() for keyword in ["predict", "forecast", "model"]):
            entities["analysis_type"] = "prediction"

        return entities

    async def _save_context_for_transfer(self, conversation_id: str, context: AgentProcessingContext, target_persona: str) -> None:
        """
        Save context for transfer to another persona using the in-memory store.

        Args:
            conversation_id: The ID of the conversation.
            context: The current AgentProcessingContext.
            target_persona: The target persona.
        """
        # Extract relevant data from AgentProcessingContext
        shared_data_to_store = {
            "last_user_message": context.message,
            "entities": context.component_data.get(self.name, {}).get("shared_across_personas", {}).get("entities"),
            "attached_files": context.initial_context.get("attached_files"), # Assuming from initial
            "recent_history": context.component_data.get(self.name, {}).get("shared_across_personas", {}).get("recent_history"),
            # Add other relevant fields from component_managed_shared_context
            "other_shared_items": context.component_data.get(self.name, {}).get("shared_across_personas", {})
        }
        
        context_to_transfer = {
            "conversation_id": conversation_id,
            "user_id": str(context.user_id),
            "source_persona_id": context.agent_config.id if context.agent_config else "unknown",
            "shared_data": shared_data_to_store,
            "original_request_timestamp": datetime.now().isoformat() # Using current time for simplicity
        }

        key = f"transfer:{conversation_id}:{target_persona}"
        self.context_store[key] = context_to_transfer # self.context_store is the in-memory dict
        logger.debug(f"Saved context for transfer to {target_persona}: {key}")

    async def _load_context_for_transfer(self, conversation_id: str, current_persona_id: str) -> Optional[Dict[str, Any]]:
        """
        Load context intended for the current persona.

        Args:
            conversation_id: The ID of the conversation.
            current_persona_id: The ID of the current persona loading the context.

        Returns:
            The shared context data, or None if no shared context is found.
        """
        key = f"transfer:{conversation_id}:{current_persona_id}"
        transferred_context_package = self.context_store.pop(key, None) # Use pop to consume it

        if transferred_context_package:
            logger.debug(f"Loaded and removed context for transfer: {key}")
            return transferred_context_package.get("shared_data")
        return None

    def _merge_external_shared_context(self, current_shared_dict: Dict[str, Any], external_shared_data: Dict[str, Any]) -> None:
        """
        Merge externally provided shared data into the component's managed shared context.

        Args:
            current_shared_dict: The component's current internal shared context dictionary.
            external_shared_data: The shared data from an external source (e.g., another persona).
        """
        if external_shared_data and isinstance(external_shared_data, dict):
            # Simple update, can be made more sophisticated (e.g., deep merge, conflict resolution)
            current_shared_dict.update(external_shared_data)
            logger.debug("Merged external shared data into component's shared context.")


    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["context_management"])
