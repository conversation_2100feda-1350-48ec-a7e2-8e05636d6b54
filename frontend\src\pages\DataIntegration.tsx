import { useState, useEffect, useRef } from "react";
import { DashboardLayout } from "@/components/DashboardLayout";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Database, FileUp, MessageSquare, RefreshCw, Upload, Loader2, AlertCircle, CheckCircle2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { dataSourceApi, FileDataSourceCreate, DatabaseDataSourceCreate, ApiDataSourceCreate, McpDataSourceCreate } from "@/lib/dataSourceApi";
import { fileApi } from "@/lib/api";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const DataIntegration = () => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState<boolean>(false);
  const [selectedFileName, setSelectedFileName] = useState<string>("");
  const [files, setFiles] = useState<any[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    fileId: "",
    dbType: "postgresql",
    host: "",
    port: 5432,
    database: "",
    username: "",
    password: "",
    apiType: "rest",
    endpoint: "",
    authType: "api_key",
    authCredentials: "",
    namespace: "default",
    collectionIds: []
  });
  const [connectionStatus, setConnectionStatus] = useState<"idle" | "testing" | "success" | "error">("idle");
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [connectionSummary, setConnectionSummary] = useState<any>(null);

  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch user files for file data source option
  useEffect(() => {
    if (selectedOption === "file-upload" && currentStep === 2) {
      const fetchFiles = async () => {
        try {
          const response = await fileApi.getFiles();
          setFiles(response.files);
        } catch (error) {
          console.error("Error fetching files:", error);
          toast({
            title: "Error",
            description: "Failed to fetch files. Please try again.",
            variant: "destructive",
          });
        }
      };

      fetchFiles();
    }
  }, [selectedOption, currentStep, toast]);

  const integrationOptions = [
    {
      id: "file-upload",
      title: "File Upload",
      description: "Upload CSV, Excel, DOC, DOCX, or PDF files",
      icon: FileUp,
    },
    {
      id: "database",
      title: "Database Connection",
      description: "Connect to your existing database",
      icon: Database,
    },
    {
      id: "api",
      title: "API Integration",
      description: "Connect through REST or GraphQL APIs",
      icon: RefreshCw,
    },
    {
      id: "mcp",
      title: "MCP Server",
      description: "Connect to Model Context Protocol servers",
      icon: MessageSquare,
    },
  ];

  const handleNext = async () => {
    if (currentStep < 3) {
      // Validate form data before proceeding
      if (currentStep === 2) {
        if (!validateForm()) {
          return;
        }

        // Test connection before proceeding to step 3
        await testConnection();
      } else {
        setCurrentStep(currentStep + 1);
      }
    } else {
      // Navigate to data chat when integration is complete
      navigate("/data-chat");
    }
  };

  // Handle navigation when clicking on step numbers
  const handleStepClick = async (step: number) => {
    // Don't do anything if clicking the current step
    if (step === currentStep) return;

    // Allow going back to previous steps freely
    if (step < currentStep) {
      setCurrentStep(step);
      return;
    }

    // For moving forward, apply validation
    if (step > currentStep) {
      // Can't skip from step 1 to 3
      if (currentStep === 1 && step === 3) {
        toast({
          title: "Navigation Error",
          description: "Please configure your data source before validating.",
          variant: "destructive",
        });
        return;
      }

      // Moving from step 1 to 2 requires a selection
      if (currentStep === 1 && step === 2) {
        if (!selectedOption) {
          toast({
            title: "Selection Required",
            description: "Please select a data source type first.",
            variant: "destructive",
          });
          return;
        }
        setCurrentStep(2);
        return;
      }

      // Moving from step 2 to 3 requires validation and connection test
      if (currentStep === 2 && step === 3) {
        if (!validateForm()) {
          return;
        }
        await testConnection();
        // Note: testConnection will set currentStep to 3 if successful
      }
    }
  };

  const validateForm = () => {
    // Basic form validation
    if (!formData.name) {
      toast({
        title: "Validation Error",
        description: "Please enter a name for your data source.",
        variant: "destructive",
      });
      return false;
    }

    if (selectedOption === "file-upload" && !formData.fileId) {
      toast({
        title: "Validation Error",
        description: "Please select a file.",
        variant: "destructive",
      });
      return false;
    }

    if (selectedOption === "database") {
      if (!formData.host || !formData.database || !formData.username || !formData.password) {
        toast({
          title: "Validation Error",
          description: "Please fill in all database connection fields.",
          variant: "destructive",
        });
        return false;
      }
    }

    if (selectedOption === "api") {
      if (!formData.endpoint) {
        toast({
          title: "Validation Error",
          description: "Please enter an API endpoint URL.",
          variant: "destructive",
        });
        return false;
      }
    }

    if (selectedOption === "mcp") {
      if (!formData.endpoint) {
        toast({
          title: "Validation Error",
          description: "Please enter an MCP server endpoint URL.",
          variant: "destructive",
        });
        return false;
      }
    }

    return true;
  };

  const testConnection = async () => {
    setConnectionStatus("testing");
    setIsLoading(true);
    setConnectionError(null);

    try {
      // Create data source based on selected option
      let response;

      if (selectedOption === "file-upload") {
        const fileDataSource: FileDataSourceCreate = {
          name: formData.name,
          type: "file",
          description: formData.description,
          file_id: formData.fileId
        };

        response = await dataSourceApi.createFileDataSource(fileDataSource);
      } else if (selectedOption === "database") {
        const dbDataSource: DatabaseDataSourceCreate = {
          name: formData.name,
          type: "database",
          description: formData.description,
          db_type: formData.dbType,
          host: formData.host,
          port: formData.port,
          database: formData.database,
          username: formData.username,
          password: formData.password
        };

        response = await dataSourceApi.createDatabaseDataSource(dbDataSource);
      } else if (selectedOption === "api") {
        const apiDataSource: ApiDataSourceCreate = {
          name: formData.name,
          type: "api",
          description: formData.description,
          api_type: formData.apiType,
          endpoint: formData.endpoint,
          auth_type: formData.authType,
          auth_credentials: formData.authCredentials ? { key: formData.authCredentials } : undefined
        };

        response = await dataSourceApi.createApiDataSource(apiDataSource);
      } else if (selectedOption === "mcp") {
        const mcpDataSource: McpDataSourceCreate = {
          name: formData.name,
          type: "mcp",
          description: formData.description,
          endpoint: formData.endpoint,
          api_key: formData.authCredentials || undefined,
          namespace: formData.namespace,
          collection_ids: []
        };

        response = await dataSourceApi.createMcpDataSource(mcpDataSource);
      }

      // Set connection summary
      setConnectionSummary({
        id: response?.id,
        type: selectedOption === "file-upload" ? "File Upload" :
              selectedOption === "database" ? "Database Connection" :
              selectedOption === "api" ? "API Integration" : "MCP Server",
        name: formData.name,
        description: formData.description
      });

      setConnectionStatus("success");
      setCurrentStep(3);
    } catch (error) {
      console.error("Connection test failed:", error);
      setConnectionStatus("error");
      setConnectionError(error instanceof Error ? error.message : "Unknown error occurred");

      toast({
        title: "Connection Failed",
        description: error instanceof Error ? error.message : "Failed to connect to data source. Please check your settings and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Update the selected file name for display
    setSelectedFileName(file.name);

    // Reset previous upload state
    setUploadError(null);
    setUploadSuccess(false);
    setUploadProgress(0);
    setIsUploading(true);

    // Create a reference to store the interval ID
    let progressInterval: NodeJS.Timeout;

    try {
      // Simulate upload progress
      progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 300);

      // Upload the file
      const response = await fileApi.uploadFile(file);

      // Clear the progress interval
      clearInterval(progressInterval);
      setUploadProgress(100);

      // Add the new file to the files list and select it
      setFiles(prev => [response, ...prev]);
      handleInputChange("fileId", response.id);

      // Show success message
      toast({
        title: "File Uploaded",
        description: `${file.name} has been uploaded successfully.`,
      });

      // Set upload success state
      setUploadSuccess(true);

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
        setSelectedFileName("");
      }
    } catch (error) {
      console.error("Error uploading file:", error);

      // Handle authentication errors specifically
      if (error instanceof Error && error.message.includes('Authentication')) {
        setUploadError("Authentication failed. Please refresh the page and log in again.");

        // Show a toast for authentication errors
        toast({
          title: "Authentication Error",
          description: "Your session has expired. Please refresh the page and log in again.",
          variant: "destructive",
        });
      } else {
        setUploadError(error instanceof Error ? error.message : "Failed to upload file. Please try again.");
      }
    } finally {
      setIsUploading(false);
      // Clear the progress interval if it's still running
      clearInterval(progressInterval);
    }
  };


  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6 max-w-4xl mx-auto"
      >
        <div className="space-y-1">
          <h1 className="text-2xl font-bold">Data Integration</h1>
          <p className="text-muted-foreground">
            Connect your data sources to get started with AI-powered analytics
          </p>
        </div>

        <div className="flex justify-between items-center mb-8">
          <div className="flex flex-col space-y-1">
            <div className="flex items-center space-x-8">
              <div className="text-xs text-muted-foreground italic">Click numbers to navigate between steps</div>
            </div>
            <div className="flex items-center space-x-8">
              <div className={`flex flex-col items-center ${currentStep >= 1 ? "text-primary" : "text-muted-foreground"}`}>
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center border-2
                    ${currentStep >= 1 ? "border-primary bg-primary/10" : "border-muted"}
                    cursor-pointer hover:shadow-md transition-all hover:scale-110`}
                  onClick={() => handleStepClick(1)}
                  role="button"
                  aria-label="Go to step 1"
                  title="Go to step 1: Select Source"
              >
                1
              </div>
              <span className="text-xs mt-1">Select Source</span>
            </div>
              <div className={`flex flex-col items-center ${currentStep >= 2 ? "text-primary" : "text-muted-foreground"}`}>
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center border-2
                    ${currentStep >= 2 ? "border-primary bg-primary/10" : "border-muted"}
                    cursor-pointer hover:shadow-md transition-all hover:scale-110`}
                  onClick={() => handleStepClick(2)}
                  role="button"
                  aria-label="Go to step 2"
                  title="Go to step 2: Configure"
              >
                2
              </div>
              <span className="text-xs mt-1">Configure</span>
            </div>
              <div className={`flex flex-col items-center ${currentStep >= 3 ? "text-primary" : "text-muted-foreground"}`}>
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center border-2
                    ${currentStep >= 3 ? "border-primary bg-primary/10" : "border-muted"}
                    cursor-pointer hover:shadow-md transition-all hover:scale-110`}
                  onClick={() => handleStepClick(3)}
                  role="button"
                  aria-label="Go to step 3"
                  title="Go to step 3: Validate"
              >
                3
              </div>
              <span className="text-xs mt-1">Validate</span>
            </div>
            </div>
          </div>
        </div>

        {currentStep === 1 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            <h2 className="text-lg font-medium">Choose a data source</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {integrationOptions.map((option) => (
                <Card
                  key={option.id}
                  className={`cursor-pointer transition-all ${
                    selectedOption === option.id
                      ? "border-primary ring-1 ring-primary"
                      : "hover:border-primary/50"
                  }`}
                  onClick={() => setSelectedOption(option.id)}
                >
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center text-center space-y-3">
                      <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                        <option.icon className="h-6 w-6 text-primary" />
                      </div>
                      <h3 className="font-medium">{option.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {option.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        )}

        {currentStep === 2 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            <h2 className="text-lg font-medium">Configure your data source</h2>
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    Configure the connection details for your selected data source.
                  </p>

                  {selectedOption === "file-upload" && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm mb-1">Data Source Name</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="My CSV Data"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Description (Optional)</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Description of this data source"
                          value={formData.description}
                          onChange={(e) => handleInputChange("description", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Select File</label>
                        <select
                          className="w-full p-2 border rounded"
                          value={formData.fileId}
                          onChange={(e) => handleInputChange("fileId", e.target.value)}
                        >
                          <option value="">Select a file...</option>
                          {files.map(file => (
                            <option key={file.id} value={file.id}>
                              {file.filename} ({file.num_rows || "unknown"} rows)
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="mt-4">
                        <label className="block text-sm mb-1">Or Upload a New File</label>
                        <div className="flex items-center gap-2">
                          <input
                            type="file"
                            ref={fileInputRef}
                            className="hidden"
                            accept=".csv,.xlsx,.xls,.doc,.docx,.pdf"
                            onChange={handleFileChange}
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => fileInputRef.current?.click()}
                            disabled={isUploading}
                            className="flex items-center gap-2"
                          >
                            <Upload className="h-4 w-4" />
                            Choose File
                          </Button>
                          <span className="text-sm text-muted-foreground">
                            {selectedFileName || "No file selected"}
                          </span>
                          {isUploading && (
                            <Loader2 className="h-4 w-4 animate-spin ml-2" />
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Supported formats: CSV, Excel, DOC, DOCX, PDF
                        </div>

                        {isUploading && (
                          <div className="mt-2">
                            <Progress value={uploadProgress} className="h-2" />
                            <p className="text-xs text-center mt-1">{uploadProgress}% Uploaded</p>
                          </div>
                        )}

                        {uploadError && (
                          <Alert variant="destructive" className="mt-2">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Upload Failed</AlertTitle>
                            <AlertDescription>{uploadError}</AlertDescription>
                          </Alert>
                        )}

                        {uploadSuccess && (
                          <Alert variant="success" className="mt-2 bg-green-50 border-green-200 text-green-800">
                            <CheckCircle2 className="h-4 w-4 text-green-600" />
                            <AlertTitle>Upload Successful</AlertTitle>
                            <AlertDescription>File has been uploaded and selected.</AlertDescription>
                          </Alert>
                        )}
                      </div>
                    </div>
                  )}

                  {selectedOption === "database" && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm mb-1">Data Source Name</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="My Database"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Description (Optional)</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Description of this data source"
                          value={formData.description}
                          onChange={(e) => handleInputChange("description", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Database Type</label>
                        <select
                          className="w-full p-2 border rounded"
                          value={formData.dbType}
                          onChange={(e) => handleInputChange("dbType", e.target.value)}
                        >
                          <option value="postgresql">PostgreSQL</option>
                          <option value="mysql">MySQL</option>
                          <option value="sqlserver">SQL Server</option>
                          <option value="mongodb">MongoDB</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Host</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="host.example.com"
                          value={formData.host}
                          onChange={(e) => handleInputChange("host", e.target.value)}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm mb-1">Port</label>
                          <input
                            type="number"
                            className="w-full p-2 border rounded"
                            placeholder="5432"
                            value={formData.port}
                            onChange={(e) => handleInputChange("port", parseInt(e.target.value))}
                          />
                        </div>
                        <div>
                          <label className="block text-sm mb-1">Database Name</label>
                          <input
                            type="text"
                            className="w-full p-2 border rounded"
                            placeholder="mydb"
                            value={formData.database}
                            onChange={(e) => handleInputChange("database", e.target.value)}
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm mb-1">Username</label>
                          <input
                            type="text"
                            className="w-full p-2 border rounded"
                            placeholder="username"
                            value={formData.username}
                            onChange={(e) => handleInputChange("username", e.target.value)}
                          />
                        </div>
                        <div>
                          <label className="block text-sm mb-1">Password</label>
                          <input
                            type="password"
                            className="w-full p-2 border rounded"
                            placeholder="********"
                            value={formData.password}
                            onChange={(e) => handleInputChange("password", e.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedOption === "mcp" && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm mb-1">Data Source Name</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="My MCP Server"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Description (Optional)</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Description of this data source"
                          value={formData.description}
                          onChange={(e) => handleInputChange("description", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">MCP Server Endpoint</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="https://mcp.example.com"
                          value={formData.endpoint}
                          onChange={(e) => handleInputChange("endpoint", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">API Key (Optional)</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Enter API key if required"
                          value={formData.authCredentials}
                          onChange={(e) => handleInputChange("authCredentials", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Namespace</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="default"
                          value={formData.namespace}
                          onChange={(e) => handleInputChange("namespace", e.target.value)}
                        />
                      </div>
                    </div>
                  )}

                  {selectedOption === "api" && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm mb-1">Data Source Name</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="My API"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Description (Optional)</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Description of this data source"
                          value={formData.description}
                          onChange={(e) => handleInputChange("description", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">API Type</label>
                        <select
                          className="w-full p-2 border rounded"
                          value={formData.apiType}
                          onChange={(e) => handleInputChange("apiType", e.target.value)}
                        >
                          <option value="rest">REST</option>
                          <option value="graphql">GraphQL</option>
                          <option value="soap">SOAP</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm mb-1">API Endpoint URL</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="https://api.example.com/v1/data"
                          value={formData.endpoint}
                          onChange={(e) => handleInputChange("endpoint", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Authentication Method</label>
                        <select
                          className="w-full p-2 border rounded"
                          value={formData.authType}
                          onChange={(e) => handleInputChange("authType", e.target.value)}
                        >
                          <option value="api_key">API Key</option>
                          <option value="oauth">OAuth 2.0</option>
                          <option value="basic">Basic Auth</option>
                          <option value="bearer">Bearer Token</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm mb-1">API Key / Token</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Enter API key or token"
                          value={formData.authCredentials}
                          onChange={(e) => handleInputChange("authCredentials", e.target.value)}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {currentStep === 3 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            <h2 className="text-lg font-medium">Validate Connection</h2>
            <Card>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div className="flex items-center justify-center p-8">
                    <div className="text-center">
                      {connectionStatus === "success" ? (
                        <>
                          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium mb-2">Connection Successful!</h3>
                          <p className="text-muted-foreground mb-4">
                            Your data source has been connected successfully. You're ready to start analyzing your data.
                          </p>
                        </>
                      ) : connectionStatus === "error" ? (
                        <>
                          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium mb-2">Connection Failed</h3>
                          <p className="text-muted-foreground mb-4">
                            {connectionError || "Failed to connect to the data source. Please check your settings and try again."}
                          </p>
                        </>
                      ) : (
                        <>
                          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
                          </div>
                          <h3 className="text-lg font-medium mb-2">Testing Connection...</h3>
                          <p className="text-muted-foreground mb-4">
                            Please wait while we test the connection to your data source.
                          </p>
                        </>
                      )}
                    </div>
                  </div>

                  {connectionStatus === "success" && connectionSummary && (
                    <div className="bg-muted/30 rounded-lg p-4">
                      <h4 className="font-medium mb-2">Connection Summary</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="text-muted-foreground">Source Type:</div>
                        <div>{connectionSummary.type}</div>

                        <div className="text-muted-foreground">Name:</div>
                        <div>{connectionSummary.name}</div>

                        {connectionSummary.description && (
                          <>
                            <div className="text-muted-foreground">Description:</div>
                            <div>{connectionSummary.description}</div>
                          </>
                        )}

                        <div className="text-muted-foreground">ID:</div>
                        <div className="truncate">{connectionSummary.id}</div>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentStep(2)}
                    >
                      Configure Again
                    </Button>
                    <div className="flex items-center gap-2">
                      <div className="text-muted-foreground text-sm">
                        Data ready for analysis
                      </div>
                      <MessageSquare className="h-4 w-4 text-primary" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        <div className="flex justify-end mt-6">
          <Button
            onClick={handleNext}
            disabled={(currentStep === 1 && !selectedOption) || isLoading}
            className="flex items-center gap-2"
          >
            {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            {currentStep === 3 ? "Go to Data Chat" : "Continue"}
            {!isLoading && <ArrowRight className="h-4 w-4" />}
          </Button>
        </div>
      </motion.div>
    </DashboardLayout>
  );
};

export default DataIntegration;
