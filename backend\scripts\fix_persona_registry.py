"""
Fix the persona registry issue.

This script fixes the issue where personas are not being registered in the agent registry.
"""

import os
import sys
import logging

# Add the parent directory to sys.path to allow importing from app
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_persona_registry():
    """
    Fix the persona registry issue.
    """
    try:
        # Import necessary modules
        from agents.registry import AgentRegistry
        from agents.persona_manager import persona_manager
        from agents.composable import ComposableAgent
        from agents.marketing_agent.composable_agent import ComposableMarketingAgent
        from agents.classification.composable_agent import ComposableClassificationAgent
        from agents.analysis_agent.composable_agent import ComposableAnalysisAgent

        # Log the current state
        logger.info("Current registered personas: %s", AgentRegistry.list_registered_personas())

        # Register the composable agents manually
        AgentRegistry.register("composable-ai", ComposableAgent)
        AgentRegistry.register("composable-marketing-ai", ComposableMarketingAgent)
        AgentRegistry.register("composable-classifier-ai", ComposableClassificationAgent)
        AgentRegistry.register("composable-analysis-ai", ComposableAnalysisAgent)

        # Log the updated state
        logger.info("Updated registered personas: %s", AgentRegistry.list_registered_personas())

        # Load configurations from YAML files
        personas_dir = os.path.join(parent_dir, "personas")
        logger.info("Loading configurations from: %s", personas_dir)

        # Fix the persona manager's file loading logic
        # The issue is that it's skipping files with dashes in the name
        # Let's manually load the configurations
        import yaml
        from pathlib import Path

        config_path = Path(personas_dir)
        for file_path in config_path.glob("*.yaml"):
            # Skip version files with multiple dashes
            if file_path.stem.count("-") > 1:
                continue

            try:
                logger.info("Processing file: %s", file_path)
                with open(file_path, "r") as f:
                    config = yaml.safe_load(f)

                persona_id = config.get("id")
                if not persona_id:
                    logger.warning("Missing persona ID in configuration file %s", file_path)
                    continue

                # Store the configuration in the registry
                AgentRegistry._configurations[persona_id] = config
                logger.info("Loaded configuration for persona %s", persona_id)
            except Exception as e:
                logger.error("Error processing configuration file %s: %s", file_path, e)

        # Log the configurations
        logger.info("Loaded configurations: %s", list(AgentRegistry._configurations.keys()))

        # Verify that the composable-marketing-ai persona is registered
        if "composable-marketing-ai" in AgentRegistry.list_registered_personas():
            logger.info("composable-marketing-ai is registered in the agent registry")
            config = AgentRegistry.get_configuration("composable-marketing-ai")
            if config:
                logger.info("composable-marketing-ai configuration is loaded")
            else:
                logger.warning("composable-marketing-ai configuration is not loaded")
        else:
            logger.error("composable-marketing-ai is not registered in the agent registry")

    except Exception as e:
        logger.error("Error fixing persona registry: %s", e, exc_info=True)

if __name__ == "__main__":
    fix_persona_registry()
