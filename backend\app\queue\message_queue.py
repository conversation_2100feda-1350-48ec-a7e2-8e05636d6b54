"""
Message queue implementation for the Datagenius backend.

This module provides a message queue implementation for handling chat messages.
"""

import logging
import asyncio
import uuid
from typing import Dict, Any, List, Callable, Coroutine, Optional
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

# Message status constants
MESSAGE_STATUS_PENDING = "pending"
MESSAGE_STATUS_PROCESSING = "processing"
MESSAGE_STATUS_COMPLETED = "completed"
MESSAGE_STATUS_FAILED = "failed"

# Task types
TASK_TYPE_PROCESS_MESSAGE = "process_message"
TASK_TYPE_INITIATE_CONVERSATION = "initiate"

class MessageTask:
    """Class representing a message task in the queue."""

    def __init__(self,
                 task_id: str,
                 user_id: int,
                 conversation_id: str,
                 message: str,
                 context: Dict[str, Any] = None,
                 metadata: Dict[str, Any] = None):
        """
        Initialize a message task.

        Args:
            task_id: Unique ID for the task
            user_id: ID of the user who sent the message
            conversation_id: ID of the conversation
            message: The message content (empty for initiation tasks)
            context: Additional context for the message (includes task_type)
            metadata: Additional metadata for the message
        """
        self.task_id = task_id
        self.user_id = user_id
        self.conversation_id = conversation_id
        self.message = message
        self.context = context or {}
        self.metadata = metadata or {}
        self.status = MESSAGE_STATUS_PENDING
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.completed_at = None
        self.result = None
        self.error = None
        # Determine task type from context, default to process_message
        self.task_type = self.context.get("task_type", TASK_TYPE_PROCESS_MESSAGE)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the task to a dictionary."""
        return {
            "task_id": self.task_id,
            "user_id": self.user_id,
            "conversation_id": self.conversation_id,
            "message": self.message,
            "context": self.context,
            "metadata": self.metadata,
            "status": self.status,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "result": self.result,
            "error": self.error,
            "task_type": self.task_type # Include task_type
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MessageTask':
        """Create a task from a dictionary."""
        task = cls(
            task_id=data["task_id"],
            user_id=data["user_id"],
            conversation_id=data["conversation_id"],
            message=data["message"],
            context=data.get("context", {}),
            metadata=data.get("metadata", {})
        )
        task.status = data.get("status", MESSAGE_STATUS_PENDING)
        task.created_at = datetime.fromisoformat(data["created_at"]) if isinstance(data["created_at"], str) else data["created_at"]
        task.updated_at = datetime.fromisoformat(data["updated_at"]) if isinstance(data["updated_at"], str) else data["updated_at"]

        if data.get("completed_at"):
            task.completed_at = datetime.fromisoformat(data["completed_at"]) if isinstance(data["completed_at"], str) else data["completed_at"]

        task.result = data.get("result")
        task.error = data.get("error")
        # Set task_type from loaded data or context
        task.task_type = data.get("task_type", task.context.get("task_type", TASK_TYPE_PROCESS_MESSAGE))
        return task


class InMemoryMessageQueue:
    """In-memory implementation of a message queue."""

    def __init__(self):
        """Initialize the message queue."""
        self.queue: List[MessageTask] = []
        self.processing: Dict[str, MessageTask] = {}
        self.completed: Dict[str, MessageTask] = {}
        self.failed: Dict[str, MessageTask] = {}
        self.handlers: List[Callable[[MessageTask], Coroutine[Any, Any, None]]] = []
        self.is_running = False
        self.worker_task = None

    async def start(self):
        """Start the message queue worker."""
        if self.is_running:
            return

        self.is_running = True
        self.worker_task = asyncio.create_task(self._worker())
        logger.info("Message queue worker started")

    async def stop(self):
        """Stop the message queue worker."""
        if not self.is_running:
            return

        self.is_running = False
        if self.worker_task:
            self.worker_task.cancel()
            try:
                await self.worker_task
            except asyncio.CancelledError:
                pass
            self.worker_task = None
        logger.info("Message queue worker stopped")

    async def enqueue(self,
                     user_id: int,
                     conversation_id: str,
                     message: str,
                     context: Dict[str, Any] = None,
                     metadata: Dict[str, Any] = None) -> str:
        """
        Add a message task to the queue.

        Args:
            user_id: ID of the user who sent the message (or associated user for initiate)
            conversation_id: ID of the conversation
            message: The message content (empty for initiation tasks)
            context: Additional context (MUST include 'task_type' if not 'process_message')
            metadata: Additional metadata

        Returns:
            The task ID
        """
        task_id = str(uuid.uuid4())
        task = MessageTask(
            task_id=task_id,
            user_id=user_id,
            conversation_id=conversation_id,
            message=message,
            context=context,
            metadata=metadata
        )
        self.queue.append(task)
        logger.info(f"Task {task_id} (type: {task.task_type}) added to queue for conversation {conversation_id}")
        return task_id

    def register_handler(self, handler: Callable[[MessageTask], Coroutine[Any, Any, None]]):
        """
        Register a handler for completed/failed tasks.

        Args:
            handler: The handler function (takes MessageTask as input)
        """
        self.handlers.append(handler)

    def get_task(self, task_id: str) -> Optional[MessageTask]:
        """Get a task by ID."""
        for task in self.queue:
            if task.task_id == task_id: return task
        if task_id in self.processing: return self.processing[task_id]
        if task_id in self.completed: return self.completed[task_id]
        if task_id in self.failed: return self.failed[task_id]
        return None

    def get_tasks_for_conversation(self, conversation_id: str) -> List[MessageTask]:
        """Get all tasks for a conversation."""
        tasks = []
        for task in self.queue:
            if task.conversation_id == conversation_id: tasks.append(task)
        for task in self.processing.values():
            if task.conversation_id == conversation_id: tasks.append(task)
        for task in self.completed.values():
            if task.conversation_id == conversation_id: tasks.append(task)
        for task in self.failed.values():
            if task.conversation_id == conversation_id: tasks.append(task)
        return tasks

    async def _handle_initiate_task(self, task: MessageTask):
        """Handles the generation of the initial agent greeting."""
        logger.info(f"Handling initiate task {task.task_id} for conversation {task.conversation_id}")
        from ..database import get_db, create_message
        from agents.registry import AgentRegistry
        from agents.components.mcp_server import MCPServerComponent
        from agents.components.essential_tools import ensure_essential_tools
        from agents.utils.prompt_template import PromptTemplate

        db = next(get_db())
        persona_id = task.context.get("persona_id")
        if not persona_id:
            raise ValueError("Missing persona_id in context for initiate task")

        agent = await AgentRegistry.create_agent_instance(persona_id)
        if not agent:
            raise ValueError(f"Failed to create agent instance for persona: {persona_id}")

        # Find the MCP server component within the agent
        mcp_server = next((comp for comp in agent.components if isinstance(comp, MCPServerComponent)), None)
        if not mcp_server:
            raise ValueError(f"MCPServerComponent not found in agent {persona_id}. Cannot generate greeting.")

        # Ensure the MCP server has all essential tools, including generate_content
        await ensure_essential_tools(mcp_server)

        # Check if there's a specific greeting prompt template, otherwise use default
        system_prompts = agent.config.get("system_prompts", {})
        greeting_prompt_template = system_prompts.get("greeting")
        default_prompt_template = system_prompts.get("default")

        # Use greeting template if available, otherwise fall back to default
        prompt_template = greeting_prompt_template or default_prompt_template

        if not prompt_template:
            logger.warning(f"No greeting or default system prompt found for persona {persona_id}. Using generic greeting.")
            prompt_template = "Hello! How can I help you today?"

        # Format the prompt
        try:
            template = PromptTemplate(prompt_template)
            formatted_prompt = template.format(
                is_first_conversation=True,
                has_data_source=False, # Assume no data source for initial greeting
                message="" # Empty message for greeting
                # Add other potential default context variables if needed by the prompt
            )

            # Log which template we're using
            template_type = "greeting" if greeting_prompt_template else "default"
            logger.info(f"Using {template_type} prompt template for persona {persona_id}")

        except Exception as e:
            logger.error(f"Error formatting prompt template for {persona_id}: {e}. Using raw template.")
            formatted_prompt = prompt_template

        # Call the generate_content tool using prompt_override
        tool_result = await mcp_server.call_tool("generate_content", {
            "content_type": "greeting", # Use special type
            "prompt_override": formatted_prompt,
            "provider": agent.config.get("provider", "groq"),
            "model": agent.config.get("model", "llama3-70b-8192"),
            "temperature": 0.7,
            # Add other minimal required fields for the tool schema if necessary
            "brand_description": "", # Example minimal field
            "target_audience": "", # Example minimal field
        })

        if tool_result.get("isError", False):
            error_content = tool_result.get("content", [{"text": "Unknown error"}])[0].get("text", "Unknown error")
            raise ValueError(f"Error generating greeting: {error_content}")

        # Extract content from the tool result
        content_items = tool_result.get("content", [])
        greeting_content = "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"])

        if not greeting_content:
            raise ValueError(f"Generated greeting content was empty for persona {persona_id}")

        # Create the initial AI message in the database
        greeting_message = create_message(
            db=db,
            conversation_id=task.conversation_id,
            sender="ai",
            content=greeting_content,
            metadata={"initial_greeting": True, "status": "completed", "complete": True} # Add status metadata
        )
        logger.info(f"Created initial greeting message {greeting_message.id} for conversation {task.conversation_id}")

        # Broadcast the new greeting message via WebSocket
        try:
            from ..api.chat import manager # Import the connection manager
            from ..utils.json_utils import ensure_serializable, sanitize_metadata

            message_data = {
                "id": greeting_message.id,
                "conversation_id": greeting_message.conversation_id,
                "sender": greeting_message.sender,
                "content": greeting_message.content,
                "metadata": sanitize_metadata(greeting_message.message_metadata),
                "created_at": greeting_message.created_at.isoformat()
            }
            await manager.broadcast({
                "type": "ai_message", # Use standard ai_message type
                "message": ensure_serializable(message_data)
            }, task.conversation_id)
            logger.info(f"Broadcasted initial greeting message {greeting_message.id} for conversation {task.conversation_id}")
        except Exception as broadcast_err:
            logger.error(f"Failed to broadcast initial greeting message for conversation {task.conversation_id}: {broadcast_err}", exc_info=True)

        # Return result (optional, maybe just the message ID or content)
        return {"message": greeting_content, "metadata": {"initial_greeting": True}}


    async def _handle_process_message_task(self, task: MessageTask):
        """Handles processing a regular user message."""
        logger.info(f"Handling process_message task {task.task_id} for conversation {task.conversation_id}")
        from ..database import get_db, get_conversation
        from agents.registry import AgentRegistry

        db = next(get_db())
        conversation = get_conversation(db, task.conversation_id)
        if not conversation:
            raise ValueError(f"Conversation {task.conversation_id} not found")

        if not conversation.persona_id:
             raise ValueError(f"Conversation {task.conversation_id} has no persona_id associated with it.")

        # Create the agent instance using the registry (handles config loading)
        agent = await AgentRegistry.create_agent_instance(conversation.persona_id)
        if not agent:
            raise ValueError(f"Failed to create agent instance for persona: {conversation.persona_id}")

        # Get recent conversation history
        from ..database import SessionLocal, Message

        # Create a new session for fetching conversation history
        history_db = SessionLocal()
        try:
            # Get the last 10 messages from the conversation (excluding the current one)
            recent_messages = history_db.query(Message).filter(
                Message.conversation_id == task.conversation_id
            ).order_by(Message.created_at.desc()).limit(10).all()

            # Reverse the messages to get them in chronological order
            recent_messages.reverse()

            # Format the messages for the context
            conversation_history = []
            for msg in recent_messages:
                conversation_history.append({
                    "sender": msg.sender,
                    "content": msg.content,
                    "metadata": msg.message_metadata
                })

            # Add conversation history to the context
            context_with_history = task.context.copy() if task.context else {}
            context_with_history["conversation_history"] = conversation_history

            logger.info(f"Added {len(conversation_history)} messages to conversation history")
        except Exception as e:
            logger.error(f"Error fetching conversation history: {str(e)}", exc_info=True)
            context_with_history = task.context
        finally:
            history_db.close()

        # Process the message using the agent's logic
        response = await agent.process_message(
            user_id=task.user_id,
            message=task.message,
            conversation_id=task.conversation_id,
            context=context_with_history # Pass along context with conversation history
        )
        return response


    async def _worker(self):
        """Worker process that processes tasks from the queue."""
        while self.is_running:
            if not self.queue:
                await asyncio.sleep(0.1)
                continue

            task = self.queue.pop(0)
            task.status = MESSAGE_STATUS_PROCESSING
            task.updated_at = datetime.now()
            self.processing[task.task_id] = task

            logger.info(f"Processing task {task.task_id} (type: {task.task_type}) for conversation {task.conversation_id}")

            try:
                # Process the task based on its type
                if task.task_type == TASK_TYPE_INITIATE_CONVERSATION:
                    task.result = await self._handle_initiate_task(task)
                elif task.task_type == TASK_TYPE_PROCESS_MESSAGE:
                    task.result = await self._handle_process_message_task(task)
                else:
                    raise ValueError(f"Unknown task type: {task.task_type}")

                # Update task status to completed
                task.status = MESSAGE_STATUS_COMPLETED
                task.updated_at = datetime.now()
                task.completed_at = datetime.now()
                del self.processing[task.task_id]
                self.completed[task.task_id] = task
                logger.info(f"Task {task.task_id} completed successfully")

            except Exception as e:
                logger.error(f"Error processing task {task.task_id}: {str(e)}", exc_info=True)
                task.status = MESSAGE_STATUS_FAILED
                task.updated_at = datetime.now()
                task.completed_at = datetime.now() # Mark completion even on failure
                task.error = str(e)
                del self.processing[task.task_id]
                self.failed[task.task_id] = task

            # Notify handlers regardless of success or failure
            for handler in self.handlers:
                try:
                    await handler(task)
                except Exception as handler_error:
                    logger.error(f"Error in task handler for task {task.task_id}: {str(handler_error)}", exc_info=True)


# Create a global instance of the message queue
message_queue = InMemoryMessageQueue()

# Start the message queue when the module is imported
# Use asyncio.ensure_future for compatibility if running in different event loops
async def start_message_queue_async():
    await message_queue.start()

# Ensure the queue starts when the application initializes
# This might be better placed in your main application startup logic (e.g., FastAPI lifespan)
# For now, ensure it runs in the current event loop if available
try:
    loop = asyncio.get_running_loop()
    loop.create_task(start_message_queue_async())
except RuntimeError: # No running event loop
    asyncio.run(start_message_queue_async())
