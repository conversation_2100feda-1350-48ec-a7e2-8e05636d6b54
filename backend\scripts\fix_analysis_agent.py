"""
<PERSON><PERSON><PERSON> to fix the composable analysis agent registration.

This script manually registers the composable analysis agent with the agent registry.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import the agent modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def fix_analysis_agent():
    """
    Fix the composable analysis agent registration.
    """
    try:
        # Import necessary modules
        from agents.registry import AgentRegistry
        from agents.analysis_agent.composable_agent import ComposableAnalysisAgent

        # Log the current state
        logger.info("Current registered personas: %s", AgentRegistry.list_registered_personas())

        # Register the composable analysis agent manually
        AgentRegistry.register("composable-analysis-ai", ComposableAnalysisAgent)

        # Log the updated state
        logger.info("Updated registered personas: %s", AgentRegistry.list_registered_personas())

        # Try to create an instance to verify it works
        import asyncio
        async def test_agent():
            agent = await AgentRegistry.create_agent_instance("composable-analysis-ai")
            if agent:
                logger.info("Successfully created composable analysis agent instance")
            else:
                logger.error("Failed to create composable analysis agent instance")

        # Run the test
        asyncio.run(test_agent())

        logger.info("Composable analysis agent registration fixed")
        return True
    except Exception as e:
        logger.error("Error fixing composable analysis agent registration: %s", str(e))
        return False

if __name__ == "__main__":
    success = fix_analysis_agent()
    if success:
        logger.info("Script completed successfully")
    else:
        logger.error("Script failed")
        sys.exit(1)
