import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Loader2, MessageSquare, Trash2, Edit, Plus, Search, X } from "lucide-react";
import { chatApi, Conversation } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow } from "date-fns";

interface ConversationListProps {
  onSelectConversation: (conversationId: string) => void;
  currentConversationId?: string;
}

export const ConversationList = ({ onSelectConversation, currentConversationId }: ConversationListProps) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [filteredConversations, setFilteredConversations] = useState<Conversation[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [editingTitle, setEditingTitle] = useState<string | null>(null);
  const [newTitle, setNewTitle] = useState("");
  const { toast } = useToast();
  const navigate = useNavigate();

  // Load conversations
  const loadConversations = async () => {
    setIsLoading(true);
    try {
      const response = await chatApi.getConversations();
      setConversations(response.conversations);
    } catch (error) {
      console.error("Failed to load conversations:", error);
      toast({
        title: "Error",
        description: "Failed to load conversation history.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load conversations on mount
  useEffect(() => {
    loadConversations();
  }, []);

  // Filter conversations based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredConversations(conversations);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = conversations.filter(conversation =>
      conversation.title?.toLowerCase().includes(query)
    );
    setFilteredConversations(filtered);
  }, [searchQuery, conversations]);

  // Start a new conversation
  const handleNewConversation = () => {
    navigate("/data-chat");
  };

  // Delete a conversation
  const handleDeleteConversation = async (conversationId: string, event: React.MouseEvent) => {
    event.stopPropagation();

    try {
      await chatApi.deleteConversation(conversationId);
      setConversations(conversations.filter(c => c.id !== conversationId));
      toast({
        title: "Conversation deleted",
        description: "The conversation has been deleted.",
      });
    } catch (error) {
      console.error("Failed to delete conversation:", error);
      toast({
        title: "Error",
        description: "Failed to delete the conversation.",
        variant: "destructive",
      });
    }
  };

  // Start editing a conversation title
  const handleStartEditing = (conversationId: string, currentTitle: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setEditingTitle(conversationId);
    setNewTitle(currentTitle);
  };

  // Save the edited title
  const handleSaveTitle = async (conversationId: string, event: React.MouseEvent) => {
    event.stopPropagation();

    if (!newTitle.trim()) {
      setEditingTitle(null);
      return;
    }

    try {
      await chatApi.updateConversation(conversationId, newTitle);
      setConversations(conversations.map(c =>
        c.id === conversationId ? { ...c, title: newTitle } : c
      ));
      toast({
        title: "Title updated",
        description: "The conversation title has been updated.",
      });
    } catch (error) {
      console.error("Failed to update conversation title:", error);
      toast({
        title: "Error",
        description: "Failed to update the conversation title.",
        variant: "destructive",
      });
    } finally {
      setEditingTitle(null);
    }
  };

  // Clear search query
  const handleClearSearch = () => {
    setSearchQuery("");
  };

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-4">
        <Button
          size="sm"
          variant="default"
          onClick={handleNewConversation}
          className="flex items-center gap-2 ml-auto"
        >
          <Plus className="h-4 w-4" />
          New Conversation
        </Button>
      </div>

      <div className="relative mb-4">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          placeholder="Search conversations..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-9 pr-9 py-2 w-full bg-gray-50 focus:bg-white transition-colors"
        />
        {searchQuery && (
          <button
            onClick={handleClearSearch}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {isLoading ? (
        <div className="flex justify-center p-8">
          <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
        </div>
      ) : filteredConversations.length === 0 && searchQuery ? (
        <div className="p-8 text-center">
          <div className="flex flex-col items-center gap-3">
            <Search className="h-10 w-10 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-700">No matching conversations</h3>
            <p className="text-gray-500">Try a different search term or clear the search</p>
            <Button
              size="sm"
              variant="outline"
              onClick={handleClearSearch}
              className="mt-2"
            >
              Clear search
            </Button>
          </div>
        </div>
      ) : filteredConversations.length === 0 && conversations.length === 0 ? (
        <Card className="p-8 text-center border-dashed border-2 bg-gray-50">
          <div className="flex flex-col items-center gap-3">
            <MessageSquare className="h-12 w-12 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-700">No conversations yet</h3>
            <p className="text-gray-500 max-w-xs">Start a new conversation to chat with your data assistant.</p>
            <Button
              size="sm"
              variant="default"
              onClick={handleNewConversation}
              className="mt-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Start a new conversation
            </Button>
          </div>
        </Card>
      ) : (
        <div className="space-y-3 max-h-[60vh] overflow-y-auto pr-1">
          {filteredConversations.map((conversation) => (
            <Card
              key={conversation.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 transition-all duration-200 hover:shadow-md group ${
                currentConversationId === conversation.id ? "bg-brand-50 border-brand-200 shadow-sm" : ""
              }`}
              onClick={() => onSelectConversation(conversation.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-full ${currentConversationId === conversation.id ? "bg-brand-100" : "bg-gray-100"}`}>
                    <MessageSquare className={`h-4 w-4 ${currentConversationId === conversation.id ? "text-brand-500" : "text-gray-500"}`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    {editingTitle === conversation.id ? (
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={newTitle}
                          onChange={(e) => setNewTitle(e.target.value)}
                          className="border rounded px-2 py-1 text-sm w-full focus:border-brand-300 focus:ring focus:ring-brand-200 focus:ring-opacity-50"
                          onClick={(e) => e.stopPropagation()}
                          autoFocus
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => handleSaveTitle(conversation.id, e)}
                          className="h-7 px-2"
                        >
                          Save
                        </Button>
                      </div>
                    ) : (
                      <div className="font-medium text-sm line-clamp-1">
                        {conversation.title || "Untitled Conversation"}
                      </div>
                    )}
                    <div className="text-xs text-gray-500 mt-1">
                      {formatDistanceToNow(new Date(conversation.updated_at), { addSuffix: true })}
                    </div>
                  </div>
                </div>

                {editingTitle !== conversation.id && (
                  <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => handleStartEditing(conversation.id, conversation.title, e)}
                      className="h-7 w-7 p-0 rounded-full hover:bg-gray-100"
                      title="Edit conversation title"
                    >
                      <Edit className="h-3.5 w-3.5 text-gray-500" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => handleDeleteConversation(conversation.id, e)}
                      className="h-7 w-7 p-0 rounded-full hover:bg-red-50 text-gray-500 hover:text-red-500"
                      title="Delete conversation"
                    >
                      <Trash2 className="h-3.5 w-3.5" />
                    </Button>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
