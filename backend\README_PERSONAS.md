# Persona Management System

This document describes the persona management system implemented in the Datagenius backend.

## Overview

The persona management system provides a robust way to manage AI personas, including:

1. Loading persona configurations from YAML files
2. Synchronizing personas with the database
3. Managing persona purchases and access control
4. Versioning personas for different configurations

## Components

### 1. PersonaManager

The `PersonaManager` class in `agents/persona_manager.py` is responsible for:

- Loading persona configurations from YAML files
- Synchronizing personas with the database
- Providing access to persona configurations

### 2. PersonaService

The `PersonaService` class in `app/services/persona_service.py` provides:

- Methods for retrieving personas from the database
- Purchase functionality for personas
- Access control for personas

### 3. AgentRegistry

The `AgentRegistry` class in `agents/registry.py` is responsible for:

- Mapping persona IDs to agent implementations
- Loading agent configurations
- Creating agent instances

### 4. Database Models

The database models for personas are defined in:

- `app/database.py`: Database tables and functions
- `app/models/persona.py`: Pydantic models for API requests/responses

## Workflow

### Initialization

1. When the application starts, the `lifespan` event handler in `app/main.py`:
   - Initializes the database
   - Synchronizes personas with the database using `PersonaManager`
   - Loads agent configurations using `AgentRegistry`

### API Endpoints

The API endpoints for personas are defined in `app/api/personas.py`:

- `GET /personas`: List all personas
- `GET /personas/{persona_id}`: Get a specific persona
- `GET /personas/access/{persona_id}`: Check if a user has access to a persona
- `GET /personas/purchased`: Get a list of personas purchased by the user
- `POST /personas/{persona_id}/purchase`: Purchase a persona

### Purchase Flow

1. User selects a persona to purchase
2. Frontend calls `POST /personas/{persona_id}/purchase`
3. `PersonaService.purchase_persona` creates a purchase record in the database
4. User can now access the persona in the data chat

### Access Control

1. When loading personas in the data chat, the frontend calls `GET /personas/purchased`
2. `PersonaService.get_user_purchased_personas` returns a list of persona IDs purchased by the user
3. Frontend only shows personas that have been purchased

## Configuration Files

Persona configurations are stored in YAML files in the `backend/personas` directory:

```yaml
id: marketing-ai
name: Marketing AI
description: AI assistant for marketing tasks
industry: Marketing
skills:
  - Marketing Strategy
  - Campaign Planning
  - Content Creation
rating: 4.8
review_count: 120
image_url: /marketing-ai.svg
price: 15.0
provider: groq
model: llama3-70b-8192
is_active: true
agent_class: agents.marketing.MarketingAgent
```

## Versioning

Persona versions are stored in YAML files with the naming convention `{persona_id}-{version}.yaml`:

```yaml
id: marketing-ai-1.1.0
name: Marketing AI (v1.1.0)
description: Enhanced AI assistant for marketing tasks
industry: Marketing
skills:
  - Marketing Strategy
  - Campaign Planning
  - Content Creation
  - Social Media Marketing
rating: 4.9
review_count: 150
image_url: /marketing-ai.svg
price: 20.0
provider: groq
model: llama3-70b-8192
is_active: true
agent_class: agents.marketing.MarketingAgent
```

## Usage

### Adding a New Persona

1. Create a new YAML file in the `backend/personas` directory
2. Implement the agent class specified in the `agent_class` field
3. Restart the application to load the new persona

### Purchasing a Persona

```python
# Using the PersonaService
from app.services import persona_service

# Purchase a persona
result = persona_service.purchase_persona(db, user_id, persona_id, payment_method="credit_card")
```

### Checking Access

```python
# Using the PersonaService
from app.services import persona_service

# Check if a user has purchased a persona
has_access = persona_service.has_user_purchased_persona(db, user_id, persona_id)

# Get all personas purchased by a user
purchased_personas = persona_service.get_user_purchased_personas(db, user_id)
```
