#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to restart the Qdrant container and verify it's working.

This script stops and restarts the Qdrant container, then verifies
that it's running and accessible.
"""

import os
import sys
import time
import logging
import subprocess
import requests
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Constants
CONTAINER_NAME = "datagenius-qdrant"
DEFAULT_HOST = os.getenv("QDRANT_HOST", "localhost")
DEFAULT_PORT = int(os.getenv("QDRANT_PORT", "6333"))

def is_qdrant_running(host=DEFAULT_HOST, port=DEFAULT_PORT):
    """Check if Qdrant is running at the specified host and port."""
    # Try multiple endpoints
    endpoints = ["/health", "/", "/healthz"]
    
    for endpoint in endpoints:
        try:
            url = f"http://{host}:{port}{endpoint}"
            logger.info(f"Checking if Qdrant is running at {url}")
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                logger.info(f"Qdrant is running at {url}, response: {response.text}")
                return True
            else:
                logger.info(f"Qdrant health check at {url} returned status code {response.status_code}")
        except requests.RequestException as e:
            logger.info(f"Failed to connect to Qdrant at {url}: {str(e)}")
    
    return False

def restart_qdrant():
    """Restart the Qdrant container."""
    try:
        # Stop the container
        logger.info(f"Stopping {CONTAINER_NAME}...")
        subprocess.run(["docker", "stop", CONTAINER_NAME], check=True)
        logger.info(f"Container {CONTAINER_NAME} stopped")
        
        # Start the container
        logger.info(f"Starting {CONTAINER_NAME}...")
        subprocess.run(["docker", "start", CONTAINER_NAME], check=True)
        logger.info(f"Container {CONTAINER_NAME} started")
        
        # Wait for container to be ready
        max_retries = 20
        retry_delay = 3
        for i in range(max_retries):
            logger.info(f"Waiting for Qdrant to be ready (attempt {i+1}/{max_retries})...")
            if is_qdrant_running():
                logger.info("Qdrant is ready!")
                return True
            time.sleep(retry_delay)
        
        logger.error("Qdrant failed to start within the timeout period")
        return False
    except subprocess.CalledProcessError as e:
        logger.error(f"Error restarting Qdrant: {e}")
        return False

def main():
    """Main function."""
    logger.info("Restarting Qdrant container...")
    if restart_qdrant():
        logger.info("Qdrant container restarted successfully")
    else:
        logger.error("Failed to restart Qdrant container")

if __name__ == "__main__":
    main()
