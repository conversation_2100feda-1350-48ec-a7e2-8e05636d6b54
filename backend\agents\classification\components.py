"""
Classification components for the Datagenius agent system.

This module provides components for text classification tasks.
"""

import logging
import json
from typing import Dict, Any, Optional, List

from agents.components.base import AgentComponent
from .hf_classifier import classify_texts_with_hf
from .llm_classifier_v2 import classify_texts_with_llm_v2

# Configure logging
logger = logging.getLogger(__name__)


class ClassificationParserComponent(AgentComponent):
    """Component for parsing classification requests."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing ClassificationParserComponent")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        message = context.get("message", "")
        ctx = context.get("context", {})

        # Parse the classification request
        classification_type, params = self._parse_classification_request(message, ctx)

        # Update the context with the parsed information
        context["classification_type"] = classification_type
        context["classification_params"] = params

        logger.info(f"Parsed classification request: type={classification_type}, params={params}")

        return context

    def _parse_classification_request(self, message: str, context: Optional[Dict[str, Any]]) -> tuple:
        """
        Parse the classification request from the message and context.

        Args:
            message: The user's message text
            context: Additional context information

        Returns:
            Tuple of (classification_type, parameters)
        """
        # Default to context if provided
        if context and "classification_type" in context:
            return context["classification_type"], context.get("params", {})

        # Parse the message to determine the classification type and parameters
        message_lower = message.lower()

        # Check for Hugging Face classification
        if any(term in message_lower for term in ["hugging face", "hf", "transformer", "bert", "roberta"]):
            # Extract parameters from the message
            params = {}

            # Look for file ID
            if "file_id" in message_lower:
                parts = message_lower.split("file_id")
                if len(parts) > 1:
                    file_id_part = parts[1].strip()
                    if file_id_part.startswith(":"):
                        file_id_part = file_id_part[1:].strip()
                    file_id = file_id_part.split()[0].strip()
                    params["file_id"] = file_id

            # Look for model name
            if "model" in message_lower:
                parts = message_lower.split("model")
                if len(parts) > 1:
                    model_part = parts[1].strip()
                    if model_part.startswith(":"):
                        model_part = model_part[1:].strip()
                    model_name = model_part.split()[0].strip()
                    params["model_name"] = model_name

            return "hf", params

        # Check for LLM classification
        elif any(term in message_lower for term in ["llm", "language model", "gpt", "openai", "groq", "gemini"]):
            # Extract parameters from the message
            params = {}

            # Look for file ID
            if "file_id" in message_lower:
                parts = message_lower.split("file_id")
                if len(parts) > 1:
                    file_id_part = parts[1].strip()
                    if file_id_part.startswith(":"):
                        file_id_part = file_id_part[1:].strip()
                    file_id = file_id_part.split()[0].strip()
                    params["file_id"] = file_id

            # Look for provider
            for provider in ["openai", "groq", "gemini", "ollama"]:
                if provider in message_lower:
                    params["provider"] = provider.capitalize()
                    break

            return "llm", params

        # Default to unknown
        return "unknown", {}


class HuggingFaceClassifierComponent(AgentComponent):
    """Component for Hugging Face classification."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing HuggingFaceClassifierComponent")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        # Check if this component should process the request
        classification_type = context.get("classification_type")
        if classification_type != "hf":
            return context

        params = context.get("classification_params", {})
        user_id = context.get("user_id")

        logger.info(f"Processing HF classification request with params: {params}")

        # Check if required parameters are provided
        if "file_id" not in params:
            context["response"] = "Please provide a file ID for classification."
            context["metadata"] = {"error": "missing_file_id"}
            return context

        if "model_name" not in params:
            context["response"] = "Please provide a model name for classification."
            context["metadata"] = {"error": "missing_model_name"}
            return context

        # For testing purposes, we'll skip the database access and use mock data
        file_id = params["file_id"]
        model_name = params["model_name"]

        # Mock file data
        mock_filename = f"mock_file_{file_id}.csv"

        # Mock classification results
        categories = [
            {"category": "Technology", "confidence": 0.85},
            {"category": "Business", "confidence": 0.72},
            {"category": "Finance", "confidence": 0.68}
        ]

        # Use the prompt template if available
        prompt_templates = context.get("agent_config", {}).get("system_prompts", {})
        if "hf_classification" in prompt_templates:
            from agents.utils.prompt_template import PromptTemplate
            template = PromptTemplate(prompt_templates["hf_classification"])
            message = template.format(categories=json.dumps(categories))
        else:
            message = (f"I've classified the file '{mock_filename}' using the '{model_name}' model. "
                      f"The classification results show that the file contains content related to "
                      f"technology, business, and finance categories.")

        # Update the context with the response
        context["response"] = message
        context["metadata"] = {
            "classification_type": "hf",
            "file_id": params["file_id"],
            "model_name": model_name,
            "sample_results": [
                {"text": "Example text 1", "labels": ["Technology", "Software"]},
                {"text": "Example text 2", "labels": ["Business", "Finance"]},
                {"text": "Example text 3", "labels": ["Technology", "Hardware"]}
            ]
        }

        return context


class LLMClassifierComponent(AgentComponent):
    """Component for LLM classification using the model provider system."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing LLMClassifierComponent with model provider system")
        self.config = config

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        # Check if this component should process the request
        classification_type = context.get("classification_type")
        if classification_type != "llm":
            return context

        params = context.get("classification_params", {})

        logger.info(f"Processing LLM classification request with params: {params}")

        # Check if required parameters are provided
        if "file_id" not in params:
            context["response"] = "Please provide a file ID for classification."
            context["metadata"] = {"error": "missing_file_id"}
            return context

        # Get the provider (default to groq)
        provider_id = params.get("provider", "Groq").lower()
        if provider_id == "groq":
            provider_id = "groq"
        elif provider_id == "openai":
            provider_id = "openai"
        elif provider_id == "gemini":
            provider_id = "gemini"
        elif provider_id == "anthropic":
            provider_id = "anthropic"
        elif provider_id == "ollama":
            provider_id = "ollama"

        # Get model configuration from agent config or use defaults
        agent_config = context.get("agent_config", {})
        model_config = agent_config.get("model_config", {})

        # Get model ID based on provider
        model_id = model_config.get(f"{provider_id}_model")
        if not model_id:
            # Use default models based on provider
            if provider_id == "groq":
                model_id = "llama3-70b-8192"
            elif provider_id == "openai":
                model_id = "gpt-3.5-turbo"
            elif provider_id == "anthropic":
                model_id = "claude-3-sonnet-20240229"
            elif provider_id == "gemini":
                model_id = "gemini-pro"
            else:
                model_id = "llama3"  # Default for Ollama

        # Mock file data for now - in a real implementation, we would load the file
        file_id = params["file_id"]
        mock_filename = f"mock_file_{file_id}.csv"

        # Mock texts and hierarchy levels - in a real implementation, we would extract these from the file
        texts = [
            "We need to improve our customer support response times.",
            "The new product launch was successful with positive feedback.",
            "Users are requesting more features in the mobile app."
        ]
        hierarchy_levels = ["Theme", "Category"]

        try:
            # Use our new classifier with the model provider system
            # In a real implementation, we would pass the actual texts from the file
            results_df = await classify_texts_with_llm_v2(
                texts=texts,
                hierarchy_levels=hierarchy_levels,
                provider_id=provider_id,
                model_id=model_id,
                config={"temperature": 0.1},
                sample_size=3  # Just use a small sample for testing
            )

            # Convert results to a list of dictionaries for the response
            sample_results = []
            for _, row in results_df.iterrows():
                result = {
                    "text": row["text"],
                    "theme": row.get("theme", "Unknown"),
                    "category": row.get("category", "Unknown")
                }
                sample_results.append(result)

            # Create a response message
            response = (f"I've classified the file '{mock_filename}' using the {provider_id.capitalize()} "
                       f"model '{model_id}'. The classification results show that the file contains "
                       f"content related to various themes and categories.")

            # Update the context with the response
            context["response"] = response
            context["metadata"] = {
                "classification_type": "llm",
                "file_id": params["file_id"],
                "provider": provider_id,
                "model": model_id,
                "sample_results": sample_results
            }

        except Exception as e:
            logger.error(f"Error classifying texts: {str(e)}", exc_info=True)
            context["response"] = f"I encountered an error while trying to classify the file: {str(e)}"
            context["metadata"] = {
                "classification_type": "llm",
                "file_id": params["file_id"],
                "provider": provider_id,
                "error": str(e)
            }

        return context


class ClassificationErrorHandlerComponent(AgentComponent):
    """Component for handling classification errors."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing ClassificationErrorHandlerComponent")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        # Check if a response has already been generated
        if "response" in context:
            return context

        # Check if classification type is unknown
        classification_type = context.get("classification_type")
        if classification_type == "unknown":
            context["response"] = ("I'm not sure what kind of classification you're looking for. "
                                  "Please specify if you want to use Hugging Face or LLM classification, "
                                  "and provide the necessary parameters.")
            context["metadata"] = {"error": "unknown_classification_type"}

        return context
