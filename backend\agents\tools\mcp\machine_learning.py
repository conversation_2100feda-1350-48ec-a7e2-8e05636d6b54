"""
Machine learning MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for performing machine learning operations
including regression, classification, clustering, and anomaly detection.
"""

import logging
import os
import io
import json
import base64
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional

from .base import BaseMCPTool

logger = logging.getLogger(__name__)


class MachineLearningTool(BaseMCPTool):
    """Tool for performing machine learning operations."""

    def __init__(self):
        """Initialize the machine learning tool."""
        super().__init__(
            name="machine_learning",
            description="Perform machine learning operations on data",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "operation": {
                        "type": "string", 
                        "enum": [
                            "regression", 
                            "classification", 
                            "clustering", 
                            "anomaly_detection",
                            "feature_importance",
                            "model_evaluation"
                        ],
                        "description": "Type of machine learning operation to perform"
                    },
                    "target_column": {
                        "type": "string",
                        "description": "Target column for supervised learning (regression/classification)"
                    },
                    "feature_columns": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Feature columns to use (if empty, all numeric columns except target will be used)"
                    },
                    "n_clusters": {
                        "type": "integer",
                        "description": "Number of clusters for clustering operations"
                    },
                    "test_size": {
                        "type": "number",
                        "description": "Proportion of data to use for testing (0.0-1.0)"
                    },
                    "random_state": {
                        "type": "integer",
                        "description": "Random seed for reproducibility"
                    }
                },
                "required": ["file_path", "operation"]
            },
            annotations={
                "title": "Machine Learning",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.data_dir = "data"

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # Check if required libraries are installed
        try:
            import sklearn
            from sklearn.model_selection import train_test_split
            from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
            from sklearn.cluster import KMeans
            from sklearn.preprocessing import StandardScaler
            from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, classification_report
        except ImportError:
            logger.warning("scikit-learn not installed. Machine learning functionality will be limited.")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the machine learning tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            # Import required libraries
            try:
                from sklearn.model_selection import train_test_split
                from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
                from sklearn.cluster import KMeans
                from sklearn.preprocessing import StandardScaler
                from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, classification_report
                from sklearn.ensemble import IsolationForest
            except ImportError:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "scikit-learn is not installed. Please install it to use machine learning functionality."
                        }
                    ]
                }

            # Extract arguments
            file_path = arguments["file_path"]
            operation = arguments["operation"]
            target_column = arguments.get("target_column")
            feature_columns = arguments.get("feature_columns", [])
            n_clusters = arguments.get("n_clusters", 3)
            test_size = arguments.get("test_size", 0.2)
            random_state = arguments.get("random_state", 42)

            # Check if the path is relative and prepend the data directory
            if not os.path.isabs(file_path):
                file_path = os.path.join(self.data_dir, file_path)

            # Check if the file exists
            if not os.path.exists(file_path):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"File not found: {file_path}"
                        }
                    ]
                }

            # Load the data
            if file_path.endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ]
                }

            # Prepare data
            # Get numeric columns
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            
            # For supervised learning, ensure target column is provided
            if operation in ["regression", "classification"] and not target_column:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Target column is required for {operation}"
                        }
                    ]
                }
            
            # For supervised learning, ensure target column exists
            if operation in ["regression", "classification"] and target_column not in df.columns:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Target column '{target_column}' not found in data"
                        }
                    ]
                }

            # For supervised learning, remove target from features if not specified
            if operation in ["regression", "classification"]:
                if not feature_columns:
                    feature_columns = [col for col in numeric_cols if col != target_column]
                
                # Ensure all feature columns exist and are numeric
                non_existent = [col for col in feature_columns if col not in df.columns]
                non_numeric = [col for col in feature_columns if col in df.columns and col not in numeric_cols]
                
                if non_existent:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Feature columns not found in data: {', '.join(non_existent)}"
                            }
                        ]
                    }
                
                if non_numeric:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Non-numeric feature columns: {', '.join(non_numeric)}"
                            }
                        ]
                    }
            
            # For unsupervised learning, use all numeric columns if not specified
            if operation in ["clustering", "anomaly_detection"] and not feature_columns:
                feature_columns = numeric_cols
            
            # Perform the operation
            if operation == "regression":
                result = self._perform_regression(df, target_column, feature_columns, test_size, random_state)
            elif operation == "classification":
                result = self._perform_classification(df, target_column, feature_columns, test_size, random_state)
            elif operation == "clustering":
                result = self._perform_clustering(df, feature_columns, n_clusters, random_state)
            elif operation == "anomaly_detection":
                result = self._perform_anomaly_detection(df, feature_columns, random_state)
            elif operation == "feature_importance":
                result = self._perform_feature_importance(df, target_column, feature_columns, random_state)
            elif operation == "model_evaluation":
                result = self._perform_model_evaluation(df, target_column, feature_columns, test_size, random_state)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported operation: {operation}"
                        }
                    ]
                }
            
            # Return the result
            return {
                "content": [
                    {
                        "type": "text",
                        "text": result["summary"]
                    }
                ],
                "metadata": result["metadata"]
            }
            
        except Exception as e:
            logger.error(f"Error executing machine learning tool: {str(e)}", exc_info=True)
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error executing machine learning tool: {str(e)}"
                    }
                ]
            }

    def _perform_regression(self, df, target_column, feature_columns, test_size, random_state):
        """Perform regression analysis."""
        from sklearn.model_selection import train_test_split
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.metrics import mean_squared_error, r2_score
        
        # Prepare data
        X = df[feature_columns]
        y = df[target_column]
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)
        
        # Train model
        model = RandomForestRegressor(n_estimators=100, random_state=random_state)
        model.fit(X_train, y_train)
        
        # Make predictions
        y_pred = model.predict(X_test)
        
        # Calculate metrics
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_test, y_pred)
        
        # Get feature importance
        feature_importance = dict(zip(feature_columns, model.feature_importances_))
        feature_importance = {k: v for k, v in sorted(feature_importance.items(), key=lambda item: item[1], reverse=True)}
        
        # Create summary
        summary = f"""
# Regression Analysis Results

## Model Performance
- Mean Squared Error (MSE): {mse:.4f}
- Root Mean Squared Error (RMSE): {rmse:.4f}
- R² Score: {r2:.4f}

## Top Feature Importance
"""
        for i, (feature, importance) in enumerate(list(feature_importance.items())[:5]):
            summary += f"- {feature}: {importance:.4f}\n"
        
        # Create metadata
        metadata = {
            "model_type": "regression",
            "target_variable": target_column,
            "features": feature_columns,
            "metrics": {
                "mse": mse,
                "rmse": rmse,
                "r2": r2
            },
            "feature_importance": feature_importance
        }
        
        return {
            "summary": summary,
            "metadata": metadata
        }

    def _perform_classification(self, df, target_column, feature_columns, test_size, random_state):
        """Perform classification analysis."""
        from sklearn.model_selection import train_test_split
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.metrics import accuracy_score, classification_report
        
        # Prepare data
        X = df[feature_columns]
        y = df[target_column]
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)
        
        # Train model
        model = RandomForestClassifier(n_estimators=100, random_state=random_state)
        model.fit(X_train, y_train)
        
        # Make predictions
        y_pred = model.predict(X_test)
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        report = classification_report(y_test, y_pred, output_dict=True)
        
        # Get feature importance
        feature_importance = dict(zip(feature_columns, model.feature_importances_))
        feature_importance = {k: v for k, v in sorted(feature_importance.items(), key=lambda item: item[1], reverse=True)}
        
        # Create summary
        summary = f"""
# Classification Analysis Results

## Model Performance
- Accuracy: {accuracy:.4f}

## Class Metrics
"""
        for class_name, metrics in report.items():
            if class_name not in ['accuracy', 'macro avg', 'weighted avg']:
                summary += f"- Class {class_name}:\n"
                summary += f"  - Precision: {metrics['precision']:.4f}\n"
                summary += f"  - Recall: {metrics['recall']:.4f}\n"
                summary += f"  - F1-Score: {metrics['f1-score']:.4f}\n"
        
        summary += "\n## Top Feature Importance\n"
        for i, (feature, importance) in enumerate(list(feature_importance.items())[:5]):
            summary += f"- {feature}: {importance:.4f}\n"
        
        # Create metadata
        metadata = {
            "model_type": "classification",
            "target_variable": target_column,
            "features": feature_columns,
            "metrics": {
                "accuracy": accuracy,
                "classification_report": report
            },
            "feature_importance": feature_importance
        }
        
        return {
            "summary": summary,
            "metadata": metadata
        }

    def _perform_clustering(self, df, feature_columns, n_clusters, random_state):
        """Perform clustering analysis."""
        from sklearn.cluster import KMeans
        from sklearn.preprocessing import StandardScaler
        
        # Prepare data
        X = df[feature_columns]
        
        # Scale data
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Perform clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=random_state)
        clusters = kmeans.fit_predict(X_scaled)
        
        # Add cluster labels to original data
        df_with_clusters = df.copy()
        df_with_clusters['cluster'] = clusters
        
        # Calculate cluster statistics
        cluster_stats = {}
        for cluster_id in range(n_clusters):
            cluster_data = df_with_clusters[df_with_clusters['cluster'] == cluster_id]
            
            # Calculate statistics for each feature column
            col_stats = {}
            for col in feature_columns:
                col_stats[col] = {
                    "mean": cluster_data[col].mean(),
                    "median": cluster_data[col].median(),
                    "min": cluster_data[col].min(),
                    "max": cluster_data[col].max()
                }
            
            cluster_stats[f"cluster_{cluster_id}"] = {
                "size": len(cluster_data),
                "percentage": len(cluster_data) / len(df) * 100,
                "column_stats": col_stats
            }
        
        # Create summary
        summary = f"""
# Clustering Analysis Results

## Cluster Distribution
"""
        for cluster_id in range(n_clusters):
            stats = cluster_stats[f"cluster_{cluster_id}"]
            summary += f"- Cluster {cluster_id}: {stats['size']} samples ({stats['percentage']:.2f}%)\n"
        
        summary += "\n## Cluster Characteristics\n"
        for cluster_id in range(n_clusters):
            stats = cluster_stats[f"cluster_{cluster_id}"]
            summary += f"### Cluster {cluster_id}\n"
            
            # Show top 3 most distinctive features
            feature_diffs = {}
            for col in feature_columns:
                cluster_mean = stats['column_stats'][col]['mean']
                overall_mean = df[col].mean()
                feature_diffs[col] = abs(cluster_mean - overall_mean) / overall_mean if overall_mean != 0 else abs(cluster_mean)
            
            top_features = sorted(feature_diffs.items(), key=lambda x: x[1], reverse=True)[:3]
            
            for feature, diff in top_features:
                cluster_mean = stats['column_stats'][feature]['mean']
                overall_mean = df[feature].mean()
                direction = "higher" if cluster_mean > overall_mean else "lower"
                
                summary += f"- {feature}: {cluster_mean:.4f} ({direction} than average of {overall_mean:.4f})\n"
        
        # Create metadata
        metadata = {
            "model_type": "clustering",
            "n_clusters": n_clusters,
            "features": feature_columns,
            "cluster_stats": cluster_stats,
            "cluster_centers": kmeans.cluster_centers_.tolist()
        }
        
        return {
            "summary": summary,
            "metadata": metadata
        }

    def _perform_anomaly_detection(self, df, feature_columns, random_state):
        """Perform anomaly detection."""
        from sklearn.ensemble import IsolationForest
        
        # Prepare data
        X = df[feature_columns]
        
        # Train model
        model = IsolationForest(random_state=random_state, contamination=0.05)
        anomalies = model.fit_predict(X)
        
        # Convert predictions to anomaly labels (1 for normal, -1 for anomaly)
        df_with_anomalies = df.copy()
        df_with_anomalies['anomaly'] = anomalies
        
        # Get anomalies
        anomaly_data = df_with_anomalies[df_with_anomalies['anomaly'] == -1]
        normal_data = df_with_anomalies[df_with_anomalies['anomaly'] == 1]
        
        # Calculate statistics
        anomaly_stats = {}
        for col in feature_columns:
            anomaly_stats[col] = {
                "anomaly_mean": anomaly_data[col].mean(),
                "normal_mean": normal_data[col].mean(),
                "anomaly_min": anomaly_data[col].min(),
                "anomaly_max": anomaly_data[col].max(),
                "normal_min": normal_data[col].min(),
                "normal_max": normal_data[col].max()
            }
        
        # Create summary
        summary = f"""
# Anomaly Detection Results

## Overview
- Total samples: {len(df)}
- Normal samples: {len(normal_data)} ({len(normal_data) / len(df) * 100:.2f}%)
- Anomalies detected: {len(anomaly_data)} ({len(anomaly_data) / len(df) * 100:.2f}%)

## Feature Comparison (Anomalies vs. Normal)
"""
        for col in feature_columns:
            stats = anomaly_stats[col]
            anomaly_mean = stats['anomaly_mean']
            normal_mean = stats['normal_mean']
            
            if normal_mean != 0:
                diff_percent = abs(anomaly_mean - normal_mean) / normal_mean * 100
            else:
                diff_percent = abs(anomaly_mean - normal_mean) * 100
            
            if diff_percent > 10:  # Only show significant differences
                direction = "higher" if anomaly_mean > normal_mean else "lower"
                summary += f"- {col}: Anomalies have {diff_percent:.2f}% {direction} values on average\n"
        
        # Create metadata
        metadata = {
            "model_type": "anomaly_detection",
            "features": feature_columns,
            "anomaly_count": len(anomaly_data),
            "normal_count": len(normal_data),
            "anomaly_percentage": len(anomaly_data) / len(df) * 100,
            "anomaly_stats": anomaly_stats
        }
        
        return {
            "summary": summary,
            "metadata": metadata
        }

    def _perform_feature_importance(self, df, target_column, feature_columns, random_state):
        """Analyze feature importance."""
        from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
        
        # Prepare data
        X = df[feature_columns]
        y = df[target_column]
        
        # Determine if regression or classification
        unique_values = y.nunique()
        is_regression = unique_values > 10 or pd.api.types.is_float_dtype(y)
        
        # Train model
        if is_regression:
            model = RandomForestRegressor(n_estimators=100, random_state=random_state)
        else:
            model = RandomForestClassifier(n_estimators=100, random_state=random_state)
        
        model.fit(X, y)
        
        # Get feature importance
        feature_importance = dict(zip(feature_columns, model.feature_importances_))
        feature_importance = {k: v for k, v in sorted(feature_importance.items(), key=lambda item: item[1], reverse=True)}
        
        # Create summary
        summary = f"""
# Feature Importance Analysis

## Target Variable: {target_column}
## Model Type: {"Regression" if is_regression else "Classification"}

## Feature Importance Ranking
"""
        for i, (feature, importance) in enumerate(feature_importance.items()):
            summary += f"{i+1}. {feature}: {importance:.4f}\n"
        
        # Create metadata
        metadata = {
            "model_type": "feature_importance",
            "target_variable": target_column,
            "features": feature_columns,
            "is_regression": is_regression,
            "feature_importance": feature_importance
        }
        
        return {
            "summary": summary,
            "metadata": metadata
        }

    def _perform_model_evaluation(self, df, target_column, feature_columns, test_size, random_state):
        """Evaluate multiple models on the data."""
        from sklearn.model_selection import train_test_split, cross_val_score
        from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
        from sklearn.linear_model import LinearRegression, LogisticRegression
        from sklearn.svm import SVR, SVC
        from sklearn.metrics import mean_squared_error, r2_score, accuracy_score
        
        # Prepare data
        X = df[feature_columns]
        y = df[target_column]
        
        # Determine if regression or classification
        unique_values = y.nunique()
        is_regression = unique_values > 10 or pd.api.types.is_float_dtype(y)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)
        
        # Define models
        if is_regression:
            models = {
                "Random Forest": RandomForestRegressor(n_estimators=100, random_state=random_state),
                "Linear Regression": LinearRegression(),
                "Support Vector Regression": SVR()
            }
            scoring = "r2"
        else:
            models = {
                "Random Forest": RandomForestClassifier(n_estimators=100, random_state=random_state),
                "Logistic Regression": LogisticRegression(random_state=random_state, max_iter=1000),
                "Support Vector Classification": SVC(random_state=random_state)
            }
            scoring = "accuracy"
        
        # Evaluate models
        results = {}
        for name, model in models.items():
            # Cross-validation
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring=scoring)
            
            # Train on full training set
            model.fit(X_train, y_train)
            
            # Test on test set
            if is_regression:
                y_pred = model.predict(X_test)
                test_mse = mean_squared_error(y_test, y_pred)
                test_rmse = np.sqrt(test_mse)
                test_r2 = r2_score(y_test, y_pred)
                
                results[name] = {
                    "cv_score_mean": cv_scores.mean(),
                    "cv_score_std": cv_scores.std(),
                    "test_mse": test_mse,
                    "test_rmse": test_rmse,
                    "test_r2": test_r2
                }
            else:
                y_pred = model.predict(X_test)
                test_accuracy = accuracy_score(y_test, y_pred)
                
                results[name] = {
                    "cv_score_mean": cv_scores.mean(),
                    "cv_score_std": cv_scores.std(),
                    "test_accuracy": test_accuracy
                }
        
        # Create summary
        summary = f"""
# Model Evaluation Results

## Target Variable: {target_column}
## Model Type: {"Regression" if is_regression else "Classification"}

## Model Comparison
"""
        if is_regression:
            # Sort by R2 score
            sorted_models = sorted(results.items(), key=lambda x: x[1]["test_r2"], reverse=True)
            
            for name, metrics in sorted_models:
                summary += f"### {name}\n"
                summary += f"- Cross-validation R² Score: {metrics['cv_score_mean']:.4f} (±{metrics['cv_score_std']:.4f})\n"
                summary += f"- Test R² Score: {metrics['test_r2']:.4f}\n"
                summary += f"- Test RMSE: {metrics['test_rmse']:.4f}\n"
        else:
            # Sort by accuracy
            sorted_models = sorted(results.items(), key=lambda x: x[1]["test_accuracy"], reverse=True)
            
            for name, metrics in sorted_models:
                summary += f"### {name}\n"
                summary += f"- Cross-validation Accuracy: {metrics['cv_score_mean']:.4f} (±{metrics['cv_score_std']:.4f})\n"
                summary += f"- Test Accuracy: {metrics['test_accuracy']:.4f}\n"
        
        # Create metadata
        metadata = {
            "model_type": "model_evaluation",
            "target_variable": target_column,
            "features": feature_columns,
            "is_regression": is_regression,
            "results": results
        }
        
        return {
            "summary": summary,
            "metadata": metadata
        }
