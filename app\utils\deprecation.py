"""
Utilities for handling deprecation warnings.

This module provides functions for logging deprecation warnings and
tracking usage of deprecated features.
"""

import logging
import functools
import os
import yaml
from typing import Any, Dict, Callable, TypeVar, cast

# Set up logging
logger = logging.getLogger(__name__)

# Type variables for function decorators
F = TypeVar('F', bound=Callable[..., Any])

def warn_json_usage(file_path: str, alternative: str = "YAML") -> None:
    """
    Log a deprecation warning for JSON file usage.

    Args:
        file_path: Path to the JSON file being used
        alternative: Alternative format to suggest (default: YAML)
    """
    logger.warning(
        f"DEPRECATION WARNING: Using JSON file format at {file_path}. "
        f"This format is deprecated and will be removed in a future version. "
        f"Please migrate to {alternative} format."
    )

def warn_on_json_load(func: F) -> F:
    """
    Decorator to add deprecation warnings when loading JSON files.

    Args:
        func: Function to decorate

    Returns:
        Decorated function
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Get the file path from args or kwargs
        file_path = None
        if args and isinstance(args[0], str) and args[0].endswith('.json'):
            file_path = args[0]
        elif 'file_path' in kwargs and isinstance(kwargs['file_path'], str) and kwargs['file_path'].endswith('.json'):
            file_path = kwargs['file_path']

        if file_path:
            warn_json_usage(file_path)

        return func(*args, **kwargs)

    return cast(F, wrapper)

def load_config_file(file_path: str, default_encoding: str = "utf-8") -> Dict[str, Any]:
    """
    Load a configuration file, supporting both JSON and YAML formats.

    Args:
        file_path: Path to the configuration file
        default_encoding: Default encoding to try first

    Returns:
        Parsed configuration as a dictionary

    Raises:
        FileNotFoundError: If the file doesn't exist
        ValueError: If the file format is not supported or the file is invalid
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Configuration file not found: {file_path}")

    # Try different encodings if the default fails
    encodings = [default_encoding, "utf-8", "utf-8-sig", "latin-1"]

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()

                # Determine file format based on extension
                if file_path.endswith('.yaml') or file_path.endswith('.yml'):
                    return yaml.safe_load(content) or {}
                else:
                    # Only YAML files are supported
                    raise ValueError(f"Unsupported file format: {file_path}. Only YAML files are supported.")
        except UnicodeDecodeError:
            # Try the next encoding
            continue
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML format in {file_path}: {str(e)}")

    # If all encodings fail
    raise ValueError(f"Could not decode file with available encodings: {file_path}")

def save_config_file(data: Dict[str, Any], file_path: str, encoding: str = "utf-8") -> bool:
    """
    Save a configuration file in YAML format.

    Args:
        data: Data to save
        file_path: Path to save the file
        encoding: File encoding

    Returns:
        True if successful, False otherwise

    Raises:
        ValueError: If the file is not a YAML file
    """
    # Check if this is a YAML file
    if not file_path.endswith(('.yaml', '.yml')):
        raise ValueError(f"File {file_path} is not a YAML file. Only YAML files are supported.")

    try:
        with open(file_path, 'w', encoding=encoding) as f:
            yaml.dump(data, f, default_flow_style=False, sort_keys=False)
        return True
    except Exception as e:
        logger.error(f"Error saving configuration file {file_path}: {str(e)}")
        return False
