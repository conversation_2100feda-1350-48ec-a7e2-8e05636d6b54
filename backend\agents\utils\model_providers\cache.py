"""
Response caching for the model provider system.

This module provides a caching system for model responses to improve performance
and reduce API costs.
"""

import logging
import hashlib
import json
import time
from typing import Dict, Any, Optional, Union, List, Tuple
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)


class ModelResponseCache:
    """Cache for model responses."""

    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        """
        Initialize the cache.

        Args:
            max_size: Maximum number of items in the cache
            ttl: Time to live in seconds
        """
        self._cache: Dict[str, Tuple[Any, float]] = {}
        self._max_size = max_size
        self._ttl = ttl
        logger.info(f"Initialized model response cache with max_size={max_size}, ttl={ttl}")

    def _generate_key(self, provider_id: str, model_id: str, prompt: str, params: Dict[str, Any]) -> str:
        """
        Generate a cache key from the request parameters.

        Args:
            provider_id: Provider ID
            model_id: Model ID
            prompt: Prompt text
            params: Additional parameters

        Returns:
            Cache key
        """
        # Create a dictionary with all parameters
        key_dict = {
            "provider_id": provider_id,
            "model_id": model_id,
            "prompt": prompt,
            "params": params
        }

        # Convert to JSON and hash
        key_json = json.dumps(key_dict, sort_keys=True)
        key_hash = hashlib.md5(key_json.encode()).hexdigest()
        return key_hash

    def get(self, provider_id: str, model_id: str, prompt: str, params: Dict[str, Any]) -> Optional[Any]:
        """
        Get a response from the cache.

        Args:
            provider_id: Provider ID
            model_id: Model ID
            prompt: Prompt text
            params: Additional parameters

        Returns:
            Cached response or None if not found
        """
        key = self._generate_key(provider_id, model_id, prompt, params)
        if key in self._cache:
            response, timestamp = self._cache[key]
            # Check if the response has expired
            if time.time() - timestamp > self._ttl:
                logger.debug(f"Cache hit for {provider_id}/{model_id}, but expired")
                del self._cache[key]
                return None
            logger.debug(f"Cache hit for {provider_id}/{model_id}")
            return response
        logger.debug(f"Cache miss for {provider_id}/{model_id}")
        return None

    def set(self, provider_id: str, model_id: str, prompt: str, params: Dict[str, Any], response: Any) -> None:
        """
        Set a response in the cache.

        Args:
            provider_id: Provider ID
            model_id: Model ID
            prompt: Prompt text
            params: Additional parameters
            response: Response to cache
        """
        # Check if the cache is full
        if len(self._cache) >= self._max_size:
            # Remove the oldest item
            oldest_key = min(self._cache.items(), key=lambda x: x[1][1])[0]
            del self._cache[oldest_key]
            logger.debug(f"Cache full, removed oldest item with key {oldest_key}")

        # Add the new item
        key = self._generate_key(provider_id, model_id, prompt, params)
        self._cache[key] = (response, time.time())
        logger.debug(f"Added response to cache for {provider_id}/{model_id}")

    def clear(self) -> None:
        """Clear the cache."""
        self._cache.clear()
        logger.info("Cleared model response cache")

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        # Count items by provider
        providers = {}
        for key, (response, timestamp) in self._cache.items():
            # Extract provider from the key
            provider = key.split("_")[0]
            if provider not in providers:
                providers[provider] = 0
            providers[provider] += 1

        # Calculate age statistics
        now = time.time()
        ages = [now - timestamp for _, timestamp in self._cache.values()]
        avg_age = sum(ages) / len(ages) if ages else 0
        max_age = max(ages) if ages else 0
        min_age = min(ages) if ages else 0

        return {
            "size": len(self._cache),
            "max_size": self._max_size,
            "ttl": self._ttl,
            "providers": providers,
            "avg_age": avg_age,
            "max_age": max_age,
            "min_age": min_age
        }


# Create a global cache instance
response_cache = ModelResponseCache()


async def get_cached_response(provider_id: str, model_id: str, prompt: str, params: Dict[str, Any]) -> Optional[Any]:
    """
    Get a response from the cache.

    Args:
        provider_id: Provider ID
        model_id: Model ID
        prompt: Prompt text
        params: Additional parameters

    Returns:
        Cached response or None if not found
    """
    return response_cache.get(provider_id, model_id, prompt, params)


async def cache_response(provider_id: str, model_id: str, prompt: str, params: Dict[str, Any], response: Any) -> None:
    """
    Cache a response.

    Args:
        provider_id: Provider ID
        model_id: Model ID
        prompt: Prompt text
        params: Additional parameters
        response: Response to cache
    """
    response_cache.set(provider_id, model_id, prompt, params, response)


async def clear_cache() -> None:
    """Clear the cache."""
    response_cache.clear()


async def get_cache_stats() -> Dict[str, Any]:
    """
    Get cache statistics.

    Returns:
        Dictionary with cache statistics
    """
    return response_cache.get_stats()
