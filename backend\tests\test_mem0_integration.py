"""
Test script for mem0ai integration.

This script tests the integration of mem0ai with Qdrant for vector database
and knowledge graph operations.
"""

import asyncio
import logging
import sys
import os
import time

# Add the parent directory to the path so we can import the backend modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from agents.utils.memory_service import MemoryService
from agents.utils.vector_service import VectorService
from agents.utils.knowledge_graph_service import KnowledgeGraphService
from agents.utils.qdrant_manager import QdrantManager
from app.config import MEM0_SELF_HOSTED

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_memory_service():
    """Test the memory service."""
    logger.info("Testing memory service...")

    # Initialize memory service
    memory_service = MemoryService()

    # Add a memory
    memory = memory_service.add_memory(
        "This is a test memory for mem0ai integration",
        user_id="test_user",
        metadata={"test": True, "category": "integration_test"}
    )

    if memory:
        logger.info(f"Added memory: {memory}")
    else:
        logger.error("Failed to add memory")
        return False

    # Search for memories
    results = memory_service.search_memories(
        "test memory",
        user_id="test_user",
        limit=5
    )

    if results and results.get("results"):
        logger.info(f"Found {len(results.get('results', []))} memories")
        for result in results.get("results", []):
            logger.info(f"Memory: {result.get('content')}")
    else:
        logger.error("Failed to search memories")
        return False

    return True

async def test_vector_service():
    """Test the vector service."""
    logger.info("Testing vector service...")

    # Initialize vector service
    vector_service = VectorService()

    # Create a test file
    test_file_path = os.path.join(os.getcwd(), "test_document.txt")
    with open(test_file_path, "w") as f:
        f.write("This is a test document for mem0ai vector database integration. ")
        f.write("It contains information about artificial intelligence and vector databases. ")
        f.write("Vector databases are used to store and retrieve vector embeddings efficiently. ")
        f.write("mem0ai is a memory system that can be used with Qdrant for vector storage.")

    try:
        # Embed the document
        vector_store_id, file_info = vector_service.embed_document(test_file_path)

        logger.info(f"Embedded document with vector store ID: {vector_store_id}")
        logger.info(f"File info: {file_info}")

        # Search the document
        results = vector_service.search_document(vector_store_id, "What is a vector database?")

        if results:
            logger.info(f"Found {len(results)} results")
            for result in results:
                logger.info(f"Result: {result.get('content')}")
        else:
            logger.error("Failed to search document")
            return False

        return True
    finally:
        # Clean up
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

async def test_knowledge_graph_service():
    """Test the knowledge graph service."""
    logger.info("Testing knowledge graph service...")

    # Initialize knowledge graph service
    kg_service = KnowledgeGraphService()

    # Create a graph
    graph_id = kg_service.create_graph(
        name="Test Graph",
        description="A test graph for mem0ai integration",
        metadata={"test": True, "category": "integration_test"}
    )

    logger.info(f"Created graph with ID: {graph_id}")

    # Add entities
    entity1_id = kg_service.add_entity(
        graph_id=graph_id,
        entity_type="Person",
        name="John Doe",
        properties={"age": 30, "occupation": "Developer"},
        description="A software developer"
    )

    entity2_id = kg_service.add_entity(
        graph_id=graph_id,
        entity_type="Organization",
        name="Acme Corp",
        properties={"industry": "Technology", "employees": 100},
        description="A technology company"
    )

    logger.info(f"Added entities: {entity1_id}, {entity2_id}")

    # Add relationship
    relationship_id = kg_service.add_relationship(
        graph_id=graph_id,
        relationship_type="WORKS_AT",
        source_id=entity1_id,
        target_id=entity2_id,
        properties={"since": 2020, "position": "Senior Developer"},
        description="John Doe works at Acme Corp"
    )

    logger.info(f"Added relationship: {relationship_id}")

    # Get entities
    entities = kg_service.get_entities(graph_id)

    if entities:
        logger.info(f"Found {len(entities)} entities")
        for entity in entities:
            logger.info(f"Entity: {entity.get('name')} ({entity.get('type')})")
    else:
        logger.error("Failed to get entities")
        return False

    # Get relationships
    relationships = kg_service.get_relationships(graph_id)

    if relationships:
        logger.info(f"Found {len(relationships)} relationships")
        for relationship in relationships:
            logger.info(f"Relationship: {relationship.get('type')} ({relationship.get('source_id')} -> {relationship.get('target_id')})")
    else:
        logger.error("Failed to get relationships")
        return False

    return True

async def test_mcp_tools():
    """Test the MCP tools with mem0ai integration."""
    logger.info("Testing MCP tools...")

    # Test document embedding tool
    try:
        from agents.tools.mcp.mem0_document_embedding import Mem0DocumentEmbeddingTool

        # Initialize the tool
        doc_tool = Mem0DocumentEmbeddingTool()
        await doc_tool.initialize({})

        # Create a test file
        test_file_path = os.path.join(os.getcwd(), "test_document.txt")
        with open(test_file_path, "w") as f:
            f.write("This is a test document for mem0ai MCP tool integration. ")
            f.write("It contains information about artificial intelligence and vector databases. ")
            f.write("Vector databases are used to store and retrieve vector embeddings efficiently. ")
            f.write("mem0ai is a memory system that can be used with Qdrant for vector storage.")

        # Test embedding
        embed_result = await doc_tool.execute({
            "operation": "embed",
            "file_path": test_file_path,
            "user_id": "test_user",
            "persona_id": "test_persona"
        })

        logger.info(f"Document embedding result: {embed_result}")

        # Test querying
        if not embed_result.get("isError", False) and "metadata" in embed_result:
            vector_store_id = embed_result["metadata"].get("vector_store_id")

            query_result = await doc_tool.execute({
                "operation": "query",
                "file_path": test_file_path,
                "query": "What is a vector database?",
                "vector_store_id": vector_store_id
            })

            logger.info(f"Document query result: {query_result}")

        # Clean up
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

        logger.info("Document embedding tool test passed")
        return True
    except Exception as e:
        logger.error(f"Error testing document embedding tool: {e}")
        return False

async def test_data_access_tool():
    """Test the data access tool with mem0ai integration."""
    logger.info("Testing data access tool...")

    try:
        from agents.tools.mcp.data_access import DataAccessTool

        # Initialize the tool
        data_tool = DataAccessTool()
        await data_tool.initialize({})

        # Create a test CSV file
        test_file_path = os.path.join(os.getcwd(), "test_data.csv")
        with open(test_file_path, "w") as f:
            f.write("id,name,value\n")
            f.write("1,Item 1,100\n")
            f.write("2,Item 2,200\n")
            f.write("3,Item 3,300\n")

        # Test embedding
        embed_result = await data_tool.execute({
            "operation": "embed",
            "data_source": test_file_path,
            "params": {
                "user_id": "test_user",
                "persona_id": "test_persona"
            }
        })

        logger.info(f"Data access embed result: {embed_result}")

        # Test searching
        if not embed_result.get("isError", False) and "metadata" in embed_result:
            vector_store_id = embed_result["metadata"].get("vector_store_id")

            search_result = await data_tool.execute({
                "operation": "search_document",
                "data_source": test_file_path,
                "params": {
                    "query": "item value",
                    "vector_store_id": vector_store_id
                }
            })

            logger.info(f"Data access search result: {search_result}")

        # Clean up
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

        logger.info("Data access tool test passed")
        return True
    except Exception as e:
        logger.error(f"Error testing data access tool: {e}")
        return False

async def main():
    """Main entry point for the test script."""
    logger.info("Starting mem0ai integration tests...")

    # Ensure Qdrant is running
    if MEM0_SELF_HOSTED:
        logger.info("Ensuring Qdrant is running...")

        # Try multiple times to ensure Qdrant is running
        max_attempts = 3
        for attempt in range(max_attempts):
            if QdrantManager.ensure_qdrant_running():
                logger.info("Qdrant is running and ready for tests")
                break

            if attempt < max_attempts - 1:
                logger.warning(f"Failed to ensure Qdrant is running (attempt {attempt+1}/{max_attempts}), retrying...")
                time.sleep(10)  # Wait longer between attempts
        else:
            logger.error(f"Failed to ensure Qdrant is running after {max_attempts} attempts")
            logger.info("Continuing with tests that don't require Qdrant...")

    results = {}

    try:
        # Test memory service
        memory_result = await test_memory_service()
        results["memory_service"] = memory_result
        logger.info(f"Memory service test {'passed' if memory_result else 'failed'}")
    except Exception as e:
        logger.error(f"Error in memory service test: {e}")
        results["memory_service"] = False

    try:
        # Test vector service
        vector_result = await test_vector_service()
        results["vector_service"] = vector_result
        logger.info(f"Vector service test {'passed' if vector_result else 'failed'}")
    except Exception as e:
        logger.error(f"Error in vector service test: {e}")
        results["vector_service"] = False

    try:
        # Test knowledge graph service
        kg_result = await test_knowledge_graph_service()
        results["knowledge_graph_service"] = kg_result
        logger.info(f"Knowledge graph service test {'passed' if kg_result else 'failed'}")
    except Exception as e:
        logger.error(f"Error in knowledge graph service test: {e}")
        results["knowledge_graph_service"] = False

    try:
        # Test MCP tools
        mcp_result = await test_mcp_tools()
        results["mcp_tools"] = mcp_result
        logger.info(f"MCP tools test {'passed' if mcp_result else 'failed'}")
    except Exception as e:
        logger.error(f"Error in MCP tools test: {e}")
        results["mcp_tools"] = False

    try:
        # Test data access tool
        data_access_result = await test_data_access_tool()
        results["data_access_tool"] = data_access_result
        logger.info(f"Data access tool test {'passed' if data_access_result else 'failed'}")
    except Exception as e:
        logger.error(f"Error in data access tool test: {e}")
        results["data_access_tool"] = False

    # Print summary
    logger.info("\n=== TEST SUMMARY ===")
    for test_name, result in results.items():
        logger.info(f"{test_name}: {'PASSED' if result else 'FAILED'}")

    # Overall result
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    logger.info(f"\nOverall: {passed}/{total} tests passed")

    logger.info("Completed mem0ai integration tests")

if __name__ == "__main__":
    asyncio.run(main())
