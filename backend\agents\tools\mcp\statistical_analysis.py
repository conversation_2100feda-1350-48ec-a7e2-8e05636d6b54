"""
Statistical analysis MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for performing statistical analysis
using PandasAI.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, Optional

# PandasAI imports
import pandasai as pai

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper
from ..pandasai_v3.cache import ResponseCache
from ..pandasai_v3.error_handler import ErrorHandler

logger = logging.getLogger(__name__)


class StatisticalAnalysisTool(BaseMCPTool):
    """Tool for performing statistical analysis using PandasAI."""

    def __init__(self):
        """Initialize the statistical analysis tool."""
        super().__init__(
            name="statistical_analysis",
            description="Performs statistical analysis (e.g., correlation, t-tests, ANOVA) by interpreting a natural language description using PandasAI.",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the data file (CSV, Excel, JSON supported)."
                    },
                    "analysis_description": {
                        "type": "string",
                        "description": "Natural language description of the statistical analysis required (e.g., 'correlation between Sales and MarketingSpend', 't-test for Score between GroupA and GroupB', 'ANOVA for Rating across different Regions')."
                    },
                    "api_key": {
                        "type": "string",
                        "description": "API key for the LLM provider."
                    },
                    "provider": {
                        "type": "string",
                        "description": "LLM provider to use (e.g., openai, groq, anthropic).",
                        "default": "openai"
                    },
                    "model": {
                        "type": "string",
                        "description": "Model name to use for the analysis."
                    }
                },
                "required": ["file_path", "analysis_description", "api_key"]
            }
        )
        self.pandasai = PandasAIWrapper()
        self.cache = ResponseCache()

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # No additional initialization needed
        pass

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the statistical analysis tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution (following the inputSchema)

        Returns:
            Tool execution results in MCP format
        """
        file_path = arguments["file_path"]
        analysis_description = arguments["analysis_description"]
        api_key = arguments["api_key"]
        provider = arguments.get("provider", "openai")
        model = arguments.get("model")

        logger.info(f"PandasAI statistical analysis requested for {file_path} with description: {analysis_description}")

        # Check if we have a cached response
        cache_key = f"{file_path}:{analysis_description}:{provider}:{model}"
        cached_result = self.cache.get(cache_key)
        if cached_result:
            logger.info(f"Using cached result for statistical analysis: {cache_key}")
            return cached_result

        # Input validation
        if not provider or not api_key:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Error: LLM Provider and API Key must be provided."
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "analysis_description": analysis_description,
                    "status": "error",
                    "error_type": "config_error"
                }
            }

        # Load the data
        try:
            if file_path.lower().endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.lower().endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.lower().endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ]
                }

            if df.empty:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"The dataframe loaded from {file_path} is empty."
                        }
                    ]
                }
        except Exception as e:
            logger.error(f"Error loading data from {file_path}: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error loading data from {file_path}: {str(e)}"
                    }
                ]
            }

        # Perform the analysis
        try:
            # Initialize PandasAI
            self.pandasai.initialize(api_key, provider)

            # Create agent with the dataframe
            if not self.pandasai.create_agent(df=df, model=model):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "Error creating PandasAI Agent"
                        }
                    ]
                }

            # Run the analysis using PandasAI wrapper
            query = f"Perform the following statistical analysis on the dataframe: {analysis_description}. Provide the key results, such as p-values, test statistics, or correlation coefficients."
            result = self.pandasai.chat(query)

            # Process the result
            if isinstance(result, (pd.DataFrame, pd.Series)):
                # Statistical tests might return dataframes (e.g., ANOVA table)
                result_df = result if isinstance(result, pd.DataFrame) else result.to_frame()
                rows = len(result_df)
                results_metadata = {"status": "success", "result_type": "dataframe", "rows_returned": rows}
                results_text = f"Statistical analysis results (table format):\n\n{result_df.to_string()}"
            elif isinstance(result, (str, int, float, bool)):
                # Simple results like p-value or correlation coefficient
                results_text = f"Statistical analysis result: {result}"
                results_metadata = {"status": "success", "result_type": str(type(result).__name__), "value": result}
            elif isinstance(result, dict):
                # Some tests might return dictionaries
                results_text = f"Statistical analysis results:\n\n{result}"
                results_metadata = {"status": "success", "result_type": "dict", "value": result}
            else:
                # Handle other types of results
                results_text = f"Statistical analysis completed. Result type: {type(result).__name__}"
                results_metadata = {"status": "success", "result_type": str(type(result).__name__)}

            response = {
                "content": [
                    {
                        "type": "text",
                        "text": results_text
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "analysis_description": analysis_description,
                    "implementation": "pandasai",
                    **results_metadata
                }
            }

            # Cache the response
            self.cache.set(cache_key, response)
            return response

        except Exception as e:
            error_handler = ErrorHandler()
            error_info = error_handler.handle_error(e, context={
                "operation": "statistical_analysis",
                "file_path": file_path,
                "analysis_description": analysis_description
            })

            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": error_info["message"]
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "analysis_description": analysis_description,
                    "error_type": error_info["error_type"],
                    "details": error_info["details"]
                }
            }
