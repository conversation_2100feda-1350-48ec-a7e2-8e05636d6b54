#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test Qdrant connection with the updated code.

This script uses the updated QdrantManager class to test the connection to Qdrant.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Add the backend directory to the path so we can import the QdrantManager
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Import the QdrantManager class
try:
    from agents.utils.qdrant_manager import QdrantManager
    logger.info("Successfully imported QdrantManager")
except ImportError as e:
    logger.error(f"Failed to import QdrantManager: {e}")
    sys.exit(1)

def main():
    """Main function to test Qdrant connection."""
    # Load environment variables
    load_dotenv()
    
    logger.info("Testing Qdrant connection...")
    
    # Check environment variables
    logger.info(f"QDRANT_HOST from environment: {os.getenv('QDRANT_HOST', 'not set')}")
    logger.info(f"QDRANT_PORT from environment: {os.getenv('QDRANT_PORT', 'not set')}")
    
    # Get the Docker host
    docker_host = QdrantManager.get_docker_host()
    logger.info(f"Docker host: {docker_host}")
    
    # Check if Qdrant is running
    if QdrantManager.is_qdrant_running(host=docker_host):
        logger.info(f"Qdrant is running at {docker_host}:{QdrantManager.DEFAULT_PORT}")
    else:
        logger.info(f"Qdrant is not running at {docker_host}:{QdrantManager.DEFAULT_PORT}")
        
        # Try to ensure Qdrant is running
        logger.info("Attempting to ensure Qdrant is running...")
        if QdrantManager.ensure_qdrant_running():
            logger.info("Successfully started Qdrant")
        else:
            logger.error("Failed to start Qdrant")
    
    # Try to connect to Qdrant using different hosts
    hosts_to_try = ["localhost", "127.0.0.1", "qdrant"]
    
    for host in hosts_to_try:
        if host != docker_host:  # Skip the host we already tried
            logger.info(f"Trying host: {host}")
            if QdrantManager.is_qdrant_running(host=host):
                logger.info(f"Qdrant is running at {host}:{QdrantManager.DEFAULT_PORT}")
            else:
                logger.info(f"Qdrant is not running at {host}:{QdrantManager.DEFAULT_PORT}")
    
    # Get Qdrant connection parameters
    connection_params = QdrantManager.get_qdrant_connection_params()
    logger.info(f"Qdrant connection parameters: {connection_params}")
    
    logger.info("Test completed.")

if __name__ == "__main__":
    main()
