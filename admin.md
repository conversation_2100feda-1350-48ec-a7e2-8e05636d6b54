# Admin Implementation Strategy for Datagenius

This document outlines the implementation strategy for adding admin functionality to the Datagenius application, focusing on managing AI personas and other administrative features.

## Current State Analysis

The Datagenius application currently has:

1. User authentication with regular user roles
2. AI personas with fixed properties (no dynamic management)
3. A marketplace where users can purchase AI personas at a fixed price ($10)
4. No dedicated admin interface or role-based access control beyond basic user/superuser flags

## Implementation Goals

Create an admin system that allows authorized users to:

1. Manage AI persona properties:
   - Set and update pricing
   - Select which AI model/provider each persona uses
   - Activate/deactivate personas
   - Edit persona metadata (description, skills, industry, etc.)

2. Control access and restrictions:
   - Set age restrictions for specific AI personas
   - Manage content filters and safety settings
   - Control which personas are available in the marketplace

3. Monitor system usage:
   - View purchase history and statistics
   - Monitor user activity and engagement
   - Track API usage across different providers

## Implementation Strategy

### Phase 1: Admin Authentication and Basic Interface

1. **Enhance User Model and Authentication**
   - Leverage the existing `is_superuser` flag in the User model
   - Create admin-specific authentication middleware
   - Implement admin login and session management

2. **Create Admin Dashboard**
   - Develop a new admin route and interface
   - Implement sidebar navigation for different admin functions
   - Create overview dashboard with key metrics

3. **Admin API Endpoints**
   - Create dedicated admin API router
   - Implement admin-only middleware for route protection
   - Set up basic CRUD operations for admin functions

### Phase 2: AI Persona Management

1. **Enhance Persona Data Model**
   - Add new fields to the `PersonaBase` model:
     - `price`: Customizable price (instead of fixed $10)
     - `provider`: AI provider to use (OpenAI, Groq, etc.)
     - `model`: Specific model to use within the provider
     - `is_active`: Boolean to control availability
     - `age_restriction`: Minimum age required
     - `content_filters`: Safety settings configuration

2. **Persona Management Interface**
   - Create a persona list view with filtering and sorting
   - Implement persona detail/edit view
   - Add bulk operations for managing multiple personas

3. **Persona Database Updates**
   - Create a dedicated database table for persona configurations
   - Implement database functions for persona management
   - Set up relationships between personas and other entities

### Phase 3: Advanced Admin Features

1. **User Management**
   - View and manage user accounts
   - Reset passwords and manage account status
   - View user activity and purchase history

2. **Analytics Dashboard**
   - Track persona usage and popularity
   - Monitor revenue and purchase patterns
   - Analyze API usage and costs

3. **System Configuration**
   - Manage global application settings
   - Configure default providers and models
   - Set up content moderation rules

## Technical Implementation Details

### Database Schema Updates

1. **Persona Table**
```sql
CREATE TABLE personas (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    industry VARCHAR(50),
    skills JSONB,
    rating FLOAT,
    review_count INTEGER,
    image_url VARCHAR(255),
    price FLOAT NOT NULL DEFAULT 10.0,
    provider VARCHAR(50) DEFAULT 'groq',
    model VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    age_restriction INTEGER DEFAULT 0,
    content_filters JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

2. **Admin Activity Log**
```sql
CREATE TABLE admin_activity_logs (
    id SERIAL PRIMARY KEY,
    admin_id INTEGER REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id VARCHAR(50),
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Backend API Endpoints

1. **Admin Authentication**
   - `POST /api/admin/login`: Admin login endpoint
   - `GET /api/admin/me`: Get current admin info

2. **Persona Management**
   - `GET /api/admin/personas`: List all personas with admin details
   - `GET /api/admin/personas/{id}`: Get detailed persona info
   - `POST /api/admin/personas`: Create new persona
   - `PUT /api/admin/personas/{id}`: Update persona
   - `PATCH /api/admin/personas/{id}/status`: Update persona status
   - `DELETE /api/admin/personas/{id}`: Delete persona

3. **User Management**
   - `GET /api/admin/users`: List all users
   - `GET /api/admin/users/{id}`: Get user details
   - `PUT /api/admin/users/{id}`: Update user
   - `PATCH /api/admin/users/{id}/status`: Update user status

4. **Analytics**
   - `GET /api/admin/analytics/purchases`: Get purchase statistics
   - `GET /api/admin/analytics/usage`: Get API usage statistics
   - `GET /api/admin/analytics/personas`: Get persona usage statistics

### Frontend Components

1. **Admin Layout**
   - Admin navigation sidebar
   - Admin header with quick actions
   - Protected routes for admin pages

2. **Persona Management**
   - Persona list table with filtering and sorting
   - Persona edit form with validation
   - Persona status toggle controls

3. **User Management**
   - User list with search and filtering
   - User detail view with activity history
   - User status management controls

4. **Dashboard**
   - Overview statistics cards
   - Usage charts and graphs
   - Recent activity timeline

## Implementation Phases

### Phase 1 (Week 1-2)
- Set up admin authentication and basic routing
- Create admin dashboard UI skeleton
- Implement persona database schema updates

### Phase 2 (Week 3-4)
- Develop persona management interface
- Implement persona CRUD operations
- Add price and provider management

### Phase 3 (Week 5-6)
- Implement user management features
- Add analytics dashboard
- Develop system configuration controls

### Phase 4 (Week 7-8)
- Implement age restrictions and content filters
- Add advanced analytics and reporting
- Perform testing and refinement

## Security Considerations

1. **Admin Authentication**
   - Implement stricter password requirements for admin accounts
   - Add IP-based restrictions for admin access
   - Consider two-factor authentication for admin accounts

2. **Audit Logging**
   - Log all admin actions for accountability
   - Implement non-repudiation for critical operations
   - Create admin activity reports

3. **Data Protection**
   - Ensure sensitive user data is properly protected
   - Implement proper access controls for all admin operations
   - Follow principle of least privilege for admin functions

## Future Enhancements

1. **Role-Based Access Control**
   - Create multiple admin roles with different permissions
   - Implement fine-grained access control for admin functions

2. **Advanced Analytics**
   - Implement predictive analytics for persona popularity
   - Add revenue forecasting and optimization

3. **Content Moderation**
   - Add tools for reviewing and moderating AI-generated content
   - Implement automated content filtering and flagging
