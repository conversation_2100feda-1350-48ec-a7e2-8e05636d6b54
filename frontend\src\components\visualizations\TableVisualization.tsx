import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Table as TableIcon } from 'lucide-react';
import { VisualizationData } from '@/utils/visualization';
import { SaveToDashboardButton } from './SaveToDashboardButton';

interface TableVisualizationProps {
  visualization: VisualizationData;
  className?: string;
}

export const TableVisualization = ({ visualization, className = '' }: TableVisualizationProps) => {
  const { data, title, description } = visualization;
  const { headers, rows } = data;

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <TableIcon className="h-5 w-5 text-brand-500" />
          <CardTitle className="text-lg">{title || 'Table'}</CardTitle>
        </div>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {headers.map((header, index) => (
                  <TableHead key={index} className="font-medium">
                    {header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {rows.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {row.map((cell, cellIndex) => (
                    <TableCell key={cellIndex}>{cell}</TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end border-t border-gray-100 bg-gray-50 py-3 px-4">
        <SaveToDashboardButton visualization={visualization} />
      </CardFooter>
    </Card>
  );
};
