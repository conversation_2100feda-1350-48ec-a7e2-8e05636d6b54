"""
Knowledge Graph Schema for the Datagenius backend.

This module defines the schema for the knowledge graph, including entity types,
relationship types, and properties.
"""

import logging
from enum import Enum
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class EntityType(str, Enum):
    """Enum for entity types in the knowledge graph."""
    CONCEPT = "concept"
    PERSON = "person"
    ORGANIZATION = "organization"
    LOCATION = "location"
    EVENT = "event"
    PRODUCT = "product"
    DOCUMENT = "document"
    TOPIC = "topic"
    CATEGORY = "category"
    CUSTOM = "custom"


class RelationshipType(str, Enum):
    """Enum for relationship types in the knowledge graph."""
    IS_A = "is_a"
    PART_OF = "part_of"
    HAS_PART = "has_part"
    RELATED_TO = "related_to"
    CREATED_BY = "created_by"
    CREATED = "created"
    LOCATED_IN = "located_in"
    WORKS_FOR = "works_for"
    EMPLOYS = "employs"
    MENTIONS = "mentions"
    MENTIONED_IN = "mentioned_in"
    SIMILAR_TO = "similar_to"
    BELONGS_TO = "belongs_to"
    CONTAINS = "contains"
    PRECEDES = "precedes"
    FOLLOWS = "follows"
    CUSTOM = "custom"


class Property(BaseModel):
    """Model for a property of an entity or relationship."""
    name: str = Field(..., description="Name of the property")
    value: Any = Field(..., description="Value of the property")
    data_type: str = Field("string", description="Data type of the property")


class Entity(BaseModel):
    """Model for an entity in the knowledge graph."""
    id: str = Field(..., description="Unique identifier for the entity")
    type: EntityType = Field(..., description="Type of the entity")
    name: str = Field(..., description="Name of the entity")
    description: Optional[str] = Field(None, description="Description of the entity")
    properties: List[Property] = Field(default_factory=list, description="Properties of the entity")
    embedding: Optional[List[float]] = Field(None, description="Vector embedding of the entity")
    source: Optional[str] = Field(None, description="Source of the entity (e.g., document ID)")
    confidence: Optional[float] = Field(None, description="Confidence score for the entity")


class Relationship(BaseModel):
    """Model for a relationship between entities in the knowledge graph."""
    id: str = Field(..., description="Unique identifier for the relationship")
    type: RelationshipType = Field(..., description="Type of the relationship")
    source_id: str = Field(..., description="ID of the source entity")
    target_id: str = Field(..., description="ID of the target entity")
    properties: List[Property] = Field(default_factory=list, description="Properties of the relationship")
    weight: Optional[float] = Field(None, description="Weight of the relationship")
    confidence: Optional[float] = Field(None, description="Confidence score for the relationship")


class KnowledgeGraph(BaseModel):
    """Model for the knowledge graph."""
    entities: Dict[str, Entity] = Field(default_factory=dict, description="Entities in the graph")
    relationships: Dict[str, Relationship] = Field(default_factory=dict, description="Relationships in the graph")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Metadata for the graph")


class KnowledgeGraphQuery(BaseModel):
    """Model for a query to the knowledge graph."""
    query_type: str = Field(..., description="Type of query (e.g., 'entity', 'relationship', 'path')")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Parameters for the query")
    limit: Optional[int] = Field(None, description="Maximum number of results to return")
    offset: Optional[int] = Field(None, description="Offset for pagination")


class KnowledgeGraphQueryResult(BaseModel):
    """Model for the result of a query to the knowledge graph."""
    query: KnowledgeGraphQuery = Field(..., description="The original query")
    entities: List[Entity] = Field(default_factory=list, description="Entities in the result")
    relationships: List[Relationship] = Field(default_factory=list, description="Relationships in the result")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Metadata for the result")
