[tool:pytest]
# Pytest configuration for Datagenius backend

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=agents
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --durations=10

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    concierge: Concierge agent tests
    memory: Memory management tests
    security: Security tests
    performance: Performance tests
    database: Database tests
    api: API tests
    agents: Agent tests
    mcp: MCP tool tests
    vector: Vector database tests
    knowledge_graph: Knowledge graph tests

# Test timeout
timeout = 300

# Asyncio mode
asyncio_mode = auto

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:transformers.*
    ignore::UserWarning:torch.*
