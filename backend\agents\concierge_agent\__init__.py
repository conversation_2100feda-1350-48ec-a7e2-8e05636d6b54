"""
Concierge Agent for the Datagenius backend.

This package contains the implementation of the concierge agent,
which serves as a guide for users, helping them select appropriate
personas and attach data.
"""

import logging

# Configure logging
logger = logging.getLogger(__name__)

# Import the concierge agent class for easier access
from .concierge import ConciergeAgent

# Import components
from .components import ConciergeStateTrackerComponent

# Export classes for easier imports
__all__ = [
    "ConciergeAgent",
    "ConciergeStateTrackerComponent"
]

logger.info("Concierge Agent module loaded")
