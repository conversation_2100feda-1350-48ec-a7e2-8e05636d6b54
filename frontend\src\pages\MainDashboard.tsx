
import { useState } from "react";
import { DashboardLayout } from "@/components/DashboardLayout";
import { DashboardGrid } from "@/components/dashboard/DashboardGrid";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { AddWidgetDialog } from "@/components/dashboard/AddWidgetDialog";
import { motion } from "framer-motion";

const MainDashboard = () => {
  const { toast } = useToast();
  const [isAddWidgetOpen, setIsAddWidgetOpen] = useState(false);
  
  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <Button
            onClick={() => setIsAddWidgetOpen(true)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Widget
          </Button>
        </div>
        
        <DashboardGrid />
        
        <AddWidgetDialog 
          open={isAddWidgetOpen} 
          onOpenChange={setIsAddWidgetOpen} 
          onWidgetAdded={() => {
            toast({
              title: "Widget added",
              description: "Your widget has been added to the dashboard",
            });
          }}
        />
      </motion.div>
    </DashboardLayout>
  );
};

export default MainDashboard;
