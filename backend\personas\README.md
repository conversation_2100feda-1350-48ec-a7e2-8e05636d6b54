# Datagenius Persona Configuration System

This directory contains configuration files for the Datagenius persona system. Each file defines a persona that can be used in the application.

## Configuration Format

Persona configurations are defined in YAML files with the following structure:

```yaml
id: persona-id
name: Persona Name
description: A detailed description of the persona.
version: 1.0.0
agent_class: agents.module.path.AgentClass
industry: Industry Category
skills:
  - Skill 1
  - Skill 2
  - Skill 3
capabilities:
  - capability_1
  - capability_2
  - capability_3
rating: 4.7
review_count: 95
image_url: /placeholder.svg
price: 10.0
provider: groq
model: llama3-8b-8192
is_active: true
age_restriction: 0
system_prompts:
  default: |
    You are an expert in your field. Your task is to help the user with their request.

    User request: "{text}"

    Provide a helpful response based on your expertise.
  specialized_prompt: |
    You are analyzing specialized data.
    The data shows: {data}

    Provide insights based on this data.
```

## Required Fields

- `id`: Unique identifier for the persona
- `name`: Display name for the persona
- `description`: Detailed description of the persona
- `agent_class`: Full import path to the agent class
- `capabilities`: List of capabilities supported by the persona

## Optional Fields

- `version`: Version of the persona configuration
- `industry`: Industry category for the persona
- `skills`: List of skills the persona has
- `rating`: Rating of the persona (0-5)
- `review_count`: Number of reviews
- `image_url`: URL to the persona's image
- `price`: Price of the persona
- `provider`: Default AI provider to use
- `model`: Default AI model to use
- `is_active`: Whether the persona is active
- `age_restriction`: Age restriction for the persona (0 = none)
- `system_prompts`: Dictionary of prompt templates

## System Prompts

The `system_prompts` section contains templates that can be used by the agent. Each template can include placeholders in the format `{placeholder_name}` that will be replaced with actual values when the prompt is used.

## Adding a New Persona

To add a new persona:

1. Create a new YAML file in this directory with a descriptive name (e.g., `my-new-persona.yaml`)
2. Define the required and optional fields as needed
3. Restart the application or reload the agent registry

The persona will be automatically loaded and registered with the agent registry.

## Persona Versioning

Datagenius supports versioning of personas, allowing you to maintain multiple versions of the same persona and switch between them as needed.

### Version File Format

Version files use the following naming convention:

```
{persona-id}-{version}.yaml
```

For example, `marketing-ai-1.0.0.yaml` would be a version file for the `marketing-ai` persona with version `1.0.0`.

The content of a version file is the same as a regular persona configuration file, but it will only be loaded when specifically requested.

### Managing Versions

Versions can be managed through the admin interface, which provides the following functionality:

1. **Creating Versions**: Create a new version of an existing persona
2. **Listing Versions**: View all available versions for a persona
3. **Activating Versions**: Set a specific version as the active version for a persona
4. **Deleting Versions**: Remove versions that are no longer needed

### Active Versions

When a version is activated, its configuration is copied to the main persona configuration file, making it the default configuration used by the system.

### Version Rollback

If a new version causes issues, you can easily roll back to a previous version by activating it through the admin interface.

## Example

See the existing persona configuration files in this directory for examples of how to define personas.
