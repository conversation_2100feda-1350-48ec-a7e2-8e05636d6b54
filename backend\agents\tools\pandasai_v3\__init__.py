"""
PandasAI v3 integration for the Datagenius backend.

This package provides integration with PandasAI v3, including a wrapper for the
PandasAI Agent class, semantic layer utilities, and LLM provider adapters.
"""

import logging

# Configure logging
logger = logging.getLogger(__name__)

# Import main classes for easier access
from .wrapper import PandasAIWrapper
from .semantic_layer import SemanticLayerManager
from .llm_providers import LLMProviderFactory
from .cache import ResponseCache
from .error_handler import ErrorHandler

# Export classes for easier imports
__all__ = [
    "PandasAIWrapper",
    "SemanticLayerManager",
    "LLMProviderFactory",
    "ResponseCache",
    "ErrorHandler"
]

# Log the PandasAI v3 integration initialization
logger.info("Initialized PandasAI v3 integration")
