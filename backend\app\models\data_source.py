"""
Data source models for the Datagenius backend.

This module provides Pydantic models for data source-related functionality.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Literal
from enum import Enum

from pydantic import BaseModel, Field


class DataSourceType(str, Enum):
    """Enum for data source types."""
    FILE = "file"
    DATABASE = "database"
    API = "api"
    MCP = "mcp"


class DataSourceBase(BaseModel):
    """Base model for data source data."""
    name: str
    type: DataSourceType
    description: Optional[str] = None
    is_active: bool = True
    metadata: Optional[Dict[str, Any]] = Field(None, alias="source_metadata")


class FileDataSourceCreate(DataSourceBase):
    """Model for creating a file data source."""
    type: Literal[DataSourceType.FILE] = DataSourceType.FILE
    file_id: str


class DatabaseDataSourceCreate(DataSourceBase):
    """Model for creating a database data source."""
    type: Literal[DataSourceType.DATABASE] = DataSourceType.DATABASE
    db_type: str  # postgresql, mysql, etc.
    connection_string: Optional[str] = None
    # Or individual connection parameters
    host: str
    port: int
    database: str
    username: str
    password: str  # Note: This should be handled securely


class ApiDataSourceCreate(DataSourceBase):
    """Model for creating an API data source."""
    type: Literal[DataSourceType.API] = DataSourceType.API
    api_type: str  # rest, graphql
    endpoint: str
    auth_type: Optional[str] = None  # api_key, oauth, basic
    auth_credentials: Optional[Dict[str, Any]] = None


class McpDataSourceCreate(DataSourceBase):
    """Model for creating an MCP data source."""
    type: Literal[DataSourceType.MCP] = DataSourceType.MCP
    endpoint: str
    api_key: Optional[str] = None
    namespace: str
    collection_ids: List[str] = []


class DataSourceResponse(DataSourceBase):
    """Model for data source data returned to the client."""
    id: str
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
        populate_by_name = True


class DataSourceListResponse(BaseModel):
    """Model for data source list response."""
    data_sources: List[DataSourceResponse]
