# Composable Classification Agent

This directory contains the Composable Classification Agent implementation for the Datagenius backend. The agent uses a component-based architecture for flexibility and modularity.

## Features

- **Text Classification**: Classify text into categories using AI models
- **Document Analysis**: Analyze and categorize document content
- **Content Categorization**: Organize content into hierarchical categories

## Files

- `composable_agent.py`: The composable agent implementation
- `components.py`: Components used by the composable agent
- `hf_classifier.py`: Hugging Face classification implementation
- `llm_classifier_v2.py`: LLM-based classification implementation

## Components

The agent is built from the following components:

- **ClassificationParserComponent**: Parses user requests to determine the classification task
- **HuggingFaceClassifierComponent**: Classifies text using Hugging Face models
- **LLMClassifierComponent**: Classifies text using language models
- **ClassificationErrorHandlerComponent**: Handles error conditions

## Usage

To use the Composable Classification Agent, you can interact with it through the API endpoints. The agent is registered with the agent registry as "composable-classifier-ai" and can be accessed through the personas API.

Example:

```python
from agents.registry import AgentRegistry

# Get the classification agent
classifier_agent = await AgentRegistry.create_agent_instance("composable-classifier-ai")

# Process a message
response = await classifier_agent.process_message(
    user_id=1,
    message="Classify the text in my uploaded file using Hugging Face models",
    conversation_id="conversation_123",
    context={"file_id": "file_uuid"}
)

# Print the response
print(response["message"])
```

## Configuration

### AI Models

The Classification Agent supports multiple AI models:

1. **Hugging Face Models**: For efficient classification using pre-trained models
2. **LLM Models**: For more flexible classification using language models like Groq, OpenAI, etc.

### Classification Types

The agent supports two main classification approaches:

1. **Hugging Face Classification**: Uses pre-trained models for efficient classification
2. **LLM Classification**: Uses language models for more flexible, context-aware classification
