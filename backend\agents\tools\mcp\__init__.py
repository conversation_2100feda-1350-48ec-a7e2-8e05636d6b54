"""
MCP tool system for the Datagenius agent architecture.

This package contains the implementation of the Model Context Protocol (MCP) tool system
for Datagenius agents, including the base tool interface, tool registry,
and specific tool implementations.
"""

import logging
from .base import MCPTool, BaseMCPTool
from .registry import MCPToolRegistry

# Configure logging
logger = logging.getLogger(__name__)

# Export classes for easier imports
__all__ = ["MCPTool", "BaseMCPTool", "MCPToolRegistry"]

# Register MCP tools
from .register import register_mcp_tools
register_mcp_tools()

# Log the MCP tool system initialization
logger.info("Initialized MCP tool system")
