#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to run database migrations for the Datagenius backend.

This script runs the Alembic migrations to update the database schema.
"""

import os
import sys
import logging
import argparse
import subprocess

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run database migrations")
    parser.add_argument("--revision", help="Revision to upgrade to (default: head)")
    parser.add_argument("--downgrade", help="Revision to downgrade to")
    return parser.parse_args()

def run_migration(revision=None, downgrade=None):
    """Run the database migration."""
    try:
        # Change to the backend directory
        os.chdir(os.path.dirname(os.path.abspath(__file__)))

        # Run the migration
        if downgrade:
            logger.info(f"Downgrading database to revision {downgrade}")
            subprocess.run(["alembic", "downgrade", downgrade], check=True)
        else:
            # Use 'heads' instead of 'head' to handle multiple heads
            revision = revision or "heads"
            logger.info(f"Upgrading database to revision {revision}")
            subprocess.run(["alembic", "upgrade", revision], check=True)

        logger.info("Migration completed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Migration failed: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error running migration: {e}")
        sys.exit(1)

def main():
    """Main entry point for the script."""
    args = parse_args()
    run_migration(args.revision, args.downgrade)

if __name__ == "__main__":
    main()
