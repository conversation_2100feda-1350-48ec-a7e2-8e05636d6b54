"""
Tests for the AgentRegistry class to ensure proper registration and handling of agent identifiers.
"""

import pytest
import os
import sys
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import the agents module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from agents.registry import AgentRegistry
from agents.composable import ComposableAgent


class TestAgentRegistry:
    """Test suite for the AgentRegistry class."""

    def setup_method(self):
        """Set up test environment before each test method."""
        # Clear the registry before each test
        AgentRegistry._registry = {}
        AgentRegistry._configurations = {}
        AgentRegistry._versions = {}

    def test_register_and_get_agent_class(self):
        """Test registering and retrieving agent classes."""
        # Register a test agent
        AgentRegistry.register("test-agent", ComposableAgent)

        # Verify the agent class can be retrieved
        agent_class = AgentRegistry.get_agent_class("test-agent")
        assert agent_class == ComposableAgent

        # Verify non-existent agent returns None
        assert AgentRegistry.get_agent_class("non-existent-agent") is None

    def test_kebab_case_identifiers(self):
        """Test that kebab-case identifiers are used consistently."""
        # Register agents with kebab-case identifiers
        AgentRegistry.register("composable-analysis-ai", ComposableAgent)
        AgentRegistry.register("composable-marketing-ai", ComposableAgent)
        AgentRegistry.register("composable-classifier-ai", ComposableAgent)
        AgentRegistry.register("concierge-agent", ComposableAgent)

        # Verify all registered personas use kebab-case
        registered_personas = AgentRegistry.list_registered_personas()
        for persona_id in registered_personas:
            # Check that persona IDs use kebab-case (contain hyphens, no underscores)
            assert "-" in persona_id
            assert "_" not in persona_id

            # Check that we can retrieve the agent class
            assert AgentRegistry.get_agent_class(persona_id) is not None

    @patch('agents.registry.persona_manager')
    def test_load_configurations(self, mock_persona_manager):
        """Test loading configurations from files."""
        # Mock the persona manager to return test configurations
        mock_configs = {
            "composable-analysis-ai": {
                "id": "composable-analysis-ai",
                "name": "Composable Analyst",
                "description": "A composable AI assistant for data analysis"
            },
            "composable-marketing-ai": {
                "id": "composable-marketing-ai",
                "name": "Composable Marketer",
                "description": "A composable AI assistant for marketing"
            }
        }
        mock_persona_manager.load_persona_configs.return_value = mock_configs

        # Load configurations
        AgentRegistry.load_configurations("dummy_path")

        # Verify configurations were loaded
        assert "composable-analysis-ai" in AgentRegistry._configurations
        assert "composable-marketing-ai" in AgentRegistry._configurations

        # Verify configuration content
        assert AgentRegistry._configurations["composable-analysis-ai"]["name"] == "Composable Analyst"
        assert AgentRegistry._configurations["composable-marketing-ai"]["name"] == "Composable Marketer"

    @pytest.mark.asyncio
    @patch('agents.registry.AgentRegistry.get_agent_class')
    async def test_create_agent_instance(self, mock_get_agent_class):
        """Test creating agent instances."""
        # Mock the agent class
        mock_agent = MagicMock()
        mock_agent_class = MagicMock(return_value=mock_agent)
        mock_get_agent_class.return_value = mock_agent_class

        # Create an agent instance
        agent = await AgentRegistry.create_agent_instance("composable-analysis-ai")

        # Verify the agent was created
        assert agent == mock_agent
        mock_get_agent_class.assert_called_once_with("composable-analysis-ai")
        mock_agent_class.assert_called_once()

        # Test with non-existent agent
        mock_get_agent_class.return_value = None
        agent = await AgentRegistry.create_agent_instance("non-existent-agent")
        assert agent is None
