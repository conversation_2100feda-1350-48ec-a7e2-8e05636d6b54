"""
Data filtering MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for filtering data based on specified conditions.
"""

import logging
import os
import json
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional

from .base import BaseMCPTool

logger = logging.getLogger(__name__)


class DataFilteringTool(BaseMCPTool):
    """Tool for filtering data based on specified conditions."""

    def __init__(self):
        """Initialize the data filtering tool."""
        super().__init__(
            name="filter_data",
            description="Filter data based on specified conditions",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "filters": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "column": {"type": "string"},
                                "operator": {
                                    "type": "string",
                                    "enum": ["=", "!=", ">", "<", ">=", "<=", "contains", "starts_with", "ends_with", "in", "not_in", "between"]
                                },
                                "value": {"type": ["string", "number", "array", "boolean", "null"]}
                            },
                            "required": ["column", "operator"]
                        }
                    },
                    "combine_with": {
                        "type": "string",
                        "enum": ["and", "or"],
                        "default": "and"
                    }
                },
                "required": ["file_path", "filters"]
            },
            annotations={
                "title": "Filter Data",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.data_dir = "data"

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        if "data_dir" in config:
            self.data_dir = config["data_dir"]
            logger.info(f"Set data directory to: {self.data_dir}")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            file_path = arguments["file_path"]
            filters = arguments["filters"]
            combine_with = arguments.get("combine_with", "and")

            # Check if the path is relative and prepend the data directory
            if not os.path.isabs(file_path):
                file_path = os.path.join(self.data_dir, file_path)

            # Check if the file exists
            if not os.path.exists(file_path):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"File not found: {file_path}"
                        }
                    ]
                }

            # Load the data
            if file_path.endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ]
                }

            # Apply filters
            filtered_df = self._apply_filters(df, filters, combine_with)
            
            # Create a summary of the filtering
            summary = {
                "original_shape": df.shape,
                "filtered_shape": filtered_df.shape,
                "filters_applied": filters,
                "combine_with": combine_with
            }
            
            # Save the filtered data to a new file
            output_path = file_path.replace(".", "_filtered.")
            if file_path.endswith(".csv"):
                filtered_df.to_csv(output_path, index=False)
            elif file_path.endswith((".xls", ".xlsx")):
                filtered_df.to_excel(output_path, index=False)
            elif file_path.endswith(".json"):
                filtered_df.to_json(output_path, orient="records")
            
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Data filtering completed successfully.\n\nSummary:\n- Original shape: {summary['original_shape']}\n- Filtered shape: {summary['filtered_shape']}\n- Filters applied: {len(filters)}\n\nFiltered data saved to: {output_path}"
                    }
                ],
                "metadata": {
                    "summary": summary,
                    "output_path": output_path,
                    "preview": filtered_df.head(10).to_dict(orient="records")
                }
            }
            
        except Exception as e:
            logger.error(f"Error filtering data: {str(e)}", exc_info=True)
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error filtering data: {str(e)}"
                    }
                ]
            }
    
    def _apply_filters(self, df: pd.DataFrame, filters: List[Dict[str, Any]], combine_with: str) -> pd.DataFrame:
        """
        Apply filters to a DataFrame.

        Args:
            df: The DataFrame to filter
            filters: List of filter conditions
            combine_with: How to combine filters ('and' or 'or')

        Returns:
            Filtered DataFrame
        """
        if not filters:
            return df
        
        # Create a mask for each filter
        masks = []
        
        for filter_condition in filters:
            column = filter_condition["column"]
            operator = filter_condition["operator"]
            value = filter_condition.get("value")
            
            # Check if the column exists
            if column not in df.columns:
                logger.warning(f"Column '{column}' not found in DataFrame")
                continue
            
            # Apply the filter based on the operator
            if operator == "=":
                mask = df[column] == value
            elif operator == "!=":
                mask = df[column] != value
            elif operator == ">":
                mask = df[column] > value
            elif operator == "<":
                mask = df[column] < value
            elif operator == ">=":
                mask = df[column] >= value
            elif operator == "<=":
                mask = df[column] <= value
            elif operator == "contains":
                mask = df[column].astype(str).str.contains(str(value), na=False)
            elif operator == "starts_with":
                mask = df[column].astype(str).str.startswith(str(value), na=False)
            elif operator == "ends_with":
                mask = df[column].astype(str).str.endswith(str(value), na=False)
            elif operator == "in":
                if not isinstance(value, list):
                    value = [value]
                mask = df[column].isin(value)
            elif operator == "not_in":
                if not isinstance(value, list):
                    value = [value]
                mask = ~df[column].isin(value)
            elif operator == "between":
                if not isinstance(value, list) or len(value) != 2:
                    logger.warning(f"Invalid value for 'between' operator: {value}")
                    continue
                mask = (df[column] >= value[0]) & (df[column] <= value[1])
            else:
                logger.warning(f"Unsupported operator: {operator}")
                continue
            
            masks.append(mask)
        
        # Combine masks
        if not masks:
            return df
        
        if combine_with.lower() == "and":
            final_mask = masks[0]
            for mask in masks[1:]:
                final_mask = final_mask & mask
        else:  # "or"
            final_mask = masks[0]
            for mask in masks[1:]:
                final_mask = final_mask | mask
        
        # Apply the final mask
        return df[final_mask]
