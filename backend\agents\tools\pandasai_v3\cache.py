"""
Response cache for PandasAI v3.

This module provides a cache for PandasAI responses to improve performance.
"""

import logging
import os
import json
import hashlib
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class ResponseCache:
    """Cache for PandasAI responses."""
    
    def __init__(self, cache_dir: str = "cache/pandasai"):
        """Initialize the response cache."""
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        
    def _get_cache_key(self, file_path: str, query: str, provider: str) -> str:
        """Generate a cache key."""
        # Create a hash of the file path, query, and provider
        key = f"{file_path}:{query}:{provider}"
        return hashlib.md5(key.encode()).hexdigest()
        
    def get(self, file_path: str, query: str, provider: str) -> Optional[Dict[str, Any]]:
        """Get a cached response."""
        key = self._get_cache_key(file_path, query, provider)
        cache_file = os.path.join(self.cache_dir, f"{key}.json")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, "r") as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error reading cache file: {e}", exc_info=True)
                return None
        return None
        
    def set(self, file_path: str, query: str, provider: str, response: Dict[str, Any]) -> bool:
        """Set a cached response."""
        key = self._get_cache_key(file_path, query, provider)
        cache_file = os.path.join(self.cache_dir, f"{key}.json")
        
        try:
            with open(cache_file, "w") as f:
                json.dump(response, f)
            return True
        except Exception as e:
            logger.error(f"Error writing cache file: {e}", exc_info=True)
            return False
