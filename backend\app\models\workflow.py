"""
SQLAlchemy ORM models for Workflow and WorkflowTask executions.
"""
import uuid as uuid_pkg
from sqlalchemy import Column, String, DateTime, ForeignKey, JSON, Enum as SAEnum, Text, Integer, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID # For PostgreSQL UUID type

from backend.app.database import Base, get_utc_now # Import Base from the existing database.py
from backend.schemas.db_schemas import WorkflowStatusEnum, TaskStatusEnum # Import enums

class WorkflowExecution(Base):
    __tablename__ = "workflow_executions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid_pkg.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    status = Column(SAEnum(WorkflowStatusEnum, name="workflow_status_enum"), nullable=False, default=WorkflowStatusEnum.CREATED)
    
    start_time = Column(DateTime(timezone=True), nullable=True)
    end_time = Column(DateTime(timezone=True), nullable=True)
    
    user_id = Column(String, nullable=True) # Assuming user_id can be a string; adjust if it's an FK to users.id (Integer)
    session_id = Column(String, nullable=True)
    
    context = Column(JSON, nullable=True, default=dict)
    metadata = Column(JSON, nullable=True, default=dict)

    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    tasks = relationship("WorkflowTaskExecution", back_populates="workflow", cascade="all, delete-orphan")

class WorkflowTaskExecution(Base):
    __tablename__ = "workflow_task_executions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid_pkg.uuid4)
    workflow_id = Column(UUID(as_uuid=True), ForeignKey("workflow_executions.id"), nullable=False, index=True)
    
    name = Column(String(255), nullable=False)
    agent_type = Column(String(255), nullable=False) # The type/ID of the agent to run
    
    input_data = Column(JSON, nullable=True, default=dict)
    # Storing dependencies as a JSON list of UUID strings.
    # For more complex dependency graphs or querying, a separate association table might be better.
    dependencies = Column(JSON, nullable=True, default=list) 
    
    status = Column(SAEnum(TaskStatusEnum, name="task_status_enum"), nullable=False, default=TaskStatusEnum.PENDING)
    
    result = Column(JSON, nullable=True)
    error = Column(Text, nullable=True)
    
    start_time = Column(DateTime(timezone=True), nullable=True)
    end_time = Column(DateTime(timezone=True), nullable=True)
    
    retry_count = Column(Integer, nullable=False, default=0)
    max_retries = Column(Integer, nullable=False, default=3)
    timeout_seconds = Column(Integer, nullable=False, default=300)
    
    metadata = Column(JSON, nullable=True, default=dict)

    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    workflow = relationship("WorkflowExecution", back_populates="tasks")
