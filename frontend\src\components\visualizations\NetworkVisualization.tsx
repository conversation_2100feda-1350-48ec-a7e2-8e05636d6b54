import { useEffect, useRef, useState } from 'react';
import { Network } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { VisualizationData } from '@/utils/visualization';
import * as d3 from 'd3';

interface NetworkVisualizationProps {
  visualization: VisualizationData;
  className?: string;
}

export const NetworkVisualization = ({ visualization, className = '' }: NetworkVisualizationProps) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const title = visualization.title || 'Network Visualization';
  const description = visualization.description || 'Network graph visualization';

  useEffect(() => {
    if (!svgRef.current || !visualization.data.nodes || !visualization.data.links) return;

    const svg = d3.select(svgRef.current);
    const width = containerRef.current?.clientWidth || 600;
    const height = containerRef.current?.clientHeight || 400;

    // Clear previous visualization
    svg.selectAll('*').remove();

    // Create the simulation
    const simulation = d3.forceSimulation(visualization.data.nodes)
      .force('link', d3.forceLink(visualization.data.links).id((d: any) => d.id))
      .force('charge', d3.forceManyBody().strength(-100))
      .force('center', d3.forceCenter(width / 2, height / 2));

    // Create links
    const link = svg.append('g')
      .attr('stroke', '#999')
      .attr('stroke-opacity', 0.6)
      .selectAll('line')
      .data(visualization.data.links)
      .join('line')
      .attr('stroke-width', (d: any) => Math.sqrt(d.value || 1));

    // Create nodes
    const node = svg.append('g')
      .attr('stroke', '#fff')
      .attr('stroke-width', 1.5)
      .selectAll('circle')
      .data(visualization.data.nodes)
      .join('circle')
      .attr('r', 5)
      .attr('fill', (d: any) => d.color || '#1f77b4')
      .call(drag(simulation) as any);

    // Add node labels
    const labels = svg.append('g')
      .selectAll('text')
      .data(visualization.data.nodes)
      .join('text')
      .attr('dx', 12)
      .attr('dy', '.35em')
      .text((d: any) => d.name || d.id)
      .style('font-size', '10px')
      .style('fill', '#333');

    // Update positions on simulation tick
    simulation.on('tick', () => {
      link
        .attr('x1', (d: any) => d.source.x)
        .attr('y1', (d: any) => d.source.y)
        .attr('x2', (d: any) => d.target.x)
        .attr('y2', (d: any) => d.target.y);

      node
        .attr('cx', (d: any) => d.x)
        .attr('cy', (d: any) => d.y);

      labels
        .attr('x', (d: any) => d.x)
        .attr('y', (d: any) => d.y);
    });

    // Drag functionality
    function drag(simulation: any) {
      function dragstarted(event: any, d: any) {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
      }

      function dragged(event: any, d: any) {
        d.fx = event.x;
        d.fy = event.y;
      }

      function dragended(event: any, d: any) {
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
      }

      return d3.drag()
        .on('start', dragstarted)
        .on('drag', dragged)
        .on('end', dragended);
    }

    // Cleanup
    return () => {
      simulation.stop();
    };
  }, [visualization.data]);

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Network className="h-5 w-5 text-brand-500" />
          <CardTitle className="text-lg">{title}</CardTitle>
        </div>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div ref={containerRef} className="h-[400px] w-full">
          <svg ref={svgRef} width="100%" height="100%" />
        </div>
      </CardContent>
    </Card>
  );
};
