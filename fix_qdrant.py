#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to check and fix Qdrant Docker container issues.

This script checks if the Qdrant Docker container is running and healthy,
and attempts to fix any issues found.
"""

import os
import sys
import time
import logging
import subprocess
import requests
import argparse
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Constants
CONTAINER_NAME = "datagenius-qdrant"
DEFAULT_HOST = os.getenv("QDRANT_HOST", "localhost")
DEFAULT_PORT = int(os.getenv("QDRANT_PORT", "6333"))

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Check and fix Qdrant Docker container issues")
    parser.add_argument("--host", default=DEFAULT_HOST, help="Qdrant host")
    parser.add_argument("--port", type=int, default=DEFAULT_PORT, help="Qdrant port")
    parser.add_argument("--force-restart", action="store_true", help="Force restart the container")
    return parser.parse_args()

def is_qdrant_running(host, port):
    """
    Check if Qdrant is running at the specified host and port.
    
    Args:
        host: The host where Qdrant is running
        port: The port where Qdrant is running
        
    Returns:
        True if Qdrant is running, False otherwise
    """
    try:
        logger.info(f"Checking if Qdrant is running at http://{host}:{port}/health")
        response = requests.get(f"http://{host}:{port}/health", timeout=5)
        if response.status_code == 200:
            logger.info(f"Qdrant is running at {host}:{port}")
            logger.info(f"Response: {response.json()}")
            return True
        else:
            logger.error(f"Qdrant health check failed with status code {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
    except requests.RequestException as e:
        logger.error(f"Failed to connect to Qdrant at {host}:{port}: {str(e)}")
        return False

def is_docker_compose_running():
    """
    Check if Docker Compose is running with Qdrant service.
    
    Returns:
        True if Docker Compose is running with Qdrant, False otherwise
    """
    try:
        # Check if the Qdrant container exists in Docker
        result = subprocess.run(
            ["docker", "ps", "-a", "--filter", f"name={CONTAINER_NAME}", "--format", "{{.Names}}"],
            capture_output=True,
            text=True,
            check=True
        )
        return CONTAINER_NAME in result.stdout
    except subprocess.CalledProcessError:
        logger.error("Failed to check if Docker Compose is running")
        return False
    except Exception as e:
        logger.error(f"Error checking Docker Compose status: {e}")
        return False

def is_container_running():
    """
    Check if the Qdrant container is running.
    
    Returns:
        True if the container is running, False otherwise
    """
    try:
        result = subprocess.run(
            ["docker", "inspect", "--format", "{{.State.Status}}", CONTAINER_NAME],
            capture_output=True,
            text=True,
            check=True
        )
        status = result.stdout.strip()
        logger.info(f"Qdrant container status: {status}")
        return status == "running"
    except subprocess.CalledProcessError:
        logger.error("Failed to get container status")
        return False
    except Exception as e:
        logger.error(f"Error checking container status: {e}")
        return False

def is_container_healthy():
    """
    Check if the Qdrant container is healthy.
    
    Returns:
        True if the container is healthy, False otherwise
    """
    try:
        result = subprocess.run(
            ["docker", "inspect", "--format", "{{.State.Health.Status}}", CONTAINER_NAME],
            capture_output=True,
            text=True,
            check=True
        )
        status = result.stdout.strip()
        logger.info(f"Qdrant container health status: {status}")
        return status == "healthy"
    except subprocess.CalledProcessError:
        logger.error("Failed to get container health status")
        return False
    except Exception as e:
        logger.error(f"Error checking container health: {e}")
        return False

def start_container():
    """
    Start the Qdrant container.
    
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Starting container {CONTAINER_NAME}")
        subprocess.run(["docker", "start", CONTAINER_NAME], check=True)
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to start container: {e}")
        return False
    except Exception as e:
        logger.error(f"Error starting container: {e}")
        return False

def restart_container():
    """
    Restart the Qdrant container.
    
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Restarting container {CONTAINER_NAME}")
        subprocess.run(["docker", "restart", CONTAINER_NAME], check=True)
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to restart container: {e}")
        return False
    except Exception as e:
        logger.error(f"Error restarting container: {e}")
        return False

def check_logs():
    """
    Check the logs of the Qdrant container.
    """
    try:
        logger.info(f"Checking logs for container {CONTAINER_NAME}")
        subprocess.run(["docker", "logs", "--tail", "20", CONTAINER_NAME], check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to check logs: {e}")
    except Exception as e:
        logger.error(f"Error checking logs: {e}")

def main():
    """Main entry point."""
    args = parse_args()
    
    # Check if Qdrant is running
    if is_qdrant_running(args.host, args.port):
        logger.info("Qdrant is running and accessible")
        return
    
    # Check if Docker Compose is running
    if not is_docker_compose_running():
        logger.error("Qdrant container does not exist in Docker")
        logger.info("Please run 'docker-compose up -d' to start all services")
        sys.exit(1)
    
    # Check if container is running
    if not is_container_running():
        logger.info("Qdrant container exists but is not running")
        if not start_container():
            logger.error("Failed to start Qdrant container")
            sys.exit(1)
    else:
        logger.info("Qdrant container is running but not responding")
        
        # Check if container is healthy
        if is_container_healthy():
            logger.info("Qdrant container is healthy but not responding. This might be a network issue.")
        else:
            logger.info("Qdrant container is not healthy")
        
        # Force restart if requested
        if args.force_restart:
            logger.info("Forcing restart of Qdrant container")
            if not restart_container():
                logger.error("Failed to restart Qdrant container")
                sys.exit(1)
    
    # Check logs
    check_logs()
    
    # Wait for Qdrant to start
    max_retries = 10
    for i in range(max_retries):
        if is_qdrant_running(args.host, args.port):
            logger.info("Qdrant is now running and accessible")
            return
        
        logger.info(f"Waiting for Qdrant to start (attempt {i+1}/{max_retries})...")
        time.sleep(2)
    
    logger.error("Failed to start Qdrant within the timeout period")
    sys.exit(1)

if __name__ == "__main__":
    main()
