import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Settings } from 'lucide-react';
import { providerApi } from '@/lib/providerApi';
import { useToast } from '@/components/ui/use-toast';

interface ApiKeyTesterProps {
  providerId: string;
}

const ApiKeyTester: React.FC<ApiKeyTesterProps> = ({ providerId }) => {
  const [isTesting, setIsTesting] = useState(false);
  const { toast } = useToast();

  const testApiKey = async () => {
    if (!providerId) return;

    setIsTesting(true);
    try {
      const response = await providerApi.getProviderModels(providerId);

      if (response.models.length > 0) {
        toast({
          title: 'API Key Valid',
          description: `Successfully loaded ${response.models.length} models from ${providerId}.`,
          variant: 'default',
        });
      } else if (response.message) {
        toast({
          title: 'API Key Error',
          description: response.message,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'API Key Error',
          description: `No models found for ${providerId}. Please check your API key configuration.`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'API Key Test Failed',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <div className="flex space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={testApiKey}
        disabled={isTesting || !providerId}
        className="bg-white hover:bg-gray-50 border-gray-300 text-gray-700 hover:text-gray-900 shadow-sm"
      >
        {isTesting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            <span>Verifying...</span>
          </>
        ) : (
          <>
            <div className="flex items-center justify-center h-5 w-5 rounded-full bg-blue-100 mr-1.5">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
              </svg>
            </div>
            <span>Verify API Key</span>
          </>
        )}
      </Button>
      <a href="/admin/settings" title="Configure API Keys">
        <Button
          variant="ghost"
          size="sm"
          type="button"
          className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 border border-transparent hover:border-blue-100"
        >
          <Settings className="h-4 w-4" />
        </Button>
      </a>
    </div>
  );
};

export default ApiKeyTester;
