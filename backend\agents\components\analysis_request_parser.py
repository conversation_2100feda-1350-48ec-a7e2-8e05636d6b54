"""
Analysis request parser component for the Datagenius agent system.

This module provides a component for parsing data analysis-related requests,
extracting key information, and determining the appropriate analysis task.
"""

import logging
import re
from typing import Dict, Any, List, Optional

from .base import AgentComponent

logger = logging.getLogger(__name__)


class AnalysisRequestParserComponent(AgentComponent):
    """Component for parsing data analysis-related requests."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the analysis request parser component.

        Args:
            config: Configuration dictionary for the component
        """
        # Define analysis types
        self.analysis_types = config.get("analysis_types", [
            "descriptive_analysis",
            "correlation_analysis",
            "trend_analysis",
            "segmentation_analysis",
            "predictive_analysis"
        ])
        
        # Define keywords for analysis type detection
        self.analysis_keywords = config.get("analysis_keywords", {
            "descriptive_analysis": ["describe", "summary", "statistics", "overview", "distribution", "mean", "median", "mode"],
            "correlation_analysis": ["correlation", "relationship", "between", "association", "connected", "related"],
            "trend_analysis": ["trend", "time", "series", "seasonal", "pattern", "historical", "forecast"],
            "segmentation_analysis": ["segment", "cluster", "group", "categorize", "classify", "divide"],
            "predictive_analysis": ["predict", "forecast", "future", "model", "regression", "classification"]
        })
        
        # Define information extraction patterns
        self.extraction_patterns = config.get("extraction_patterns", {
            "target_variable": [
                r"target\s+variable(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"predict(?:\s+the)?\s+(.*?)(?=\n\n|\n[A-Z]|$)",
                r"forecast(?:\s+the)?\s+(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "time_period": [
                r"time\s+period(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"date\s+range(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"from\s+(.*?)\s+to\s+(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "specific_columns": [
                r"columns?(?:\s+of\s+interest|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"variables?(?:\s+of\s+interest|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"focus\s+on\s+(?:columns|variables)(?:\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "specific_question": [
                r"question(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"analyze(?:\s+the)?\s+(.*?)(?=\n\n|\n[A-Z]|$)",
                r"find(?:\s+out)?\s+(.*?)(?=\n\n|\n[A-Z]|$)"
            ]
        })

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        # Check if this component should be skipped
        if context.get("skip_analysis_parser", False):
            logger.debug(f"Skipping analysis request parser component: {self.name}")
            return context

        # Get message from context
        message = context.get("message", "")
        if not message:
            logger.warning("No message found in context")
            return context

        try:
            # Determine the analysis type
            analysis_type = self._determine_analysis_type(message)
            logger.info(f"Determined analysis type: {analysis_type}")
            
            # Extract information from the message
            extracted_info = self._extract_information(message)
            logger.info(f"Extracted information: {', '.join(extracted_info.keys())}")
            
            # Update context with analysis type and extracted information
            context["analysis_type"] = analysis_type
            context.update(extracted_info)
            
            # Set prompt template name based on analysis type
            context["prompt_template_name"] = analysis_type
            
            # Add metadata
            context["metadata"]["analysis_type"] = analysis_type
            context["metadata"]["extracted_info"] = list(extracted_info.keys())
            
            return context
            
        except Exception as e:
            logger.error(f"Error parsing analysis request: {str(e)}", exc_info=True)
            # Don't modify the context on error
            return context

    def _determine_analysis_type(self, message: str) -> str:
        """
        Determine the analysis type from the message.

        Args:
            message: User message

        Returns:
            Analysis type string
        """
        message_lower = message.lower()
        
        # Check for explicit analysis type mentions
        for analysis_type, keywords in self.analysis_keywords.items():
            for keyword in keywords:
                if keyword.lower() in message_lower:
                    return analysis_type
        
        # Default to descriptive analysis if no specific analysis type is detected
        return "descriptive_analysis"

    def _extract_information(self, message: str) -> Dict[str, str]:
        """
        Extract analysis-related information from the message.

        Args:
            message: User message

        Returns:
            Dictionary of extracted information
        """
        extracted_info = {}
        
        # Apply extraction patterns
        for info_type, patterns in self.extraction_patterns.items():
            for pattern in patterns:
                matches = re.search(pattern, message, re.IGNORECASE | re.DOTALL)
                if matches:
                    extracted_text = matches.group(1).strip()
                    if extracted_text:
                        extracted_info[info_type] = extracted_text
                        break
        
        return extracted_info
