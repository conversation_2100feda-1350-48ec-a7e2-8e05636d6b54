"""
Data Source API endpoints for the Datagenius backend.

This module provides API endpoints for data source management.
"""

import logging
import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ..models.data_source import (
    DataSourceResponse, DataSourceListResponse,
    FileDataSourceCreate, DatabaseDataSourceCreate, ApiDataSourceCreate, McpDataSourceCreate
)
from ..models.auth import User
from ..database import get_db, create_data_source, get_data_source, get_user_data_sources, delete_data_source, get_file
from ..auth import get_current_active_user
from ..utils.mcp_client import test_mcp_connection
from .. import config

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/data-sources", tags=["Data Sources"])

@router.post("/file", response_model=DataSourceResponse)
async def create_file_data_source(
    data_source: FileDataSourceCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new file data source.

    Args:
        data_source: The data source to create
        db: Database session
        current_user: Current authenticated user

    Returns:
        Data source information
    """
    logger.info(f"User {current_user.id} creating file data source")

    # Validate that the file exists and belongs to the user
    file = get_file(db, data_source.file_id)
    if not file or file.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="File not found")

    # Create data source
    return create_data_source(db, current_user.id, data_source)

@router.post("/database", response_model=DataSourceResponse)
async def create_database_data_source(
    data_source: DatabaseDataSourceCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new database data source.

    Args:
        data_source: The data source to create
        db: Database session
        current_user: Current authenticated user

    Returns:
        Data source information
    """
    logger.info(f"User {current_user.id} creating database data source")

    # Test the database connection before saving
    try:
        # Implement connection testing logic here
        # For now, we'll just log a message
        logger.info(f"Testing database connection to {data_source.host}:{data_source.port}/{data_source.database}")
        # In a real implementation, we would test the connection here
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Database connection failed: {str(e)}")

    # Create data source
    return create_data_source(db, current_user.id, data_source)

@router.post("/api", response_model=DataSourceResponse)
async def create_api_data_source(
    data_source: ApiDataSourceCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new API data source.

    Args:
        data_source: The data source to create
        db: Database session
        current_user: Current authenticated user

    Returns:
        Data source information
    """
    logger.info(f"User {current_user.id} creating API data source")

    # Test the API connection before saving
    try:
        # Implement API testing logic here
        # For now, we'll just log a message
        logger.info(f"Testing API connection to {data_source.endpoint}")
        # In a real implementation, we would test the connection here
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"API connection failed: {str(e)}")

    # Create data source
    return create_data_source(db, current_user.id, data_source)

@router.post("/mcp", response_model=DataSourceResponse)
async def create_mcp_data_source(
    data_source: McpDataSourceCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new MCP data source.

    Args:
        data_source: The data source to create
        db: Database session
        current_user: Current authenticated user

    Returns:
        Data source information
    """
    logger.info(f"User {current_user.id} creating MCP data source")

    # Test the MCP connection before saving
    try:
        # Test MCP connection using the utility function
        test_mcp_connection(data_source.endpoint, data_source.api_key, data_source.namespace)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"MCP connection failed: {str(e)}")

    # Create data source
    return create_data_source(db, current_user.id, data_source)

@router.get("", response_model=DataSourceListResponse)
async def list_data_sources(
    skip: int = 0,
    limit: int = 100,
    type: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    List data sources.

    Args:
        skip: Number of data sources to skip
        limit: Maximum number of data sources to return
        type: Filter by data source type
        db: Database session
        current_user: Current authenticated user

    Returns:
        List of data sources
    """
    logger.info(f"User {current_user.id} listing data sources")

    data_sources = get_user_data_sources(db, current_user.id, skip, limit, type)

    # Filter out file data sources where the file no longer exists
    filtered_data_sources = []
    for ds in data_sources:
        if ds.type == "file":
            # Check if the file exists
            file_id = ds.source_metadata.get("file_id")
            if file_id:
                file = get_file(db, file_id)
                if file:
                    filtered_data_sources.append(ds)
                else:
                    logger.warning(f"File {file_id} referenced by data source {ds.id} not found")
        else:
            # Non-file data sources are always included
            filtered_data_sources.append(ds)

    return {"data_sources": filtered_data_sources}

@router.get("/{data_source_id}", response_model=DataSourceResponse)
async def get_data_source_by_id(
    data_source_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a data source by ID.

    Args:
        data_source_id: The ID of the data source to get
        db: Database session
        current_user: Current authenticated user

    Returns:
        Data source information
    """
    logger.info(f"User {current_user.id} getting data source {data_source_id}")

    data_source = get_data_source(db, data_source_id)
    if not data_source or data_source.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="Data source not found")

    # Check if it's a file data source and if the file exists
    if data_source.type == "file":
        file_id = data_source.source_metadata.get("file_id")
        if file_id:
            file = get_file(db, file_id)
            if not file:
                logger.warning(f"File {file_id} referenced by data source {data_source_id} not found")
                raise HTTPException(status_code=404, detail="File referenced by this data source no longer exists")

    return data_source

@router.delete("/{data_source_id}", response_model=dict)
async def delete_data_source_by_id(
    data_source_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a data source by ID.

    Args:
        data_source_id: The ID of the data source to delete
        db: Database session
        current_user: Current authenticated user

    Returns:
        Success message
    """
    logger.info(f"User {current_user.id} deleting data source {data_source_id}")

    data_source = get_data_source(db, data_source_id)
    if not data_source or data_source.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="Data source not found")

    delete_data_source(db, data_source_id)
    return {"message": "Data source deleted successfully"}
