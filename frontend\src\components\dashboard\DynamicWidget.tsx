import { X } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DashboardWidget } from "@/stores/dashboard-store";
import { ChartVisualization } from "@/components/visualizations/ChartVisualization";
import { TableVisualization } from "@/components/visualizations/TableVisualization";
import { TreeVisualization } from "@/components/visualizations/TreeVisualization";
import { NetworkVisualization } from "@/components/visualizations/NetworkVisualization";
import { HeatmapVisualization } from "@/components/visualizations/HeatmapVisualization";

interface DynamicWidgetProps {
  widget: DashboardWidget;
  onRemove: () => void;
}

export function DynamicWidget({ widget, onRemove }: DynamicWidgetProps) {
  const renderContent = () => {
    const visualization = {
      type: widget.type,
      title: widget.title,
      data: widget.data,
      config: widget.config
    };

    switch (widget.type) {
      case "chart":
        return <ChartVisualization visualization={visualization} />;
      case "table":
        return <TableVisualization visualization={visualization} />;
      case "tree":
        return <TreeVisualization visualization={visualization} />;
      case "network":
        return <NetworkVisualization visualization={visualization} />;
      case "heatmap":
        return <HeatmapVisualization visualization={visualization} />;
      default:
        return (
          <Card>
            <CardHeader>
              <CardTitle>{widget.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="p-4 text-center text-gray-500">
                Unknown widget type: {widget.type}
              </div>
            </CardContent>
          </Card>
        );
    }
  };

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-2 right-2 z-10 h-8 w-8 rounded-full bg-white/90 shadow-sm hover:bg-gray-100"
        onClick={onRemove}
      >
        <X className="h-4 w-4" />
      </Button>
      {renderContent()}
    </div>
  );
}
