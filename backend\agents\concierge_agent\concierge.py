"""
Concierge Agent for Datagenius.

This agent guides users, recommends personas, and assists with data interactions.
Implements intelligent user guidance, persona recommendation, and workflow coordination.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..enhanced_composable import EnhancedComposableAgent
from ..registry import AgentRegistry

logger = logging.getLogger(__name__)


@dataclass
class UserIntent:
    """Represents a user's intent parsed from their message."""
    intent_type: str  # 'persona_request', 'data_help', 'general_question', 'task_request'
    confidence: float
    entities: Dict[str, Any]
    suggested_personas: List[str]
    requires_data: bool
    complexity_score: float


@dataclass
class ConversationContext:
    """Maintains conversation context for better recommendations."""
    user_id: str
    session_id: str
    conversation_history: List[Dict[str, Any]]
    user_preferences: Dict[str, Any]
    current_task: Optional[str]
    attached_data: List[Dict[str, Any]]
    last_interaction: datetime


class ConciergeAgent(EnhancedComposableAgent):
    """
    The Concierge Agent acts as the primary point of contact, guiding users
    and coordinating interactions with other specialized agents.

    Features:
    - Intelligent intent recognition
    - Persona recommendation based on user needs
    - Data attachment assistance
    - Workflow coordination
    - Context-aware conversations
    """

    def __init__(self):
        """Initialize the Concierge Agent."""
        super().__init__()
        self.agent_registry = AgentRegistry
        self.conversation_contexts: Dict[str, ConversationContext] = {}
        self.intent_patterns = self._load_intent_patterns()
        logger.info("Concierge Agent initialized with intelligent guidance capabilities.")

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the Concierge Agent, loading its specific components.

        Args:
            config: Configuration dictionary for the agent.
        """
        await super()._initialize(config)

        # Initialize persona recommendation thresholds
        self.recommendation_threshold = config.get("recommendation_threshold", 0.7)
        self.max_recommendations = config.get("max_recommendations", 3)
        self.consider_user_history = config.get("consider_user_history", True)

        logger.info(f"Concierge Agent '{self.name}' initialized with {len(self.components)} components.")

    def _load_intent_patterns(self) -> Dict[str, List[str]]:
        """Load intent recognition patterns from configuration or use defaults."""
        # Try to get patterns from agent configuration
        config = self.agent_registry.get_configuration("concierge-agent")
        if config and "intent_patterns" in config:
            return config["intent_patterns"]

        # Default patterns if not configured
        return {
            "persona_request": [
                "recommend", "suggest", "which agent", "which persona", "help me choose",
                "what should I use", "best for", "need help with"
            ],
            "data_help": [
                "upload", "attach", "data", "file", "csv", "excel", "pdf", "analyze my data",
                "process my file", "import data"
            ],
            "analysis_request": [
                "analyze", "analysis", "insights", "patterns", "trends", "statistics",
                "visualize", "chart", "graph", "report"
            ],
            "marketing_request": [
                "marketing", "campaign", "content", "social media", "seo", "advertising",
                "promotion", "brand", "copy", "strategy"
            ],
            "classification_request": [
                "classify", "categorize", "label", "tag", "organize", "sort",
                "group", "cluster", "identify"
            ]
        }

    async def parse_user_intent(self, message: str, user_context: Dict[str, Any]) -> UserIntent:
        """
        Parse user intent from their message using NLP and pattern matching.

        Args:
            message: User's message
            user_context: Context about the user and conversation

        Returns:
            UserIntent object with parsed information
        """
        message_lower = message.lower()
        intent_scores = {}
        entities = {}

        # Pattern-based intent recognition
        for intent_type, patterns in self.intent_patterns.items():
            score = sum(1 for pattern in patterns if pattern in message_lower)
            if score > 0:
                intent_scores[intent_type] = score / len(patterns)

        # Determine primary intent
        if not intent_scores:
            primary_intent = "general_question"
            confidence = 0.5
        else:
            primary_intent = max(intent_scores, key=intent_scores.get)
            confidence = intent_scores[primary_intent]

        # Extract entities (simplified - could be enhanced with NER)
        entities = self._extract_entities(message_lower)

        # Suggest personas based on intent
        suggested_personas = await self._suggest_personas_for_intent(
            primary_intent, entities, user_context
        )

        # Determine if data is required
        requires_data = any(keyword in message_lower for keyword in [
            "my data", "file", "csv", "excel", "analyze", "upload"
        ])

        # Calculate complexity score
        complexity_score = self._calculate_complexity(message, entities)

        return UserIntent(
            intent_type=primary_intent,
            confidence=confidence,
            entities=entities,
            suggested_personas=suggested_personas,
            requires_data=requires_data,
            complexity_score=complexity_score
        )

    def _extract_entities(self, message: str) -> Dict[str, Any]:
        """Extract entities from user message."""
        entities = {}

        # File type detection
        file_types = ["csv", "excel", "pdf", "json", "xml"]
        for file_type in file_types:
            if file_type in message:
                entities["file_type"] = file_type

        # Industry detection
        industries = ["finance", "healthcare", "retail", "marketing", "education", "technology"]
        for industry in industries:
            if industry in message:
                entities["industry"] = industry

        # Task type detection
        task_types = ["analysis", "visualization", "report", "prediction", "classification"]
        for task_type in task_types:
            if task_type in message:
                entities["task_type"] = task_type

        return entities

    async def _suggest_personas_for_intent(
        self,
        intent_type: str,
        entities: Dict[str, Any],
        user_context: Dict[str, Any]
    ) -> List[str]:
        """Suggest appropriate personas based on intent and context."""
        # Get intent-persona mapping from configuration
        config = self.agent_registry.get_configuration("concierge-agent")
        intent_persona_map = {}

        if config and "intent_persona_mapping" in config:
            intent_persona_map = config["intent_persona_mapping"]
        else:
            # Default mapping - dynamically discover available personas
            available_personas = self.agent_registry.list_registered_personas()

            # Build dynamic mapping based on available personas
            intent_persona_map = {
                "analysis_request": [p for p in available_personas if "analyst" in p or "data" in p],
                "marketing_request": [p for p in available_personas if "market" in p],
                "classification_request": [p for p in available_personas if "classif" in p],
                "data_help": [p for p in available_personas if "analyst" in p or "data" in p],
                "persona_request": []  # Will be handled by recommendation logic
            }

        base_suggestions = intent_persona_map.get(intent_type, [])

        # Entity-based refinement using dynamic persona discovery
        if entities.get("file_type") in ["csv", "excel"]:
            # Find personas that handle data analysis
            data_personas = [p for p in self.agent_registry.list_registered_personas()
                           if "analyst" in p or "data" in p]
            for persona in data_personas:
                if persona not in base_suggestions:
                    base_suggestions.append(persona)

        if entities.get("task_type") == "visualization":
            # Find personas that handle visualization
            viz_personas = [p for p in self.agent_registry.list_registered_personas()
                          if "analyst" in p or "visual" in p]
            for persona in viz_personas:
                if persona not in base_suggestions:
                    base_suggestions.append(persona)

        # User history consideration (if enabled)
        if self.consider_user_history and user_context.get("user_id"):
            # Add logic to consider user's previous successful interactions
            # This could be enhanced to query user interaction history
            pass

        return base_suggestions[:self.max_recommendations]

    def _calculate_complexity(self, message: str, entities: Dict[str, Any]) -> float:
        """Calculate task complexity score."""
        complexity = 0.0

        # Length factor
        complexity += min(len(message.split()) / 50, 0.3)

        # Entity count factor
        complexity += min(len(entities) / 10, 0.3)

        # Complexity keywords
        complex_keywords = [
            "advanced", "complex", "detailed", "comprehensive", "multiple",
            "integrate", "combine", "correlate", "predict", "model"
        ]

        complexity += sum(0.1 for keyword in complex_keywords if keyword in message.lower())

        return min(complexity, 1.0)

    async def get_conversation_context(self, user_id: str, session_id: str) -> ConversationContext:
        """Get or create conversation context for a user session."""
        context_key = f"{user_id}_{session_id}"

        if context_key not in self.conversation_contexts:
            self.conversation_contexts[context_key] = ConversationContext(
                user_id=user_id,
                session_id=session_id,
                conversation_history=[],
                user_preferences={},
                current_task=None,
                attached_data=[],
                last_interaction=datetime.now()
            )

        return self.conversation_contexts[context_key]

    async def update_conversation_context(
        self,
        context: ConversationContext,
        message: str,
        response: str,
        intent: UserIntent
    ) -> None:
        """Update conversation context with new interaction."""
        context.conversation_history.append({
            "timestamp": datetime.now().isoformat(),
            "user_message": message,
            "agent_response": response,
            "intent": intent.intent_type,
            "confidence": intent.confidence
        })

        # Keep only last 10 interactions for memory efficiency
        if len(context.conversation_history) > 10:
            context.conversation_history = context.conversation_history[-10:]

        context.last_interaction = datetime.now()

        # Update current task if applicable
        if intent.intent_type in ["analysis_request", "marketing_request", "classification_request"]:
            context.current_task = intent.intent_type

    async def generate_welcome_message(self, user_context: Dict[str, Any]) -> str:
        """Generate a personalized welcome message for the user."""
        user_name = user_context.get("user_name", "there")

        welcome_messages = [
            f"Hello {user_name}! I'm your Datagenius Concierge. I'm here to help you find the perfect AI persona for your needs and guide you through your data journey.",
            f"Welcome back, {user_name}! Ready to unlock insights from your data? I can recommend the best AI persona for your specific task.",
            f"Hi {user_name}! I'm here to make your Datagenius experience seamless. Whether you need data analysis, marketing content, or classification - I'll guide you to the right persona."
        ]

        # Select message based on user history
        if user_context.get("is_returning_user"):
            return welcome_messages[1]
        else:
            return welcome_messages[0]

    async def generate_persona_recommendation(
        self,
        intent: UserIntent,
        context: ConversationContext
    ) -> str:
        """Generate a detailed persona recommendation response."""
        # Note: context parameter reserved for future personalization features
        if not intent.suggested_personas:
            return self._generate_general_guidance(intent)

        response_parts = []

        # Add confidence-based intro
        if intent.confidence > 0.8:
            response_parts.append("Based on your request, I'm confident I can recommend the perfect persona:")
        elif intent.confidence > 0.6:
            response_parts.append("I have some good recommendations for you:")
        else:
            response_parts.append("Let me suggest a few personas that might help:")

        # Add persona recommendations
        for i, persona_id in enumerate(intent.suggested_personas, 1):
            persona_info = await self._get_persona_info(persona_id)
            if persona_info:
                response_parts.append(f"\n{i}. **{persona_info['name']}**: {persona_info['description']}")

        # Add data attachment guidance if needed
        if intent.requires_data:
            response_parts.append("\n💡 **Tip**: Don't forget to attach your data file before starting the conversation with your chosen persona for the best results!")

        # Add complexity guidance
        if intent.complexity_score > 0.7:
            response_parts.append("\n🔧 **Note**: Your task seems complex. Consider breaking it down into smaller steps for better results.")

        return "\n".join(response_parts)

    async def _get_persona_info(self, persona_id: str) -> Optional[Dict[str, Any]]:
        """Get persona information dynamically from the registry."""
        try:
            # First try to get configuration from the registry
            config = self.agent_registry.get_configuration(persona_id)
            if config:
                return {
                    "name": config.get("name", persona_id.replace("-", " ").title()),
                    "description": config.get("description", "AI assistant for specialized tasks.")
                }

            # Fallback: try to get agent class and extract info
            agent_class = self.agent_registry.get_agent_class(persona_id)
            if agent_class:
                # Extract name from class name
                class_name = agent_class.__name__
                name = class_name.replace("Agent", "").replace("AI", " AI")

                # Use docstring as description if available
                description = agent_class.__doc__ or "AI assistant for specialized tasks."

                return {
                    "name": name,
                    "description": description.strip()
                }

            # Final fallback: generate basic info from persona_id
            return {
                "name": persona_id.replace("-", " ").title(),
                "description": f"AI assistant specialized in {persona_id.replace('-', ' ')} tasks."
            }

        except Exception as e:
            logger.error(f"Error fetching persona info for {persona_id}: {e}")
            return None

    def _generate_general_guidance(self, intent: UserIntent) -> str:
        """Generate general guidance when no specific personas are recommended."""
        guidance_map = {
            "general_question": "I'm here to help! You can ask me to recommend an AI persona for your specific task, or I can help you upload and analyze your data. What would you like to do?",
            "persona_request": "I'd be happy to recommend a persona! Could you tell me more about what you're trying to accomplish? For example:\n• Analyze data (CSV, Excel files)\n• Create marketing content\n• Classify or organize information",
            "data_help": "Great! I can help you with data. Here's what you can do:\n• Upload CSV or Excel files for analysis\n• Get insights and visualizations\n• Process documents (PDF, Word)\n\nWhat type of data are you working with?"
        }

        return guidance_map.get(intent.intent_type, "I'm here to help! What can I assist you with today?")

    async def generate_data_attachment_guidance(self, file_type: Optional[str] = None) -> str:
        """Generate guidance for data attachment."""
        base_message = "📎 **Data Attachment Help**\n\n"

        if file_type:
            file_guidance = {
                "csv": "CSV files are perfect for the Composable Analyst! You'll get detailed analysis, visualizations, and statistical insights.",
                "excel": "Excel files work great with our data analysis personas. They can handle multiple sheets and complex data structures.",
                "pdf": "PDF files can be processed for text extraction and analysis. Great for document classification and content analysis."
            }
            base_message += file_guidance.get(file_type, "This file type is supported by our data processing personas.")
        else:
            base_message += "You can attach various file types:\n• **CSV/Excel**: For data analysis and visualization\n• **PDF/Word**: For document processing and text analysis\n• **JSON/XML**: For structured data analysis"

        base_message += "\n\n🎯 **Pro Tip**: Upload your file first, then tell me what you want to discover about your data!"

        return base_message

    async def handle_persona_handoff(
        self,
        target_persona: str,
        user_message: str,
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Handle handoff to a specific persona with context preservation."""
        try:
            # Prepare handoff context
            handoff_context = {
                "original_message": user_message,
                "user_intent": context.current_task,
                "conversation_history": context.conversation_history[-3:],  # Last 3 interactions
                "attached_data": context.attached_data,
                "user_preferences": context.user_preferences,
                "handoff_reason": f"User request routed from Concierge Agent",
                "timestamp": datetime.now().isoformat()
            }

            # Create agent instance
            agent_instance = await self.agent_registry.create_agent_instance(target_persona)

            if not agent_instance:
                return {
                    "success": False,
                    "message": f"Sorry, I couldn't connect you to the {target_persona} persona. Please try again.",
                    "error": "agent_instantiation_failed"
                }

            # Prepare handoff message
            handoff_message = f"Hello! The Concierge has connected you with me to help with: {user_message}"

            return {
                "success": True,
                "agent_instance": agent_instance,
                "handoff_context": handoff_context,
                "handoff_message": handoff_message,
                "target_persona": target_persona
            }

        except Exception as e:
            logger.error(f"Error in persona handoff to {target_persona}: {e}")
            return {
                "success": False,
                "message": "I encountered an issue connecting you to that persona. Please try again.",
                "error": str(e)
            }

    async def cleanup_old_contexts(self, max_age_hours: int = 24) -> None:
        """Clean up old conversation contexts to manage memory."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        contexts_to_remove = [
            key for key, context in self.conversation_contexts.items()
            if context.last_interaction < cutoff_time
        ]

        for key in contexts_to_remove:
            del self.conversation_contexts[key]

        if contexts_to_remove:
            logger.info(f"Cleaned up {len(contexts_to_remove)} old conversation contexts")

    async def process_message(
        self,
        message: str,
        user_id: str, # Changed from user_context
        conversation_id: str, # Changed from session_id
        context: Dict[str, Any] # This is the new user_context
    ) -> Dict[str, Any]:
        """
        Main message processing method for the Concierge Agent.

        Args:
            message: User's message
            user_id: The ID of the user.
            conversation_id: The ID of the conversation (used as session_id).
            context: Additional context from the orchestrator (replaces old user_context).

        Returns:
            Response dictionary with message and metadata
        """
        logger.info(f"ConciergeAgent.process_message received message: '{message[:50]}...', user_id: {user_id}, conversation_id: {conversation_id}, context keys: {list(context.keys()) if context else None}")
        try:
            # Get conversation context using user_id and conversation_id (as session_id)
            conversation_state_context = await self.get_conversation_context(
                user_id if user_id else "anonymous", # Ensure user_id is not None
                conversation_id
            )

            # Parse user intent using the passed 'context' as 'user_context'
            intent = await self.parse_user_intent(message, context)

            # Generate appropriate response
            if intent.intent_type == "persona_request" or intent.suggested_personas:
                response_message = await self.generate_persona_recommendation(intent, conversation_state_context)
            elif intent.intent_type == "data_help":
                file_type = intent.entities.get("file_type")
                response_message = await self.generate_data_attachment_guidance(file_type)
            elif message.lower().strip() in ["hello", "hi", "hey", "start"]:
                # Pass the orchestrator's context as user_context
                response_message = await self.generate_welcome_message(context)
            else:
                response_message = self._generate_general_guidance(intent)

            # Update conversation context
            await self.update_conversation_context(conversation_state_context, message, response_message, intent)

            # Prepare response metadata
            response_metadata = {
                "intent": intent.intent_type,
                "confidence": intent.confidence,
                "suggested_personas": intent.suggested_personas,
                "requires_data": intent.requires_data,
                "complexity_score": intent.complexity_score,
                "entities": intent.entities
            }

            # Clean up old contexts periodically
            if len(self.conversation_contexts) > 100:
                await self.cleanup_old_contexts()

            return {
                "message": response_message,
                "metadata": response_metadata,
                "success": True
            }

        except Exception as e:
            logger.error(f"Error processing message in Concierge Agent: {e}")
            return {
                "message": "I apologize, but I encountered an issue processing your request. Please try again.",
                "metadata": {"error": str(e)},
                "success": False
            }
