"""
Knowledge Graph service for Datagenius using mem0ai.

This module provides a knowledge graph service using mem0ai, replacing the
previous NetworkX implementation. It handles entity and relationship extraction,
storage, and retrieval for knowledge graph operations.
"""

import os
import logging
import uuid
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path

from mem0 import Memory

from app.config import (
    MEM0_API_KEY,
    MEM0_ENDPOINT,
    MEM0_SELF_HOSTED,
    MEM0_DEFAULT_TTL,
    MEM0_MAX_MEMORIES,
    MEM0_MEMORY_THRESHOLD
)
from .memory_service import MemoryService

logger = logging.getLogger(__name__)

class KnowledgeGraphService:
    """
    Knowledge Graph service using mem0ai.

    This service provides a unified interface for knowledge graph operations
    across all AI personas in the Datagenius application. It implements the
    Singleton pattern to ensure a single instance is shared across the application.
    """

    _instance = None

    def __new__(cls):
        """Implement singleton pattern for knowledge graph service."""
        if cls._instance is None:
            cls._instance = super(KnowledgeGraphService, cls).__new__(cls)
            cls._instance._initialize()
            logger.info("Initialized mem0ai Knowledge Graph service")
        return cls._instance

    def _initialize(self):
        """Initialize the knowledge graph service with configuration."""
        try:
            # Use the existing memory service to leverage its configuration
            self.memory_service = MemoryService()
            self.memory = self.memory_service.memory

            # Set configuration
            self.kg_dir = os.path.join(os.getcwd(), "knowledge_graphs")
            os.makedirs(self.kg_dir, exist_ok=True)

            # Track initialization status
            self.initialized = self.memory_service.initialized
            logger.info(f"Knowledge Graph service initialized successfully (self-hosted: {MEM0_SELF_HOSTED})")
        except Exception as e:
            self.initialized = False
            logger.error(f"Failed to initialize Knowledge Graph service: {e}")

    def create_graph(self, name: str, description: str = "", metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Create a new knowledge graph.

        Args:
            name: Name of the graph
            description: Description of the graph
            metadata: Additional metadata for the graph

        Returns:
            ID of the created graph
        """
        if not self.initialized:
            logger.warning("Knowledge Graph service not initialized, cannot create graph")
            raise RuntimeError("Knowledge Graph service not initialized")

        try:
            # Generate a unique ID for the graph
            graph_id = str(uuid.uuid4())

            # Create metadata for the graph
            graph_metadata = {
                "name": name,
                "description": description,
                "is_knowledge_graph": True,
                "graph_id": graph_id,
                **(metadata or {})
            }

            # Add graph to mem0ai as a memory with special metadata
            result = self.memory.add(
                f"Knowledge Graph: {name}\nDescription: {description}",
                user_id="system",  # Use system user for knowledge graphs
                metadata=graph_metadata
            )

            logger.info(f"Created knowledge graph '{name}' with ID: {graph_id}")
            return graph_id
        except Exception as e:
            logger.error(f"Error creating knowledge graph: {e}")
            raise

    def add_entity(self, graph_id: str, entity_type: str, name: str,
                  properties: Optional[Dict[str, Any]] = None,
                  description: str = "") -> str:
        """
        Add an entity to the knowledge graph.

        Args:
            graph_id: ID of the graph
            entity_type: Type of the entity
            name: Name of the entity
            properties: Additional properties for the entity
            description: Description of the entity

        Returns:
            ID of the created entity
        """
        if not self.initialized:
            logger.warning("Knowledge Graph service not initialized, cannot add entity")
            raise RuntimeError("Knowledge Graph service not initialized")

        try:
            # Generate a unique ID for the entity
            entity_id = str(uuid.uuid4())

            # Create metadata for the entity
            entity_metadata = {
                "entity_type": entity_type,
                "name": name,
                "properties": properties or {},
                "is_entity": True,
                "graph_id": graph_id,
                "entity_id": entity_id
            }

            # Add entity to mem0ai as a memory with special metadata
            content = f"Entity: {name}\nType: {entity_type}\nDescription: {description}"
            result = self.memory.add(
                content,
                user_id="system",  # Use system user for knowledge graphs
                metadata=entity_metadata
            )

            logger.info(f"Added entity '{name}' of type '{entity_type}' to graph {graph_id}")
            return entity_id
        except Exception as e:
            logger.error(f"Error adding entity to knowledge graph: {e}")
            raise

    def add_relationship(self, graph_id: str, relationship_type: str,
                        source_id: str, target_id: str,
                        properties: Optional[Dict[str, Any]] = None,
                        description: str = "") -> str:
        """
        Add a relationship to the knowledge graph.

        Args:
            graph_id: ID of the graph
            relationship_type: Type of the relationship
            source_id: ID of the source entity
            target_id: ID of the target entity
            properties: Additional properties for the relationship
            description: Description of the relationship

        Returns:
            ID of the created relationship
        """
        if not self.initialized:
            logger.warning("Knowledge Graph service not initialized, cannot add relationship")
            raise RuntimeError("Knowledge Graph service not initialized")

        try:
            # Generate a unique ID for the relationship
            relationship_id = str(uuid.uuid4())

            # Create metadata for the relationship
            relationship_metadata = {
                "relationship_type": relationship_type,
                "source_id": source_id,
                "target_id": target_id,
                "properties": properties or {},
                "is_relationship": True,
                "graph_id": graph_id,
                "relationship_id": relationship_id
            }

            # Add relationship to mem0ai as a memory with special metadata
            content = f"Relationship: {relationship_type}\nSource: {source_id}\nTarget: {target_id}\nDescription: {description}"
            result = self.memory.add(
                content,
                user_id="system",  # Use system user for knowledge graphs
                metadata=relationship_metadata
            )

            logger.info(f"Added relationship '{relationship_type}' from {source_id} to {target_id} in graph {graph_id}")
            return relationship_id
        except Exception as e:
            logger.error(f"Error adding relationship to knowledge graph: {e}")
            raise

    def get_entities(self, graph_id: str, entity_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get entities from the knowledge graph.

        Args:
            graph_id: ID of the graph
            entity_type: Optional type to filter entities

        Returns:
            List of entities
        """
        if not self.initialized:
            logger.warning("Knowledge Graph service not initialized, cannot get entities")
            return []

        try:
            # Create metadata filter
            metadata_filter = {
                "is_entity": True,
                "graph_id": graph_id
            }

            # Add entity type filter if provided
            if entity_type:
                metadata_filter["entity_type"] = entity_type

            # Search memories using mem0ai with metadata filter
            results = self.memory.search(
                "",  # Empty query to match based on metadata only
                user_id="system",  # Use system user for knowledge graphs
                limit=100,  # Increase limit to get more entities
                metadata_filter=metadata_filter
            )

            # Format results
            entities = []
            for result in results.get("results", []):
                metadata = result.get("metadata", {})
                entities.append({
                    "id": metadata.get("entity_id"),
                    "type": metadata.get("entity_type"),
                    "name": metadata.get("name"),
                    "properties": metadata.get("properties", {}),
                    "content": result.get("content", "")
                })

            logger.debug(f"Found {len(entities)} entities in graph {graph_id}")
            return entities
        except Exception as e:
            logger.error(f"Error getting entities from knowledge graph: {e}")
            return []

    def get_relationships(self, graph_id: str, relationship_type: Optional[str] = None,
                         source_id: Optional[str] = None, target_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get relationships from the knowledge graph.

        Args:
            graph_id: ID of the graph
            relationship_type: Optional type to filter relationships
            source_id: Optional source entity ID to filter relationships
            target_id: Optional target entity ID to filter relationships

        Returns:
            List of relationships
        """
        if not self.initialized:
            logger.warning("Knowledge Graph service not initialized, cannot get relationships")
            return []

        try:
            # Create metadata filter
            metadata_filter = {
                "is_relationship": True,
                "graph_id": graph_id
            }

            # Add additional filters if provided
            if relationship_type:
                metadata_filter["relationship_type"] = relationship_type
            if source_id:
                metadata_filter["source_id"] = source_id
            if target_id:
                metadata_filter["target_id"] = target_id

            # Search memories using mem0ai with metadata filter
            results = self.memory.search(
                "",  # Empty query to match based on metadata only
                user_id="system",  # Use system user for knowledge graphs
                limit=100,  # Increase limit to get more relationships
                metadata_filter=metadata_filter
            )

            # Format results
            relationships = []
            for result in results.get("results", []):
                metadata = result.get("metadata", {})
                relationships.append({
                    "id": metadata.get("relationship_id"),
                    "type": metadata.get("relationship_type"),
                    "source_id": metadata.get("source_id"),
                    "target_id": metadata.get("target_id"),
                    "properties": metadata.get("properties", {}),
                    "content": result.get("content", "")
                })

            logger.debug(f"Found {len(relationships)} relationships in graph {graph_id}")
            return relationships
        except Exception as e:
            logger.error(f"Error getting relationships from knowledge graph: {e}")
            return []



    async def query_graph(
        self,
        graph_id: str,
        query: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Query the knowledge graph using natural language.

        Args:
            graph_id: ID of the knowledge graph
            query: Natural language query
            limit: Maximum number of results

        Returns:
            List of matching entities and relationships
        """
        if not self.memory:
            raise RuntimeError("Memory service not initialized")

        try:
            # Search for relevant memories in the graph
            results = self.memory.search(
                query=query,
                user_id="system",
                limit=limit
            )

            # Filter results to only include items from this graph
            graph_results = []
            for result in results:
                metadata = result.get("metadata", {})
                if metadata.get("graph_id") == graph_id:
                    graph_results.append({
                        "content": result.get("memory", ""),
                        "metadata": metadata,
                        "score": result.get("score", 0.0)
                    })

            logger.info(f"Found {len(graph_results)} results for query '{query}' in graph {graph_id}")
            return graph_results
        except Exception as e:
            logger.error(f"Error querying knowledge graph: {e}")
            raise

    async def get_entity(
        self,
        graph_id: str,
        entity_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific entity.

        Args:
            graph_id: ID of the knowledge graph
            entity_id: ID of the entity

        Returns:
            Entity information or None if not found
        """
        if not self.memory:
            raise RuntimeError("Memory service not initialized")

        try:
            # Search for the specific entity
            results = self.memory.search(
                query=f"entity_id:{entity_id}",
                user_id="system",
                limit=1
            )

            for result in results:
                metadata = result.get("metadata", {})
                if (metadata.get("graph_id") == graph_id and
                    metadata.get("entity_id") == entity_id and
                    metadata.get("is_entity")):
                    return {
                        "entity_id": entity_id,
                        "content": result.get("memory", ""),
                        "metadata": metadata
                    }

            return None
        except Exception as e:
            logger.error(f"Error getting entity {entity_id}: {e}")
            raise

    async def find_path(
        self,
        graph_id: str,
        source_entity_id: str,
        target_entity_id: str,
        max_depth: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Find connections/path between two entities.

        Args:
            graph_id: ID of the knowledge graph
            source_entity_id: ID of the source entity
            target_entity_id: ID of the target entity
            max_depth: Maximum search depth

        Returns:
            List of paths between entities
        """
        if not self.memory:
            raise RuntimeError("Memory service not initialized")

        try:
            # Get all relationships in the graph
            relationships = self.memory.search(
                query="is_relationship:true",
                user_id="system",
                limit=1000  # Large limit to get all relationships
            )

            # Filter relationships for this graph
            graph_relationships = []
            for rel in relationships:
                metadata = rel.get("metadata", {})
                if (metadata.get("graph_id") == graph_id and
                    metadata.get("is_relationship")):
                    graph_relationships.append(metadata)

            # Simple path finding (could be enhanced with more sophisticated algorithms)
            paths = []

            # Direct connection
            for rel in graph_relationships:
                if (rel.get("source_entity_id") == source_entity_id and
                    rel.get("target_entity_id") == target_entity_id):
                    paths.append([{
                        "source": source_entity_id,
                        "target": target_entity_id,
                        "relationship": rel.get("relationship_type"),
                        "depth": 1
                    }])

            # Two-hop connections (simplified)
            if not paths and max_depth >= 2:
                for rel1 in graph_relationships:
                    if rel1.get("source_entity_id") == source_entity_id:
                        intermediate = rel1.get("target_entity_id")
                        for rel2 in graph_relationships:
                            if (rel2.get("source_entity_id") == intermediate and
                                rel2.get("target_entity_id") == target_entity_id):
                                paths.append([
                                    {
                                        "source": source_entity_id,
                                        "target": intermediate,
                                        "relationship": rel1.get("relationship_type"),
                                        "depth": 1
                                    },
                                    {
                                        "source": intermediate,
                                        "target": target_entity_id,
                                        "relationship": rel2.get("relationship_type"),
                                        "depth": 2
                                    }
                                ])

            logger.info(f"Found {len(paths)} paths between {source_entity_id} and {target_entity_id}")
            return paths
        except Exception as e:
            logger.error(f"Error finding path between entities: {e}")
            raise
