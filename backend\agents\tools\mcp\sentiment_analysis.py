"""
Sentiment analysis MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for performing sentiment analysis on text data.
"""

import logging
import os
import json
import pandas as pd
import numpy as np
import base64
import re
import string
from typing import Dict, Any, List, Optional
import plotly.express as px
import plotly.graph_objects as go

from .base import BaseMCPTool

logger = logging.getLogger(__name__)

try:
    from nltk.sentiment.vader import SentimentIntensityAnalyzer
    import nltk
    from nltk.corpus import stopwords
    from nltk.stem import WordNetLemmatizer
    
    # Download NLTK resources if needed
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt')
    
    try:
        nltk.data.find('corpora/stopwords')
    except LookupError:
        nltk.download('stopwords')
    
    try:
        nltk.data.find('corpora/wordnet')
    except LookupError:
        nltk.download('wordnet')
    
    try:
        nltk.data.find('sentiment/vader_lexicon')
    except LookupError:
        nltk.download('vader_lexicon')
    
    NLTK_AVAILABLE = True
except ImportError:
    logger.warning("NLTK not available. Sentiment analysis will use a simplified approach.")
    NLTK_AVAILABLE = False


class SentimentAnalysisTool(BaseMCPTool):
    """Tool for performing sentiment analysis on text data."""

    def __init__(self):
        """Initialize the sentiment analysis tool."""
        super().__init__(
            name="analyze_sentiment",
            description="Perform sentiment analysis on text data",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "text_column": {"type": "string"}
                },
                "required": ["file_path"]
            },
            annotations={
                "title": "Analyze Sentiment",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.data_dir = "data"

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        if "data_dir" in config:
            self.data_dir = config["data_dir"]
            logger.info(f"Set data directory to: {self.data_dir}")

    def _text_preprocessing(self, text: str) -> str:
        """
        Preprocess text for sentiment analysis.

        Args:
            text: The text to preprocess

        Returns:
            Preprocessed text
        """
        if not NLTK_AVAILABLE:
            # Simple preprocessing if NLTK is not available
            text = str(text).lower()
            text = re.sub(r'<[^>]*>', '', text)
            text = re.sub(r'[' + string.punctuation + ']', '', text)
            return text
        
        # Convert text to lowercase
        text = str(text).lower()
        
        # Remove HTML tags and special characters
        text = re.sub(r'<[^>]*>', '', text)
        text = re.sub(r'[' + string.punctuation + ']', '', text)
        
        # Tokenize text
        tokens = nltk.word_tokenize(text)
        
        # Remove stopwords
        stop_words = set(stopwords.words('english'))
        tokens = [t for t in tokens if t not in stop_words]
        
        # Lemmatize words
        lemmatizer = WordNetLemmatizer()
        tokens = [lemmatizer.lemmatize(t) for t in tokens]
        
        # Join tokens back into a string
        return ' '.join(tokens)

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            file_path = arguments["file_path"]
            text_column = arguments.get("text_column")

            # Check if the path is relative and prepend the data directory
            if not os.path.isabs(file_path):
                file_path = os.path.join(self.data_dir, file_path)

            # Check if the file exists
            if not os.path.exists(file_path):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"File not found: {file_path}"
                        }
                    ]
                }

            # Load the data
            if file_path.endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ]
                }

            # If text_column is not specified, try to find a suitable column
            if not text_column:
                # Look for columns with 'text', 'comment', 'review', 'description', 'message' in the name
                text_column_candidates = [col for col in df.columns if any(keyword in col.lower() for keyword in ['text', 'comment', 'review', 'description', 'message'])]
                
                if text_column_candidates:
                    text_column = text_column_candidates[0]
                else:
                    # If no suitable column found, use the first string column
                    string_columns = df.select_dtypes(include=['object']).columns.tolist()
                    if string_columns:
                        text_column = string_columns[0]
                    else:
                        return {
                            "isError": True,
                            "content": [
                                {
                                    "type": "text",
                                    "text": "No suitable text column found. Please specify a text_column."
                                }
                            ]
                        }

            # Check if the column exists
            if text_column not in df.columns:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Column '{text_column}' not found in the data."
                        }
                    ]
                }

            # Create a copy of the data
            result_data = df.copy()
            
            # Preprocess text data
            result_data['Clean_Text'] = result_data[text_column].apply(lambda x: self._text_preprocessing(str(x)))
            
            if NLTK_AVAILABLE:
                # Initialize sentiment analyzer
                sia = SentimentIntensityAnalyzer()
                
                # Perform sentiment analysis
                result_data['Sentiment'] = result_data['Clean_Text'].apply(lambda x: sia.polarity_scores(x)['compound'])
                
                # Create sentiment labels with improved thresholds
                result_data['Sentiment_Label'] = pd.cut(
                    result_data['Sentiment'],
                    bins=[-1, -0.1, 0.1, 1],
                    labels=['Negative', 'Neutral', 'Positive'],
                    include_lowest=True
                )
            else:
                # Simple sentiment analysis using keyword matching
                positive_words = set(['good', 'great', 'excellent', 'amazing', 'wonderful', 'best', 'love', 'happy', 'positive', 'recommend'])
                negative_words = set(['bad', 'terrible', 'awful', 'worst', 'hate', 'poor', 'negative', 'disappointing', 'disappointed', 'horrible'])
                
                def simple_sentiment(text):
                    words = set(text.split())
                    pos_count = len(words.intersection(positive_words))
                    neg_count = len(words.intersection(negative_words))
                    
                    if pos_count > neg_count:
                        return 0.5  # Positive
                    elif neg_count > pos_count:
                        return -0.5  # Negative
                    else:
                        return 0.0  # Neutral
                
                result_data['Sentiment'] = result_data['Clean_Text'].apply(simple_sentiment)
                
                # Create sentiment labels
                result_data['Sentiment_Label'] = pd.cut(
                    result_data['Sentiment'],
                    bins=[-1, -0.1, 0.1, 1],
                    labels=['Negative', 'Neutral', 'Positive'],
                    include_lowest=True
                )
            
            # Create a summary of the sentiment analysis
            sentiment_counts = result_data['Sentiment_Label'].value_counts().to_dict()
            total_texts = len(result_data)
            
            summary = {
                "total_texts": total_texts,
                "positive_texts": sentiment_counts.get('Positive', 0),
                "negative_texts": sentiment_counts.get('Negative', 0),
                "neutral_texts": sentiment_counts.get('Neutral', 0),
                "positive_percentage": round(sentiment_counts.get('Positive', 0) / total_texts * 100, 2),
                "negative_percentage": round(sentiment_counts.get('Negative', 0) / total_texts * 100, 2),
                "neutral_percentage": round(sentiment_counts.get('Neutral', 0) / total_texts * 100, 2),
                "average_sentiment": round(result_data['Sentiment'].mean(), 2)
            }
            
            # Create visualizations
            try:
                # Sentiment distribution
                fig = px.pie(
                    result_data,
                    names='Sentiment_Label',
                    title='Sentiment Distribution',
                    color='Sentiment_Label',
                    color_discrete_map={
                        'Positive': 'green',
                        'Neutral': 'gray',
                        'Negative': 'red'
                    }
                )
                
                # Convert the figure to JSON
                plot_json = fig.to_json()
                
                # Also generate a static image as fallback
                img_bytes = fig.to_image(format="png")
                img_base64 = base64.b64encode(img_bytes).decode("utf-8")
                
                summary["visualization"] = {
                    "plot_json": plot_json,
                    "plot_image": f"data:image/png;base64,{img_base64}"
                }
                
                # Check if there are any date columns for time-based analysis
                date_columns = []
                for col in df.columns:
                    try:
                        pd.to_datetime(df[col])
                        date_columns.append(col)
                    except:
                        pass
                
                if date_columns:
                    date_column = date_columns[0]
                    
                    # Ensure date is in datetime format
                    result_data[date_column] = pd.to_datetime(result_data[date_column], errors='coerce')
                    
                    # Group by date and calculate sentiment counts
                    daily_sentiment = result_data.groupby(pd.Grouper(key=date_column, freq='D'))['Sentiment_Label'].value_counts().unstack().fillna(0)
                    
                    # Create an interactive line chart
                    fig2 = go.Figure()
                    for label in daily_sentiment.columns:
                        fig2.add_trace(go.Scatter(x=daily_sentiment.index, y=daily_sentiment[label],
                                               mode='lines', name=label))
                    
                    fig2.update_layout(
                        title="Sentiment Over Time",
                        xaxis_title="Date",
                        yaxis_title="Count"
                    )
                    
                    # Convert the figure to JSON
                    plot_json2 = fig2.to_json()
                    
                    # Also generate a static image as fallback
                    img_bytes2 = fig2.to_image(format="png")
                    img_base64_2 = base64.b64encode(img_bytes2).decode("utf-8")
                    
                    summary["time_visualization"] = {
                        "plot_json": plot_json2,
                        "plot_image": f"data:image/png;base64,{img_base64_2}"
                    }
            except Exception as e:
                logger.warning(f"Error creating sentiment visualizations: {str(e)}")
            
            # Save the sentiment analysis results to a new file
            output_path = file_path.replace(".", "_sentiment.")
            if file_path.endswith(".csv"):
                result_data.to_csv(output_path, index=False)
            elif file_path.endswith((".xls", ".xlsx")):
                result_data.to_excel(output_path, index=False)
            elif file_path.endswith(".json"):
                result_data.to_json(output_path, orient="records")
            
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Sentiment analysis completed successfully.\n\nSummary:\n- Total texts: {summary['total_texts']}\n- Positive: {summary['positive_percentage']}% ({summary['positive_texts']} texts)\n- Neutral: {summary['neutral_percentage']}% ({summary['neutral_texts']} texts)\n- Negative: {summary['negative_percentage']}% ({summary['negative_texts']} texts)\n- Average sentiment: {summary['average_sentiment']}\n\nResults saved to: {output_path}"
                    },
                    {
                        "type": "image",
                        "image": {
                            "url": summary.get("visualization", {}).get("plot_image", "")
                        }
                    }
                ],
                "metadata": {
                    "summary": summary,
                    "output_path": output_path,
                    "preview": result_data[['Clean_Text', 'Sentiment', 'Sentiment_Label']].head(10).to_dict(orient="records")
                }
            }
            
        except Exception as e:
            logger.error(f"Error performing sentiment analysis: {str(e)}", exc_info=True)
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error performing sentiment analysis: {str(e)}"
                    }
                ]
            }
