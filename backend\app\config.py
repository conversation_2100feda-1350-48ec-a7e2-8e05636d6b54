"""
Configuration settings for the Datagenius backend.

This module provides configuration settings for the Datagenius backend,
loaded from environment variables.
"""

import os
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL")
DATABASE_ECHO = os.getenv("DATABASE_ECHO", "false").lower() == "true"

# JWT configuration
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-for-development-only")
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

# Security configuration
MAX_REFRESH_COUNT = int(os.getenv("MAX_REFRESH_COUNT", "30"))  # Maximum number of refreshes before requiring re-authentication
MAX_CONCURRENT_SESSIONS = int(os.getenv("MAX_CONCURRENT_SESSIONS", "5"))  # Maximum number of active sessions per user
ENFORCE_IP_VALIDATION = os.getenv("ENFORCE_IP_VALIDATION", "false").lower() == "true"  # Whether to enforce IP validation
IP_CHANGE_LOCKOUT = os.getenv("IP_CHANGE_LOCKOUT", "false").lower() == "true"  # Whether to lock out users on IP change

# Redis configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

# File upload configuration
UPLOAD_DIR = os.getenv("UPLOAD_DIR", "temp_uploads")
MAX_UPLOAD_SIZE = int(os.getenv("MAX_UPLOAD_SIZE", "10485760"))  # 10 MB

# LLM provider configuration
GROQ_API_KEY = os.getenv("GROQ_API_KEY", "")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "")

# Google OAuth configuration
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID", "")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET", "")
GOOGLE_REDIRECT_URI = os.getenv("GOOGLE_REDIRECT_URI", "http://localhost:5173/auth/google/callback")

# LLM endpoint configuration
GROQ_ENDPOINT = os.getenv("GROQ_ENDPOINT", "https://api.groq.com/openai/v1")
OPENAI_ENDPOINT = os.getenv("OPENAI_ENDPOINT", "https://api.openai.com/v1")
GEMINI_ENDPOINT = os.getenv("GEMINI_ENDPOINT", "https://generativelanguage.googleapis.com")
OPENROUTER_ENDPOINT = os.getenv("OPENROUTER_ENDPOINT", "https://openrouter.ai/api/v1")
OLLAMA_ENDPOINT = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
REQUESTY_ENDPOINT= os.getenv("REQUESTY_ENDPOINT","https://router.requesty.ai/v1")

# mem0ai configuration
MEM0_API_KEY = os.getenv("MEM0_API_KEY", "")
MEM0_ENDPOINT = os.getenv("MEM0_ENDPOINT", "")
MEM0_SELF_HOSTED = os.getenv("MEM0_SELF_HOSTED", "false").lower() == "true"
MEM0_DEFAULT_TTL = int(os.getenv("MEM0_DEFAULT_TTL", "2592000"))  # 30 days in seconds
MEM0_MAX_MEMORIES = int(os.getenv("MEM0_MAX_MEMORIES", "1000"))
MEM0_MEMORY_THRESHOLD = float(os.getenv("MEM0_MEMORY_THRESHOLD", "0.7"))

# Qdrant configuration
QDRANT_HOST = os.getenv("QDRANT_HOST", "localhost")
QDRANT_PORT = int(os.getenv("QDRANT_PORT", "6333"))

# Frontend URL for redirects
FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:5173")

# Email configuration
EMAIL_ENABLED = os.getenv("EMAIL_ENABLED", "false").lower() == "true"
EMAIL_SENDER = os.getenv("EMAIL_SENDER", "<EMAIL>")
EMAIL_SMTP_SERVER = os.getenv("EMAIL_SMTP_SERVER", "smtp.example.com")
EMAIL_SMTP_PORT = int(os.getenv("EMAIL_SMTP_PORT", "587"))
EMAIL_SMTP_USER = os.getenv("EMAIL_SMTP_USER", "")
EMAIL_SMTP_PASSWORD = os.getenv("EMAIL_SMTP_PASSWORD", "")
EMAIL_USE_TLS = os.getenv("EMAIL_USE_TLS", "true").lower() == "true"

# CORS configuration
CORS_ORIGINS = os.getenv("CORS_ORIGINS", "*").split(",")

# Debug configuration
DEBUG = os.getenv("DEBUG", "false").lower() == "true"

# Application configuration
APP_NAME = os.getenv("APP_NAME", "Datagenius")
APP_VERSION = os.getenv("APP_VERSION", "1.0.0")
APP_DESCRIPTION = os.getenv("APP_DESCRIPTION", "AI-powered data analysis platform")

# Log configuration settings
logger.info("Loaded configuration settings:")
logger.info(f"DATABASE_URL: {DATABASE_URL.split('://')[0]}://*****")
logger.info(f"DATABASE_ECHO: {DATABASE_ECHO}")
logger.info(f"JWT_ALGORITHM: {JWT_ALGORITHM}")
logger.info(f"ACCESS_TOKEN_EXPIRE_MINUTES: {ACCESS_TOKEN_EXPIRE_MINUTES}")
logger.info(f"REFRESH_TOKEN_EXPIRE_DAYS: {REFRESH_TOKEN_EXPIRE_DAYS}")
logger.info(f"UPLOAD_DIR: {UPLOAD_DIR}")
logger.info(f"MAX_UPLOAD_SIZE: {MAX_UPLOAD_SIZE}")
logger.info(f"EMAIL_ENABLED: {EMAIL_ENABLED}")
logger.info(f"DEBUG: {DEBUG}")
logger.info(f"APP_NAME: {APP_NAME}")
logger.info(f"APP_VERSION: {APP_VERSION}")
logger.info(f"GOOGLE_CLIENT_ID: {'Configured' if GOOGLE_CLIENT_ID else 'Not configured'}")
logger.info(f"GOOGLE_REDIRECT_URI: {GOOGLE_REDIRECT_URI}")
logger.info(f"MEM0_API_KEY: {'Configured' if MEM0_API_KEY else 'Not configured'}")
logger.info(f"MEM0_SELF_HOSTED: {MEM0_SELF_HOSTED}")
logger.info(f"MEM0_DEFAULT_TTL: {MEM0_DEFAULT_TTL}")
