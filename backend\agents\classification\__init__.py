"""
Classification agent implementation.

This package contains the implementation of the Classification Agent,
which is responsible for text classification tasks.
"""

import logging

# Configure logging
logger = logging.getLogger(__name__)

# Import the hf_classifier module explicitly
from . import hf_classifier

# Register classification components
from .register import register_classification_components
register_classification_components()

# Import the MCP-based components for easy access
from .components_mcp import (
    ClassificationParserComponent,
    MCPClassifierComponent,
    ClassificationErrorHandlerComponent
)

# Import the MCP-based agent for easy access
from .composable_agent_mcp import ComposableClassificationAgent

logger.info("Initialized classification agent package")
