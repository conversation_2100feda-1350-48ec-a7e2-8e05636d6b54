"""
Pydantic models for the Marketing Agent.

This module defines Pydantic models for marketing agent requests and responses.
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class MarketingTaskRequest(BaseModel):
    """Request model for marketing task."""
    task_type: str = Field(..., description="Type of marketing task to perform")
    brand_description: str = Field("", description="Description of the brand")
    target_audience: str = Field("", description="Description of the target audience")
    products_services: str = Field("", description="Description of products or services")
    marketing_goals: str = Field("", description="Marketing goals")
    existing_content: str = Field("", description="Existing marketing content")
    keywords: str = Field("", description="Keywords to target")
    suggested_topics: str = Field("", description="Suggested topics")
    tone: str = Field("Professional", description="Tone of the content")
    file_id: Optional[int] = Field(None, description="ID of the uploaded file to analyze")
    is_first_conversation: bool = Field(False, description="Whether this is the first message in a conversation")
    has_data_source: bool = Field(False, description="Whether a data source is attached to the conversation")


class MarketingTaskResponse(BaseModel):
    """Response model for marketing task."""
    content: str = Field(..., description="Generated marketing content")
    task_type: str = Field(..., description="Type of marketing task performed")
    is_first_conversation: bool = Field(False, description="Whether this is the first message in a conversation")
    has_data_source: bool = Field(False, description="Whether a data source is attached to the conversation")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class MarketingCapability(BaseModel):
    """Model for a marketing capability."""
    id: str = Field(..., description="Capability identifier")
    name: str = Field(..., description="Display name of the capability")
    description: str = Field(..., description="Description of the capability")


class MarketingCapabilitiesResponse(BaseModel):
    """Response model for marketing capabilities."""
    capabilities: List[MarketingCapability] = Field(..., description="List of marketing capabilities")
