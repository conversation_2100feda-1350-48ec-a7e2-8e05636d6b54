# Admin Implementation Plan for Datagenius

This document provides a detailed implementation plan for adding admin functionality to the Datagenius application, focusing on managing AI personas and other administrative features.

## Database Schema Updates

### 1. Update Persona Model

First, we need to enhance the database schema to store additional persona properties:

```sql
-- Create personas table if it doesn't exist
CREATE TABLE IF NOT EXISTS personas (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    industry VARCHAR(50),
    skills JSONB,
    rating FLOAT,
    review_count INTEGER,
    image_url VARCHAR(255),
    price FLOAT NOT NULL DEFAULT 10.0,
    provider VARCHAR(50) DEFAULT 'groq',
    model VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    age_restriction INTEGER DEFAULT 0,
    content_filters JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create admin activity logs table
CREATE TABLE IF NOT EXISTS admin_activity_logs (
    id SERIAL PRIMARY KEY,
    admin_id INTEGER REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id VARCHAR(50),
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Backend Implementation

### 1. Update Models

#### File: `backend/app/models/persona.py`
- Enhance the `PersonaBase` model with additional fields for price, provider, model, etc.
- Add admin-specific models for persona management

#### File: `backend/app/models/admin.py` (New)
- Create models for admin-specific functionality
- Define models for admin activity logs

### 2. Update Database Functions

#### File: `backend/app/database.py`
- Add functions for managing personas in the database
- Add functions for admin activity logging

### 3. Create Admin API Router

#### File: `backend/app/api/admin.py` (New)
- Create admin-specific API endpoints
- Implement CRUD operations for personas
- Add user management endpoints
- Add analytics endpoints

### 4. Update Main Application

#### File: `backend/app/main.py`
- Include the admin router in the FastAPI application

### 5. Create Admin Authentication Middleware

#### File: `backend/app/auth/admin.py` (New)
- Create admin-specific authentication middleware
- Implement admin-only route protection

## Frontend Implementation

### 1. Create Admin Routes

#### File: `frontend/src/App.tsx`
- Add admin routes to the React Router configuration
- Protect admin routes with admin-specific authentication

```jsx
// Add these routes to the existing Routes component
<Route path="/admin" element={
  <AdminProtectedRoute>
    <AdminDashboard />
  </AdminProtectedRoute>
} />
<Route path="/admin/personas" element={
  <AdminProtectedRoute>
    <AdminPersonas />
  </AdminProtectedRoute>
} />
<Route path="/admin/users" element={
  <AdminProtectedRoute>
    <AdminUsers />
  </AdminProtectedRoute>
} />
<Route path="/admin/analytics" element={
  <AdminProtectedRoute>
    <AdminAnalytics />
  </AdminProtectedRoute>
} />
<Route path="/admin/settings" element={
  <AdminProtectedRoute>
    <AdminSettings />
  </AdminProtectedRoute>
} />
```

### 2. Create Admin Protected Route Component

#### File: `frontend/src/components/auth/AdminProtectedRoute.tsx` (New)
- Create a component to protect admin routes
- Check if the user has admin privileges

### 3. Create Admin Context

#### File: `frontend/src/contexts/AdminContext.tsx` (New)
- Create a context for admin-specific state and functions
- Provide admin-specific API functions

### 4. Create Admin API Functions

#### File: `frontend/src/lib/adminApi.ts` (New)
- Create API functions for admin-specific endpoints
- Implement functions for persona management, user management, etc.

### 5. Create Admin Pages

#### File: `frontend/src/pages/admin/AdminDashboard.tsx` (New)
- Create the main admin dashboard page
- Display key metrics and recent activity

#### File: `frontend/src/pages/admin/AdminPersonas.tsx` (New)
- Create the persona management page
- Implement CRUD operations for personas

#### File: `frontend/src/pages/admin/AdminUsers.tsx` (New)
- Create the user management page
- Implement user management functions

#### File: `frontend/src/pages/admin/AdminAnalytics.tsx` (New)
- Create the analytics dashboard page
- Display usage statistics and charts

#### File: `frontend/src/pages/admin/AdminSettings.tsx` (New)
- Create the admin settings page
- Implement system configuration options

### 6. Create Admin Components

#### File: `frontend/src/components/admin/AdminLayout.tsx` (New)
- Create a layout component for admin pages
- Include admin navigation and header

#### File: `frontend/src/components/admin/PersonaForm.tsx` (New)
- Create a form component for editing personas
- Implement validation and submission logic

#### File: `frontend/src/components/admin/UserTable.tsx` (New)
- Create a table component for displaying users
- Implement sorting, filtering, and pagination

#### File: `frontend/src/components/admin/ActivityLog.tsx` (New)
- Create a component for displaying admin activity logs
- Implement filtering and pagination

## Detailed Implementation Steps

### Phase 1: Database and Backend Setup

1. **Update Database Schema**
   - Add new fields to the personas table
   - Create admin activity logs table

2. **Update Persona Models**
   - Enhance `PersonaBase` with new fields
   - Create admin-specific models

3. **Create Admin API Endpoints**
   - Implement persona management endpoints
   - Implement user management endpoints
   - Implement analytics endpoints

4. **Implement Admin Authentication**
   - Create admin-specific authentication middleware
   - Implement admin-only route protection

### Phase 2: Frontend Admin Dashboard

1. **Create Admin Routes**
   - Add admin routes to React Router
   - Create admin-protected route component

2. **Create Admin Dashboard**
   - Implement admin dashboard layout
   - Create overview metrics and charts

3. **Create Admin Navigation**
   - Implement admin sidebar navigation
   - Create admin header with quick actions

### Phase 3: Persona Management

1. **Create Persona List Page**
   - Implement persona table with sorting and filtering
   - Add actions for edit, delete, and status toggle

2. **Create Persona Edit Page**
   - Implement form for editing persona properties
   - Add validation and error handling

3. **Implement Persona Status Management**
   - Add controls for activating/deactivating personas
   - Implement age restriction settings

### Phase 4: User Management and Analytics

1. **Create User Management Page**
   - Implement user table with sorting and filtering
   - Add actions for editing user properties

2. **Create Analytics Dashboard**
   - Implement charts for usage statistics
   - Add revenue and purchase analytics

3. **Create Activity Logs**
   - Implement activity log display
   - Add filtering and search functionality

## File Changes Summary

### Backend Files to Create:
1. `backend/app/models/admin.py`
2. `backend/app/api/admin.py`
3. `backend/app/auth/admin.py`

### Backend Files to Modify:
1. `backend/app/models/persona.py`
2. `backend/app/database.py`
3. `backend/app/main.py`

### Frontend Files to Create:
1. `frontend/src/components/auth/AdminProtectedRoute.tsx`
2. `frontend/src/contexts/AdminContext.tsx`
3. `frontend/src/lib/adminApi.ts`
4. `frontend/src/pages/admin/AdminDashboard.tsx`
5. `frontend/src/pages/admin/AdminPersonas.tsx`
6. `frontend/src/pages/admin/AdminUsers.tsx`
7. `frontend/src/pages/admin/AdminAnalytics.tsx`
8. `frontend/src/pages/admin/AdminSettings.tsx`
9. `frontend/src/components/admin/AdminLayout.tsx`
10. `frontend/src/components/admin/PersonaForm.tsx`
11. `frontend/src/components/admin/UserTable.tsx`
12. `frontend/src/components/admin/ActivityLog.tsx`

### Frontend Files to Modify:
1. `frontend/src/App.tsx`
2. `frontend/src/components/LeftNavbar.tsx` (to add admin navigation for admin users)

## Testing Plan

1. **Database Schema Testing**
   - Verify that the new schema can be created without errors
   - Test data migration for existing personas

2. **API Endpoint Testing**
   - Test admin authentication and authorization
   - Test CRUD operations for personas
   - Test user management endpoints
   - Test analytics endpoints

3. **Frontend Testing**
   - Test admin route protection
   - Test persona management UI
   - Test user management UI
   - Test analytics dashboard

## Deployment Considerations

1. **Database Migration**
   - Create migration scripts for the new schema
   - Test migration on a staging environment

2. **Feature Flags**
   - Implement feature flags for gradual rollout
   - Allow enabling/disabling admin features

3. **Documentation**
   - Create admin user documentation
   - Document API endpoints for future reference
