"""
Component responsible for routing incoming requests to the appropriate agent/persona.
"""

import logging
from typing import Dict, Any, Optional

# Import AgentRegistry to check available agents if needed later
# from ..agents.registry import AgentRegistry

logger = logging.getLogger(__name__)


class RoutingComponent:
    """
    Determines the target agent for a given user request.
    """

    def __init__(self):
        """Initialize the RoutingComponent."""
        # self.agent_registry = AgentRegistry # Could be injected if needed
        logger.info("RoutingComponent initialized.")

    async def determine_target_agent(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Analyzes the message and context to determine the best agent.

        Args:
            message: The user's message text.
            context: Optional context dictionary.

        Returns:
            The ID/Name of the target agent (e.g., "ConciergeAgent", "MarketingAgent").
        """
        if context is None:
            context = {}

        # DEBUG LOGGING: Log routing decision process
        logger.info(f"=== ROUTING COMPONENT DEBUG ===")
        logger.info(f"Message: '{message}'")
        logger.info(f"Context: {context}")

        # Check if there's a current persona in context that should be maintained
        current_persona = context.get("current_persona") or context.get("persona_id")
        if current_persona:
            logger.info(f"Current persona in context: {current_persona}")
        else:
            logger.info("No current persona found in context")

        logger.debug(f"RoutingComponent determining target for message: {message[:100]}...")

        # --- Basic Routing Logic (Placeholder) ---
        # This logic should be expanded significantly based on requirements.
        # Could involve:
        # - NLP analysis of the message intent.
        # - Checking conversation history/state.
        # - Looking at attached data sources.
        # - Using rules defined in configuration.
        # - Calling a dedicated LLM for routing decisions.

        lower_message = message.lower()

        # Example: Route to marketing agent for specific keywords
        marketing_keywords = ["marketing", "campaign", "advertisement", "social media post", "seo"]
        if any(keyword in lower_message for keyword in marketing_keywords):
            logger.info("Routing to marketing agent based on keywords.")
            # Use consistent kebab-case format for agent IDs
            final_target = "composable-marketing-ai"
            logger.info(f"=== ROUTING DECISION: {final_target} ===")
            logger.info(f"=== END ROUTING COMPONENT DEBUG ===")
            return final_target

        # Example: Route to analysis agent for specific keywords
        analysis_keywords = ["analyze", "analysis", "data insights", "csv", "excel", "report"]
        if any(keyword in lower_message for keyword in analysis_keywords):
             logger.info("Routing to analysis agent based on keywords.")
             # Use consistent kebab-case format for agent IDs
             final_target = "composable-analysis-ai"
             logger.info(f"=== ROUTING DECISION: {final_target} ===")
             logger.info(f"=== END ROUTING COMPONENT DEBUG ===")
             return final_target

        # Default Route: concierge agent handles general queries, guidance, etc.
        logger.info("Defaulting route to concierge agent.")
        final_target = "concierge-agent"
        logger.info(f"=== ROUTING DECISION: {final_target} ===")
        logger.info(f"=== END ROUTING COMPONENT DEBUG ===")
        return final_target
