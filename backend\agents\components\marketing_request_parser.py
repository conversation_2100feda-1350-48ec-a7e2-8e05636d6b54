"""
Marketing request parser component for the Datagenius agent system.

This module provides a component for parsing marketing-related requests,
extracting key information, and determining the appropriate marketing task.
"""

import logging
import re
from typing import Dict, Any, List, Optional

from backend.schemas.agent_config_schemas import AgentProcessingContext # Added
from .base import AgentComponent

logger = logging.getLogger(__name__)


class MarketingRequestParserComponent(AgentComponent):
    """Component for parsing marketing-related requests."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the marketing request parser component.

        Args:
            config: Configuration dictionary for the component
        """
        # Define task types
        self.task_types = config.get("task_types", [
            "marketing_strategy",
            "campaign_strategy",
            "social_media_content",
            "seo_optimization",
            "post_composer"
        ])
        
        # Define keywords for task type detection
        self.task_keywords = config.get("task_keywords", {
            "marketing_strategy": ["strategy", "plan", "marketing plan", "overall", "comprehensive"],
            "campaign_strategy": ["campaign", "launch", "promotion", "event", "advertising"],
            "social_media_content": ["social media", "facebook", "instagram", "twitter", "linkedin", "tiktok", "content calendar"],
            "seo_optimization": ["seo", "search engine", "keyword", "ranking", "organic", "google"],
            "post_composer": ["post", "write", "create content", "caption", "article", "blog"]
        })
        
        # Define information extraction patterns
        self.extraction_patterns = config.get("extraction_patterns", {
            "brand_description": [
                r"brand(?:\s+is|\s*:\s*|\s+description\s*:?\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"company(?:\s+is|\s*:\s*|\s+description\s*:?\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"business(?:\s+is|\s*:\s*|\s+description\s*:?\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "target_audience": [
                r"target\s+audience(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"customers?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"demographic(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "products_services": [
                r"products?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"services?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"offerings?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "marketing_goals": [
                r"goals?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"objectives?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"aim(?:\s+to|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "tone": [
                r"tone(?:\s+should\s+be|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"voice(?:\s+should\s+be|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"style(?:\s+should\s+be|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ]
        })

    async def process(self, context: AgentProcessingContext) -> AgentProcessingContext:
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object.
        """
        # Check if this component should be skipped
        if context.metadata.get("skip_marketing_parser", False):
            logger.debug(f"Skipping marketing request parser component: {self.name}")
            return context

        # Get message from context
        message = context.message or ""
        if not message:
            logger.warning("No message found in context")
            # Optionally set a response if this component is expected to always have a message
            # context.response = "No message provided for marketing request parsing."
            return context

        try:
            # Determine the task type
            task_type = self._determine_task_type(message)
            logger.info(f"Determined task type: {task_type}")
            
            # Extract information from the message
            extracted_info = self._extract_information(message)
            logger.info(f"Extracted information: {', '.join(extracted_info.keys())}")
            
            # Update context metadata with task type and extracted information
            context.metadata["task_type"] = task_type
            context.metadata.update(extracted_info) # Merge extracted info into metadata
            
            # Set prompt template name based on task type in metadata
            context.metadata["prompt_template_name"] = task_type
            
            # Store the keys of the extracted info for reference
            context.metadata["extracted_info_keys"] = list(extracted_info.keys())
            
            return context
            
        except Exception as e:
            logger.error(f"Error parsing marketing request: {str(e)}", exc_info=True)
            context.add_error(self.name, f"marketing_parse_error: {str(e)}")
            # Optionally set a user-facing error response
            # context.response = "Sorry, I encountered an issue parsing your marketing request."
            return context

    def _determine_task_type(self, message: str) -> str:
        """
        Determine the marketing task type from the message.

        Args:
            message: User message

        Returns:
            Task type string
        """
        message_lower = message.lower()
        
        # Check for explicit task type mentions
        for task_type, keywords in self.task_keywords.items():
            for keyword in keywords:
                if keyword.lower() in message_lower:
                    return task_type
        
        # Default to marketing strategy if no specific task type is detected
        return "marketing_strategy"

    def _extract_information(self, message: str) -> Dict[str, str]:
        """
        Extract marketing-related information from the message.

        Args:
            message: User message

        Returns:
            Dictionary of extracted information
        """
        extracted_info = {}
        
        # Apply extraction patterns
        for info_type, patterns in self.extraction_patterns.items():
            for pattern in patterns:
                matches = re.search(pattern, message, re.IGNORECASE | re.DOTALL)
                if matches:
                    extracted_text = matches.group(1).strip()
                    if extracted_text:
                        extracted_info[info_type] = extracted_text
                        break
        
        # Set default tone if not extracted
        if "tone" not in extracted_info:
            extracted_info["tone"] = "Professional"
            
        return extracted_info
