import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Send } from 'lucide-react';

interface NotificationSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

const NotificationSettings = ({ settings, onUpdate }: NotificationSettingsProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    // Email Settings
    emailEnabled: true,
    emailProvider: 'smtp',
    smtpHost: '',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    smtpSecure: true,
    senderEmail: '',
    senderName: '',
    
    // Email Templates
    welcomeEmailEnabled: true,
    welcomeEmailSubject: 'Welcome to Datagenius',
    welcomeEmailTemplate: 'Welcome to Datagenius! We\'re excited to have you on board.',
    
    passwordResetEmailEnabled: true,
    passwordResetEmailSubject: 'Reset Your Password',
    passwordResetEmailTemplate: 'You requested a password reset. Click the link below to reset your password.',
    
    verificationEmailEnabled: true,
    verificationEmailSubject: 'Verify Your Email',
    verificationEmailTemplate: 'Please verify your email address by clicking the link below.',
    
    // In-App Notifications
    inAppNotificationsEnabled: true,
    notifyOnPersonaPurchase: true,
    notifyOnNewMessage: true,
    notifyOnSystemUpdates: true,
    
    // Push Notifications
    pushNotificationsEnabled: false,
    firebaseServerKey: '',
    
    // Admin Notifications
    adminNotificationsEnabled: true,
    adminEmailAddresses: '',
    notifyAdminOnNewUser: true,
    notifyAdminOnPurchase: true,
    notifyAdminOnError: true,
  });

  // Initialize form data when settings are loaded
  useEffect(() => {
    if (settings) {
      setFormData({
        // Email Settings
        emailEnabled: settings.emailEnabled ?? true,
        emailProvider: settings.emailProvider || 'smtp',
        smtpHost: settings.smtpHost || '',
        smtpPort: settings.smtpPort || 587,
        smtpUsername: settings.smtpUsername || '',
        smtpPassword: settings.smtpPassword || '',
        smtpSecure: settings.smtpSecure ?? true,
        senderEmail: settings.senderEmail || '',
        senderName: settings.senderName || '',
        
        // Email Templates
        welcomeEmailEnabled: settings.welcomeEmailEnabled ?? true,
        welcomeEmailSubject: settings.welcomeEmailSubject || 'Welcome to Datagenius',
        welcomeEmailTemplate: settings.welcomeEmailTemplate || 'Welcome to Datagenius! We\'re excited to have you on board.',
        
        passwordResetEmailEnabled: settings.passwordResetEmailEnabled ?? true,
        passwordResetEmailSubject: settings.passwordResetEmailSubject || 'Reset Your Password',
        passwordResetEmailTemplate: settings.passwordResetEmailTemplate || 'You requested a password reset. Click the link below to reset your password.',
        
        verificationEmailEnabled: settings.verificationEmailEnabled ?? true,
        verificationEmailSubject: settings.verificationEmailSubject || 'Verify Your Email',
        verificationEmailTemplate: settings.verificationEmailTemplate || 'Please verify your email address by clicking the link below.',
        
        // In-App Notifications
        inAppNotificationsEnabled: settings.inAppNotificationsEnabled ?? true,
        notifyOnPersonaPurchase: settings.notifyOnPersonaPurchase ?? true,
        notifyOnNewMessage: settings.notifyOnNewMessage ?? true,
        notifyOnSystemUpdates: settings.notifyOnSystemUpdates ?? true,
        
        // Push Notifications
        pushNotificationsEnabled: settings.pushNotificationsEnabled ?? false,
        firebaseServerKey: settings.firebaseServerKey || '',
        
        // Admin Notifications
        adminNotificationsEnabled: settings.adminNotificationsEnabled ?? true,
        adminEmailAddresses: settings.adminEmailAddresses || '',
        notifyAdminOnNewUser: settings.notifyAdminOnNewUser ?? true,
        notifyAdminOnPurchase: settings.notifyAdminOnPurchase ?? true,
        notifyAdminOnError: settings.notifyAdminOnError ?? true,
      });
    }
  }, [settings]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const updated = { ...prev, [name]: value };
      onUpdate(updated);
      return updated;
    });
  };

  // Handle number input change
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue)) {
      setFormData((prev) => {
        const updated = { ...prev, [name]: numValue };
        onUpdate(updated);
        return updated;
      });
    }
  };

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => {
      const updated = { ...prev, [name]: value };
      onUpdate(updated);
      return updated;
    });
  };

  // Handle checkbox change
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData((prev) => {
      const updated = { ...prev, [name]: checked };
      onUpdate(updated);
      return updated;
    });
  };

  // Test email configuration
  const testEmailConfig = () => {
    toast({
      title: 'Sending Test Email',
      description: 'Attempting to send a test email...',
    });

    // Simulate API call
    setTimeout(() => {
      const success = Math.random() > 0.3; // 70% chance of success for demo
      if (success) {
        toast({
          title: 'Test Email Sent',
          description: 'The test email was sent successfully.',
        });
      } else {
        toast({
          title: 'Email Error',
          description: 'Failed to send test email. Please check your SMTP settings.',
          variant: 'destructive',
        });
      }
    }, 2000);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Email Settings</h3>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="emailEnabled"
            checked={formData.emailEnabled}
            onCheckedChange={(checked) => handleCheckboxChange('emailEnabled', checked as boolean)}
          />
          <Label htmlFor="emailEnabled">Enable Email Notifications</Label>
        </div>
        
        {formData.emailEnabled && (
          <div className="space-y-4 mt-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="emailProvider" className="text-right">
                Email Provider
              </Label>
              <Select
                value={formData.emailProvider}
                onValueChange={(value) => handleSelectChange('emailProvider', value)}
              >
                <SelectTrigger id="emailProvider" className="col-span-3">
                  <SelectValue placeholder="Select email provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="smtp">SMTP</SelectItem>
                  <SelectItem value="sendgrid">SendGrid</SelectItem>
                  <SelectItem value="mailgun">Mailgun</SelectItem>
                  <SelectItem value="ses">Amazon SES</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {formData.emailProvider === 'smtp' && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="smtpHost" className="text-right">
                    SMTP Host
                  </Label>
                  <Input
                    id="smtpHost"
                    name="smtpHost"
                    value={formData.smtpHost}
                    onChange={handleInputChange}
                    className="col-span-3"
                    placeholder="smtp.example.com"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="smtpPort" className="text-right">
                    SMTP Port
                  </Label>
                  <Input
                    id="smtpPort"
                    name="smtpPort"
                    type="number"
                    value={formData.smtpPort}
                    onChange={handleNumberChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="smtpUsername" className="text-right">
                    SMTP Username
                  </Label>
                  <Input
                    id="smtpUsername"
                    name="smtpUsername"
                    value={formData.smtpUsername}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="smtpPassword" className="text-right">
                    SMTP Password
                  </Label>
                  <Input
                    id="smtpPassword"
                    name="smtpPassword"
                    type="password"
                    value={formData.smtpPassword}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="flex items-center space-x-2 ml-[25%]">
                  <Checkbox
                    id="smtpSecure"
                    checked={formData.smtpSecure}
                    onCheckedChange={(checked) => handleCheckboxChange('smtpSecure', checked as boolean)}
                  />
                  <Label htmlFor="smtpSecure">Use Secure Connection (TLS)</Label>
                </div>
              </>
            )}
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="senderEmail" className="text-right">
                Sender Email
              </Label>
              <Input
                id="senderEmail"
                name="senderEmail"
                type="email"
                value={formData.senderEmail}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="<EMAIL>"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="senderName" className="text-right">
                Sender Name
              </Label>
              <Input
                id="senderName"
                name="senderName"
                value={formData.senderName}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="Datagenius"
              />
            </div>
            
            <div className="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={testEmailConfig}
                disabled={!formData.smtpHost || !formData.senderEmail}
              >
                <Send className="h-4 w-4 mr-2" />
                Test Email Configuration
              </Button>
            </div>
          </div>
        )}
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Email Templates</h3>
        
        <div className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="welcomeEmailEnabled"
                checked={formData.welcomeEmailEnabled}
                onCheckedChange={(checked) => handleCheckboxChange('welcomeEmailEnabled', checked as boolean)}
              />
              <Label htmlFor="welcomeEmailEnabled">Welcome Email</Label>
            </div>
            
            {formData.welcomeEmailEnabled && (
              <div className="space-y-4 ml-6">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="welcomeEmailSubject" className="text-right">
                    Subject
                  </Label>
                  <Input
                    id="welcomeEmailSubject"
                    name="welcomeEmailSubject"
                    value={formData.welcomeEmailSubject}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="welcomeEmailTemplate" className="text-right pt-2">
                    Template
                  </Label>
                  <Textarea
                    id="welcomeEmailTemplate"
                    name="welcomeEmailTemplate"
                    value={formData.welcomeEmailTemplate}
                    onChange={handleInputChange}
                    className="col-span-3"
                    rows={4}
                  />
                </div>
              </div>
            )}
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="passwordResetEmailEnabled"
                checked={formData.passwordResetEmailEnabled}
                onCheckedChange={(checked) => handleCheckboxChange('passwordResetEmailEnabled', checked as boolean)}
              />
              <Label htmlFor="passwordResetEmailEnabled">Password Reset Email</Label>
            </div>
            
            {formData.passwordResetEmailEnabled && (
              <div className="space-y-4 ml-6">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="passwordResetEmailSubject" className="text-right">
                    Subject
                  </Label>
                  <Input
                    id="passwordResetEmailSubject"
                    name="passwordResetEmailSubject"
                    value={formData.passwordResetEmailSubject}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="passwordResetEmailTemplate" className="text-right pt-2">
                    Template
                  </Label>
                  <Textarea
                    id="passwordResetEmailTemplate"
                    name="passwordResetEmailTemplate"
                    value={formData.passwordResetEmailTemplate}
                    onChange={handleInputChange}
                    className="col-span-3"
                    rows={4}
                  />
                </div>
              </div>
            )}
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="verificationEmailEnabled"
                checked={formData.verificationEmailEnabled}
                onCheckedChange={(checked) => handleCheckboxChange('verificationEmailEnabled', checked as boolean)}
              />
              <Label htmlFor="verificationEmailEnabled">Email Verification</Label>
            </div>
            
            {formData.verificationEmailEnabled && (
              <div className="space-y-4 ml-6">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="verificationEmailSubject" className="text-right">
                    Subject
                  </Label>
                  <Input
                    id="verificationEmailSubject"
                    name="verificationEmailSubject"
                    value={formData.verificationEmailSubject}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="verificationEmailTemplate" className="text-right pt-2">
                    Template
                  </Label>
                  <Textarea
                    id="verificationEmailTemplate"
                    name="verificationEmailTemplate"
                    value={formData.verificationEmailTemplate}
                    onChange={handleInputChange}
                    className="col-span-3"
                    rows={4}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">In-App Notifications</h3>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="inAppNotificationsEnabled"
              checked={formData.inAppNotificationsEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('inAppNotificationsEnabled', checked as boolean)}
            />
            <Label htmlFor="inAppNotificationsEnabled">Enable In-App Notifications</Label>
          </div>
          
          {formData.inAppNotificationsEnabled && (
            <div className="space-y-2 ml-6">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="notifyOnPersonaPurchase"
                  checked={formData.notifyOnPersonaPurchase}
                  onCheckedChange={(checked) => handleCheckboxChange('notifyOnPersonaPurchase', checked as boolean)}
                />
                <Label htmlFor="notifyOnPersonaPurchase">Notify on Persona Purchase</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="notifyOnNewMessage"
                  checked={formData.notifyOnNewMessage}
                  onCheckedChange={(checked) => handleCheckboxChange('notifyOnNewMessage', checked as boolean)}
                />
                <Label htmlFor="notifyOnNewMessage">Notify on New Message</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="notifyOnSystemUpdates"
                  checked={formData.notifyOnSystemUpdates}
                  onCheckedChange={(checked) => handleCheckboxChange('notifyOnSystemUpdates', checked as boolean)}
                />
                <Label htmlFor="notifyOnSystemUpdates">Notify on System Updates</Label>
              </div>
            </div>
          )}
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Push Notifications</h3>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="pushNotificationsEnabled"
              checked={formData.pushNotificationsEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('pushNotificationsEnabled', checked as boolean)}
            />
            <Label htmlFor="pushNotificationsEnabled">Enable Push Notifications</Label>
          </div>
          
          {formData.pushNotificationsEnabled && (
            <div className="grid grid-cols-4 items-center gap-4 ml-6">
              <Label htmlFor="firebaseServerKey" className="text-right">
                Firebase Server Key
              </Label>
              <Input
                id="firebaseServerKey"
                name="firebaseServerKey"
                type="password"
                value={formData.firebaseServerKey}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
          )}
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Admin Notifications</h3>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="adminNotificationsEnabled"
              checked={formData.adminNotificationsEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('adminNotificationsEnabled', checked as boolean)}
            />
            <Label htmlFor="adminNotificationsEnabled">Enable Admin Notifications</Label>
          </div>
          
          {formData.adminNotificationsEnabled && (
            <div className="space-y-4 ml-6">
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="adminEmailAddresses" className="text-right pt-2">
                  Admin Emails
                </Label>
                <Textarea
                  id="adminEmailAddresses"
                  name="adminEmailAddresses"
                  value={formData.adminEmailAddresses}
                  onChange={handleInputChange}
                  className="col-span-3"
                  rows={2}
                  placeholder="Enter email addresses separated by commas"
                />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="notifyAdminOnNewUser"
                    checked={formData.notifyAdminOnNewUser}
                    onCheckedChange={(checked) => handleCheckboxChange('notifyAdminOnNewUser', checked as boolean)}
                  />
                  <Label htmlFor="notifyAdminOnNewUser">Notify on New User Registration</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="notifyAdminOnPurchase"
                    checked={formData.notifyAdminOnPurchase}
                    onCheckedChange={(checked) => handleCheckboxChange('notifyAdminOnPurchase', checked as boolean)}
                  />
                  <Label htmlFor="notifyAdminOnPurchase">Notify on New Purchase</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="notifyAdminOnError"
                    checked={formData.notifyAdminOnError}
                    onCheckedChange={(checked) => handleCheckboxChange('notifyAdminOnError', checked as boolean)}
                  />
                  <Label htmlFor="notifyAdminOnError">Notify on System Errors</Label>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;
