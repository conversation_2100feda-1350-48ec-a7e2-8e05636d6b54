"""
CRUD operations for WorkflowExecution and WorkflowTaskExecution models.
"""
import uuid
from datetime import datetime
from typing import List, Optional, Type
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy.exc import SQLAlchemyError
import logging

from backend.app.models.workflow import WorkflowExecution, WorkflowTaskExecution
from backend.schemas.db_schemas import (
    WorkflowCreate, WorkflowUpdate, WorkflowInDB,
    TaskCreate, TaskUpdate, TaskInDB,
    WorkflowStatusEnum, TaskStatusEnum
)

logger = logging.getLogger(__name__)

# === WorkflowExecution CRUD ===

def create_workflow_execution(db: Session, workflow_data: WorkflowCreate) -> WorkflowExecution:
    """Create a new workflow execution record."""
    try:
        db_workflow = WorkflowExecution(**workflow_data.model_dump())
        db.add(db_workflow)
        db.commit()
        db.refresh(db_workflow)
        logger.info(f"Created workflow execution {db_workflow.id}")
        return db_workflow
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Error creating workflow execution: {e}", exc_info=True)
        raise

def get_workflow_execution(db: Session, workflow_id: uuid.UUID) -> Optional[WorkflowExecution]:
    """Get a workflow execution by its ID."""
    try:
        return db.query(WorkflowExecution).filter(WorkflowExecution.id == workflow_id).first()
    except SQLAlchemyError as e:
        logger.error(f"Error fetching workflow execution {workflow_id}: {e}", exc_info=True)
        return None

def get_workflow_execution_with_tasks(db: Session, workflow_id: uuid.UUID) -> Optional[WorkflowExecution]:
    """Get a workflow execution by ID, with its tasks eagerly loaded."""
    try:
        return db.query(WorkflowExecution).options(
            selectinload(WorkflowExecution.tasks)
        ).filter(WorkflowExecution.id == workflow_id).first()
    except SQLAlchemyError as e:
        logger.error(f"Error fetching workflow execution {workflow_id} with tasks: {e}", exc_info=True)
        return None

def update_workflow_execution(db: Session, workflow_id: uuid.UUID, workflow_update_data: WorkflowUpdate) -> Optional[WorkflowExecution]:
    """Update a workflow execution."""
    try:
        db_workflow = get_workflow_execution(db, workflow_id)
        if db_workflow:
            update_data = workflow_update_data.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_workflow, key, value)
            db.commit()
            db.refresh(db_workflow)
            logger.info(f"Updated workflow execution {workflow_id}")
            return db_workflow
        return None
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Error updating workflow execution {workflow_id}: {e}", exc_info=True)
        raise

def delete_workflow_execution(db: Session, workflow_id: uuid.UUID) -> bool:
    """Delete a workflow execution. Associated tasks will also be deleted due to cascade."""
    try:
        db_workflow = get_workflow_execution(db, workflow_id)
        if db_workflow:
            db.delete(db_workflow)
            db.commit()
            logger.info(f"Deleted workflow execution {workflow_id}")
            return True
        return False
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Error deleting workflow execution {workflow_id}: {e}", exc_info=True)
        raise

def get_old_workflows_for_cleanup(db: Session, cutoff_time: datetime) -> List[WorkflowExecution]:
    """Get workflows older than cutoff_time that are in a completed, failed, or cancelled state."""
    try:
        return db.query(WorkflowExecution).filter(
            WorkflowExecution.status.in_([WorkflowStatusEnum.COMPLETED, WorkflowStatusEnum.FAILED, WorkflowStatusEnum.CANCELLED]),
            WorkflowExecution.end_time < cutoff_time
        ).all()
    except SQLAlchemyError as e:
        logger.error(f"Error fetching old workflows for cleanup: {e}", exc_info=True)
        return []

# === WorkflowTaskExecution CRUD ===

def create_workflow_task_execution(db: Session, task_data: TaskCreate, workflow_id: uuid.UUID) -> WorkflowTaskExecution:
    """Create a new workflow task execution record."""
    try:
        # Ensure workflow_id from TaskCreate matches the one provided, or just use workflow_id param
        task_dict = task_data.model_dump()
        task_dict['workflow_id'] = workflow_id # Ensure workflow_id is set correctly
        db_task = WorkflowTaskExecution(**task_dict)
        db.add(db_task)
        db.commit()
        db.refresh(db_task)
        logger.info(f"Created task execution {db_task.id} for workflow {workflow_id}")
        return db_task
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Error creating task execution for workflow {workflow_id}: {e}", exc_info=True)
        raise

def get_workflow_task_execution(db: Session, task_id: uuid.UUID) -> Optional[WorkflowTaskExecution]:
    """Get a workflow task execution by its ID."""
    try:
        return db.query(WorkflowTaskExecution).filter(WorkflowTaskExecution.id == task_id).first()
    except SQLAlchemyError as e:
        logger.error(f"Error fetching task execution {task_id}: {e}", exc_info=True)
        return None

def update_workflow_task_execution(db: Session, task_id: uuid.UUID, task_update_data: TaskUpdate) -> Optional[WorkflowTaskExecution]:
    """Update a workflow task execution."""
    try:
        db_task = get_workflow_task_execution(db, task_id)
        if db_task:
            update_data = task_update_data.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_task, key, value)
            db.commit()
            db.refresh(db_task)
            logger.info(f"Updated task execution {task_id}")
            return db_task
        return None
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Error updating task execution {task_id}: {e}", exc_info=True)
        raise

def get_tasks_for_workflow(db: Session, workflow_id: uuid.UUID) -> List[WorkflowTaskExecution]:
    """Get all tasks for a given workflow."""
    try:
        return db.query(WorkflowTaskExecution).filter(WorkflowTaskExecution.workflow_id == workflow_id).all()
    except SQLAlchemyError as e:
        logger.error(f"Error fetching tasks for workflow {workflow_id}: {e}", exc_info=True)
        return []

def count_active_tasks_for_workflow(db: Session, workflow_id: uuid.UUID) -> int:
    """Count active (pending, running, waiting) tasks for a workflow."""
    try:
        return db.query(WorkflowTaskExecution).filter(
            WorkflowTaskExecution.workflow_id == workflow_id,
            WorkflowTaskExecution.status.in_([TaskStatusEnum.PENDING, TaskStatusEnum.RUNNING, TaskStatusEnum.WAITING])
        ).count()
    except SQLAlchemyError as e:
        logger.error(f"Error counting active tasks for workflow {workflow_id}: {e}", exc_info=True)
        return -1 # Indicate error

def count_terminally_failed_tasks_for_workflow(db: Session, workflow_id: uuid.UUID) -> int:
    """Count terminally failed tasks for a workflow."""
    try:
        return db.query(WorkflowTaskExecution).filter(
            WorkflowTaskExecution.workflow_id == workflow_id,
            WorkflowTaskExecution.status == TaskStatusEnum.FAILED,
            WorkflowTaskExecution.retry_count >= WorkflowTaskExecution.max_retries
        ).count()
    except SQLAlchemyError as e:
        logger.error(f"Error counting terminally failed tasks for workflow {workflow_id}: {e}", exc_info=True)
        return -1 # Indicate error

def get_tasks_by_dependency(db: Session, workflow_id: uuid.UUID, dependency_id_str: str) -> List[WorkflowTaskExecution]:
    """
    Get tasks within a workflow that have a specific dependency.
    Note: This is a basic implementation assuming dependencies are stored as a list of strings.
    For performance on large datasets, consider a more optimized approach if using JSONB (e.g., PostgreSQL specific operators).
    """
    try:
        # This is a simple string containment check, might not be performant for large JSON arrays.
        # In PostgreSQL, you could use `WorkflowTaskExecution.dependencies.contains([dependency_id_str])` if dependencies is a JSONB array.
        # For generic JSON, a LIKE query is often used but can be slow.
        # tasks = db.query(WorkflowTaskExecution).filter(
        #     WorkflowTaskExecution.workflow_id == workflow_id,
        #     WorkflowTaskExecution.dependencies.astext.like(f'%"{dependency_id_str}"%') # Example for generic JSON
        # ).all()

        # A more robust Python-side filtering if the above is not suitable/performant:
        all_workflow_tasks = get_tasks_for_workflow(db, workflow_id)
        dependent_tasks = [
            task for task in all_workflow_tasks
            if task.dependencies and dependency_id_str in task.dependencies
        ]
        return dependent_tasks

    except SQLAlchemyError as e:
        logger.error(f"Error fetching tasks by dependency for workflow {workflow_id}: {e}", exc_info=True)
        return []
