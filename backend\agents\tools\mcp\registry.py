"""
MCP tool registry for the Datagenius backend.

This module provides a registry for MCP-compatible tools, allowing tools to be
registered, retrieved, and instantiated by name.
"""

import logging
from typing import Dict, Type, Optional, List, Any
from .base import MCPTool

logger = logging.getLogger(__name__)


class MCPToolRegistry:
    """Registry for MCP-compatible agent tools."""

    _tools: Dict[str, Type[MCPTool]] = {}

    @classmethod
    def register(cls, name: str, tool_class: Type[MCPTool]) -> None:
        """
        Register a tool class.

        Args:
            name: Name of the tool
            tool_class: Tool class to register
        """
        cls._tools[name] = tool_class
        logger.info(f"Registered MCP tool class {tool_class.__name__} with name '{name}'")

    @classmethod
    def get_tool_class(cls, name: str) -> Optional[Type[MCPTool]]:
        """
        Get a tool class by name.

        Args:
            name: Name of the tool

        Returns:
            Tool class if found, None otherwise
        """
        tool_class = cls._tools.get(name)
        if tool_class is None:
            logger.warning(f"No MCP tool class found for name '{name}'")
        return tool_class

    @classmethod
    def list_registered_tools(cls) -> List[str]:
        """
        List all registered tool names.

        Returns:
            List of registered tool names
        """
        return list(cls._tools.keys())

    @classmethod
    async def create_tool_instance(cls, name: str, config: Dict[str, Any] = None) -> Optional[MCPTool]:
        """
        Create an instance of a tool by name and initialize it with configuration.

        Args:
            name: Name of the tool
            config: Configuration dictionary for the tool

        Returns:
            Initialized tool instance if found, None otherwise
        """
        tool_class = cls.get_tool_class(name)
        if tool_class is None:
            return None

        if config is None:
            config = {}

        try:
            tool = tool_class()
            await tool.initialize(config)
            return tool
        except Exception as e:
            logger.error(f"Error initializing MCP tool '{name}': {e}")
            return None
