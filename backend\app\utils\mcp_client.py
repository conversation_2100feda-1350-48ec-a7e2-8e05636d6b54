"""
MCP client for the Datagenius backend.

This module provides utilities for connecting to and querying MCP servers.
"""

import logging
import requests
from typing import List, Dict, Any, Optional

# Configure logging
logger = logging.getLogger(__name__)

class McpClient:
    """Client for interacting with MCP servers."""

    def __init__(self, endpoint: str, api_key: Optional[str] = None, namespace: str = "default"):
        """Initialize the MCP client.

        Args:
            endpoint: The MCP server endpoint
            api_key: Optional API key for authentication
            namespace: The namespace to use
        """
        self.endpoint = endpoint.rstrip("/")
        self.api_key = api_key
        self.namespace = namespace
        self.headers = {}
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"

    def get_collections(self) -> List[Dict[str, Any]]:
        """Get a list of collections from the MCP server.

        Returns:
            List of collection objects
        """
        url = f"{self.endpoint}/collections?namespace={self.namespace}"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()["collections"]

    def get_collection(self, collection_id: str) -> Dict[str, Any]:
        """Get a collection by ID.

        Args:
            collection_id: The ID of the collection to get

        Returns:
            Collection object
        """
        url = f"{self.endpoint}/collections/{collection_id}?namespace={self.namespace}"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def query_collection(self, collection_id: str, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Query a collection.

        Args:
            collection_id: The ID of the collection to query
            query: The query string
            limit: Maximum number of results to return

        Returns:
            List of document objects
        """
        url = f"{self.endpoint}/collections/{collection_id}/query?namespace={self.namespace}"
        data = {
            "query": query,
            "limit": limit
        }
        response = requests.post(url, headers=self.headers, json=data)
        response.raise_for_status()
        return response.json()["documents"]

    def get_document(self, collection_id: str, document_id: str) -> Dict[str, Any]:
        """Get a document by ID.

        Args:
            collection_id: The ID of the collection containing the document
            document_id: The ID of the document to get

        Returns:
            Document object
        """
        url = f"{self.endpoint}/collections/{collection_id}/documents/{document_id}?namespace={self.namespace}"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()

def test_mcp_connection(endpoint: str, api_key: Optional[str] = None, namespace: str = "default") -> bool:
    """Test the connection to an MCP server.

    Args:
        endpoint: The MCP server endpoint
        api_key: Optional API key for authentication
        namespace: The namespace to use

    Returns:
        True if the connection is successful, False otherwise

    Raises:
        Exception: If the connection fails
    """
    try:
        client = McpClient(endpoint, api_key, namespace)
        client.get_collections()
        return True
    except Exception as e:
        logger.error(f"MCP connection test failed: {str(e)}")
        raise
