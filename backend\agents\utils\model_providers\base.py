"""
Base model provider interface for the Datagenius backend.

This module provides the base interface for model providers, which are responsible
for initializing and managing AI models from different providers.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union

# Import LangChain models with compatibility for different versions
try:
    # Try newer LangChain structure
    from langchain_core.language_models.base import BaseLanguageModel
    from langchain_core.language_models.chat_models import BaseChatModel
except ImportError:
    try:
        # Try older LangChain structure
        from langchain.schema.language_model import BaseLanguageModel
        from langchain.schema.chat_model import BaseChatModel
    except ImportError:
        # Fallback to even older structure
        from langchain.base_language import BaseLanguageModel
        from langchain.chat_models.base import BaseChatModel

from .exceptions import ModelProviderError, ModelInitializationError, ModelNotFoundError

# Configure logging
logger = logging.getLogger(__name__)


class ModelProvider(ABC):
    """Interface for model providers."""

    @property
    @abstractmethod
    def provider_id(self) -> str:
        """Get the provider ID."""
        pass

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Get the provider name."""
        pass

    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the provider with configuration.

        Args:
            config: Configuration dictionary for the provider
        """
        pass

    @abstractmethod
    async def get_model(self, model_id: str, config: Optional[Dict[str, Any]] = None) -> Union[BaseLanguageModel, BaseChatModel]:
        """
        Get a model instance by ID.

        Args:
            model_id: ID of the model to get
            config: Optional configuration overrides for the model

        Returns:
            Initialized model instance
        """
        pass

    @abstractmethod
    async def list_models(self) -> List[Dict[str, Any]]:
        """
        List available models from this provider.

        Returns:
            List of model metadata dictionaries
        """
        pass

    @abstractmethod
    async def is_available(self) -> bool:
        """
        Check if the provider is available.

        Returns:
            True if the provider is available, False otherwise
        """
        pass


class BaseModelProvider(ModelProvider):
    """Base implementation of the model provider interface."""

    def __init__(self):
        """Initialize the base model provider."""
        self._initialized = False
        self._api_key = None
        self._endpoint = None
        self._models_cache = None
        self._default_model_id = None
        self._config = {}

    @property
    def provider_id(self) -> str:
        """Get the provider ID."""
        raise NotImplementedError("Subclasses must implement provider_id")

    @property
    def provider_name(self) -> str:
        """Get the provider name."""
        raise NotImplementedError("Subclasses must implement provider_name")

    @property
    def is_initialized(self) -> bool:
        """Check if the provider is initialized."""
        return self._initialized

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the provider with configuration.

        Args:
            config: Configuration dictionary for the provider
        """
        self._config = config
        self._api_key = config.get("api_key")
        self._endpoint = config.get("endpoint")
        self._default_model_id = config.get("default_model")
        self._initialized = True
        logger.info(f"Initialized {self.provider_name} provider")

    async def get_model(self, model_id: Optional[str] = None, config: Optional[Dict[str, Any]] = None) -> Union[BaseLanguageModel, BaseChatModel]:
        """
        Get a model instance by ID.

        Args:
            model_id: ID of the model to get (uses default if None)
            config: Optional configuration overrides for the model

        Returns:
            Initialized model instance

        Raises:
            ModelProviderError: If the provider is not initialized
            ModelNotFoundError: If the model is not found
            ModelInitializationError: If there's an error initializing the model
        """
        if not self.is_initialized:
            raise ModelProviderError(f"Provider {self.provider_id} is not initialized")

        # Use default model if none specified
        if model_id is None:
            model_id = self._default_model_id
            if model_id is None:
                raise ModelNotFoundError(f"No model ID specified and no default model configured for {self.provider_id}")

        # Merge configuration
        model_config = self._config.copy()
        if config:
            model_config.update(config)

        try:
            return await self._initialize_model(model_id, model_config)
        except Exception as e:
            logger.error(f"Error initializing model {model_id} from {self.provider_id}: {str(e)}", exc_info=True)
            raise ModelInitializationError(f"Error initializing model {model_id}: {str(e)}")

    async def list_models(self) -> List[Dict[str, Any]]:
        """
        List available models from this provider.

        Returns:
            List of model metadata dictionaries

        Raises:
            ModelProviderError: If the provider is not initialized
        """
        if not self.is_initialized:
            raise ModelProviderError(f"Provider {self.provider_id} is not initialized")

        # Use cached models if available
        if self._models_cache is not None:
            return self._models_cache

        try:
            models = await self._fetch_models()
            self._models_cache = models
            return models
        except Exception as e:
            logger.error(f"Error fetching models from {self.provider_id}: {str(e)}", exc_info=True)
            return []

    async def is_available(self) -> bool:
        """
        Check if the provider is available.

        Returns:
            True if the provider is available, False otherwise
        """
        if not self.is_initialized:
            return False

        try:
            # Try to list models as a basic availability check
            models = await self.list_models()
            return len(models) > 0
        except Exception:
            return False

    async def _initialize_model(self, model_id: str, config: Dict[str, Any]) -> Union[BaseLanguageModel, BaseChatModel]:
        """
        Initialize a model instance.

        Args:
            model_id: ID of the model to initialize
            config: Configuration for the model

        Returns:
            Initialized model instance

        Raises:
            NotImplementedError: If not implemented by subclass
        """
        raise NotImplementedError("Subclasses must implement _initialize_model")

    async def _fetch_models(self) -> List[Dict[str, Any]]:
        """
        Fetch available models from the provider.

        Returns:
            List of model metadata dictionaries

        Raises:
            NotImplementedError: If not implemented by subclass
        """
        raise NotImplementedError("Subclasses must implement _fetch_models")
