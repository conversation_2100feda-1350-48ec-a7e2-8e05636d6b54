"""
LLM processor component for the Datagenius agent system.

This module provides a component for processing messages using LLMs.
"""

import logging
import os
from typing import Dict, Any, List, Optional

from .base import AgentComponent
from ..utils.prompt_template import PromptTemplate

logger = logging.getLogger(__name__)

# Import LLM providers conditionally to avoid hard dependencies
try:
    from langchain_groq import ChatGroq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False
    logger.warning("Groq integration not available. Install with 'pip install langchain-groq'")

try:
    from langchain_openai import ChatOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI integration not available. Install with 'pip install langchain-openai'")


class LLMProcessorComponent(AgentComponent):
    """Component for processing messages using LLMs."""

    def __init__(self):
        """Initialize the LLM processor component."""
        super().__init__()
        self.llm = None
        self.prompt_templates = {}
        self.default_prompt_name = "default"

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        # Initialize LLM based on provider
        provider = config.get("provider", "openai").lower()

        if provider == "groq":
            if not GROQ_AVAILABLE:
                raise ImportError("Groq integration not available. Install with 'pip install langchain-groq'")

            api_key = config.get("api_key", os.getenv("GROQ_API_KEY", ""))
            model_name = config.get("model", "llama3-70b-8192")
            temperature = config.get("temperature", 0.7)

            try:
                # Try with the newer parameter name (groq_api_key)
                self.llm = ChatGroq(
                    temperature=temperature,
                    model_name=model_name,
                    groq_api_key=api_key
                )
            except TypeError:
                # Fall back to the older parameter name (api_key)
                self.llm = ChatGroq(
                    temperature=temperature,
                    model_name=model_name,
                    api_key=api_key
                )

            logger.info(f"Initialized Groq LLM with model {model_name}")

        elif provider == "openai":
            if not OPENAI_AVAILABLE:
                raise ImportError("OpenAI integration not available. Install with 'pip install langchain-openai'")

            self.llm = ChatOpenAI(
                temperature=config.get("temperature", 0.7),
                model_name=config.get("model", "gpt-3.5-turbo"),
                api_key=config.get("api_key", os.getenv("OPENAI_API_KEY", ""))
            )
            logger.info(f"Initialized OpenAI LLM with model {config.get('model', 'gpt-3.5-turbo')}")

        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")

        # Load prompt templates
        if "prompt_templates" in config:
            for name, template in config["prompt_templates"].items():
                self.prompt_templates[name] = PromptTemplate(template)
                logger.debug(f"Loaded prompt template '{name}'")

        # Set default prompt name
        self.default_prompt_name = config.get("default_prompt", "default")

        # Ensure we have at least one prompt template
        if not self.prompt_templates and "default_prompt_template" in config:
            self.prompt_templates["default"] = PromptTemplate(config["default_prompt_template"])
            logger.debug("Loaded default prompt template")

    def get_prompt(self, name: str = None, **kwargs) -> str:
        """
        Get a formatted prompt by name.

        Args:
            name: Name of the prompt template (uses default if None)
            **kwargs: Variables to substitute in the template

        Returns:
            Formatted prompt
        """
        if name is None:
            name = self.default_prompt_name

        if name not in self.prompt_templates:
            logger.warning(f"Prompt template '{name}' not found, using default")
            name = "default"
            if name not in self.prompt_templates:
                logger.error(f"Default prompt template not found")
                return f"Error: No prompt template found for '{name}'"

        return self.prompt_templates[name].format(**kwargs)

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        if not self.llm:
            logger.error("LLM not initialized")
            context["response"] = "I'm sorry, but my language model is not properly configured."
            context["metadata"]["error"] = "llm_not_initialized"
            return context

        # Get the user message
        user_message = context.get("message", "")
        if not user_message:
            logger.warning("Empty user message")
            context["response"] = "I didn't receive any message to process."
            return context

        # Check if we should use a specific prompt template
        prompt_name = context.get("prompt_name", self.default_prompt_name)

        # Prepare prompt variables
        prompt_vars = {
            "message": user_message,
            "user_id": context.get("user_id", "unknown"),
            "conversation_id": context.get("conversation_id", "unknown"),
        }

        # Add any additional context variables
        if "context" in context and isinstance(context["context"], dict):
            prompt_vars.update(context["context"])

        # Format the prompt
        try:
            prompt = self.get_prompt(prompt_name, **prompt_vars)
        except Exception as e:
            logger.error(f"Error formatting prompt: {e}")
            context["response"] = "I encountered an error while processing your request."
            context["metadata"]["error"] = f"prompt_format_error: {str(e)}"
            return context

        # Call the LLM
        try:
            logger.debug(f"Calling LLM with prompt: {prompt[:100]}...")
            response = self.llm.invoke(prompt)
            response_text = response.content

            # Update the context with the response
            context["response"] = response_text
            context["metadata"]["llm_provider"] = self.config.get("provider", "unknown")
            context["metadata"]["llm_model"] = self.config.get("model", "unknown")

            logger.debug(f"LLM response: {response_text[:100]}...")
            return context

        except Exception as e:
            logger.error(f"Error calling LLM: {e}")
            context["response"] = "I encountered an error while generating a response."
            context["metadata"]["error"] = f"llm_error: {str(e)}"
            return context

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.

        Returns:
            List of capability strings
        """
        return ["llm_processing"] + self.config.get("capabilities", [])
