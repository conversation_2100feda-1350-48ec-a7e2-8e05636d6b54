
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Activity, Table2 } from "lucide-react";

interface VisualizationType {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
}

interface VisualizationSelectorProps {
  selectedVisualizations: string[];
  setSelectedVisualizations: (visualizations: string[]) => void;
}

export function VisualizationSelector({
  selectedVisualizations,
  setSelectedVisualizations,
}: VisualizationSelectorProps) {
  const visualizationTypes: VisualizationType[] = [
    {
      id: "line",
      name: "Line Chart",
      description: "Visualize trends over time",
      icon: LineChart,
    },
    {
      id: "bar",
      name: "Bar Chart",
      description: "Compare values across categories",
      icon: Bar<PERSON><PERSON>,
    },
    {
      id: "pie",
      name: "Pie Chart",
      description: "Show proportion between categories",
      icon: PieChart,
    },
    {
      id: "scatter",
      name: "Scatter Plot",
      description: "Display correlation between variables",
      icon: Scatter<PERSON><PERSON>,
    },
    {
      id: "kpi",
      name: "KPI Cards",
      description: "Key performance indicators overview",
      icon: Activity,
    },
    {
      id: "table",
      name: "Data Table",
      description: "Show detailed data in tabular format",
      icon: Table2,
    },
  ];

  const toggleVisualization = (id: string) => {
    if (selectedVisualizations.includes(id)) {
      setSelectedVisualizations(selectedVisualizations.filter((v) => v !== id));
    } else {
      setSelectedVisualizations([...selectedVisualizations, id]);
    }
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
      {visualizationTypes.map((visualization) => (
        <div
          key={visualization.id}
          className={`
            p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md
            ${
              selectedVisualizations.includes(visualization.id)
                ? "border-primary bg-primary/5"
                : "border-border hover:border-primary/50"
            }
          `}
          onClick={() => toggleVisualization(visualization.id)}
        >
          <div className="flex items-start gap-4">
            <visualization.icon className="h-8 w-8 text-primary shrink-0" />
            <div>
              <h3 className="font-medium">{visualization.name}</h3>
              <p className="text-xs text-muted-foreground">
                {visualization.description}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
