"""
File API endpoints for the Datagenius backend.

This module provides API endpoints for file management.
"""

import logging
import uuid
import os
import pandas as pd
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from sqlalchemy.orm import Session

from ..models.file import FileResponse, FileListResponse
from ..models.auth import User
from ..database import get_db, create_file, get_file, get_user_files, delete_file
from ..auth import get_current_active_user
from .. import config

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/files", tags=["Files"])


@router.post("", response_model=FileResponse)
async def upload_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Upload a file.

    Args:
        file: The file to upload
        db: Database session
        current_user: Current authenticated user

    Returns:
        File information
    """
    logger.info(f"User {current_user.id} uploading file {file.filename}")

    # Check file size
    file_size = 0
    content = await file.read()
    file_size = len(content)
    await file.seek(0)

    if file_size > config.MAX_UPLOAD_SIZE:
        logger.warning(f"File {file.filename} too large: {file_size} bytes")
        raise HTTPException(status_code=400, detail="File too large")

    # Create upload directory if it doesn't exist
    os.makedirs(config.UPLOAD_DIR, exist_ok=True)

    # Generate a unique filename
    file_uuid = str(uuid.uuid4())
    file_extension = file.filename.split(".")[-1]
    file_path = os.path.join(config.UPLOAD_DIR, f"{file_uuid}.{file_extension}")

    # Save the file
    with open(file_path, "wb") as f:
        f.write(content)

    # Extract metadata for CSV and Excel files
    num_rows = None
    columns = None
    if file_extension.lower() in ["csv", "xlsx", "xls"]:
        try:
            if file_extension.lower() == "csv":
                df = pd.read_csv(file_path)
            else:
                df = pd.read_excel(file_path)
            
            num_rows = len(df)
            columns = df.columns.tolist()
        except Exception as e:
            logger.error(f"Error extracting metadata from file: {str(e)}", exc_info=True)

    # Create file record in database
    db_file = create_file(
        db,
        user_id=current_user.id,
        filename=file.filename,
        file_path=file_path,
        file_size=file_size,
        num_rows=num_rows,
        columns=columns
    )

    return db_file


@router.get("", response_model=FileListResponse)
async def list_files(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    List files.

    Args:
        skip: Number of files to skip
        limit: Maximum number of files to return
        db: Database session
        current_user: Current authenticated user

    Returns:
        List of files
    """
    logger.info(f"User {current_user.id} listing files")

    files = get_user_files(db, current_user.id, skip, limit)
    return {"files": files}


@router.get("/{file_id}", response_model=FileResponse)
async def get_file_info(
    file_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get file information.

    Args:
        file_id: ID of the file
        db: Database session
        current_user: Current authenticated user

    Returns:
        File information
    """
    logger.info(f"User {current_user.id} getting file {file_id}")

    file = get_file(db, file_id)
    if not file:
        logger.warning(f"File {file_id} not found")
        raise HTTPException(status_code=404, detail="File not found")

    if file.user_id != current_user.id:
        logger.warning(f"User {current_user.id} attempted to access file {file_id} owned by user {file.user_id}")
        raise HTTPException(status_code=403, detail="Not authorized to access this file")

    return file


@router.delete("/{file_id}")
async def delete_file_endpoint(
    file_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a file.

    Args:
        file_id: ID of the file
        db: Database session
        current_user: Current authenticated user

    Returns:
        Success message
    """
    logger.info(f"User {current_user.id} deleting file {file_id}")

    file = get_file(db, file_id)
    if not file:
        logger.warning(f"File {file_id} not found")
        raise HTTPException(status_code=404, detail="File not found")

    if file.user_id != current_user.id:
        logger.warning(f"User {current_user.id} attempted to delete file {file_id} owned by user {file.user_id}")
        raise HTTPException(status_code=403, detail="Not authorized to delete this file")

    # Delete the file from disk
    try:
        os.remove(file.file_path)
    except Exception as e:
        logger.error(f"Error deleting file from disk: {str(e)}", exc_info=True)

    # Delete the file from the database
    delete_file(db, file_id)

    return {"message": "File deleted successfully"}
