#!/usr/bin/env python
"""
Script to check if Qdrant is running and accessible.

This script checks if Qdrant is running and accessible, and provides
information about the connection.
"""

import os
import sys
import requests
import logging
import argparse
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Check if Qdrant is running and accessible")
    parser.add_argument("--host", default=os.getenv("QDRANT_HOST", "localhost"), help="Qdrant host")
    parser.add_argument("--port", type=int, default=int(os.getenv("QDRANT_PORT", "6333")), help="Qdrant port")
    return parser.parse_args()

def check_qdrant(host, port):
    """
    Check if Qdrant is running and accessible.
    
    Args:
        host: Qdrant host
        port: Qdrant port
        
    Returns:
        True if Qdrant is running, False otherwise
    """
    url = f"http://{host}:{port}/health"
    logger.info(f"Checking Qdrant health at {url}")
    
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            logger.info(f"Qdrant is running at {host}:{port}")
            logger.info(f"Response: {response.json()}")
            return True
        else:
            logger.error(f"Qdrant health check failed with status code {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
    except requests.RequestException as e:
        logger.error(f"Failed to connect to Qdrant at {host}:{port}: {str(e)}")
        return False

def get_collections(host, port):
    """
    Get list of collections from Qdrant.
    
    Args:
        host: Qdrant host
        port: Qdrant port
    """
    url = f"http://{host}:{port}/collections"
    logger.info(f"Getting collections from {url}")
    
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            collections = response.json()
            logger.info(f"Collections: {collections}")
        else:
            logger.error(f"Failed to get collections with status code {response.status_code}")
            logger.error(f"Response: {response.text}")
    except requests.RequestException as e:
        logger.error(f"Failed to get collections: {str(e)}")

def main():
    """Main entry point."""
    args = parse_args()
    
    # Check if Qdrant is running
    if check_qdrant(args.host, args.port):
        # Get collections
        get_collections(args.host, args.port)
    else:
        logger.error("Qdrant is not running or not accessible")
        sys.exit(1)
        
    logger.info("Qdrant check completed successfully")

if __name__ == "__main__":
    main()
