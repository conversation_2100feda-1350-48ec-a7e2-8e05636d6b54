"""
Test script for the model provider system.

This script tests the model provider system by initializing providers and models.
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any, List

# Add the parent directory to the path so we can import from the backend package
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.utils.model_providers.utils import (
    get_model,
    list_available_providers,
    list_available_models
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_providers():
    """Test the model provider system by listing available providers."""
    logger.info("Testing model provider system...")
    
    # List available providers
    providers = await list_available_providers()
    logger.info(f"Available providers: {providers}")
    
    return providers


async def test_models():
    """Test the model provider system by listing available models."""
    logger.info("Testing model listing...")
    
    # List available models
    models_by_provider = await list_available_models()
    
    for provider_id, models in models_by_provider.items():
        logger.info(f"Models for provider '{provider_id}':")
        for model in models:
            logger.info(f"  - {model['id']}: {model['name']} ({model['context_length']} tokens)")
    
    return models_by_provider


async def test_model_initialization():
    """Test the model provider system by initializing a model."""
    logger.info("Testing model initialization...")
    
    try:
        # Try to get a model (will use the default provider)
        model = await get_model()
        logger.info(f"Successfully initialized model: {model}")
        
        # Try a simple completion
        if hasattr(model, "invoke"):
            result = await model.ainvoke("Hello, world!")
            logger.info(f"Model response: {result}")
        
        return True
    except Exception as e:
        logger.error(f"Error initializing model: {str(e)}", exc_info=True)
        return False


async def main():
    """Run all tests."""
    logger.info("Starting model provider system tests...")
    
    # Test providers
    providers = await test_providers()
    
    # Test models
    models = await test_models()
    
    # Test model initialization
    success = await test_model_initialization()
    
    logger.info(f"Tests completed. Model initialization {'succeeded' if success else 'failed'}.")


if __name__ == "__main__":
    asyncio.run(main())
