"""
PandasAI v3 wrapper module.

This module provides a wrapper for the PandasAI v3 Agent class, making it easier
to use PandasAI v3 in the Datagenius backend.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, Optional, Union, List

import pandasai as pai
from pandasai import Agent
from .llm_providers import LLMProviderFactory

logger = logging.getLogger(__name__)

class PandasAIWrapper:
    """Wrapper for PandasAI v3 Agent class."""

    def __init__(self):
        """Initialize the PandasAI wrapper."""
        self.api_key = None
        self.agent = None
        self.df = None
        self.config = {}

    def initialize(self, api_key: str, provider: str = "openai"):
        """Initialize PandasAI with API key."""
        self.api_key = api_key
        self.provider = provider
        # We'll use the API key when creating the agent
        logger.info(f"Initialized PandasAI with {provider} API key")

    def load_dataframe(self, file_path: str) -> bool:
        """Load a dataframe from a file path."""
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                logger.error(f"File does not exist: {file_path}")

                # Try to find the file in common directories
                search_dirs = ["data", "uploads", "backend/data", "backend/uploads", "temp_uploads", "backend/temp_uploads", "."]
                for directory in search_dirs:
                    base_name = os.path.basename(file_path)
                    potential_path = os.path.join(directory, base_name)
                    if os.path.exists(potential_path):
                        logger.info(f"Found file at alternative path: {potential_path}")
                        file_path = potential_path
                        break

                # If still not found, return False
                if not os.path.exists(file_path):
                    logger.error(f"Could not find file at any location")
                    return False

            # Load the dataframe based on file extension
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext == '.csv':
                logger.info(f"Loading CSV file: {file_path}")
                try:
                    # Try with different encodings if needed
                    self.df = pai.read_csv(file_path)
                except UnicodeDecodeError:
                    logger.info("Trying with different encoding (latin1)")
                    self.df = pai.read_csv(file_path, encoding='latin1')
            elif file_ext in ['.xlsx', '.xls']:
                logger.info(f"Loading Excel file: {file_path}")
                self.df = pai.read_excel(file_path)
            elif file_ext == '.json':
                logger.info(f"Loading JSON file: {file_path}")
                self.df = pai.read_json(file_path)
            else:
                # Try to infer the file type from content
                logger.warning(f"Unsupported file extension: {file_ext}, attempting to infer file type")

                # Try as CSV first
                try:
                    self.df = pai.read_csv(file_path)
                    logger.info(f"Successfully loaded as CSV: {file_path}")
                except Exception as csv_error:
                    logger.warning(f"Failed to load as CSV: {csv_error}")

                    # Try as Excel
                    try:
                        self.df = pai.read_excel(file_path)
                        logger.info(f"Successfully loaded as Excel: {file_path}")
                    except Exception as excel_error:
                        logger.warning(f"Failed to load as Excel: {excel_error}")

                        # Try as JSON
                        try:
                            self.df = pai.read_json(file_path)
                            logger.info(f"Successfully loaded as JSON: {file_path}")
                        except Exception as json_error:
                            logger.error(f"Could not load file as any supported format: {file_path}")
                            return False

            # Verify the dataframe was loaded successfully
            if self.df is None or self.df.empty:
                logger.error(f"Loaded dataframe is empty or None: {file_path}")
                return False

            logger.info(f"Successfully loaded dataframe from {file_path} with shape {self.df.shape}")
            return True
        except Exception as e:
            logger.error(f"Error loading dataframe from {file_path}: {e}", exc_info=True)
            return False

    def create_agent(self, df: Optional[pd.DataFrame] = None, model: Optional[str] = None) -> bool:
        """Create a PandasAI Agent instance."""
        try:
            if df is not None:
                self.df = df

            if self.df is None:
                logger.error("No dataframe loaded")
                return False

            if not hasattr(self, 'provider') or not self.api_key:
                logger.error("Provider or API key not initialized")
                return False

            # Create LLM provider using our factory
            llm = LLMProviderFactory.create_provider(
                provider=self.provider,
                api_key=self.api_key,
                model=model
            )

            # Create agent with the LLM provider
            self.agent = Agent(self.df, config={"llm": llm})
            logger.info(f"Created PandasAI Agent instance with {self.provider} provider")
            return True
        except Exception as e:
            logger.error(f"Error creating PandasAI Agent: {e}", exc_info=True)
            return False

    def chat(self, query: str) -> Dict[str, Any]:
        """Chat with the PandasAI Agent."""
        try:
            if self.agent is None:
                logger.error("PandasAI Agent not initialized")
                return {"error": "Agent not initialized"}

            response = self.agent.chat(query)
            logger.info(f"PandasAI chat response type: {type(response)}")

            # Process and format the response
            return self._format_response(response)
        except Exception as e:
            logger.error(f"Error in PandasAI chat: {e}", exc_info=True)
            return {"error": str(e)}

    def _format_response(self, response: Any) -> Dict[str, Any]:
        """Format the response from PandasAI."""
        # Handle different response types
        if isinstance(response, pd.DataFrame):
            return {
                "type": "dataframe",
                "data": response.to_dict(orient="records"),
                "columns": response.columns.tolist()
            }
        elif isinstance(response, (int, float)):
            return {
                "type": "number",
                "value": response
            }
        elif hasattr(response, "image_path") and response.image_path:
            # Handle chart response
            return {
                "type": "chart",
                "image_path": response.image_path
            }
        else:
            # Default to text response
            return {
                "type": "text",
                "text": str(response)
            }

    def train(self, instructions: Optional[str] = None,
              queries: Optional[List[str]] = None,
              codes: Optional[List[str]] = None) -> bool:
        """Train the PandasAI Agent."""
        try:
            if self.agent is None:
                logger.error("PandasAI Agent not initialized")
                return False

            if instructions:
                self.agent.train(docs=instructions)
                logger.info("Trained PandasAI Agent with instructions")

            if queries and codes and len(queries) == len(codes):
                self.agent.train(queries=queries, codes=codes)
                logger.info(f"Trained PandasAI Agent with {len(queries)} Q/A pairs")

            return True
        except Exception as e:
            logger.error(f"Error training PandasAI Agent: {e}", exc_info=True)
            return False
