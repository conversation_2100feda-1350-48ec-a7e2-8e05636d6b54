# Datagenius Agent Architecture

This directory contains the implementation of the agent-based architecture for the Datagenius backend. The architecture is built around composable agents that can be configured through YAML files.

## Architecture

The agent architecture is based on the following components:

- **Base Agent**: Abstract base class that all agents implement
- **Composable Agent**: Agent implementation that uses a component-based approach
- **Components**: Specialized modules that handle specific aspects of agent functionality
- **Tools**: Shared utilities that can be used by any agent

## Agent Types

The system includes the following composable agent types:

1. **Composable Classifier**: Text classification specialist (`ComposableClassificationAgent`)
2. **Composable Marketer**: Marketing content generation specialist (`ComposableMarketingAgent`)
3. **Composable Analyst**: Data analysis and visualization specialist (`ComposableAnalysisAgent`)

## Configuration

Agents are configured through YAML files in the `backend/personas` directory. Each YAML file defines:

- Agent metadata (ID, name, description, etc.)
- Agent class to instantiate
- Components to include
- System prompts
- Capabilities

Example:

```yaml
id: composable-classifier-ai
name: Composable Classifier
description: A composable AI assistant for text classification tasks
version: 1.0.0
agent_class: agents.classification.composable_agent.ComposableClassificationAgent
components:
  - type: classification_parser
    name: request_parser
  - type: hf_classifier
    name: huggingface_classifier
  - type: llm_classifier
    name: llm_classifier
  - type: error_handler
    name: classification_error_handler
```

## Component System

The component system allows for modular agent construction:

- Components are registered with the `ComponentRegistry`
- Each component handles a specific aspect of agent functionality
- Components are executed in sequence when processing a message
- Components can be shared across different agent types

## Usage

To use an agent:

```python
from agents.registry import AgentRegistry

# Create an agent instance
agent = await AgentRegistry.create_agent_instance("composable-marketing-ai")

# Process a message
response = await agent.process_message(
    user_id=1,
    message="Create a marketing strategy for a new fitness app",
    conversation_id="conversation_123",
    context={}
)

# Print the response
print(response["message"])
```

## Adding New Agents

To add a new agent:

1. Create a new composable agent class that extends `ComposableAgent`
2. Register any specialized components with the `ComponentRegistry`
3. Create a YAML configuration file in the `backend/personas` directory
4. Restart the application to load the new agent
