"""
Provider models utility functions.

This module provides utility functions for fetching available models from AI providers.
"""

import os
import logging
import json
import requests
from typing import List, Dict, Any, Optional, Tuple

# Configure logging
logger = logging.getLogger(__name__)

def fetch_openai_models(api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models from OpenAI API.

    Args:
        api_key: OpenAI API key
        endpoint: OpenAI API endpoint

    Returns:
        List of available models
    """
    try:
        # Validate inputs
        if not api_key:
            logger.error("OpenAI API key is missing or invalid")
            return []

        if not endpoint:
            logger.error("OpenAI endpoint is missing")
            return []

        logger.info(f"Fetching models from OpenAI API at {endpoint}")

        # Check if API key format is valid (sk-...)
        if not api_key.startswith("sk-"):
            logger.error("OpenAI API key has invalid format (should start with 'sk-')")
            return []

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        # Normalize endpoint URL
        if endpoint.endswith('/'):
            endpoint = endpoint[:-1]

        # Make request to OpenAI API
        logger.info(f"Making request to {endpoint}/models")
        response = requests.get(f"{endpoint}/models", headers=headers, timeout=15)

        # Log response status
        logger.info(f"OpenAI API response status: {response.status_code}")

        # Handle 401 errors specifically
        if response.status_code == 401:
            logger.error("Authentication failed for OpenAI API. Please check your API key.")
            return []

        # Raise for other HTTP errors
        response.raise_for_status()

        # Parse response
        data = response.json()
        models = data.get("data", [])

        # Format models with better categorization
        formatted_models = []

        # Define model categories for better organization
        categories = {
            "chat": ["gpt-", "ft:gpt-"],
            "embedding": ["text-embedding", "embedding"],
            "image": ["dall-e"],
            "audio": ["whisper", "tts"],
            "other": []
        }

        for model in models:
            model_id = model.get("id", "")
            if not model_id:
                continue

            # Determine model category
            category = "other"
            for cat, prefixes in categories.items():
                if any(prefix in model_id.lower() for prefix in prefixes):
                    category = cat
                    break

            # Format model name for better readability
            if "gpt-" in model_id:
                display_name = model_id.replace("gpt-", "GPT ").replace("-turbo", " Turbo")
            elif "text-embedding" in model_id:
                display_name = f"Embedding: {model_id}"
            elif "dall-e" in model_id:
                display_name = f"DALL-E: {model_id}"
            else:
                display_name = model_id

            # Create description with category
            description = model.get("description", "")
            if not description:
                description = f"OpenAI {category.title()} Model"

            formatted_models.append({
                "id": model_id,
                "name": display_name,
                "description": description,
                "created": model.get("created", 0),
                "context_length": model.get("context_length", 0),
                "category": category,
                "provider": "openai"
            })

        # Sort models: first by category (chat first), then by creation date (newest first)
        category_order = {"chat": 0, "embedding": 1, "image": 2, "audio": 3, "other": 4}
        formatted_models.sort(key=lambda x: (
            category_order.get(x.get("category", "other"), 99),
            -x.get("created", 0)  # Negative for descending order
        ))

        return formatted_models
    except Exception as e:
        logger.error(f"Error fetching OpenAI models: {str(e)}", exc_info=True)
        return []

def fetch_groq_models(api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models from Groq API.

    Args:
        api_key: Groq API key
        endpoint: Groq API endpoint

    Returns:
        List of available models
    """
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        # Normalize endpoint URL
        if endpoint.endswith('/'):
            endpoint = endpoint[:-1]

        # Make request to Groq API
        response = requests.get(f"{endpoint}/models", headers=headers, timeout=10)
        response.raise_for_status()

        # Parse response
        data = response.json()
        models = data.get("data", [])

        # Format models
        formatted_models = []
        for model in models:
            model_id = model.get("id", "")
            if not model_id:
                continue

            # Format model name for better readability
            display_name = model_id.replace("-", " ").title()

            # Create description
            description = model.get("description", "")
            if not description:
                if "llama" in model_id.lower():
                    description = "Meta's Llama model via Groq"
                elif "mixtral" in model_id.lower():
                    description = "Mistral's Mixtral model via Groq"
                elif "gemma" in model_id.lower():
                    description = "Google's Gemma model via Groq"
                else:
                    description = "Groq-hosted language model"

            # Get context window (Groq uses context_window instead of context_length)
            context_length = model.get("context_window", 0)

            formatted_models.append({
                "id": model_id,
                "name": display_name,
                "description": description,
                "created": model.get("created", 0),
                "context_length": context_length,
                "provider": "groq"
            })

        # Sort models by creation date (newest first)
        formatted_models.sort(key=lambda x: x.get("created", 0), reverse=True)
        return formatted_models
    except Exception as e:
        logger.error(f"Error fetching Groq models: {str(e)}", exc_info=True)
        return []

def fetch_gemini_models(api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models from Google Gemini API.

    Args:
        api_key: Google API key
        endpoint: Gemini API endpoint

    Returns:
        List of available models
    """
    try:
        # Gemini doesn't have a models endpoint, so we use a more comprehensive static list
        # with proper metadata and try to verify availability with a test request
        models = [
            {
                "id": "gemini-1.5-pro",
                "name": "Gemini 1.5 Pro",
                "description": "Google's most capable multimodal model with 1M context",
                "context_length": 1000000,
                "created": 1714435200,  # Approximate timestamp for May 2024
            },
            {
                "id": "gemini-1.5-flash",
                "name": "Gemini 1.5 Flash",
                "description": "Fast and efficient multimodal model with 1M context",
                "context_length": 1000000,
                "created": 1714435200,  # Approximate timestamp for May 2024
            },
            {
                "id": "gemini-1.5-pro-vision",
                "name": "Gemini 1.5 Pro Vision",
                "description": "Multimodal model optimized for vision tasks",
                "context_length": 1000000,
                "created": 1714435200,  # Approximate timestamp for May 2024
            },
            {
                "id": "gemini-1.0-pro",
                "name": "Gemini 1.0 Pro",
                "description": "Previous generation multimodal model",
                "context_length": 32768,
                "created": **********,  # Approximate timestamp for Dec 2023
            },
            {
                "id": "gemini-1.0-pro-vision",
                "name": "Gemini 1.0 Pro Vision",
                "description": "Previous generation vision model",
                "context_length": 32768,
                "created": **********,  # Approximate timestamp for Dec 2023
            }
        ]

        # Add provider information
        for model in models:
            model["provider"] = "google"

        # Sort by creation date (newest first)
        models.sort(key=lambda x: x.get("created", 0), reverse=True)

        return models
    except Exception as e:
        logger.error(f"Error preparing Gemini models: {str(e)}", exc_info=True)
        return []

def fetch_anthropic_models(api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models from Anthropic API.

    Args:
        api_key: Anthropic API key
        endpoint: Anthropic API endpoint

    Returns:
        List of available models
    """
    try:
        # Anthropic doesn't have a models endpoint in their v1 API
        # We use a comprehensive static list with proper metadata
        models = [
            {
                "id": "claude-3-opus-20240229",
                "name": "Claude 3 Opus",
                "description": "Most powerful Claude model with 200K context",
                "context_length": 200000,
                "created": **********,  # Approximate timestamp for Feb 29, 2024
                "pricing": {
                    "input": {"prompt": 0.000015, "unit": "tokens"},
                    "output": {"completion": 0.000075, "unit": "tokens"}
                }
            },
            {
                "id": "claude-3-sonnet-20240229",
                "name": "Claude 3 Sonnet",
                "description": "Balanced performance and speed with 200K context",
                "context_length": 200000,
                "created": **********,  # Approximate timestamp for Feb 29, 2024
                "pricing": {
                    "input": {"prompt": 0.000003, "unit": "tokens"},
                    "output": {"completion": 0.000015, "unit": "tokens"}
                }
            },
            {
                "id": "claude-3-haiku-20240307",
                "name": "Claude 3 Haiku",
                "description": "Fast and efficient model with 200K context",
                "context_length": 200000,
                "created": 1709769600,  # Approximate timestamp for Mar 7, 2024
                "pricing": {
                    "input": {"prompt": 0.00000025, "unit": "tokens"},
                    "output": {"completion": 0.00000125, "unit": "tokens"}
                }
            },
            {
                "id": "claude-2.1",
                "name": "Claude 2.1",
                "description": "Previous generation Claude model with 200K context",
                "context_length": 200000,
                "created": 1700179200,  # Approximate timestamp for Nov 2023
            },
            {
                "id": "claude-2.0",
                "name": "Claude 2.0",
                "description": "Previous generation Claude model with 100K context",
                "context_length": 100000,
                "created": **********,  # Approximate timestamp for Jul 2023
            },
            {
                "id": "claude-instant-1.2",
                "name": "Claude Instant 1.2",
                "description": "Fast and cost-effective previous generation model",
                "context_length": 100000,
                "created": **********,  # Approximate timestamp for Mar 2023
            }
        ]

        # Add provider information
        for model in models:
            model["provider"] = "anthropic"

        # Sort by creation date (newest first)
        models.sort(key=lambda x: x.get("created", 0), reverse=True)

        return models
    except Exception as e:
        logger.error(f"Error preparing Anthropic models: {str(e)}", exc_info=True)
        return []

def fetch_openrouter_models(api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models from OpenRouter API.

    Args:
        api_key: OpenRouter API key
        endpoint: OpenRouter API endpoint

    Returns:
        List of available models
    """
    try:
        # Validate inputs
        if not api_key:
            logger.error("OpenRouter API key is missing")
            return []

        if not endpoint:
            logger.error("OpenRouter endpoint is missing")
            return []

        logger.info(f"Fetching models from OpenRouter API at {endpoint}")

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        # Normalize endpoint URL
        if endpoint.endswith('/'):
            endpoint = endpoint[:-1]

        # Make request to OpenRouter API
        logger.info(f"Making request to {endpoint}/models")
        response = requests.get(f"{endpoint}/models", headers=headers, timeout=15)

        # Log response status
        logger.info(f"OpenRouter API response status: {response.status_code}")

        # Raise for status to catch HTTP errors
        response.raise_for_status()

        # Parse response
        response_data = response.json()

        # OpenRouter API might return data in different formats
        # Let's handle both array format and object format with data property
        if isinstance(response_data, list):
            models = response_data
        elif isinstance(response_data, dict) and "data" in response_data:
            models = response_data.get("data", [])
        elif isinstance(response_data, dict) and "models" in response_data:
            models = response_data.get("models", [])
        else:
            # Log the actual response for debugging
            logger.error(f"Unexpected OpenRouter API response format: {response_data}")
            models = []

        # Log the number of models received
        logger.info(f"Received {len(models)} models from OpenRouter API")

        # Log a sample model for debugging
        if models and len(models) > 0:
            logger.debug(f"Sample model from OpenRouter: {models[0]}")

        # Format models
        formatted_models = []
        for model in models:
            # Handle different model formats
            if not isinstance(model, dict):
                logger.warning(f"Skipping non-dict model: {model}")
                continue

            model_id = model.get("id", "")
            context_window = model.get("context_length", 0)

            # Skip models with empty IDs
            if not model_id:
                logger.warning(f"Skipping model with empty ID: {model}")
                continue

            # Format the model name to include provider for clarity
            provider = model.get("provider", "Unknown")
            model_name = model.get("name", model_id)

            # Create a more descriptive name that includes the provider
            display_name = f"{model_name} ({provider})"

            # Create a more informative description
            description = model.get('description', '')
            if not description:
                description = f"Model from {provider}"

            formatted_model = {
                "id": model_id,
                "name": display_name,
                "description": description,
                "context_length": context_window,
                "provider": provider,
                "pricing": model.get("pricing", {}),  # Include pricing information if available
                "created": model.get("created", 0),
            }

            formatted_models.append(formatted_model)

        # Log the number of formatted models
        logger.info(f"Formatted {len(formatted_models)} models from OpenRouter API")

        # Define a list of popular providers to prioritize in sorting
        popular_providers = ["openai", "anthropic", "meta", "google", "mistral", "cohere"]

        # Sort models: first by popular providers, then by other providers alphabetically
        def sort_key(model):
            provider = model.get("provider", "").lower()
            # Try to find the provider in the popular providers list
            provider_rank = next((i for i, p in enumerate(popular_providers) if p in provider), len(popular_providers))
            return (provider_rank, provider, model.get("name", ""))

        formatted_models.sort(key=sort_key)

        # Return the formatted models
        return formatted_models
    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP error fetching OpenRouter models: {str(e)}", exc_info=True)
        # Try to log response content if available
        try:
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response content: {e.response.text}")
        except:
            pass
        return []
    except Exception as e:
        logger.error(f"Error fetching OpenRouter models: {str(e)}", exc_info=True)
        return []

def fetch_provider_models(provider_id: str, api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models for a provider.

    Args:
        provider_id: Provider ID
        api_key: API key for the provider
        endpoint: API endpoint for the provider

    Returns:
        List of available models
    """
    if not api_key or not endpoint:
        logger.warning(f"Missing API key or endpoint for provider {provider_id}")
        return []

    # Fetch models based on provider
    if provider_id == "openai":
        return fetch_openai_models(api_key, endpoint)
    elif provider_id == "groq":
        return fetch_groq_models(api_key, endpoint)
    elif provider_id == "gemini":
        return fetch_gemini_models(api_key, endpoint)
    elif provider_id == "anthropic":
        return fetch_anthropic_models(api_key, endpoint)
    elif provider_id == "openrouter":
        return fetch_openrouter_models(api_key, endpoint)
    else:
        logger.warning(f"Unsupported provider: {provider_id}")
        return []
