"""
Chat models for the Datagenius backend.

This module provides Pydantic models for chat functionality.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field


class ConversationBase(BaseModel):
    """Base model for conversation data."""
    title: str
    persona_id: str
    metadata: Optional[Dict[str, Any]] = Field(None, alias="conversation_metadata")


class ConversationCreate(ConversationBase):
    """Model for creating a new conversation."""
    pass


class ConversationUpdate(BaseModel):
    """Model for updating a conversation."""
    title: Optional[str] = None
    is_archived: Optional[bool] = None
    metadata: Optional[Dict[str, Any]] = None


class ConversationResponse(ConversationBase):
    """Model for conversation data returned to the client."""
    id: str
    user_id: int
    created_at: datetime
    updated_at: datetime
    is_archived: bool
    messages: List["MessageResponse"] = []

    class Config:
        orm_mode = True
        populate_by_name = True


class ConversationListResponse(BaseModel):
    """Model for conversation list response."""
    conversations: List[ConversationResponse]
    total: Optional[int] = None


class MessageBase(BaseModel):
    """Base model for message data."""
    sender: str  # "user" or "ai"
    content: str
    metadata: Optional[Dict[str, Any]] = Field(None, alias="message_metadata")


class MessageCreate(MessageBase):
    """Model for creating a new message."""
    conversation_id: str


class MessageResponse(MessageBase):
    """Model for message data returned to the client."""
    id: str
    conversation_id: str
    created_at: datetime

    class Config:
        orm_mode = True
        populate_by_name = True


class SendMessageRequest(BaseModel):
    """Model for sending a message."""
    conversation_id: str
    message: str
    context: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class SendMessageResponse(BaseModel):
    """Model for send message response."""
    conversation_id: str
    user_message: MessageResponse
    ai_message: Optional[MessageResponse] = None
