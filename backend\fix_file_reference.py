import os
import json
from sqlalchemy.orm import Session
from app.database import get_db, SessionLocal
from app.models.data_source import DataSource

DATA_SOURCE_ID = "d4dcaa06-757a-4368-891e-8cc2ed38fdf5"
CORRECT_FILE_ID = "d7e8b883-0839-4260-9a36-37aeebe9c485"

def update_data_source_file_id(db: Session, data_source_id: str, correct_file_id: str):
    """Updates the file_id in the source_metadata of a specific DataSource."""
    data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()

    if not data_source:
        print(f"Error: Data source with ID {data_source_id} not found.")
        return

    print(f"Found data source: {data_source.id}, Name: {data_source.name}")
    print(f"Current source_metadata: {data_source.source_metadata}")

    if data_source.type != 'file':
        print(f"Error: Data source {data_source_id} is not of type 'file'.")
        return

    # Ensure source_metadata is a dictionary
    metadata = data_source.source_metadata
    if not isinstance(metadata, dict):
        print(f"Warning: source_metadata for {data_source_id} is not a dictionary. Initializing.")
        metadata = {} # Or handle based on expected structure if it exists but isn't dict

    # Update the file_id
    metadata['file_id'] = correct_file_id
    
    # SQLAlchemy handles JSONB updates correctly when the object is mutated
    # No need to explicitly set data_source.source_metadata = metadata if it was already a dict

    print(f"Attempting to update file_id to: {correct_file_id}")
    
    try:
        db.commit()
        db.refresh(data_source)
        print(f"Successfully updated source_metadata for data source {data_source_id}.")
        print(f"New source_metadata: {data_source.source_metadata}")
    except Exception as e:
        db.rollback()
        print(f"Error updating database: {e}")

if __name__ == "__main__":
    print(f"Attempting to fix file reference for data source: {DATA_SOURCE_ID}")
    db = SessionLocal()
    try:
        update_data_source_file_id(db, DATA_SOURCE_ID, CORRECT_FILE_ID)
    finally:
        db.close()
        print("Database session closed.")
