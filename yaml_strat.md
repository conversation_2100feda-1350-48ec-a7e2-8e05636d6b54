# JSON to YAML Transition Strategy for Datagenius

## 1. Introduction

This document outlines a comprehensive strategy for transitioning from JSON to YAML across the Datagenius project. The goal is to improve configuration readability, maintainability, and consistency while ensuring backward compatibility during the transition period.

### 1.1 Motivation

YAML offers several advantages over JSON for configuration and schema definition:
- Better human readability with indentation-based structure
- Support for comments to document configuration options
- More flexible syntax for complex configurations
- Better handling of multiline strings (important for system prompts)
- Reduced syntax noise (no quotes around keys, fewer commas and brackets)

### 1.2 Current State

The Datagenius project currently uses:
- YAML for persona configurations and agent definitions
- JSON for vector database metadata, knowledge graphs, and tool schemas
- Hardcoded JSON objects for MCP tool schemas in Python code
- JSON for API communication (which will remain as JSON)

## 2. Implementation Plan

The transition will follow a phased approach to minimize disruption to the existing codebase.

### 2.1 Phase 1: Infrastructure and Utilities (Week 1)

#### 2.1.1 Create YAML Utility Module

Create a new utility module `app/utils/yaml_utils.py` with functions for:

```python
def load_yaml(file_path: str, default_encoding: str = "utf-8") -> Dict[str, Any]:
    """
    Load YAML file with robust encoding handling.
    
    Args:
        file_path: Path to the YAML file
        default_encoding: Default encoding to try first
        
    Returns:
        Parsed YAML content as a dictionary
    """
    # Implementation similar to existing code in persona_manager.py
    
def save_yaml(data: Dict[str, Any], file_path: str, encoding: str = "utf-8") -> bool:
    """
    Save data to a YAML file.
    
    Args:
        data: Data to save
        file_path: Path to save the file
        encoding: File encoding
        
    Returns:
        True if successful, False otherwise
    """
    # Implementation with proper error handling
    
def json_to_yaml(json_data: Dict[str, Any]) -> str:
    """
    Convert JSON data to YAML string.
    
    Args:
        json_data: JSON data as dictionary
        
    Returns:
        YAML string representation
    """
    # Implementation using PyYAML
    
def yaml_to_json(yaml_data: str) -> Dict[str, Any]:
    """
    Convert YAML string to JSON data.
    
    Args:
        yaml_data: YAML string
        
    Returns:
        JSON data as dictionary
    """
    # Implementation using PyYAML
    
def convert_file(input_path: str, output_path: str, input_format: str, output_format: str) -> bool:
    """
    Convert between JSON and YAML files.
    
    Args:
        input_path: Path to input file
        output_path: Path to output file
        input_format: 'json' or 'yaml'
        output_format: 'json' or 'yaml'
        
    Returns:
        True if successful, False otherwise
    """
    # Implementation with proper error handling
```

#### 2.1.2 Create Schema Utilities

Add schema-specific utilities to handle JSON Schema to YAML conversion:

```python
def schema_to_yaml(schema: Dict[str, Any], include_comments: bool = True) -> str:
    """
    Convert JSON Schema to YAML with optional explanatory comments.
    
    Args:
        schema: JSON Schema as dictionary
        include_comments: Whether to include explanatory comments
        
    Returns:
        YAML string representation of the schema
    """
    # Implementation with special handling for schema-specific elements
    
def yaml_to_schema(yaml_data: str) -> Dict[str, Any]:
    """
    Convert YAML to JSON Schema.
    
    Args:
        yaml_data: YAML string
        
    Returns:
        JSON Schema as dictionary
    """
    # Implementation with validation for schema correctness
```

#### 2.1.3 Create Conversion Scripts

Create scripts to automate the conversion process:

1. `scripts/convert_json_to_yaml.py` - Convert JSON files to YAML
2. `scripts/convert_yaml_to_json.py` - Convert YAML files to JSON (for backward compatibility)
3. `scripts/validate_yaml_schema.py` - Validate YAML files against schemas

### 2.2 Phase 2: Vector Database Metadata (Week 2)

#### 2.2.1 Update Vector Database Store

Modify the vector database storage to support YAML:

1. Update `vector_db` module to read/write metadata in YAML format
2. Create a migration script to convert existing JSON metadata files to YAML
3. Implement backward compatibility to still read JSON files during transition

#### 2.2.2 Convert Existing Metadata Files

Run the conversion script to convert all existing vector database metadata files:

```bash
python scripts/convert_json_to_yaml.py --directory backend/vector_db --pattern "*_info.json" --backup
```

### 2.3 Phase 3: Knowledge Graph Storage (Week 3)

#### 2.3.1 Update Knowledge Graph Store

Modify the knowledge graph storage to use YAML:

1. Update `agents/knowledge_graph/store.py` to save/load graphs in YAML format
2. Create a migration script to convert existing JSON graph files to YAML
3. Implement backward compatibility to still read JSON files during transition

#### 2.3.2 Convert Existing Knowledge Graphs

Run the conversion script to convert all existing knowledge graph files:

```bash
python scripts/convert_json_to_yaml.py --directory backend/knowledge_graphs --pattern "*.json" --backup
```

### 2.4 Phase 4: MCP Tool Schemas (Week 4-5)

#### 2.4.1 Create Schema Directory

Create a dedicated directory for tool schemas:

```
backend/schemas/tools/
```

#### 2.4.2 Extract Tool Schemas

For each MCP tool:

1. Extract the input and output schemas to YAML files
2. Update the tool code to load schemas from files
3. Add schema validation to ensure correctness

Example schema file (`backend/schemas/tools/data_visualization.yaml`):

```yaml
name: data_visualization
description: Visualize data using various chart types
input_schema:
  type: object
  properties:
    file_path:
      type: string
      description: Path to the data file (CSV, Excel, JSON supported)
    chart_type:
      type: string
      enum:
        - bar
        - line
        - scatter
        - pie
        - heatmap
      description: Type of chart to create
    # Additional properties...
  required:
    - file_path
    - chart_type
output_schema:
  type: object
  properties:
    tool_name:
      type: string
    status:
      type: string
    isError:
      type: boolean
    content:
      type: array
      items:
        type: object
        properties:
          type:
            type: string
            enum:
              - text
              - image
          text:
            type: string
          src:
            type: string
            description: Base64 encoded image data URI
        required:
          - type
    # Additional properties...
annotations:
  title: Visualize Data
  readOnlyHint: true
  openWorldHint: false
```

#### 2.4.3 Update Tool Base Class

Update the `BaseMCPTool` class to support loading schemas from YAML files:

```python
def __init__(self, name: str, description: str, schema_path: Optional[str] = None, **kwargs):
    """
    Initialize the tool.
    
    Args:
        name: Tool name
        description: Tool description
        schema_path: Path to the schema YAML file (relative to schemas/tools)
        **kwargs: Additional arguments for backward compatibility
    """
    self._name = name
    self._description = description
    
    if schema_path:
        # Load schemas from YAML file
        schema_file = os.path.join("schemas", "tools", schema_path)
        schema_data = yaml_utils.load_yaml(schema_file)
        self._input_schema = schema_data.get("input_schema", {})
        self._output_schema = schema_data.get("output_schema", {})
        self._annotations = schema_data.get("annotations", {})
    else:
        # Backward compatibility: use kwargs
        self._input_schema = kwargs.get("input_schema", {})
        self._output_schema = kwargs.get("output_schema", {})
        self._annotations = kwargs.get("annotations", {})
```

### 2.5 Phase 5: Frontend Configuration (Week 6)

#### 2.5.1 Update Frontend Configuration

Convert frontend configuration files to YAML:

1. Create YAML versions of configuration files
2. Update build process to convert YAML to JSON during build
3. Add documentation for frontend developers

#### 2.5.2 Form Schema Extraction

For form schemas:

1. Create a schema generator to extract Zod schemas to YAML
2. Create a schema loader to generate Zod schemas from YAML
3. Update form components to use the schema loader

### 2.6 Phase 6: Documentation and Training (Week 7)

#### 2.6.1 Update Documentation

1. Update all documentation to reflect the YAML-based approach
2. Create a style guide for YAML configuration files
3. Document the transition process and backward compatibility

#### 2.6.2 Developer Training

1. Conduct a training session on YAML best practices
2. Create examples and templates for common YAML patterns
3. Update onboarding documentation for new developers

## 3. Backward Compatibility Strategy

To ensure a smooth transition, the following backward compatibility measures will be implemented:

### 3.1 Dual Format Support

During the transition period (estimated 2-3 months):

1. All modules will support both JSON and YAML formats
2. File format detection will be based on file extension
3. Conversion utilities will be available for developers

### 3.2 Deprecation Timeline

1. **Month 1-2**: Introduce YAML support while maintaining JSON
2. **Month 3**: Mark JSON format as deprecated in documentation
3. **Month 4-5**: Log warnings when JSON format is used
4. **Month 6**: Remove JSON support for configuration (API will still use JSON)

## 4. Testing Strategy

### 4.1 Unit Tests

1. Create unit tests for all YAML utility functions
2. Add tests for schema validation and conversion
3. Ensure backward compatibility is tested

### 4.2 Integration Tests

1. Create tests that verify the entire configuration pipeline
2. Test with both JSON and YAML formats during transition
3. Verify that all components work correctly with YAML

### 4.3 Migration Testing

1. Test the migration scripts on sample data
2. Verify that converted files maintain semantic equivalence
3. Test edge cases (empty files, complex nested structures, etc.)

## 5. Risks and Mitigations

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Data loss during conversion | High | Low | Create backups before conversion, implement validation |
| Performance degradation | Medium | Low | Benchmark YAML vs JSON parsing, optimize if needed |
| Developer resistance | Medium | Medium | Provide training and clear documentation |
| Backward compatibility issues | High | Medium | Thorough testing, gradual transition |
| Schema validation errors | Medium | Medium | Create robust validation tools, provide clear error messages |

## 6. Success Criteria

The transition will be considered successful when:

1. All configuration files are in YAML format
2. Tool schemas are externalized and maintainable
3. No regression in functionality or performance
4. Developers can easily create and modify configurations
5. Documentation is updated to reflect the YAML approach

## 7. Resources and Dependencies

### 7.1 Required Libraries

- PyYAML (already in use)
- jsonschema (for validation)
- pydantic (for schema integration)

### 7.2 Developer Resources

- Estimated 1-2 developers for 7 weeks
- QA support for testing
- Documentation writer for updates

## 8. Timeline and Milestones

| Phase | Description | Timeline | Milestone |
|-------|-------------|----------|-----------|
| 1 | Infrastructure and Utilities | Week 1 | YAML utilities available |
| 2 | Vector Database Metadata | Week 2 | Vector DB using YAML |
| 3 | Knowledge Graph Storage | Week 3 | Knowledge graphs using YAML |
| 4 | MCP Tool Schemas | Week 4-5 | All tool schemas externalized |
| 5 | Frontend Configuration | Week 6 | Frontend using YAML |
| 6 | Documentation and Training | Week 7 | All documentation updated |
| - | Backward Compatibility Period | Month 2-5 | - |
| - | Full Transition Complete | Month 6 | JSON support removed |
