"""
MCP tool registration for the Datagenius backend.

This module registers MCP tools with the tool registry.
"""

import logging
from .registry import MCP<PERSON><PERSON>Registry
from .text_processing import TextProcessingTool
from .data_cleaning import DataCleaningTool
from .advanced_query import AdvancedQueryTool
from .data_filtering import <PERSON>F<PERSON><PERSON>Tool
from .sentiment_analysis import SentimentA<PERSON>ys<PERSON>Tool
from .text_classification import TextClassificationTool
from .content_generation import ContentGenerationTool
from .document_embedding import DocumentEmbeddingTool
from .pandasai_analysis import PandasAIAnalysisTool
from .pandasai_visualization import PandasAIVisualizationTool
from .pandasai_query import PandasAIQueryTool
from .statistical_analysis import StatisticalAnalysisTool
from .data_storytelling import DataStorytellingTool
from .natural_language_query import NaturalLanguageQueryTool
from .deployment import DeploymentTool
from .data_access import DataAccessTool
from .knowledge_graph import KnowledgeGraphTool

# Import mem0-based tools
from .mem0_document_embedding import Mem0DocumentEmbeddingTool

logger = logging.getLogger(__name__)


def register_mcp_tools():
    """Register all MCP tools with the registry."""
    # Register data cleaning tool
    MCPToolRegistry.register("data_cleaning", DataCleaningTool)
    logger.info("Registered DataCleaningTool with MCP tool registry")

    # Register advanced query tool
    MCPToolRegistry.register("advanced_query", AdvancedQueryTool)
    logger.info("Registered AdvancedQueryTool with MCP tool registry")

    # Register data filtering tool
    MCPToolRegistry.register("data_filtering", DataFilteringTool)
    logger.info("Registered DataFilteringTool with MCP tool registry")

    # Register sentiment analysis tool
    MCPToolRegistry.register("sentiment_analysis", SentimentAnalysisTool)
    logger.info("Registered SentimentAnalysisTool with MCP tool registry")

    # Register text processing tool
    MCPToolRegistry.register("text_processing", TextProcessingTool)
    logger.info("Registered TextProcessingTool with MCP tool registry")

    # Register classification and marketing tools
    MCPToolRegistry.register("text_classification", TextClassificationTool)
    logger.info("Registered TextClassificationTool with MCP tool registry")

    # Register content generation tool
    MCPToolRegistry.register("generate_content", ContentGenerationTool)
    logger.info("Registered ContentGenerationTool with MCP tool registry")

    # Register document embedding tool
    MCPToolRegistry.register("document_embedding", DocumentEmbeddingTool)
    logger.info("Registered DocumentEmbeddingTool with MCP tool registry")

    # Register PandasAI v3 tools
    MCPToolRegistry.register("pandasai_analysis", PandasAIAnalysisTool)
    logger.info("Registered PandasAIAnalysisTool with MCP tool registry")

    MCPToolRegistry.register("pandasai_visualization", PandasAIVisualizationTool)
    logger.info("Registered PandasAIVisualizationTool with MCP tool registry")

    MCPToolRegistry.register("pandasai_query", PandasAIQueryTool)
    logger.info("Registered PandasAIQueryTool with MCP tool registry")

    # Register statistical analysis tool
    MCPToolRegistry.register("statistical_analysis", StatisticalAnalysisTool)
    logger.info("Registered StatisticalAnalysisTool with MCP tool registry")

    # Register data storytelling tool
    MCPToolRegistry.register("data_storytelling", DataStorytellingTool)
    logger.info("Registered DataStorytellingTool with MCP tool registry")

    # Register natural language query tool
    MCPToolRegistry.register("natural_language_query", NaturalLanguageQueryTool)
    logger.info("Registered NaturalLanguageQueryTool with MCP tool registry")

    # Register deployment tool
    MCPToolRegistry.register("deployment", DeploymentTool)
    logger.info("Registered DeploymentTool with MCP tool registry")

    # Register data access tool
    MCPToolRegistry.register("data_access", DataAccessTool)
    logger.info("Registered DataAccessTool with MCP tool registry")

    # Register knowledge graph tool
    MCPToolRegistry.register("knowledge_graph", KnowledgeGraphTool)
    logger.info("Registered KnowledgeGraphTool with MCP tool registry")

    # Register mem0-based tools
    MCPToolRegistry.register("mem0_document_embedding", Mem0DocumentEmbeddingTool)
    logger.info("Registered Mem0DocumentEmbeddingTool with MCP tool registry")

    # Log the registered tools
    tool_names = MCPToolRegistry.list_registered_tools()
    logger.info(f"Registered MCP tools: {tool_names}")
