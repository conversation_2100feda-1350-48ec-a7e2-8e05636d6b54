/**
 * Utility for generating a device fingerprint
 */
import { v5 as uuidv5 } from 'uuid';

// Namespace for generating UUIDs (using a random UUID as namespace)
const NAMESPACE = '1b671a64-40d5-491e-99b0-da01ff1f3341';

/**
 * Generate a device fingerprint based on browser and device characteristics
 * @returns A UUID v5 string representing the device fingerprint
 */
export const generateDeviceFingerprint = async (): Promise<string> => {
  try {
    // Collect browser and device information
    const userAgent = navigator.userAgent;
    const language = navigator.language;
    const platform = navigator.platform;
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    const screenDepth = window.screen.colorDepth;
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const touchPoints = navigator.maxTouchPoints;
    
    // Collect browser capabilities
    const cookiesEnabled = navigator.cookieEnabled;
    const localStorageAvailable = !!window.localStorage;
    const sessionStorageAvailable = !!window.sessionStorage;
    
    // Collect canvas fingerprint
    const canvasFingerprint = await getCanvasFingerprint();
    
    // Combine all data into a single string
    const fingerprintData = [
      userAgent,
      language,
      platform,
      `${screenWidth}x${screenHeight}x${screenDepth}`,
      timezone,
      touchPoints,
      cookiesEnabled,
      localStorageAvailable,
      sessionStorageAvailable,
      canvasFingerprint
    ].join('|');
    
    // Generate a UUID v5 from the fingerprint data
    return uuidv5(fingerprintData, NAMESPACE);
  } catch (error) {
    console.error('Error generating device fingerprint:', error);
    // Fallback to a random UUID if fingerprinting fails
    return uuidv5(Date.now().toString(), NAMESPACE);
  }
};

/**
 * Get a fingerprint from canvas rendering
 * @returns A string representing the canvas fingerprint
 */
const getCanvasFingerprint = async (): Promise<string> => {
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      return 'canvas-not-supported';
    }
    
    // Set canvas dimensions
    canvas.width = 200;
    canvas.height = 50;
    
    // Fill background
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw text
    ctx.fillStyle = '#000000';
    ctx.font = '16px Arial';
    ctx.fillText('DataGenius Fingerprint', 10, 30);
    
    // Draw shapes
    ctx.strokeStyle = '#ff0000';
    ctx.beginPath();
    ctx.arc(160, 25, 15, 0, Math.PI * 2);
    ctx.stroke();
    
    // Get data URL and hash it
    return canvas.toDataURL();
  } catch (error) {
    console.error('Error generating canvas fingerprint:', error);
    return 'canvas-error';
  }
};

/**
 * Store the device fingerprint in localStorage
 * @param fingerprint The device fingerprint to store
 */
export const storeDeviceFingerprint = (fingerprint: string): void => {
  try {
    localStorage.setItem('device_fingerprint', fingerprint);
  } catch (error) {
    console.error('Error storing device fingerprint:', error);
  }
};

/**
 * Get the stored device fingerprint from localStorage
 * @returns The stored device fingerprint or null if not found
 */
export const getStoredDeviceFingerprint = (): string | null => {
  try {
    return localStorage.getItem('device_fingerprint');
  } catch (error) {
    console.error('Error retrieving device fingerprint:', error);
    return null;
  }
};

/**
 * Initialize device fingerprinting
 * Generates and stores a device fingerprint if one doesn't exist
 * @returns The device fingerprint
 */
export const initializeDeviceFingerprint = async (): Promise<string> => {
  const storedFingerprint = getStoredDeviceFingerprint();
  
  if (storedFingerprint) {
    return storedFingerprint;
  }
  
  const fingerprint = await generateDeviceFingerprint();
  storeDeviceFingerprint(fingerprint);
  return fingerprint;
};
