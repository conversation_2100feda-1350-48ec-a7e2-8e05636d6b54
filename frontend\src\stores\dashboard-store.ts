import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { VisualizationType } from '@/utils/visualization';

export interface DashboardWidget {
  id: string;
  type: VisualizationType;
  title: string;
  data: any;
  config?: any;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
}

interface DashboardState {
  widgets: DashboardWidget[];
  addWidget: (widget: DashboardWidget) => void;
  updateWidget: (id: string, widget: Partial<DashboardWidget>) => void;
  removeWidget: (id: string) => void;
  updateWidgetPosition: (id: string, position: { x: number; y: number; w: number; h: number }) => void;
  clearWidgets: () => void;
}

export const useDashboardStore = create<DashboardState>()(
  persist(
    (set) => ({
      widgets: [],
      
      addWidget: (widget) => 
        set((state) => ({
          widgets: [...state.widgets, widget]
        })),
      
      updateWidget: (id, updatedWidget) => 
        set((state) => ({
          widgets: state.widgets.map((widget) => 
            widget.id === id ? { ...widget, ...updatedWidget } : widget
          )
        })),
      
      removeWidget: (id) => 
        set((state) => ({
          widgets: state.widgets.filter((widget) => widget.id !== id)
        })),
      
      updateWidgetPosition: (id, position) => 
        set((state) => ({
          widgets: state.widgets.map((widget) => 
            widget.id === id ? { ...widget, position } : widget
          )
        })),
      
      clearWidgets: () => 
        set({ widgets: [] }),
    }),
    {
      name: 'dashboard-storage',
    }
  )
);
