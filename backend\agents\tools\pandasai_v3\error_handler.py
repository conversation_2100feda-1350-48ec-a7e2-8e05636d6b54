"""
Error handler for PandasAI v3.

This module provides error handling utilities for PandasAI operations.
"""

import logging
import traceback
from typing import Dict, Any, Callable, Optional

logger = logging.getLogger(__name__)

class ErrorHandler:
    """Error handler for PandasAI operations."""
    
    @staticmethod
    def handle_error(func: Callable, fallback: Optional[Callable] = None, *args, **kwargs) -> Dict[str, Any]:
        """Handle errors in PandasAI operations."""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error in PandasAI operation: {e}", exc_info=True)
            
            # Log detailed error information
            error_info = {
                "error": str(e),
                "traceback": traceback.format_exc(),
                "args": args,
                "kwargs": kwargs
            }
            logger.debug(f"Error details: {error_info}")
            
            # Try fallback if provided
            if fallback:
                try:
                    logger.info(f"Attempting fallback for failed PandasAI operation")
                    return fallback(*args, **kwargs)
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed: {fallback_error}", exc_info=True)
                    
            # Return error response
            return {"error": str(e)}
