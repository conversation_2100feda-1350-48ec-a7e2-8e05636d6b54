import { defineConfig, PluginOption } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import fs from "fs"; // Added for file system operations
import YAML from "js-yaml"; // Added for YAML parsing

// Custom Vite plugin to convert components.yaml to components.json
function yamlToJsonPlugin(): PluginOption {
  return {
    name: "yaml-to-json",
    buildStart() {
      const yamlFilePath = path.resolve(__dirname, "components.yaml");
      const jsonFilePath = path.resolve(__dirname, "components.json");

      try {
        if (fs.existsSync(yamlFilePath)) {
          console.log(`Converting ${yamlFilePath} to ${jsonFilePath}`);
          const yamlContent = fs.readFileSync(yamlFilePath, "utf8");
          const jsonContent = YAML.load(yamlContent);
          // Add back the $schema property if it was commented out in YAML
          if (typeof jsonContent === 'object' && jsonContent !== null && !('$schema' in jsonContent)) {
            (jsonContent as any).$schema = "https://ui.shadcn.com/schema.json";
          }
          fs.writeFileSync(jsonFilePath, JSON.stringify(jsonContent, null, 2));
          console.log(`${jsonFilePath} created successfully.`);
        } else {
          console.warn(`${yamlFilePath} not found. Skipping conversion.`);
        }
      } catch (error) {
        console.error(`Error converting YAML to JSON: ${error}`);
        // Optionally, re-throw or handle error to stop build
      }
    },
  };
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
    },
  },
  plugins: [
    yamlToJsonPlugin(), // Added custom plugin
    react()
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
