"""
Data access MCP tool for the Datagenius backend.

This module provides a unified MCP-compatible tool for accessing data from various sources.
It handles file discovery, data loading, and basic data operations across different agents.
It integrates with mem0ai for document processing and semantic search.
"""

import logging
import os
import io
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple

from .base import BaseMCPTool
from app.config import UPLOAD_DIR
from app.utils.json_utils import sanitize_json
from ...utils.vector_service import VectorService
from ...utils.memory_service import MemoryService

# Configure logging
logger = logging.getLogger(__name__)


class DataAccessTool(BaseMCPTool):
    """Tool for accessing data from various sources."""

    def __init__(self):
        """Initialize the data access tool."""
        super().__init__(
            name="data_access",
            description="Access data from various sources including files, databases, and APIs",
            input_schema={
                "type": "object",
                "properties": {
                    "data_source": {
                        "type": ["object", "string"],
                        "description": "Data source information (can be an object with id/name or a string identifier)"
                    },
                    "operation": {
                        "type": "string",
                        "enum": ["load", "describe", "head", "tail", "info", "sample", "query", "search", "send_to_persona", "embed", "search_document"],
                        "description": "Operation to perform on the data"
                    },
                    "params": {
                        "type": "object",
                        "description": "Additional parameters for the operation"
                    }
                },
                "required": ["operation"]
            },
            annotations={
                "title": "Data Access",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.data_dir = "data"
        self.upload_dir = UPLOAD_DIR

        # Get the absolute path to the backend directory
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        self.backend_dir = current_dir

        # Create absolute paths for important directories
        self.abs_upload_dir = os.path.join(current_dir, self.upload_dir)
        self.abs_data_dir = os.path.join(current_dir, self.data_dir)

        # Add both relative and absolute paths to search directories
        self.search_dirs = [
            self.upload_dir,
            self.data_dir,
            "uploads",
            "backend/data",
            "backend/uploads",
            "backend/temp_uploads",  # Add explicit path to backend/temp_uploads
            self.abs_upload_dir,     # Add absolute path to upload directory
            self.abs_data_dir,       # Add absolute path to data directory
            "."
        ]

        # Initialize vector and memory services
        self.vector_service = VectorService()
        self.memory_service = MemoryService()

        logger.info(f"Current directory: {current_dir}")
        logger.info(f"Absolute upload directory: {self.abs_upload_dir}")
        logger.info(f"Absolute data directory: {self.abs_data_dir}")
        self.supported_extensions = ['.csv', '.xlsx', '.xls', '.json', '.pdf', '.docx', '.txt']

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # Update directories if provided in config
        if "data_dir" in config:
            self.data_dir = config.get("data_dir")
            self.abs_data_dir = os.path.join(self.backend_dir, self.data_dir)
            logger.info(f"Updated data directory to: {self.data_dir}")
            logger.info(f"Updated absolute data directory to: {self.abs_data_dir}")

        if "upload_dir" in config:
            self.upload_dir = config.get("upload_dir")
            self.abs_upload_dir = os.path.join(self.backend_dir, self.upload_dir)
            logger.info(f"Updated upload directory to: {self.upload_dir}")
            logger.info(f"Updated absolute upload directory to: {self.abs_upload_dir}")

        # Add custom search directories if provided
        if "search_dirs" in config:
            self.search_dirs.extend(config["search_dirs"])
            # Remove duplicates while preserving order
            self.search_dirs = list(dict.fromkeys(self.search_dirs))
            logger.info(f"Added custom search directories: {config['search_dirs']}")

        # Make sure our absolute paths are in the search directories
        if self.abs_upload_dir not in self.search_dirs:
            self.search_dirs.append(self.abs_upload_dir)
        if self.abs_data_dir not in self.search_dirs:
            self.search_dirs.append(self.abs_data_dir)

        # Also add backend/temp_uploads explicitly
        backend_temp_uploads = os.path.join(self.backend_dir, "temp_uploads")
        if backend_temp_uploads not in self.search_dirs:
            self.search_dirs.append(backend_temp_uploads)

        logger.info(f"Initialized data access tool with data directory: {self.data_dir}")
        logger.info(f"Upload directory: {self.upload_dir}")
        logger.info(f"Search directories: {self.search_dirs}")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the data access tool.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            operation = arguments["operation"]
            data_source = arguments.get("data_source")
            params = arguments.get("params", {})

            logger.info(f"Data access operation: {operation}")
            logger.info(f"Data source: {data_source}")
            logger.info(f"Parameters: {params}")

            # Find and load the data
            file_path, df = await self._find_and_load_data(data_source)

            if df is None:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Could not load data from source: {data_source}. Please make sure you have attached a valid data file using the 'Attach Data' button. Supported formats include CSV, Excel, and JSON files."
                        }
                    ],
                    "metadata": {
                        "error_type": "DataNotFound",
                        "error_details": "No valid data source was found or could be loaded",
                        "component": "DataAccessTool",
                        "data_source": str(data_source) if data_source else "None",
                        "operation": operation if operation else "None",
                        "supported_formats": self.supported_extensions
                    }
                }

            logger.info(f"Successfully loaded data from {file_path} with shape {df.shape}")

            # Prepare metadata with basic info about the dataframe
            metadata = {
                "file_path": file_path,
                "columns": df.columns.tolist(),
                "shape": df.shape,
                "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
                "dataframe": df  # Add the dataframe to metadata for preview generation
            }

            # Perform the requested operation
            if operation == "load":
                # Just return basic info about the loaded data
                result = f"Data loaded successfully from {file_path}. Shape: {df.shape}"

            elif operation == "describe":
                # Get statistical description
                desc_df = df.describe(include='all')
                result = desc_df.to_string()

                # Add the description as structured data for better visualization
                metadata["description"] = desc_df.to_dict()

            elif operation == "head":
                n = params.get("n", 5)
                head_df = df.head(n)
                result = head_df.to_string()

                # Add the preview data in a structured format
                metadata["preview_data"] = head_df.to_dict(orient="records")
                metadata["preview_type"] = "head"

            elif operation == "tail":
                n = params.get("n", 5)
                tail_df = df.tail(n)
                result = tail_df.to_string()

                # Add the preview data in a structured format
                metadata["preview_data"] = tail_df.to_dict(orient="records")
                metadata["preview_type"] = "tail"

            elif operation == "sample":
                n = params.get("n", 5)
                sample_df = df.sample(n=min(n, len(df)))
                result = sample_df.to_string()

                # Add the preview data in a structured format
                metadata["preview_data"] = sample_df.to_dict(orient="records")
                metadata["preview_type"] = "sample"

            elif operation == "info":
                buffer = io.StringIO()
                df.info(buf=buffer)
                result = buffer.getvalue()

                # Add missing values count
                metadata["missing_values"] = df.isna().sum().to_dict()

            elif operation == "query":
                query = params.get("query", "")
                if not query:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Query parameter is required for query operation"
                            }
                        ]
                    }

                try:
                    filtered_df = df.query(query)
                    result = filtered_df.to_string()

                    # Add the filtered data
                    metadata["filtered_data"] = filtered_df.head(10).to_dict(orient="records")
                    metadata["query"] = query
                    metadata["filtered_shape"] = filtered_df.shape

                except Exception as e:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Error executing query: {str(e)}"
                            }
                        ]
                    }

            elif operation == "search":
                search_term = params.get("search_term", "")
                if not search_term:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "search_term parameter is required for search operation"
                            }
                        ]
                    }

                # Search across all string columns
                string_cols = df.select_dtypes(include=['object']).columns.tolist()
                if not string_cols:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "No string columns found to search in"
                            }
                        ]
                    }

                # Create a mask for rows that contain the search term in any string column
                mask = pd.Series(False, index=df.index)
                for col in string_cols:
                    mask = mask | df[col].astype(str).str.contains(search_term, case=False, na=False)

                search_results = df[mask]
                result = f"Found {len(search_results)} rows containing '{search_term}'"

                # Add the search results
                metadata["search_results"] = search_results.head(10).to_dict(orient="records")
                metadata["search_term"] = search_term
                metadata["search_count"] = len(search_results)

            elif operation == "send_to_persona":
                # This operation prepares file data to be sent to an AI persona
                # Extract file content and metadata for the AI persona
                file_info = {
                    "file_path": file_path,
                    "file_name": os.path.basename(file_path),
                    "file_extension": os.path.splitext(file_path)[1].lower(),
                    "file_size_bytes": os.path.getsize(file_path),
                    "last_modified": os.path.getmtime(file_path),
                    "data_shape": df.shape,
                    "columns": df.columns.tolist(),
                    "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()}
                }

                # Get a sample of the data (first few rows)
                sample_size = params.get("sample_size", 10)  # Default to 10 rows
                include_table = params.get("include_table", True)  # Default to including table
                sample_df = df.head(sample_size)

                # Get basic statistics
                try:
                    desc_df = df.describe(include='all')
                    file_info["statistics"] = desc_df.to_dict()
                except Exception as e:
                    logger.warning(f"Could not generate statistics for file: {str(e)}")

                # Prepare the result with file content and metadata
                result = f"File '{os.path.basename(file_path)}' has been processed and is ready for analysis."

                # Add detailed information about the file
                result += f"\n\nFile Information:"
                result += f"\n- File name: {file_info['file_name']}"
                result += f"\n- File type: {file_info['file_extension']}"
                result += f"\n- File size: {file_info['file_size_bytes']} bytes"
                result += f"\n- Data shape: {file_info['data_shape'][0]} rows × {file_info['data_shape'][1]} columns"

                # Add column information
                result += f"\n\nColumns ({len(file_info['columns'])}):"
                for col, dtype in file_info['dtypes'].items():
                    result += f"\n- {col} ({dtype})"

                # Add the sample data in a structured format for visualization
                # Sanitize data to handle NaN, Infinity, and other special values
                metadata["file_info"] = sanitize_json(file_info)
                metadata["sample_data"] = sanitize_json(sample_df.to_dict(orient="records"))
                metadata["preview_type"] = "sample"
                metadata["preview_data"] = sanitize_json(sample_df.to_dict(orient="records"))  # Add preview_data for consistency

                # Set the table content for visualization
                preview_data = metadata["sample_data"]

                # Create a table visualization if include_table is True
                if include_table and preview_data and len(preview_data) > 0:
                    # We'll set a flag to ensure the table is included in the response
                    metadata["force_table"] = True

            elif operation == "embed":
                # This operation embeds a document using mem0ai
                # Check if the file is a document type
                file_extension = os.path.splitext(file_path)[1].lower()
                if file_extension not in ['.pdf', '.docx', '.txt']:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Unsupported file type for document embedding: {file_extension}. Supported types are PDF, DOCX, and TXT."
                            }
                        ]
                    }

                # Get user ID and persona ID from params
                user_id = params.get("user_id", "system")
                persona_id = params.get("persona_id", "unknown")

                try:
                    # Create metadata with user and persona information
                    metadata_info = {
                        "user_id": user_id,
                        "persona_id": persona_id,
                        "conversation_id": params.get("conversation_id", "unknown")
                    }

                    # Embed the document using the vector service
                    vector_store_id, file_info = self.vector_service.embed_document(
                        file_path=file_path,
                        chunk_size=params.get("chunk_size", 1000),
                        chunk_overlap=params.get("chunk_overlap", 200)
                    )

                    # Update file_info with user and persona information
                    file_info.update(metadata_info)

                    result = f"Document embedded successfully. Vector store ID: {vector_store_id}"

                    # Add document info to metadata
                    metadata["vector_store_id"] = vector_store_id
                    metadata["file_info"] = file_info

                except Exception as e:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Error embedding document: {str(e)}"
                            }
                        ]
                    }

            elif operation == "search_document":
                # This operation searches a document using mem0ai
                query = params.get("query")
                if not query:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Query parameter is required for search_document operation"
                            }
                        ]
                    }

                # Get vector store ID from params or try to find it for the file
                vector_store_id = params.get("vector_store_id")

                if not vector_store_id:
                    # Try to find the vector store ID for the file
                    # This is a simplified version - in a real implementation, you would need to
                    # look up the vector store ID from a database or metadata file
                    try:
                        # Check if there's a YAML file with metadata for this file
                        vector_db_dir = os.path.join(os.getcwd(), "vector_db")
                        for info_file in os.listdir(vector_db_dir):
                            if info_file.endswith("_info.yaml"):
                                info_path = os.path.join(vector_db_dir, info_file)
                                import yaml
                                with open(info_path, 'r') as f:
                                    file_info = yaml.safe_load(f)

                                if file_info and file_info.get("file_path") == file_path:
                                    vector_store_id = file_info.get("vector_store_id")
                                    if vector_store_id:
                                        logger.info(f"Found vector store ID for {file_path}: {vector_store_id}")
                                        break
                    except Exception as e:
                        logger.error(f"Error finding vector store ID: {e}")

                if not vector_store_id:
                    # If we still don't have a vector store ID, embed the document
                    try:
                        # Create metadata with user and persona information
                        metadata_info = {
                            "user_id": params.get("user_id", "system"),
                            "persona_id": params.get("persona_id", "unknown"),
                            "conversation_id": params.get("conversation_id", "unknown")
                        }

                        # Embed the document using the vector service
                        vector_store_id, file_info = self.vector_service.embed_document(
                            file_path=file_path,
                            chunk_size=params.get("chunk_size", 1000),
                            chunk_overlap=params.get("chunk_overlap", 200)
                        )

                        # Update file_info with user and persona information
                        file_info.update(metadata_info)

                        logger.info(f"Embedded document {file_path} with vector store ID: {vector_store_id}")
                    except Exception as e:
                        return {
                            "isError": True,
                            "content": [
                                {
                                    "type": "text",
                                    "text": f"Error embedding document: {str(e)}"
                                }
                            ]
                        }

                try:
                    # Query the document using the vector service
                    results = self.vector_service.search_document(
                        vector_store_id=vector_store_id,
                        query=query,
                        limit=params.get("limit", 5)
                    )

                    # Format the results
                    formatted_results = []
                    for result_item in results:
                        formatted_results.append({
                            "content": result_item.get("content", ""),
                            "metadata": result_item.get("metadata", {})
                        })

                    result = f"Query results for '{query}':\n\n{results[0].get('content', '') if results else 'No results found.'}"

                    # Add results to metadata
                    metadata["query"] = query
                    metadata["results"] = formatted_results
                    metadata["vector_store_id"] = vector_store_id

                except Exception as e:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Error searching document: {str(e)}"
                            }
                        ]
                    }

            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported operation: {operation}"
                        }
                    ]
                }

            # Create a table visualization for the data preview if available
            table_content = None

            # Initialize preview_data with a default empty list to avoid UnboundLocalError
            preview_data = []

            if "preview_data" in metadata:
                preview_data = metadata["preview_data"]
            elif "filtered_data" in metadata:
                preview_data = metadata["filtered_data"]
            elif "search_results" in metadata:
                preview_data = metadata["search_results"]
            elif "sample_data" in metadata:
                preview_data = metadata["sample_data"]
            elif "dataframe" in metadata and metadata["dataframe"] is not None:
                # If we have a dataframe but no preview data, generate a preview
                preview_data = []  # Initialize with empty list to avoid reference before assignment
                try:
                    df = metadata["dataframe"]
                    # Take the first 10 rows as a preview
                    preview_rows = min(10, len(df))

                    # Convert all values to Python native types to avoid serialization issues
                    df_preview = df.head(preview_rows).copy()

                    # Replace NaN values with None
                    df_preview = df_preview.where(pd.notna(df_preview), None)

                    # Convert to records with explicit string conversion for all values
                    preview_data = []
                    for _, row in df_preview.iterrows():
                        record = {}
                        for col in df_preview.columns:
                            # Convert each value to a Python native type
                            val = row[col]
                            if val is None:
                                record[col] = None
                            elif isinstance(val, (np.integer, np.int64)):
                                record[col] = int(val)
                            elif isinstance(val, (np.floating, np.float64)):
                                if np.isnan(val):
                                    record[col] = None
                                else:
                                    record[col] = float(val)
                            else:
                                record[col] = str(val)
                        preview_data.append(record)

                    metadata["preview_data"] = preview_data
                    logger.info(f"Generated preview data from dataframe with {len(preview_data)} rows")
                except Exception as e:
                    logger.error(f"Error generating preview data from dataframe: {e}")
                    # Continue with empty preview_data

            # Check if we should force include a table (for send_to_persona operation)
            force_table = metadata.get("force_table", False)

            if preview_data and len(preview_data) > 0:
                # For send_to_persona, add a message about the data preview
                if force_table and operation == "send_to_persona":
                    result += f"\n\nHere's a preview of the first {len(preview_data)} rows:"

                # Ensure all data is properly sanitized for JSON serialization
                sanitized_preview_data = sanitize_json(preview_data)

                # Make sure we have data before creating the table
                if sanitized_preview_data and len(sanitized_preview_data) > 0:
                    table_content = {
                        "type": "table",
                        "table": {
                            "headers": list(sanitized_preview_data[0].keys()),
                            "rows": [[str(row.get(col, "")) if row.get(col) is not None else ""
                                     for col in sanitized_preview_data[0].keys()]
                                    for row in sanitized_preview_data]
                        }
                    }
                else:
                    # Create an empty table with default headers if no data
                    table_content = {
                        "type": "table",
                        "table": {
                            "headers": ["No data available"],
                            "rows": [["No preview data could be generated"]]
                        }
                    }
                    logger.warning("No preview data available to create table visualization")

            # Return the result with sanitized metadata
            # Ensure all metadata is properly sanitized for JSON serialization
            sanitized_metadata = sanitize_json(metadata)

            # Remove the dataframe from metadata before returning (it's not JSON serializable)
            if "dataframe" in sanitized_metadata:
                del sanitized_metadata["dataframe"]

            response = {
                "content": [
                    {
                        "type": "text",
                        "text": result
                    }
                ],
                "metadata": sanitized_metadata
            }

            # Always add table visualization, even if it's an empty table
            # This ensures the frontend always shows a table
            if table_content:
                response["content"].append(table_content)

                # Add explicit visualization metadata for the frontend
                # This ensures the table visualization is properly displayed
                response["metadata"]["visualization"] = {
                    "type": "table",
                    "title": f"Data Preview: {os.path.basename(file_path)}",
                    "description": f"Preview of data from {os.path.basename(file_path)}",
                    "data": table_content.get("table", {})
                }
                logger.info(f"Added visualization metadata to response")

            return response

        except Exception as e:
            logger.error(f"Error in data access tool: {e}", exc_info=True)
            error_message = f"Error accessing data: {str(e)}"

            # Add more detailed error information for debugging
            if hasattr(e, "__cause__") and e.__cause__:
                error_message += f" Caused by: {str(e.__cause__)}"

            # Create error metadata and sanitize it
            error_metadata = {
                "error_type": e.__class__.__name__,
                "error_details": str(e),
                "component": "DataAccessTool",
                "data_source": str(data_source) if data_source else "None",
                "operation": operation if operation else "None"
            }

            # Sanitize the error metadata
            sanitized_error_metadata = sanitize_json(error_metadata)

            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": error_message
                    }
                ],
                "metadata": sanitized_error_metadata
            }

    async def _find_and_load_data(self, data_source: Any) -> Tuple[Optional[str], Optional[pd.DataFrame]]:
        """
        Find and load data from the given data source.

        Args:
            data_source: Data source information

        Returns:
            Tuple of (file_path, dataframe) if successful, or (None, None) if not
        """
        # Extract file information from data source
        file_id = None
        file_name = None
        file_path_direct = None

        # Try to get file ID and name from different possible locations
        if isinstance(data_source, dict):
            # Direct ID and name - check all common ID field names
            file_id = (data_source.get("id") or
                      data_source.get("file_id") or
                      data_source.get("fileId") or
                      data_source.get("dataSourceId"))

            file_name = data_source.get("name", "your data")

            # Check for direct file path
            file_path_direct = (data_source.get("file_path") or
                               data_source.get("filePath") or
                               data_source.get("path"))

            # Check in source_metadata - this is the most reliable location for file IDs
            if "source_metadata" in data_source:
                source_metadata = data_source.get("source_metadata", {})
                if isinstance(source_metadata, dict):
                    # Try multiple possible ID field names
                    source_file_id = (source_metadata.get("file_id") or
                                     source_metadata.get("id") or
                                     source_metadata.get("fileId") or
                                     source_metadata.get("dataId"))

                    if source_file_id:
                        file_id = source_file_id
                        logger.info(f"Found file ID in source_metadata: {file_id}")

                    if not file_name:
                        file_name = (source_metadata.get("file_name") or
                                    source_metadata.get("name") or
                                    source_metadata.get("fileName") or
                                    "your data")

            # Check in metadata
            if not file_id and "metadata" in data_source:
                metadata = data_source.get("metadata", {})
                if isinstance(metadata, dict):
                    metadata_file_id = (metadata.get("file_id") or
                                       metadata.get("id") or
                                       metadata.get("fileId") or
                                       metadata.get("dataId"))

                    if metadata_file_id:
                        file_id = metadata_file_id
                        logger.info(f"Found file ID in metadata: {file_id}")

                    if not file_name:
                        file_name = (metadata.get("file_name") or
                                    metadata.get("name") or
                                    metadata.get("fileName") or
                                    "your data")

        # If we still don't have a file ID, try to use the data source itself as an ID
        if not file_id and isinstance(data_source, (str, int)):
            file_id = str(data_source)
            file_name = "your data"

        logger.info(f"Extracted file_id: {file_id}, file_name: {file_name}, direct_path: {file_path_direct}")

        # Initialize possible file paths list
        possible_file_paths = []

        # Log the extracted information
        logger.info(f"File resolution - ID: {file_id}, Name: {file_name}, Direct path: {file_path_direct}")

        # PRIORITY 1: If we have a file ID, try various combinations FIRST
        # This is the most reliable way to find the correct file
        if file_id:
            logger.info(f"Prioritizing file ID-based paths for ID: {file_id}")

            # Try common file ID patterns in all search directories
            for directory in self.search_dirs:
                # Try with various extensions
                for ext in self.supported_extensions:
                    # Common file ID patterns
                    id_patterns = [
                        f"{file_id}{ext}",                # Direct ID with extension
                        f"file_{file_id}{ext}",           # file_ID pattern
                        f"upload_{file_id}{ext}",         # upload_ID pattern
                        f"data_{file_id}{ext}",           # data_ID pattern
                        f"attachment_{file_id}{ext}"      # attachment_ID pattern
                    ]

                    # Add all ID patterns to possible paths
                    for pattern in id_patterns:
                        path = os.path.join(directory, pattern)
                        possible_file_paths.append(path)
                        logger.debug(f"Added ID-based path: {path}")

        # PRIORITY 2: If we have a direct file path, use it next
        if file_path_direct:
            logger.info(f"Adding direct file path: {file_path_direct}")
            possible_file_paths.append(file_path_direct)

            # Also try with different base directories
            if not os.path.isabs(file_path_direct):
                for directory in self.search_dirs:
                    path = os.path.join(directory, file_path_direct)
                    possible_file_paths.append(path)
                    logger.debug(f"Added direct path with directory: {path}")

        # PRIORITY 3: If we have a file name, try with that too
        if file_name and file_name != "your data":
            logger.info(f"Adding name-based paths for: {file_name}")

            # Clean the file name (remove spaces, special chars)
            clean_name = file_name.replace(" ", "_").lower()

            for directory in self.search_dirs:
                # If file_name contains extension, try it directly
                if "." in file_name:
                    possible_file_paths.append(os.path.join(directory, file_name))
                    possible_file_paths.append(os.path.join(directory, clean_name))
                # Otherwise try with common extensions
                else:
                    for ext in self.supported_extensions:
                        possible_file_paths.append(os.path.join(directory, f"{file_name}{ext}"))
                        possible_file_paths.append(os.path.join(directory, f"{clean_name}{ext}"))

        # Log file search parameters
        logger.info(f"Searching for file with ID: {file_id}, Name: {file_name}")

        # Check for recently uploaded files in all search directories
        for directory in self.search_dirs:
            if os.path.exists(directory):
                try:
                    # List all files in the directory
                    files = [os.path.join(directory, f) for f in os.listdir(directory)
                            if os.path.isfile(os.path.join(directory, f))]

                    # Filter for data files with supported extensions
                    data_files = [f for f in files if any(f.endswith(ext) for ext in self.supported_extensions)]

                    if not data_files:
                        continue

                    # If we have a file name, add files that match the name pattern
                    if file_name and file_name != "your data":
                        name_matches = [f for f in data_files if file_name.lower() in os.path.basename(f).lower()]
                        for match in name_matches:
                            if match not in possible_file_paths:
                                possible_file_paths.append(match)
                                logger.info(f"Added name-matched file: {match}")

                    # Sort by modification time (most recent first)
                    data_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

                    # Add the most recent file to our list of possibilities
                    if data_files and data_files[0] not in possible_file_paths:
                        possible_file_paths.append(data_files[0])
                        logger.info(f"Added most recent data file from {directory}: {data_files[0]}")

                except Exception as e:
                    logger.error(f"Error listing files in {directory}: {str(e)}")

        # Log all possible file paths for debugging
        logger.info(f"Possible file paths to check: {possible_file_paths}")

        # Remove duplicates while preserving order
        possible_file_paths = list(dict.fromkeys(possible_file_paths))

        # Log all possible paths we're checking
        logger.info(f"Checking the following possible file paths: {possible_file_paths}")

        # Get current working directory for reference
        cwd = os.getcwd()
        logger.info(f"Current working directory: {cwd}")

        # Check if any of the possible paths exist
        file_path = None

        # Group paths by type for better logging
        id_based_paths = [p for p in possible_file_paths if file_id and (file_id in os.path.basename(p))]
        direct_paths = [p for p in possible_file_paths if file_path_direct and (file_path_direct in p)]
        name_based_paths = [p for p in possible_file_paths if p not in id_based_paths and p not in direct_paths]

        # Log the number of paths in each category
        logger.info(f"Checking {len(id_based_paths)} ID-based paths, {len(direct_paths)} direct paths, and {len(name_based_paths)} name-based paths")

        # First check ID-based paths (highest priority)
        if id_based_paths:
            logger.info(f"Checking ID-based paths first for ID: {file_id}")
            for path in id_based_paths:
                # Try both as-is and with absolute path
                paths_to_check = [path]
                if not os.path.isabs(path):
                    abs_path = os.path.abspath(path)
                    paths_to_check.append(abs_path)

                for check_path in paths_to_check:
                    if os.path.exists(check_path):
                        file_path = check_path
                        logger.info(f"✓ Found data file by ID at path: {file_path}")
                        break
                    else:
                        logger.debug(f"✗ ID-based path does not exist: {check_path}")

                if file_path:
                    break

        # Then check direct paths if no file found yet
        if not file_path and direct_paths:
            logger.info("Checking direct file paths")
            for path in direct_paths:
                # Try both as-is and with absolute path
                paths_to_check = [path]
                if not os.path.isabs(path):
                    abs_path = os.path.abspath(path)
                    paths_to_check.append(abs_path)

                for check_path in paths_to_check:
                    if os.path.exists(check_path):
                        file_path = check_path
                        logger.info(f"✓ Found data file by direct path: {file_path}")
                        break
                    else:
                        logger.debug(f"✗ Direct path does not exist: {check_path}")

                if file_path:
                    break

        # Finally check name-based paths if still no file found
        if not file_path and name_based_paths:
            logger.info("Checking name-based paths")
            for path in name_based_paths:
                # Try both as-is and with absolute path
                paths_to_check = [path]
                if not os.path.isabs(path):
                    abs_path = os.path.abspath(path)
                    paths_to_check.append(abs_path)

                for check_path in paths_to_check:
                    if os.path.exists(check_path):
                        file_path = check_path
                        logger.info(f"✓ Found data file by name at path: {file_path}")
                        break
                    else:
                        logger.debug(f"✗ Name-based path does not exist: {check_path}")

                if file_path:
                    break

        # If no file found, search for recently uploaded files
        if not file_path:
            logger.warning(f"No file found for ID {file_id} or name {file_name}")

            # Prioritize the backend/temp_uploads directory
            priority_dirs = [
                os.path.join(self.backend_dir, "temp_uploads"),
                self.abs_upload_dir
            ]

            # Add priority directories to the beginning of search_dirs for this search
            search_dirs = priority_dirs + [d for d in self.search_dirs if d not in priority_dirs]
            logger.info(f"Searching for recently uploaded files in these directories (in order): {search_dirs}")

            # Enhanced file search - prioritize ID-based search first, then name-based
            # First try ID-based search
            if file_id:
                logger.info(f"Enhanced search for file with ID: {file_id}")
                for directory in search_dirs:
                    if os.path.exists(directory):
                        try:
                            logger.info(f"Checking directory for ID-matched files: {directory}")
                            all_files = [os.path.join(directory, f) for f in os.listdir(directory)
                                        if os.path.isfile(os.path.join(directory, f))]

                            # Filter for supported file types
                            data_files = [f for f in all_files if any(f.lower().endswith(ext) for ext in self.supported_extensions)]

                            # Look for any file with the ID in the filename
                            id_matched_files = [f for f in data_files if file_id in os.path.basename(f)]
                            if id_matched_files:
                                logger.info(f"Found files matching ID '{file_id}': {id_matched_files}")
                                # Sort by modification time (most recent first)
                                id_matched_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                                file_path = id_matched_files[0]
                                logger.info(f"Using most recent ID-matched file: {file_path}")
                                break
                        except Exception as e:
                            logger.error(f"Error during enhanced file ID search in {directory}: {str(e)}")

                # If we found a file by ID, we can skip the rest of the search
                if file_path:
                    logger.info(f"Found file via enhanced ID search: {file_path}")
                    # Continue to the data loading section
                else:
                    logger.warning(f"No file found matching ID '{file_id}', trying name-based search")

            # Then try name-based search if ID search failed
            if not file_path and file_name and file_name != "your data":
                logger.info(f"Enhanced search for file with name: {file_name}")
                for directory in search_dirs:
                    if os.path.exists(directory):
                        try:
                            logger.info(f"Checking directory for name-matched files: {directory}")
                            all_files = [os.path.join(directory, f) for f in os.listdir(directory)
                                        if os.path.isfile(os.path.join(directory, f))]

                            # Filter for supported file types
                            data_files = [f for f in all_files if any(f.lower().endswith(ext) for ext in self.supported_extensions)]

                            # Look for any file with the specified name
                            name_matched_files = [f for f in data_files if file_name.lower() in os.path.basename(f).lower()]
                            if name_matched_files:
                                logger.info(f"Found files matching name '{file_name}': {name_matched_files}")
                                # Sort by modification time (most recent first)
                                name_matched_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                                file_path = name_matched_files[0]
                                logger.info(f"Using most recent name-matched file: {file_path}")
                                break
                        except Exception as e:
                            logger.error(f"Error during enhanced file name search in {directory}: {str(e)}")

                # If we found a file by name, we can skip the rest of the search
                if file_path:
                    logger.info(f"Found file via enhanced name search: {file_path}")
                    # Continue to the data loading section
                else:
                    logger.warning(f"No file found matching name '{file_name}', continuing with regular search")

            # Continue with regular search if no file found by name
            recent_files = []
            if not file_path:  # Only continue if we haven't found a file yet
                for directory in search_dirs:
                    if os.path.exists(directory):
                        try:
                            logger.info(f"Checking directory: {directory}")
                            # List all files in the directory
                            files = [os.path.join(directory, f) for f in os.listdir(directory)
                                    if os.path.isfile(os.path.join(directory, f))]

                            # Filter for data files
                            data_files = [f for f in files if any(f.endswith(ext) for ext in self.supported_extensions)]

                            if not data_files:
                                logger.info(f"No supported data files found in {directory}")
                                continue

                            logger.info(f"Found {len(data_files)} data files in {directory}")

                            # If we have a file name, prioritize files with matching names
                            if file_name and file_name != "your data":
                                # Check for files with matching name (case insensitive)
                                matching_files = [f for f in data_files if file_name.lower() in os.path.basename(f).lower()]
                                if matching_files:
                                    logger.info(f"Found files matching name '{file_name}': {matching_files}")
                                    # Add matching files to the beginning of our list
                                    recent_files.extend(matching_files)
                                    # If we found matching files, we can stop searching
                                    if recent_files:
                                        break

                                # Sort by modification time (most recent first)
                                data_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

                                # Add to our list
                                recent_files.extend(data_files)

                                # If we found files in this directory, we can stop searching
                                # This prioritizes files in directories that come first in the search_dirs list
                                if recent_files:
                                    logger.info(f"Found files in {directory}, stopping search")
                                    break
                        except Exception as e:
                            logger.error(f"Error listing files in {directory}: {str(e)}")

            # If we found any files, use the most recent one
            if recent_files:
                file_path = recent_files[0]
                logger.info(f"Using most recently modified file: {file_path}")
            else:
                logger.warning("No suitable data files found in any search directory")

        # If still no file found, log a clear error
        if not file_path:
            logger.warning("No data file found that matches the requested criteria")

        # If we have a file path, try to load the data
        if file_path:
            try:
                # Load the data based on file extension
                file_extension = file_path.lower()

                if file_extension.endswith('.csv'):
                    df = pd.read_csv(file_path)
                    logger.info(f"Loaded CSV file with shape: {df.shape}")
                elif file_extension.endswith(('.xlsx', '.xls')):
                    df = pd.read_excel(file_path)
                    logger.info(f"Loaded Excel file with shape: {df.shape}")
                elif file_extension.endswith('.json'):
                    df = pd.read_json(file_path)
                    logger.info(f"Loaded JSON file with shape: {df.shape}")
                elif file_extension.endswith(('.pdf', '.docx', '.txt')):
                    # For document files, create a simple dataframe with file info
                    # This allows us to use the same interface for all file types
                    file_info = {
                        "file_path": file_path,
                        "file_name": os.path.basename(file_path),
                        "file_extension": os.path.splitext(file_path)[1].lower(),
                        "file_size_bytes": os.path.getsize(file_path),
                        "last_modified": os.path.getmtime(file_path)
                    }

                    # Create a dataframe with file info
                    df = pd.DataFrame([file_info])
                    logger.info(f"Loaded document file: {file_path}")
                else:
                    logger.error(f"Unsupported file format: {file_path}")
                    return None, None

                return file_path, df
            except Exception as e:
                logger.error(f"Error loading data from {file_path}: {str(e)}", exc_info=True)
                return None, None

        return None, None


