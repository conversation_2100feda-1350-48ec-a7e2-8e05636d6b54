
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, AlertCircle, Megaphone, Info } from "lucide-react";
import { Button } from "@/components/ui/button";

interface Notification {
  id: string;
  type: "info" | "warning" | "critical";
  title: string;
  message: string;
  time: string;
}

interface NotificationsPanelProps {
  className?: string;
}

export function NotificationsPanel({ className = "" }: NotificationsPanelProps) {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: "1",
      type: "info",
      title: "New feature available",
      message: "You can now customize your dashboard layout.",
      time: "Just now",
    },
    {
      id: "2",
      type: "warning",
      title: "Subscription expiring soon",
      message: "Your subscription will expire in 7 days.",
      time: "2 hours ago",
    },
    {
      id: "3",
      type: "critical",
      title: "System maintenance",
      message: "Scheduled maintenance on June 15th from 2-4 AM.",
      time: "1 day ago",
    },
  ]);

  const getIcon = (type: Notification["type"]) => {
    switch (type) {
      case "info":
        return <Info className="h-5 w-5 text-blue-500" />;
      case "warning":
        return <Megaphone className="h-5 w-5 text-yellow-500" />;
      case "critical":
        return <AlertCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const dismissNotification = (id: string) => {
    setNotifications(notifications.filter(notification => notification.id !== id));
  };

  if (notifications.length === 0) {
    return (
      <div className={`p-4 text-center text-muted-foreground ${className}`}>
        No new notifications
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="mb-2 last:mb-0"
          >
            <div className="flex items-start p-3 rounded-md bg-muted/50 hover:bg-muted/70 transition-colors">
              <div className="mr-3 mt-0.5">{getIcon(notification.type)}</div>
              <div className="flex-1">
                <div className="font-medium">{notification.title}</div>
                <div className="text-sm text-muted-foreground">{notification.message}</div>
                <div className="text-xs text-muted-foreground mt-1">{notification.time}</div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 -mr-1 -mt-1"
                onClick={() => dismissNotification(notification.id)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}
