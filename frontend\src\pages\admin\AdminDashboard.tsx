import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { adminApi, AdminDashboardStats } from '@/lib/adminApi';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Users,
  Bot,
  DollarSign,
  ShoppingCart,
  ArrowRight,
  Loader2,
  AlertCircle,
} from 'lucide-react';
import { motion } from 'framer-motion';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<AdminDashboardStats | null>(null);

  const { data, isLoading, error } = useQuery({
    queryKey: ['adminDashboardStats'],
    queryFn: () => adminApi.getDashboardStats(),
  });

  useEffect(() => {
    if (data) {
      setStats(data);
    }
  }, [data]);

  const statCards = [
    {
      title: 'Total Users',
      value: stats?.total_users || 0,
      icon: Users,
      color: 'bg-blue-500',
      onClick: () => navigate('/admin/users'),
    },
    {
      title: 'Active Users',
      value: stats?.active_users || 0,
      icon: Users,
      color: 'bg-green-500',
      onClick: () => navigate('/admin/users?is_active=true'),
    },
    {
      title: 'Total Personas',
      value: stats?.total_personas || 0,
      icon: Bot,
      color: 'bg-purple-500',
      onClick: () => navigate('/admin/personas'),
    },
    {
      title: 'Active Personas',
      value: stats?.active_personas || 0,
      icon: Bot,
      color: 'bg-indigo-500',
      onClick: () => navigate('/admin/personas?is_active=true'),
    },
    {
      title: 'Total Purchases',
      value: stats?.total_purchases || 0,
      icon: ShoppingCart,
      color: 'bg-amber-500',
      onClick: () => navigate('/admin/analytics'),
    },
    {
      title: 'Total Revenue',
      value: `$${stats?.total_revenue?.toFixed(2) || '0.00'}`,
      icon: DollarSign,
      color: 'bg-emerald-500',
      onClick: () => navigate('/admin/analytics'),
    },
    {
      title: 'Recent Purchases (30d)',
      value: stats?.recent_purchases || 0,
      icon: ShoppingCart,
      color: 'bg-orange-500',
      onClick: () => navigate('/admin/analytics'),
    },
    {
      title: 'Recent Revenue (30d)',
      value: `$${stats?.recent_revenue?.toFixed(2) || '0.00'}`,
      icon: DollarSign,
      color: 'bg-teal-500',
      onClick: () => navigate('/admin/analytics'),
    },
  ];

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <AlertCircle className="h-12 w-12 text-destructive mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Dashboard</h2>
          <p className="text-muted-foreground mb-4">
            There was a problem loading the dashboard data.
          </p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => window.location.reload()}>
              Refresh
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((card, index) => (
            <motion.div
              key={card.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={card.onClick}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    {card.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center">
                    <div className="text-2xl font-bold">{card.value}</div>
                    <div className={`p-2 rounded-full ${card.color}`}>
                      <card.icon className="h-5 w-5 text-white" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  className="w-full justify-between"
                  onClick={() => navigate('/admin/personas')}
                >
                  <span>Manage AI Personas</span>
                  <ArrowRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-between"
                  onClick={() => navigate('/admin/users')}
                >
                  <span>Manage Users</span>
                  <ArrowRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-between"
                  onClick={() => navigate('/admin/analytics')}
                >
                  <span>View Analytics</span>
                  <ArrowRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-between"
                  onClick={() => navigate('/admin/activity')}
                >
                  <span>View Activity Logs</span>
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Database</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                    Healthy
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>API Services</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                    Operational
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>AI Providers</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                    Connected
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Storage</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                    85% Available
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
