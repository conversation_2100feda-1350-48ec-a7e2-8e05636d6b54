import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { X, Copy, Check, Download, ChevronDown, RefreshCw } from 'lucide-react';
import { ClassificationConfigForm, ClassificationConfig } from '@/components/classification/ClassificationConfigForm';
import { ClassificationResultsVisualization, ClassificationResult } from '@/components/classification/ClassificationResultsVisualization';
import { MarketingContentForm, MarketingContentFormData } from '@/components/marketing/MarketingContentForm';
import { Persona } from '@/lib/api';
import { useProviders } from '@/hooks/useProviders';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface SpecializedComponentRendererProps {
  persona: Persona | null;
  onClose: () => void;
  onSubmitClassification?: (config: ClassificationConfig) => Promise<void>;
  onSubmitMarketingContent?: (data: MarketingContentFormData) => Promise<void>;
  classificationResults?: ClassificationResult[];
  classificationConfig?: ClassificationConfig | null;
  generatedMarketingContent?: string;
  isLoading?: boolean;
}

// Function to format markdown-like content with enhanced styling
const formatContentForDisplay = (rawContent: string) => {
  if (!rawContent) return '';

  // First, let's identify and wrap list sections to properly handle them
  let processedContent = rawContent;

  // Process special case for bold text with colon (like "**Digital Marketing:**")
  processedContent = processedContent.replace(/\*\*([^*:]+):\*\*/g, '<span class="font-semibold text-brand-700">$1:</span>');

  // Process regular inline bold text (text between ** **)
  processedContent = processedContent.replace(/\*\*([^*]+)\*\*/g, '<strong class="font-bold text-gray-900">$1</strong>');

  // Find consecutive list items and wrap them in <ul> or <ol> tags
  const wrapLists = (content: string) => {
    // First, handle unordered lists (- or *)
    let result = content.replace(/((^|\n)[\s]*[-*][\s]+.+\n?)+/gm, (match) => {
      const listItems = match.split('\n')
        .filter(line => line.trim().match(/^[-*][\s]+/))
        .map(line => line.trim().replace(/^[-*][\s]+/, ''))
        .map(item => `<li class="py-1">${item}</li>`)
        .join('');
      return `<ul class="list-disc pl-8 my-4 space-y-1">${listItems}</ul>`;
    });

    // Then, handle ordered lists (1. 2. etc)
    result = result.replace(/((^|\n)[\s]*\d+\.[\s]+.+\n?)+/gm, (match) => {
      const listItems = match.split('\n')
        .filter(line => line.trim().match(/^\d+\.[\s]+/))
        .map(line => line.trim().replace(/^\d+\.[\s]+/, ''))
        .map(item => `<li class="py-1">${item}</li>`)
        .join('');
      return `<ol class="list-decimal pl-8 my-4 space-y-1">${listItems}</ol>`;
    });

    return result;
  };

  // Process lists first
  processedContent = wrapLists(processedContent);

  // Now process the rest of the content
  let formattedContent = processedContent
    // Replace markdown headings with styled HTML
    .replace(/^# (.*?)$/gm, '<h1 class="text-2xl font-bold mt-8 mb-4 pb-2 border-b border-gray-200">$1</h1>')
    .replace(/^## (.*?)$/gm, '<h2 class="text-xl font-bold mt-6 mb-3 text-gray-800">$1</h2>')
    .replace(/^### (.*?)$/gm, '<h3 class="text-lg font-semibold mt-5 mb-2 text-gray-700">$1</h3>')

    // Handle any remaining list items that weren't caught in the list wrapping
    // (these would be isolated list items not part of a sequence)
    .replace(/^[-*] (.*?)$/gm, '<div class="flex items-start my-2"><span class="mr-2 mt-1">•</span><span>$1</span></div>')
    .replace(/^(\d+)\. (.*?)$/gm, '<div class="flex items-start my-2"><span class="mr-2 font-medium">$1.</span><span>$2</span></div>')

    // Handle special formatting for section headers and keyword sections
    .replace(/"(\*\*[^*]+\*\*)"(.*?)$/gm, '<div class="font-semibold text-gray-800 mt-4">$1$2</div>')
    .replace(/^##\s+([^#]+)(.*?)$/gm, '<div class="font-semibold text-brand-700 text-lg mt-6 mb-3 pb-1 border-b border-gray-200">$1$2</div>')

    // Add special styling for bullet points with section headers (like "Campaign Elements:", "Channels:", etc.)
    .replace(/^• ([^:]+):(.*)$/gm, '<div class="mt-5 mb-3"><span class="font-semibold text-brand-800 text-base">$1:$2</span></div>')

    // Replace plus signs with a more visually appealing campaign element styling
    .replace(/^\+ (.*?)$/gm, '<div class="flex items-start my-3 ml-4 p-2 pl-3 rounded-md bg-brand-50 border-l-4 border-brand-400 hover:bg-brand-100 transition-colors"><span class="text-gray-800 font-medium">$1</span></div>')

    // Replace double newlines with paragraph breaks
    .replace(/\n\n/g, '</p><p class="my-3">')

    // Preserve single newlines
    .replace(/\n/g, '<br />');

  // Wrap in a paragraph if not already wrapped in a block element
  if (!formattedContent.match(/^<(h1|h2|h3|ul|ol|div|p)/)) {
    formattedContent = `<p class="my-3">${formattedContent}</p>`;
  }

  return formattedContent;
};

export function SpecializedComponentRenderer({
  persona,
  onClose,
  onSubmitClassification,
  onSubmitMarketingContent,
  classificationResults = [],
  classificationConfig = null,
  generatedMarketingContent = '',
  isLoading = false,
}: SpecializedComponentRendererProps) {
  const [activeTab, setActiveTab] = useState<'form' | 'results'>('form');
  const [isCopied, setIsCopied] = useState(false);
  const { providers } = useProviders();
  const { toast } = useToast();

  // Handle copy to clipboard
  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedMarketingContent);
      setIsCopied(true);
      toast({
        title: "Copied to clipboard",
        description: "Content has been copied to your clipboard",
      });
      setTimeout(() => setIsCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
      toast({
        title: "Copy failed",
        description: "Failed to copy content to clipboard",
        variant: "destructive",
      });
    }
  };

  // Handle download as markdown
  const handleDownloadMarkdown = () => {
    const blob = new Blob([generatedMarketingContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'marketing-content.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Handle download as DOCX
  const handleDownloadDocx = async () => {
    try {
      // Call the API to convert markdown to DOCX
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';
      const response = await fetch(`${API_BASE_URL}/docx/convert-to-docx`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: generatedMarketingContent,
          title: 'Generated Marketing Content'
        }),
      });

      if (!response.ok) {
        // Try to get more detailed error information
        let errorDetail = response.statusText;
        try {
          const errorData = await response.json();
          errorDetail = errorData.detail || errorData.message || errorDetail;
        } catch (e) {
          // If we can't parse the error response, just use the status text
        }
        console.error(`DOCX conversion failed: ${response.status} - ${errorDetail}`);
        throw new Error(`Server responded with ${response.status}: ${errorDetail}`);
      }

      const data = await response.json();

      // Convert base64 to blob
      const binaryString = atob(data.base64_docx);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const blob = new Blob([bytes], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });

      // Create download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = data.filename || 'marketing-content.docx';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Downloaded as DOCX",
        description: "Content has been downloaded as a Word document",
      });
    } catch (err) {
      console.error('Failed to download as DOCX: ', err);
      toast({
        title: "Download failed",
        description: "Failed to download content as DOCX",
        variant: "destructive",
      });
    }
  };

  // If no persona is selected, don't render anything
  if (!persona) {
    return null;
  }

  // Determine which specialized component to render based on persona capabilities
  const hasClassificationCapability = persona.capabilities?.some(cap =>
    cap.includes('classification') || cap.includes('text_classification')
  );

  const hasMarketingCapability = persona.capabilities?.some(cap =>
    cap.includes('marketing') || cap.includes('content_generation')
  );

  // Mock models for the form
  const mockModels = [
    { id: 'llama3-70b-8192', name: 'Llama 3 70B' },
    { id: 'gpt-4o', name: 'GPT-4o' },
    { id: 'claude-3-opus', name: 'Claude 3 Opus' },
    { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' }
  ];

  return (
    <Card className="w-full mb-4 relative">
      <Button
        variant="ghost"
        size="icon"
        className="absolute right-2 top-2 z-10"
        onClick={onClose}
      >
        <X className="h-4 w-4" />
      </Button>
      <CardContent className="pt-6">
        {hasClassificationCapability && (
          <div className="space-y-4">
            {classificationResults.length > 0 && classificationConfig ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex space-x-2">
                    <Button
                      variant={activeTab === 'form' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveTab('form')}
                    >
                      Configure
                    </Button>
                    <Button
                      variant={activeTab === 'results' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveTab('results')}
                    >
                      Results
                    </Button>
                  </div>
                </div>

                {activeTab === 'form' ? (
                  <ClassificationConfigForm
                    onSubmit={onSubmitClassification || (() => Promise.resolve())}
                    defaultValues={classificationConfig}
                    isLoading={isLoading}
                  />
                ) : (
                  <ClassificationResultsVisualization
                    results={classificationResults}
                    classificationType={classificationConfig.classification_type}
                  />
                )}
              </div>
            ) : (
              <ClassificationConfigForm
                onSubmit={onSubmitClassification || (() => Promise.resolve())}
                isLoading={isLoading}
              />
            )}
          </div>
        )}

        {hasMarketingCapability && (
          <div className="space-y-4">
            {generatedMarketingContent ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex space-x-2">
                    <Button
                      variant={activeTab === 'form' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveTab('form')}
                    >
                      Configure
                    </Button>
                    <Button
                      variant={activeTab === 'results' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveTab('results')}
                    >
                      Results
                    </Button>
                  </div>
                </div>

                {activeTab === 'form' ? (
                  <MarketingContentForm
                    onSubmit={onSubmitMarketingContent || (() => Promise.resolve())}
                    isLoading={isLoading}
                    providers={providers}
                    models={mockModels}
                  />
                ) : (
                  <Card className="shadow-md border-gray-200">
                    <CardHeader className="bg-gradient-to-r from-white to-gray-50 border-b border-gray-100">
                      <CardTitle className="text-brand-700">Generated Marketing Content</CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                      <div
                        className="bg-white p-6 rounded-md overflow-auto max-h-[500px] prose prose-slate max-w-none text-base leading-relaxed"
                      >
                        <div
                          className="prose-headings:text-brand-800 prose-h1:border-b prose-h1:border-gray-200 prose-h1:pb-2
                                    prose-p:text-gray-700 prose-p:leading-relaxed prose-p:my-3
                                    prose-ul:pl-6 prose-ol:pl-6 prose-li:my-1 prose-li:text-gray-700
                                    prose-strong:text-gray-800 prose-strong:font-semibold"
                          dangerouslySetInnerHTML={{ __html: formatContentForDisplay(generatedMarketingContent) }}
                        />
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between border-t border-gray-100 bg-gray-50 py-3 px-4">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCopyToClipboard}
                          className={cn(
                            "flex items-center gap-1 bg-white",
                            isCopied && "text-green-600 border-green-200"
                          )}
                        >
                          {isCopied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                          {isCopied ? "Copied" : "Copy"}
                        </Button>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="flex items-center gap-1 bg-white">
                              <Download className="h-4 w-4" />
                              Download
                              <ChevronDown className="h-4 w-4 ml-1" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem onClick={handleDownloadMarkdown}>
                              Download as Markdown (.md)
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={handleDownloadDocx}>
                              Download as Word (.docx)
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardFooter>
                  </Card>
                )}
              </div>
            ) : (
              <MarketingContentForm
                onSubmit={onSubmitMarketingContent || (() => Promise.resolve())}
                isLoading={isLoading}
                providers={providers}
                models={mockModels}
              />
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
