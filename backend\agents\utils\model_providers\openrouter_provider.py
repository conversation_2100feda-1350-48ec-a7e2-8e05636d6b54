"""
OpenRouter model provider for the Datagenius backend.

This module provides a model provider implementation for OpenRouter,
which provides access to multiple AI models through a single API.
"""

import logging
import requests
from typing import Dict, Any, List, Union, Optional
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.language_models.chat_models import BaseChatModel

from .base import BaseModelProvider
from .exceptions import ModelInitializationError, ModelNotFoundError
from .config import get_provider_config

# Configure logging
logger = logging.getLogger(__name__)


class OpenRouterProvider(BaseModelProvider):
    """Model provider implementation for OpenRouter."""

    @property
    def provider_id(self) -> str:
        """Get the provider ID."""
        return "openrouter"

    @property
    def provider_name(self) -> str:
        """Get the provider name."""
        return "OpenRouter"

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the provider with configuration.

        Args:
            config: Configuration dictionary for the provider
        """
        await super().initialize(config)

        # Get provider configuration
        provider_config = get_provider_config("openrouter")

        # Set default model if not specified
        if not self._default_model_id:
            self._default_model_id = provider_config.get("default_model", "openai/gpt-3.5-turbo")

        # Set default endpoint if not specified
        if not self._endpoint:
            self._endpoint = provider_config.get("endpoint", "https://openrouter.ai/api/v1")

        # Set API key from configuration if not specified
        if not self._api_key:
            self._api_key = provider_config.get("api_key", "")

        # Check if we have an API key
        if not self._api_key:
            logger.warning("No OpenRouter API key provided")

    async def _initialize_model(self, model_id: str, config: Dict[str, Any]) -> Union[BaseLanguageModel, BaseChatModel]:
        """
        Initialize a model instance.

        Args:
            model_id: ID of the model to initialize
            config: Configuration for the model

        Returns:
            Initialized model instance

        Raises:
            ModelInitializationError: If there's an error initializing the model
            ModelNotFoundError: If the model is not found
        """
        try:
            # Check if the model exists
            models = await self.list_models()
            model_exists = any(model["id"] == model_id for model in models)

            if not model_exists:
                logger.warning(f"Model '{model_id}' not found in OpenRouter models list, but will try anyway")

            # Import here to avoid hard dependencies
            try:
                from langchain_openai import ChatOpenAI
                import httpx # Import httpx
            except ImportError:
                logger.warning("langchain-openai or httpx not installed, attempting to install...")
                import subprocess
                subprocess.check_call(["pip", "install", "langchain-openai", "httpx"])
                from langchain_openai import ChatOpenAI
                import httpx

            # Configure custom synchronous httpx client with required headers for OpenRouter
            openrouter_headers = {
                "HTTP-Referer": config.get("referer", "https://datagenius.app"),
                "X-Title": config.get("title", "Datagenius App")
            }
            # Use synchronous client
            sync_http_client = httpx.Client(headers=openrouter_headers)

            # Initialize the model using standard parameters
            # ChatOpenAI should handle the base URL and API key for OpenAI-compatible endpoints
            model = ChatOpenAI(
                temperature=config.get("temperature", 0.7),
                model=model_id,
                openai_api_key=self._api_key,
                openai_api_base=f"{self._endpoint}"
                # Removed http_client parameter
            )

            logger.info(f"Initialized OpenRouter model '{model_id}' using standard ChatOpenAI parameters")
            return model # Corrected indentation

        except ImportError as e:
            raise ModelInitializationError(f"Error importing OpenAI for OpenRouter: {str(e)}")
        except Exception as e:
            raise ModelInitializationError(f"Error initializing OpenRouter model '{model_id}': {str(e)}")

    async def _fetch_models(self) -> List[Dict[str, Any]]:
        """
        Fetch available models from OpenRouter.

        Returns:
            List of model metadata dictionaries
        """
        if not self._api_key:
            # Return a static list of known models if no API key
            return [
                {
                    "id": "openai/gpt-3.5-turbo",
                    "name": "GPT-3.5 Turbo",
                    "description": "OpenAI's GPT-3.5 Turbo model",
                    "context_length": 16385,
                    "provider": "openrouter"
                },
                {
                    "id": "openai/gpt-4",
                    "name": "GPT-4",
                    "description": "OpenAI's GPT-4 model",
                    "context_length": 8192,
                    "provider": "openrouter"
                },
                {
                    "id": "anthropic/claude-3-opus",
                    "name": "Claude 3 Opus",
                    "description": "Anthropic's Claude 3 Opus model",
                    "context_length": 200000,
                    "provider": "openrouter"
                },
                {
                    "id": "anthropic/claude-3-sonnet",
                    "name": "Claude 3 Sonnet",
                    "description": "Anthropic's Claude 3 Sonnet model",
                    "context_length": 200000,
                    "provider": "openrouter"
                },
                {
                    "id": "meta-llama/llama-3-70b-instruct",
                    "name": "Llama 3 70B",
                    "description": "Meta's Llama 3 70B model",
                    "context_length": 8192,
                    "provider": "openrouter"
                }
            ]

        try:
            # Make request to OpenRouter API
            headers = {
                "Authorization": f"Bearer {self._api_key}"
            }

            response = requests.get(f"{self._endpoint}/models", headers=headers, timeout=10)
            response.raise_for_status()

            # Parse response
            data = response.json()
            models = data.get("data", [])

            # Format models
            formatted_models = []
            for model in models:
                model_id = model.get("id", "")

                # Get display name
                display_name = model.get("name", model_id)

                # Get context window
                context_length = model.get("context_length", 0)

                # Get pricing information
                pricing = model.get("pricing", {})
                prompt_price = pricing.get("prompt", 0)
                completion_price = pricing.get("completion", 0)

                formatted_models.append({
                    "id": model_id,
                    "name": display_name,
                    "description": model.get("description", ""),
                    "context_length": context_length,
                    "prompt_price": prompt_price,
                    "completion_price": completion_price,
                    "provider": "openrouter"
                })

            return formatted_models

        except Exception as e:
            logger.error(f"Error fetching OpenRouter models: {str(e)}", exc_info=True)
            # Return a static list of known models as a fallback
            return [
                {
                    "id": "openai/gpt-3.5-turbo",
                    "name": "GPT-3.5 Turbo",
                    "description": "OpenAI's GPT-3.5 Turbo model",
                    "context_length": 16385,
                    "provider": "openrouter"
                },
                {
                    "id": "openai/gpt-4",
                    "name": "GPT-4",
                    "description": "OpenAI's GPT-4 model",
                    "context_length": 8192,
                    "provider": "openrouter"
                }
            ]

    async def is_available(self) -> bool:
        """
        Check if the OpenRouter API is available.

        Returns:
            True if the API is available, False otherwise
        """
        if not self._api_key:
            return False

        try:
            # Make a simple request to check if the API key is valid
            headers = {
                "Authorization": f"Bearer {self._api_key}"
            }

            response = requests.get(f"{self._endpoint}/models", headers=headers, timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"Error checking OpenRouter API availability: {str(e)}")
            return False
