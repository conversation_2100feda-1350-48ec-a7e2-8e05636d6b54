"""
Debug logging script to identify JSON serialization and database issues.

This script adds additional logging to help diagnose the following issues:
1. Object of type MetaData is not JSON serializable
2. can't adapt type 'dict' in PostgreSQL
"""

import logging
import json
from sqlalchemy.orm import Session
from app.database import get_conversation, update_conversation

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("debug_logs")

def log_json_serialization(obj):
    """
    Test if an object is JSON serializable and log the result.
    
    Args:
        obj: The object to test
        
    Returns:
        bool: True if serializable, False otherwise
    """
    try:
        json.dumps(obj)
        logger.info(f"Object is JSON serializable: {type(obj)}")
        return True
    except TypeError as e:
        logger.error(f"Object is not JSON serializable: {type(obj)}, Error: {str(e)}")
        # Try to identify problematic keys if it's a dict
        if isinstance(obj, dict):
            for key, value in obj.items():
                try:
                    json.dumps(value)
                except TypeError:
                    logger.error(f"Non-serializable value in key '{key}': {type(value)}")
        return False

def debug_update_conversation(db: Session, conversation_id: str, update_data: dict):
    """
    Debug wrapper for update_conversation function.
    
    Args:
        db: Database session
        conversation_id: ID of the conversation to update
        update_data: Dictionary of data to update
        
    Returns:
        The result of update_conversation
    """
    logger.debug(f"Debugging update_conversation for conversation {conversation_id}")
    logger.debug(f"Update data: {update_data}")
    
    # Check if update_data is empty
    if not update_data:
        logger.warning("Empty update_data dictionary provided")
    
    # Check if title is a dict
    if 'title' in update_data and isinstance(update_data['title'], dict):
        logger.error(f"Title is a dictionary: {update_data['title']}")
    
    # Get the conversation to check its current state
    conversation = get_conversation(db, conversation_id)
    if conversation:
        logger.debug(f"Current conversation title: {conversation.title}")
        logger.debug(f"Current conversation metadata: {conversation.conversation_metadata}")
    else:
        logger.error(f"Conversation {conversation_id} not found")
    
    return update_conversation(db, conversation_id, update_data)

def debug_message_metadata(message):
    """
    Debug message metadata to check for non-serializable objects.
    
    Args:
        message: The message object to check
    """
    logger.debug(f"Debugging message metadata for message {message.id}")
    
    # Check if metadata is serializable
    if hasattr(message, 'message_metadata') and message.message_metadata:
        logger.debug(f"Message metadata type: {type(message.message_metadata)}")
        log_json_serialization(message.message_metadata)
    elif hasattr(message, 'metadata') and message.metadata:
        logger.debug(f"Message metadata type: {type(message.metadata)}")
        log_json_serialization(message.metadata)
    else:
        logger.debug("No metadata found on message")
