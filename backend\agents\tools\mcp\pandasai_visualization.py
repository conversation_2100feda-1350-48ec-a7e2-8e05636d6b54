"""
PandasAI v3 visualization MCP tool.

This module provides an MCP-compatible tool for visualizing data using PandasAI v3.
It integrates with mem0ai for enhanced visualization capabilities.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, Optional
import base64

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper
from ..pandasai_v3.cache import ResponseCache
from ..pandasai_v3.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from app.utils.json_utils import sanitize_json
from ...utils.memory_service import MemoryService
from ...utils.vector_service import VectorService

logger = logging.getLogger(__name__)

class PandasAIVisualizationTool(BaseMCPTool):
    """Tool for visualizing data using PandasAI v3."""

    def __init__(self):
        """Initialize the PandasAI visualization tool."""
        super().__init__(
            name="pandasai_visualization",
            description="Visualize data using PandasAI v3 with mem0ai integration",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "data_source": {
                        "type": ["object", "string"],
                        "description": "Data source information (can be an object with id/name or a string identifier)"
                    },
                    "prompt": {"type": "string"},
                    "api_key": {"type": "string"},
                    "provider": {"type": "string", "default": "openai"},
                    "model": {"type": "string"},
                    "user_id": {"type": "string"},
                    "persona_id": {"type": "string"},
                    "conversation_id": {"type": "string"},
                    "store_in_memory": {"type": "boolean", "default": True}
                },
                "required": ["prompt"]  # Removed api_key from required to allow fallbacks
            }
        )
        self.pandasai = PandasAIWrapper()
        self.cache = ResponseCache()
        self.memory_service = MemoryService()
        self.vector_service = VectorService()

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the PandasAI visualization tool."""
        file_path = arguments.get("file_path")
        data_source = arguments.get("data_source")
        prompt = arguments.get("prompt")
        api_key = arguments.get("api_key")
        provider = arguments.get("provider", "openai")

        # Log the input arguments for debugging
        logger.info(f"PandasAI visualization tool called with: prompt='{prompt}', provider='{provider}'")
        logger.info(f"File path: {file_path}, Data source: {data_source}")

        # Get API key from environment if not provided
        if not api_key:
            # Try to get API key from environment variables
            env_var_name = f"{provider.upper()}_API_KEY"
            api_key = os.getenv(env_var_name)
            logger.info(f"Using API key from environment variable {env_var_name}")

            # If still no API key, try OpenAI as fallback
            if not api_key and provider != "openai":
                logger.info(f"No API key for {provider}, trying OpenAI as fallback")
                provider = "openai"
                api_key = os.getenv("OPENAI_API_KEY")

            # If still no API key, return error
            if not api_key:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"No API key provided for {provider} and no fallback available. Please provide an API key."}]
                }

        # Process data source to get file path
        if data_source and not file_path:
            logger.info(f"Processing data source to get file path: {data_source}")

            # If data_source is a dict with file_path, use it directly
            if isinstance(data_source, dict) and "file_path" in data_source:
                file_path = data_source["file_path"]
                logger.info(f"Using file_path from data_source: {file_path}")
            else:
                try:
                    # Import the data access tool
                    from .data_access import DataAccessTool

                    # Create and initialize the tool
                    data_tool = DataAccessTool()
                    await data_tool.initialize({})

                    # Call the tool to load the data
                    data_result = await data_tool.execute({
                        "data_source": data_source,
                        "operation": "load",
                        "params": {"create_sample": True}
                    })

                    # Check if we got a valid result
                    if not data_result.get("isError", False) and "metadata" in data_result:
                        # Extract the file path from the result
                        file_path = data_result["metadata"].get("file_path")
                        logger.info(f"Retrieved file path from data_access tool: {file_path}")
                    else:
                        logger.error(f"Error retrieving file path from data_access tool: {data_result}")
                        return {
                            "isError": True,
                            "content": [{"type": "text", "text": "Could not access the data source. Please provide a valid file path or data source."}]
                        }
                except Exception as e:
                    logger.error(f"Error using data_access tool: {e}", exc_info=True)
                    return {
                        "isError": True,
                        "content": [{"type": "text", "text": f"Error accessing data source: {str(e)}"}]
                    }

        # Try to find file path if it's still not available
        if not file_path and isinstance(data_source, dict):
            # Try to extract file information from data source
            file_id = data_source.get("id")
            file_name = data_source.get("name")

            if file_id or file_name:
                logger.info(f"Trying to find file path for ID: {file_id}, Name: {file_name}")

                # Check common locations
                possible_paths = []

                # Add paths based on file_id
                if file_id:
                    possible_paths.extend([
                        f"data/{file_id}.csv",
                        f"uploads/{file_id}.csv",
                        f"backend/data/{file_id}.csv",
                        f"data/{file_id}.xlsx",
                        f"uploads/{file_id}.xlsx",
                        f"backend/data/{file_id}.xlsx"
                    ])

                # Add paths based on file_name
                if file_name:
                    possible_paths.extend([
                        f"data/{file_name}.csv",
                        f"uploads/{file_name}.csv",
                        f"backend/data/{file_name}.csv",
                        f"data/{file_name}.xlsx",
                        f"uploads/{file_name}.xlsx",
                        f"backend/data/{file_name}.xlsx",
                        f"data/{file_name}",
                        f"uploads/{file_name}",
                        f"backend/data/{file_name}"
                    ])

                # Add any additional common paths if needed
                pass

                # Check if any of these paths exist
                for path in possible_paths:
                    if os.path.exists(path):
                        file_path = path
                        logger.info(f"Found file at path: {file_path}")
                        break

        # Ensure we have a file path
        if not file_path:
            return {
                "isError": True,
                "content": [{"type": "text", "text": "No file path could be determined from the data source. Please provide a valid file path or data source."}]
            }

        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"File does not exist: {file_path}")
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"File does not exist: {file_path}"}]
            }

        # Check cache first
        cached_result = self.cache.get(file_path, prompt, provider)
        if cached_result:
            logger.info(f"Using cached result for visualization prompt: {prompt}")
            return cached_result

        try:
            # Initialize PandasAI
            logger.info(f"Initializing PandasAI with provider: {provider}")
            self.pandasai.initialize(api_key, provider)

            # Load dataframe
            logger.info(f"Loading dataframe from file: {file_path}")
            if not self.pandasai.load_dataframe(file_path):
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error loading dataframe from {file_path}. Please make sure it's a valid data file (CSV, Excel, etc.)."}]
                }

            # Create agent with model if provided
            model = arguments.get("model")
            logger.info(f"Creating PandasAI agent with model: {model}")
            if not self.pandasai.create_agent(model=model):
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": "Error creating PandasAI Agent. Please check your API key and provider settings."}]
                }

            # Add visualization-specific prompt
            viz_prompt = f"Create a visualization for: {prompt}. Return only the chart without any explanation."
            logger.info(f"Sending visualization prompt to PandasAI: {viz_prompt}")

            # Chat with agent
            result = self.pandasai.chat(viz_prompt)
            logger.info(f"PandasAI result type: {result.get('type')}")

            # Handle error
            if "error" in result:
                logger.error(f"Error in PandasAI visualization: {result['error']}")
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error in PandasAI visualization: {result['error']}"}]
                }

            # For visualization, we expect a chart result
            if result["type"] != "chart":
                logger.error(f"Expected chart result but got: {result['type']}")
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Expected a chart result, but got {result['type']}. Try rephrasing your visualization request."}]
                }

            # Read image file and convert to base64
            try:
                image_path = result["image_path"]
                logger.info(f"Reading chart image from: {image_path}")

                if not os.path.exists(image_path):
                    logger.error(f"Chart image file does not exist: {image_path}")
                    return {
                        "isError": True,
                        "content": [{"type": "text", "text": "The visualization was generated but the image file could not be found."}]
                    }

                with open(image_path, "rb") as image_file:
                    encoded_image = base64.b64encode(image_file.read()).decode("utf-8")

                # Create a proper visualization object that the frontend can render
                visualization_data = {
                    "type": "chart",
                    "title": f"Visualization for: {prompt}",
                    "description": f"Generated using {provider} provider",
                    "data": {
                        "image": f"data:image/png;base64,{encoded_image}",
                        "labels": ["Visualization"],
                        "datasets": [
                            {
                                "label": "Data Visualization",
                                "data": [1],
                                "backgroundColor": "rgba(75, 192, 192, 0.2)",
                                "borderColor": "rgba(75, 192, 192, 1)"
                            }
                        ]
                    },
                    "config": {
                        "type": "custom",
                        "options": {}
                    }
                }

                # Create metadata and sanitize it to handle any potential NaN values
                metadata = {
                    "file_path": file_path,
                    "prompt": prompt,
                    "provider": provider,
                    "image_path": image_path,
                    "visualization": visualization_data  # Add the visualization data to the metadata
                }

                # Sanitize metadata to ensure it's JSON serializable
                sanitized_metadata = sanitize_json(metadata)

                response = {
                    "isError": False,
                    "content": [
                        {"type": "text", "text": f"Visualization for: {prompt}"},
                        {"type": "image", "src": f"data:image/png;base64,{encoded_image}"}
                    ],
                    "metadata": sanitized_metadata
                }

                # Cache the response
                self.cache.set(file_path, prompt, provider, response)
                logger.info("Successfully generated and cached visualization")

                # Store the visualization in memory if requested
                store_in_memory = arguments.get("store_in_memory", True)
                if store_in_memory:
                    user_id = arguments.get("user_id", "system")
                    persona_id = arguments.get("persona_id", "unknown")
                    conversation_id = arguments.get("conversation_id", "unknown")

                    # Create metadata for the memory
                    memory_metadata = {
                        "type": "visualization",
                        "file_path": file_path,
                        "prompt": prompt,
                        "provider": provider,
                        "persona_id": persona_id,
                        "conversation_id": conversation_id,
                        "visualization_type": "pandasai",
                        "image_path": image_path
                    }

                    # Store in memory
                    try:
                        self.memory_service.add_memory(
                            content=f"Visualization of {os.path.basename(file_path)}: {prompt}",
                            user_id=user_id,
                            metadata=memory_metadata
                        )
                        logger.info(f"Stored visualization in memory for user {user_id}")
                    except Exception as e:
                        logger.error(f"Error storing visualization in memory: {e}")

                return response
            except Exception as e:
                logger.error(f"Error reading chart image: {e}", exc_info=True)
                error_metadata = {
                    "error_type": e.__class__.__name__,
                    "error_details": str(e),
                    "file_path": file_path,
                    "prompt": prompt,
                    "provider": provider
                }

                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error displaying chart: {str(e)}"}],
                    "metadata": sanitize_json(error_metadata)
                }

        except Exception as e:
            logger.error(f"Error executing PandasAI visualization tool: {e}", exc_info=True)
            error_metadata = {
                "error_type": e.__class__.__name__,
                "error_details": str(e),
                "component": "PandasAIVisualizationTool"
            }

            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Error: {str(e)}"}],
                "metadata": sanitize_json(error_metadata)
            }
