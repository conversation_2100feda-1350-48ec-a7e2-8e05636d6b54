"""
Simple script to test the concierge agent implementation.
"""

import asyncio
import logging
import sys
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the agent registry
from backend.agents.registry import AgentRegistry


async def test_concierge_agent():
    """Test the concierge agent implementation."""
    try:
        # List all registered personas
        personas = AgentRegistry.list_registered_personas()
        logger.info(f"Registered personas: {personas}")
        
        # Check if the concierge agent is registered
        if "concierge-agent" in personas:
            logger.info("Concierge agent is registered")
            
            # Create an instance of the concierge agent
            agent = await AgentRegistry.create_agent_instance("concierge-agent")
            logger.info(f"Created concierge agent instance: {agent}")
            
            # Process a test message
            response = await agent.process_message(
                user_id=1,
                message="Hello, I need help with data analysis",
                conversation_id="test_conversation",
                context={}
            )
            
            # Print the response
            logger.info(f"Response: {response}")
            
            return True
        else:
            logger.error("Concierge agent is not registered")
            return False
    except Exception as e:
        logger.error(f"Error testing concierge agent: {e}")
        return False


if __name__ == "__main__":
    # Run the test
    loop = asyncio.get_event_loop()
    result = loop.run_until_complete(test_concierge_agent())
    
    # Exit with appropriate status code
    sys.exit(0 if result else 1)
