"""
Agent models for the Datagenius backend.

This module provides Pydantic models for agent-related functionality.
"""

from typing import Optional, List, Dict, Any

from pydantic import BaseModel


class AgentInfo(BaseModel):
    """Model for agent information."""
    persona_id: str
    name: str
    description: str
    capabilities: List[str]


class AgentListResponse(BaseModel):
    """Model for agent list response."""
    agents: List[AgentInfo]


class AgentInvokeRequest(BaseModel):
    """Model for agent invocation request."""
    message: str
    conversation_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = {}
    config: Optional[Dict[str, Any]] = {}


class AgentResponse(BaseModel):
    """Model for agent response."""
    message: str
    conversation_id: str
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
