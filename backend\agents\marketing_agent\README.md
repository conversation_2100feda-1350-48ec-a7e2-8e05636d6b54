# Composable Marketing Agent

This directory contains the Composable Marketing Agent implementation for the Datagenius backend. The agent uses a component-based architecture with Model Context Protocol (MCP) tools for flexibility and modularity.

## Files

- `composable_agent_mcp.py`: The MCP-based composable agent implementation
- `components_mcp.py`: MCP-based components used by the composable agent
- `models.py`: Pydantic models for the agent

## Features

The Composable Marketing Agent provides the following capabilities:

- **Marketing Strategy**: Generate comprehensive marketing strategies
- **Campaign Strategy**: Create campaign concepts and plans
- **Social Media Content Strategy**: Develop social media content strategies
- **SEO Optimization Strategy**: Create SEO optimization strategies
- **Post Composer**: Generate social media posts

## Components

The agent is built from the following MCP-based components:

- **MarketingParserComponent**: Parses user requests to determine the marketing task
- **MCPContentGeneratorComponent**: Generates marketing content using MCP tools
- **MCPServerComponent**: Provides access to MCP tools for content generation

## Usage

To use the Composable Marketing Agent, you can interact with it through the API endpoints. The agent is registered with the agent registry as "composable-marketing-ai" and can be accessed through the personas API.

Example:

```python
from agents.registry import AgentRegistry

# Get the marketing agent
marketing_agent = await AgentRegistry.create_agent_instance("composable-marketing-ai")

# Process a message
response = await marketing_agent.process_message(
    user_id=1,
    message="Create a marketing strategy for a new fitness app",
    conversation_id="conversation_123",
    context={}
)

# Print the response
print(response["message"])
```

## Configuration

### AI Models
The Marketing Agent supports two types of AI models:
1. **ChatGroq (Cloud)**: Requires a GROQ_API_KEY in the .env file
2. **Ollama (Local)**: Requires a local Ollama server running with the llama3 model

## Dependencies

- Python 3.10+
- FastAPI
- LangChain
- FAISS (for document retrieval)
- python-docx (for document generation)
- python-dotenv
