"""
Performance optimization system for Datagenius.

This module provides comprehensive performance optimization including
caching strategies, memory management, and response time optimization.
"""

import logging
import asyncio
import time
import weakref
from typing import Dict, Any, Optional, List, Callable, Union
from datetime import datetime, timedelta
from functools import wraps, lru_cache
from contextlib import asynccontextmanager

import redis
import pickle
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text

from ..database import engine
from ..config import REDIS_URL
from ..monitoring.metrics import metrics

logger = logging.getLogger(__name__)


class CacheManager:
    """
    Advanced caching system with multiple cache layers and intelligent invalidation.
    """

    def __init__(self):
        """Initialize the cache manager."""
        self.redis_client = None
        self.memory_cache = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0
        }
        self.max_memory_cache_size = 1000
        self.default_ttl = 3600  # 1 hour
        
        # Initialize Redis connection
        self._init_redis()

    def _init_redis(self):
        """Initialize Redis connection."""
        try:
            self.redis_client = redis.from_url(REDIS_URL, decode_responses=True)
            self.redis_client.ping()
            logger.info("Redis cache initialized successfully")
        except Exception as e:
            logger.warning(f"Redis not available, using memory cache only: {e}")
            self.redis_client = None

    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache with multi-layer lookup.
        
        Args:
            key: Cache key
            default: Default value if not found
            
        Returns:
            Cached value or default
        """
        # Try memory cache first (fastest)
        if key in self.memory_cache:
            entry = self.memory_cache[key]
            if entry['expires'] > time.time():
                self.cache_stats["hits"] += 1
                return entry['value']
            else:
                # Expired, remove from memory cache
                del self.memory_cache[key]
        
        # Try Redis cache
        if self.redis_client:
            try:
                cached_data = self.redis_client.get(f"datagenius:{key}")
                if cached_data:
                    value = pickle.loads(cached_data.encode('latin1'))
                    # Store in memory cache for faster future access
                    self._set_memory_cache(key, value, self.default_ttl)
                    self.cache_stats["hits"] += 1
                    return value
            except Exception as e:
                logger.warning(f"Redis cache error: {e}")
        
        self.cache_stats["misses"] += 1
        return default

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set value in cache with multi-layer storage.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            
        Returns:
            True if successful
        """
        ttl = ttl or self.default_ttl
        
        # Store in memory cache
        self._set_memory_cache(key, value, ttl)
        
        # Store in Redis cache
        if self.redis_client:
            try:
                serialized_value = pickle.dumps(value).decode('latin1')
                self.redis_client.setex(f"datagenius:{key}", ttl, serialized_value)
                return True
            except Exception as e:
                logger.warning(f"Redis cache set error: {e}")
        
        return True  # Memory cache succeeded

    def _set_memory_cache(self, key: str, value: Any, ttl: int):
        """Set value in memory cache with size management."""
        # Check if we need to evict items
        if len(self.memory_cache) >= self.max_memory_cache_size:
            # Remove oldest expired items first
            current_time = time.time()
            expired_keys = [
                k for k, v in self.memory_cache.items()
                if v['expires'] <= current_time
            ]
            
            for expired_key in expired_keys:
                del self.memory_cache[expired_key]
                self.cache_stats["evictions"] += 1
            
            # If still too many items, remove oldest
            if len(self.memory_cache) >= self.max_memory_cache_size:
                oldest_key = min(
                    self.memory_cache.keys(),
                    key=lambda k: self.memory_cache[k]['created']
                )
                del self.memory_cache[oldest_key]
                self.cache_stats["evictions"] += 1
        
        self.memory_cache[key] = {
            'value': value,
            'expires': time.time() + ttl,
            'created': time.time()
        }

    async def delete(self, key: str) -> bool:
        """Delete key from all cache layers."""
        # Remove from memory cache
        if key in self.memory_cache:
            del self.memory_cache[key]
        
        # Remove from Redis cache
        if self.redis_client:
            try:
                self.redis_client.delete(f"datagenius:{key}")
            except Exception as e:
                logger.warning(f"Redis cache delete error: {e}")
        
        return True

    async def clear_pattern(self, pattern: str):
        """Clear all keys matching a pattern."""
        # Clear from memory cache
        keys_to_delete = [k for k in self.memory_cache.keys() if pattern in k]
        for key in keys_to_delete:
            del self.memory_cache[key]
        
        # Clear from Redis cache
        if self.redis_client:
            try:
                keys = self.redis_client.keys(f"datagenius:*{pattern}*")
                if keys:
                    self.redis_client.delete(*keys)
            except Exception as e:
                logger.warning(f"Redis pattern clear error: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = (self.cache_stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "memory_cache_size": len(self.memory_cache),
            "hit_rate": round(hit_rate, 2),
            "total_hits": self.cache_stats["hits"],
            "total_misses": self.cache_stats["misses"],
            "total_evictions": self.cache_stats["evictions"]
        }


# Global cache manager instance
cache_manager = CacheManager()


def cached(ttl: int = 3600, key_prefix: str = ""):
    """
    Decorator for caching function results.
    
    Args:
        ttl: Time to live in seconds
        key_prefix: Prefix for cache key
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"{key_prefix}{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Try to get from cache
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache_manager.set(cache_key, result, ttl)
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # For synchronous functions, use asyncio to handle cache operations
            cache_key = f"{key_prefix}{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Try to get from cache (simplified for sync)
            if cache_key in cache_manager.memory_cache:
                entry = cache_manager.memory_cache[cache_key]
                if entry['expires'] > time.time():
                    return entry['value']
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_manager._set_memory_cache(cache_key, result, ttl)
            
            return result
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


class ConnectionPoolManager:
    """
    Database connection pool optimization and management.
    """

    def __init__(self):
        """Initialize connection pool manager."""
        self.pool_stats = {
            "active_connections": 0,
            "total_connections": 0,
            "connection_errors": 0
        }

    @asynccontextmanager
    async def get_connection(self):
        """Get optimized database connection."""
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            self.pool_stats["active_connections"] += 1
            self.pool_stats["total_connections"] += 1
            
            # Update metrics
            metrics.update_database_connections(self.pool_stats["active_connections"])
            
            yield session
            
        except Exception as e:
            self.pool_stats["connection_errors"] += 1
            logger.error(f"Database connection error: {e}")
            session.rollback()
            raise
        finally:
            session.close()
            self.pool_stats["active_connections"] -= 1
            metrics.update_database_connections(self.pool_stats["active_connections"])

    async def optimize_queries(self, session):
        """Apply query optimizations."""
        # Enable query plan caching
        await session.execute(text("SET plan_cache_mode = force_generic_plan"))
        
        # Set optimal work memory
        await session.execute(text("SET work_mem = '256MB'"))
        
        # Enable parallel query execution
        await session.execute(text("SET max_parallel_workers_per_gather = 4"))

    def get_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        return self.pool_stats.copy()


# Global connection pool manager
pool_manager = ConnectionPoolManager()


class MemoryManager:
    """
    Memory usage optimization and garbage collection management.
    """

    def __init__(self):
        """Initialize memory manager."""
        self.object_registry = weakref.WeakSet()
        self.memory_stats = {
            "gc_collections": 0,
            "objects_tracked": 0,
            "memory_freed": 0
        }

    def register_object(self, obj: Any):
        """Register object for memory tracking."""
        self.object_registry.add(obj)
        self.memory_stats["objects_tracked"] += 1

    async def cleanup_memory(self):
        """Perform memory cleanup operations."""
        import gc
        
        # Force garbage collection
        collected = gc.collect()
        self.memory_stats["gc_collections"] += 1
        self.memory_stats["memory_freed"] += collected
        
        # Clear expired cache entries
        await self._cleanup_expired_cache()
        
        logger.info(f"Memory cleanup completed, freed {collected} objects")

    async def _cleanup_expired_cache(self):
        """Clean up expired cache entries."""
        current_time = time.time()
        expired_keys = [
            key for key, entry in cache_manager.memory_cache.items()
            if entry['expires'] <= current_time
        ]
        
        for key in expired_keys:
            del cache_manager.memory_cache[key]
            cache_manager.cache_stats["evictions"] += 1

    def get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage statistics."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            "rss_mb": round(memory_info.rss / 1024 / 1024, 2),
            "vms_mb": round(memory_info.vms / 1024 / 1024, 2),
            "percent": round(process.memory_percent(), 2),
            "objects_tracked": len(self.object_registry),
            "gc_stats": self.memory_stats
        }


# Global memory manager
memory_manager = MemoryManager()


class ResponseTimeOptimizer:
    """
    Response time optimization through various techniques.
    """

    def __init__(self):
        """Initialize response time optimizer."""
        self.response_times = []
        self.max_history = 1000

    def record_response_time(self, endpoint: str, duration: float):
        """Record response time for analysis."""
        self.response_times.append({
            "endpoint": endpoint,
            "duration": duration,
            "timestamp": time.time()
        })
        
        # Keep only recent history
        if len(self.response_times) > self.max_history:
            self.response_times = self.response_times[-self.max_history:]

    def get_performance_insights(self) -> Dict[str, Any]:
        """Get performance insights and recommendations."""
        if not self.response_times:
            return {"message": "No performance data available"}
        
        # Calculate statistics
        durations = [rt["duration"] for rt in self.response_times]
        avg_duration = sum(durations) / len(durations)
        max_duration = max(durations)
        min_duration = min(durations)
        
        # Identify slow endpoints
        endpoint_stats = {}
        for rt in self.response_times:
            endpoint = rt["endpoint"]
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = []
            endpoint_stats[endpoint].append(rt["duration"])
        
        slow_endpoints = []
        for endpoint, times in endpoint_stats.items():
            avg_time = sum(times) / len(times)
            if avg_time > avg_duration * 1.5:  # 50% slower than average
                slow_endpoints.append({
                    "endpoint": endpoint,
                    "avg_duration": round(avg_time, 3),
                    "requests": len(times)
                })
        
        return {
            "avg_response_time": round(avg_duration, 3),
            "max_response_time": round(max_duration, 3),
            "min_response_time": round(min_duration, 3),
            "total_requests": len(self.response_times),
            "slow_endpoints": sorted(slow_endpoints, key=lambda x: x["avg_duration"], reverse=True)[:5]
        }


# Global response time optimizer
response_optimizer = ResponseTimeOptimizer()


async def optimize_performance():
    """
    Run comprehensive performance optimization.
    """
    logger.info("Starting performance optimization")
    
    # Memory cleanup
    await memory_manager.cleanup_memory()
    
    # Cache optimization
    cache_stats = cache_manager.get_stats()
    if cache_stats["hit_rate"] < 70:  # Less than 70% hit rate
        logger.warning(f"Low cache hit rate: {cache_stats['hit_rate']}%")
    
    # Performance insights
    insights = response_optimizer.get_performance_insights()
    if insights.get("slow_endpoints"):
        logger.warning(f"Slow endpoints detected: {len(insights['slow_endpoints'])}")
    
    logger.info("Performance optimization completed")


def performance_monitor(func):
    """Decorator to monitor function performance."""
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            duration = time.time() - start_time
            endpoint = getattr(func, '__name__', 'unknown')
            response_optimizer.record_response_time(endpoint, duration)
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            duration = time.time() - start_time
            endpoint = getattr(func, '__name__', 'unknown')
            response_optimizer.record_response_time(endpoint, duration)
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper


# Periodic optimization task
async def start_performance_monitoring():
    """Start background performance monitoring."""
    while True:
        try:
            await optimize_performance()
            await asyncio.sleep(300)  # Run every 5 minutes
        except Exception as e:
            logger.error(f"Performance monitoring error: {e}")
            await asyncio.sleep(60)  # Wait 1 minute on error


# Initialize performance monitoring
def initialize_performance_optimization():
    """Initialize performance optimization system."""
    asyncio.create_task(start_performance_monitoring())
    logger.info("Performance optimization system initialized")
