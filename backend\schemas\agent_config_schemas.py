"""
Pydantic schemas for validating agent and component configurations.
"""
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator, model_validator

class BaseComponentConfig(BaseModel):
    """
    Base model for individual component configurations within a persona.
    Allows for arbitrary additional fields via 'config' or direct properties.
    """
    type: str = Field(..., description="The registered type name of the component.")
    name: Optional[str] = Field(None, description="An optional unique name for this component instance within the agent.")
    # Allow arbitrary other fields for component-specific configuration
    # For example, a component might define 'model_name: str' or 'api_key_env: str'
    # These will be captured by Pydantic's extra='allow' if not explicitly defined.

    model_config = {"extra": "allow"}  # Allow other fields not explicitly defined

class PersonaConfig(BaseModel):
    """
    Pydantic model for validating the structure of a persona YAML configuration file.
    """
    id: str = Field(..., description="Unique identifier for the persona.")
    name: str = Field(..., description="Display name of the persona.")
    class_path: str = Field(..., description="Full Python import path to the agent class (e.g., backend.agents.composable.ComposableAgent).")
    description: Optional[str] = Field("", description="A brief description of the persona's purpose or capabilities.")

    system_prompts: Dict[str, str] = Field(default_factory=dict, description="A dictionary of named prompt templates available to the agent.")
    capabilities: List[str] = Field(default_factory=list, description="A list of capabilities this persona/agent possesses.")

    # Specific to ComposableAgent, but included here for validation.
    # A custom validator could ensure this is only present if class_path points to a ComposableAgent.
    components: Optional[List[BaseComponentConfig]] = Field(None, description="List of component configurations if the agent is composable.")

    # Fields often used for metadata, UI display, or database synchronization
    industry: Optional[str] = Field(None, description="Industry focus of the persona, if applicable.")
    skills: List[str] = Field(default_factory=list, description="List of skills associated with the persona.")
    rating: Optional[float] = Field(None, ge=0, le=5, description="User rating, typically between 0 and 5.")
    review_count: Optional[int] = Field(None, ge=0, description="Number of reviews received.")
    image_url: Optional[str] = Field(None, description="URL to an image representing the persona.")
    price: Optional[float] = Field(None, ge=0, description="Price or cost associated with using the persona, if applicable.")
    provider: Optional[str] = Field(None, description="The provider of the underlying model or service (e.g., 'openai', 'anthropic').")
    model: Optional[str] = Field(None, description="Specific model name used by the persona (e.g., 'gpt-4-turbo', 'claude-3-opus').")
    is_active: bool = Field(True, description="Whether the persona is currently active and available for use.")
    age_restriction: Optional[int] = Field(None, ge=0, description="Minimum age restriction for using this persona, if any.")
    content_filters: Dict[str, Any] = Field(default_factory=dict, description="Configuration for content filters applied by this persona.")

    @field_validator('class_path')
    @classmethod
    def validate_class_path_format(cls, v):
        if not v or '.' not in v or v.endswith('.'):
            raise ValueError('class_path must be a valid Python import path (e.g., module.submodule.ClassName)')
        return v

    @model_validator(mode='before')
    @classmethod
    def ensure_id_from_filename_if_missing(cls, values):
        # This validator is more relevant if we load directly from a file object
        # In PersonaManager, we derive persona_id from config.get("id") or filename
        # For now, 'id' is required in the Pydantic model.
        # If we wanted to allow 'id' to be optional in YAML and derive it,
        # this is where such logic could go, but it's complex with Pydantic's flow.
        return values

    model_config = {"extra": "allow"}  # Allow fields not explicitly defined in the model, they will be part of the validated data.
                                   # This is useful if some personas have custom top-level fields.

# --- Schemas for ComposableAgent Context ---

class AgentError(BaseModel):
    """Represents an error that occurred in a component."""
    component: str
    error: str
    details: Optional[Dict[str, Any]] = None

class AgentProcessingContext(BaseModel):
    """
    Pydantic model for the context dictionary used within ComposableAgent
    and passed between its components.
    """
    # Input fields
    user_id: Union[int, str] # Can be int or str, adapt as per your user ID system
    message: str
    conversation_id: str

    # Initial context passed to process_message, can contain arbitrary data
    # from the WorkflowManager or other callers.
    initial_context: Dict[str, Any] = Field(default_factory=dict)

    # Agent-specific information
    agent_config: PersonaConfig # The full config of the currently running agent
    # agent_components: List[Any] # Type this more strictly if possible, e.g., List['AgentComponent'] - requires forward ref or careful import
                                 # For now, List[Any] to avoid circular dependencies if AgentComponent imports this.
                                 # This field might be better managed internally by the agent rather than passed around if components don't need it.

    # Fields populated during processing by components
    response: str = "" # The textual response being built by the agent

    # Structured data that components might produce or consume.
    # Examples:
    # classified_intent: Optional[str] = None
    # retrieved_documents: List[Dict[str, Any]] = Field(default_factory=list)
    # generated_image_url: Optional[str] = None
    # analysis_results: Optional[Dict[str, Any]] = None

    # File processing specific fields
    file_processed: Optional[bool] = Field(None, description="Flag indicating if a file was processed as part of the request.")
    file_data: Optional[Dict[str, Any]] = Field(None, description="Data extracted or resulting from file processing.")

    # Metadata and error tracking
    metadata: Dict[str, Any] = Field(default_factory=dict, description="General metadata accumulated during processing.")
    errors: List[AgentError] = Field(default_factory=list, description="List of errors encountered during component processing.")

    # Allow components to add their own arbitrary data to the context.
    # This provides flexibility but should be used judiciously.
    # Prefer defining common shared fields explicitly above.
    component_data: Dict[str, Any] = Field(default_factory=dict, description="A space for components to store their specific data.")

    model_config = {"extra": "allow"}  # Allows other fields not explicitly defined, useful for dynamic data from components.
                                   # arbitrary_types_allowed = True # If you need to store non-Pydantic types like AgentComponent instances directly.
                                   # However, it's often better to pass identifiers or serializable data.

    def add_error(self, component_name: str, error_message: str, details: Optional[Dict[str, Any]] = None):
        self.errors.append(AgentError(component=component_name, error=error_message, details=details))

    # Example of how a component might set/get its specific data:
    def set_component_data(self, component_name: str, data: Any):
        self.component_data[component_name] = data

    def get_component_data(self, component_name: str) -> Optional[Any]:
        return self.component_data.get(component_name)
