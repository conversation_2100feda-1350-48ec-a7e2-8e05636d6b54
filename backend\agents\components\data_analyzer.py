"""
Data analyzer component for the Datagenius agent system.

This module provides a component for analyzing data, generating statistics,
and creating visualizations without Streamlit dependencies.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union
import json
from pathlib import Path
import os

from backend.schemas.agent_config_schemas import AgentProcessingContext # Added
from .base import AgentComponent

logger = logging.getLogger(__name__)


class DataAnalyzerComponent(AgentComponent):
    """Component for analyzing data and generating insights."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the data analyzer component.

        Args:
            config: Configuration dictionary for the component
        """
        # Set analysis options
        self.max_categorical_values = config.get("max_categorical_values", 20)
        self.correlation_threshold = config.get("correlation_threshold", 0.3)
        self.outlier_threshold = config.get("outlier_threshold", 3.0)  # Z-score threshold
        self.visualization_dir = config.get("visualization_dir", "visualizations")
        
        # Create visualization directory if it doesn't exist
        os.makedirs(self.visualization_dir, exist_ok=True)
        
        # Set available analysis types
        self.analysis_types = {
            "descriptive_analysis": self._perform_descriptive_analysis,
            "correlation_analysis": self._perform_correlation_analysis,
            "trend_analysis": self._perform_trend_analysis,
            "segmentation_analysis": self._perform_segmentation_analysis,
            "predictive_analysis": self._perform_predictive_analysis
        }

    async def process(self, context: AgentProcessingContext) -> AgentProcessingContext:
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object.
        """
        # Check if this component should be skipped
        if context.metadata.get("skip_data_analyzer", False):
            logger.debug(f"Skipping data analyzer component: {self.name}")
            return context

        # Check if there's data in the context (assuming from initial_context or a previous component)
        data = context.initial_context.get("data") 
        if data is None: # Could also check component_data from specific previous components if needed
            # Attempt to get from component_data if a known retriever component ran
            data_retriever_name = "DataRetrieverComponent" # Or EnhancedDataRetrieverComponent
            if data_retriever_name in context.component_data:
                data = context.component_data[data_retriever_name].get("data")

        if data is None:
            logger.debug("No data found in context (checked initial_context and common data retriever component_data)")
            # context.response = "No data available for analysis." # Optional user-facing message
            return context

        try:
            # Check if data is a pandas DataFrame
            if not isinstance(data, pd.DataFrame):
                logger.warning(f"Data is not a pandas DataFrame: {type(data)}")
                context.add_error(self.name, "data_not_dataframe", {"actual_type": str(type(data))})
                context.response = "The provided data is not in the expected DataFrame format for analysis."
                return context
            
            # Get analysis type from context metadata
            analysis_type = context.metadata.get("analysis_type", "descriptive_analysis")
            
            # Check if analysis type is supported
            if analysis_type not in self.analysis_types:
                logger.warning(f"Unsupported analysis type: {analysis_type}, defaulting to descriptive_analysis")
                analysis_type = "descriptive_analysis"
            
            # Perform analysis
            logger.info(f"Performing {analysis_type} on data")
            analysis_results = await self.analysis_types[analysis_type](data, context) # Pass AgentProcessingContext
            
            # Add analysis results to component_data
            context.component_data.setdefault(self.name, {})["analysis_results"] = analysis_results
            context.metadata["analysis_performed"] = analysis_type # Use a different key to avoid conflict if analysis_type was an input
            
            return context
            
        except Exception as e:
            logger.error(f"Error analyzing data: {str(e)}", exc_info=True)
            context.add_error(self.name, f"data_analysis_error: {str(e)}")
            context.response = "An error occurred during data analysis. Please check the logs."
            return context

    async def _perform_descriptive_analysis(self, data: pd.DataFrame, context: AgentProcessingContext) -> Dict[str, Any]:
        """
        Perform descriptive analysis on the data.

        Args:
            data: DataFrame to analyze
            context: AgentProcessingContext object

        Returns:
            Dictionary of analysis results
        """
        # Get basic information
        num_rows, num_cols = data.shape
        
        # Get column types
        numeric_cols = data.select_dtypes(include=["number"]).columns.tolist()
        categorical_cols = data.select_dtypes(include=["object", "category"]).columns.tolist()
        datetime_cols = data.select_dtypes(include=["datetime"]).columns.tolist()
        
        # Calculate summary statistics for numeric columns
        numeric_stats = {}
        for col in numeric_cols:
            numeric_stats[col] = {
                "mean": data[col].mean(),
                "median": data[col].median(),
                "std": data[col].std(),
                "min": data[col].min(),
                "max": data[col].max(),
                "missing": data[col].isnull().sum(),
                "missing_percent": (data[col].isnull().sum() / num_rows) * 100
            }
            
            # Detect outliers using Z-score
            z_scores = np.abs((data[col] - data[col].mean()) / data[col].std())
            outliers = data.loc[z_scores > self.outlier_threshold, col]
            numeric_stats[col]["outliers_count"] = len(outliers)
            numeric_stats[col]["outliers_percent"] = (len(outliers) / num_rows) * 100
        
        # Calculate summary statistics for categorical columns
        categorical_stats = {}
        for col in categorical_cols:
            value_counts = data[col].value_counts()
            top_values = value_counts.head(self.max_categorical_values)
            
            categorical_stats[col] = {
                "unique_values": data[col].nunique(),
                "missing": data[col].isnull().sum(),
                "missing_percent": (data[col].isnull().sum() / num_rows) * 100,
                "top_values": {str(k): int(v) for k, v in top_values.items()},
                "top_values_percent": {str(k): float(v / num_rows * 100) for k, v in top_values.items()}
            }
        
        # Calculate summary statistics for datetime columns
        datetime_stats = {}
        for col in datetime_cols:
            datetime_stats[col] = {
                "min": data[col].min().isoformat() if not pd.isna(data[col].min()) else None,
                "max": data[col].max().isoformat() if not pd.isna(data[col].max()) else None,
                "missing": data[col].isnull().sum(),
                "missing_percent": (data[col].isnull().sum() / num_rows) * 100
            }
        
        # Create overall summary
        overall_summary = {
            "num_rows": num_rows,
            "num_cols": num_cols,
            "num_numeric_cols": len(numeric_cols),
            "num_categorical_cols": len(categorical_cols),
            "num_datetime_cols": len(datetime_cols),
            "total_missing_values": data.isnull().sum().sum(),
            "total_missing_percent": (data.isnull().sum().sum() / (num_rows * num_cols)) * 100,
            "memory_usage": data.memory_usage(deep=True).sum()
        }
        
        # Combine all results
        analysis_results = {
            "overall_summary": overall_summary,
            "numeric_stats": numeric_stats,
            "categorical_stats": categorical_stats,
            "datetime_stats": datetime_stats
        }
        
        return analysis_results

    async def _perform_correlation_analysis(self, data: pd.DataFrame, context: AgentProcessingContext) -> Dict[str, Any]:
        """
        Perform correlation analysis on the data.

        Args:
            data: DataFrame to analyze
            context: AgentProcessingContext object

        Returns:
            Dictionary of analysis results
        """
        # Get numeric columns
        numeric_cols = data.select_dtypes(include=["number"]).columns.tolist()
        
        # Calculate correlation matrix
        corr_matrix = data[numeric_cols].corr().round(3)
        
        # Convert to dictionary
        corr_dict = corr_matrix.to_dict()
        
        # Find strong correlations
        strong_correlations = []
        for col1 in numeric_cols:
            for col2 in numeric_cols:
                if col1 != col2:
                    corr_value = corr_matrix.loc[col1, col2]
                    if abs(corr_value) >= self.correlation_threshold:
                        strong_correlations.append({
                            "variable1": col1,
                            "variable2": col2,
                            "correlation": corr_value,
                            "strength": "strong positive" if corr_value >= 0.7 else 
                                       "moderate positive" if corr_value >= 0.3 else
                                       "strong negative" if corr_value <= -0.7 else
                                       "moderate negative"
                        })
        
        # Sort by absolute correlation value
        strong_correlations.sort(key=lambda x: abs(x["correlation"]), reverse=True)
        
        # Create analysis results
        analysis_results = {
            "correlation_matrix": corr_dict,
            "strong_correlations": strong_correlations,
            "num_strong_correlations": len(strong_correlations),
            "correlation_threshold": self.correlation_threshold
        }
        
        return analysis_results

    async def _perform_trend_analysis(self, data: pd.DataFrame, context: AgentProcessingContext) -> Dict[str, Any]:
        """
        Perform trend analysis on the data.

        Args:
            data: DataFrame to analyze
            context: AgentProcessingContext object

        Returns:
            Dictionary of analysis results
        """
        # Check if there's a datetime column
        datetime_cols = data.select_dtypes(include=["datetime"]).columns.tolist()
        
        # If no datetime columns, try to convert a column to datetime
        if not datetime_cols:
            # Look for columns that might contain dates
            potential_date_cols = []
            for col in data.columns:
                # Check if column name suggests it's a date
                if any(date_term in col.lower() for date_term in ["date", "time", "day", "month", "year"]):
                    potential_date_cols.append(col)
            
            # Try to convert potential date columns
            for col in potential_date_cols:
                try:
                    data[f"{col}_dt"] = pd.to_datetime(data[col])
                    datetime_cols.append(f"{col}_dt")
                    logger.info(f"Converted column {col} to datetime")
                    break
                except:
                    continue
        
        # If still no datetime columns, return error
        if not datetime_cols:
            return {
                "error": "No datetime columns found for trend analysis",
                "suggestion": "Please provide a dataset with a datetime column or specify a column that can be converted to datetime"
            }
        
        # Use the first datetime column
        date_col = datetime_cols[0]
        
        # Get numeric columns
        numeric_cols = data.select_dtypes(include=["number"]).columns.tolist()
        
        # Set date column as index
        data_trend = data.copy()
        data_trend.set_index(date_col, inplace=True)
        
        # Resample data to different time periods
        trends = {}
        
        # Try different resampling periods based on the date range
        date_range = (data[date_col].max() - data[date_col].min()).days
        
        if date_range > 365 * 2:  # More than 2 years
            resample_periods = ["M", "Q", "Y"]
        elif date_range > 90:  # More than 3 months
            resample_periods = ["D", "W", "M"]
        else:
            resample_periods = ["D", "W"]
        
        for period in resample_periods:
            period_name = {
                "D": "daily",
                "W": "weekly",
                "M": "monthly",
                "Q": "quarterly",
                "Y": "yearly"
            }.get(period, period)
            
            trends[period_name] = {}
            
            for col in numeric_cols[:5]:  # Limit to first 5 numeric columns
                try:
                    # Resample and calculate mean
                    resampled = data_trend[col].resample(period).mean()
                    
                    # Convert to list of [timestamp, value] pairs
                    trend_data = [[ts.isoformat(), float(val) if not pd.isna(val) else None] 
                                 for ts, val in zip(resampled.index, resampled.values)]
                    
                    trends[period_name][col] = trend_data
                except Exception as e:
                    logger.warning(f"Error resampling {col} with period {period}: {e}")
        
        # Calculate basic statistics for each time period
        time_stats = {}
        for period_name, period_data in trends.items():
            time_stats[period_name] = {}
            
            for col, values in period_data.items():
                # Extract just the values (second element of each pair)
                just_values = [v[1] for v in values if v[1] is not None]
                
                if just_values:
                    time_stats[period_name][col] = {
                        "mean": np.mean(just_values),
                        "min": np.min(just_values),
                        "max": np.max(just_values),
                        "start_value": values[0][1],
                        "end_value": values[-1][1],
                        "change": values[-1][1] - values[0][1] if values[0][1] is not None and values[-1][1] is not None else None,
                        "percent_change": ((values[-1][1] - values[0][1]) / values[0][1] * 100) 
                                         if values[0][1] is not None and values[-1][1] is not None and values[0][1] != 0 else None
                    }
        
        # Create analysis results
        analysis_results = {
            "date_column": date_col,
            "date_range_days": date_range,
            "trends": trends,
            "time_stats": time_stats
        }
        
        return analysis_results

    async def _perform_segmentation_analysis(self, data: pd.DataFrame, context: AgentProcessingContext) -> Dict[str, Any]:
        """
        Perform segmentation analysis on the data.

        Args:
            data: DataFrame to analyze
            context: AgentProcessingContext object

        Returns:
            Dictionary of analysis results
        """
        try:
            from sklearn.cluster import KMeans
            from sklearn.preprocessing import StandardScaler
            from sklearn.decomposition import PCA
        except ImportError:
            return {
                "error": "Required libraries not installed",
                "suggestion": "Please install scikit-learn to use segmentation analysis"
            }
        
        # Get numeric columns
        numeric_cols = data.select_dtypes(include=["number"]).columns.tolist()
        
        # Need at least 2 numeric columns for clustering
        if len(numeric_cols) < 2:
            return {
                "error": "Not enough numeric columns for segmentation analysis",
                "suggestion": "Please provide a dataset with at least 2 numeric columns"
            }
        
        # Select numeric data
        numeric_data = data[numeric_cols].copy()
        
        # Drop rows with missing values
        numeric_data = numeric_data.dropna()
        
        # If no data left, return error
        if len(numeric_data) == 0:
            return {
                "error": "No complete rows found for segmentation analysis",
                "suggestion": "Please clean your data to remove missing values"
            }
        
        # Standardize the data
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(numeric_data)
        
        # Determine optimal number of clusters (2-6)
        max_clusters = min(6, len(numeric_data) // 10)  # At least 10 samples per cluster
        max_clusters = max(2, max_clusters)  # At least 2 clusters
        
        inertia = []
        for k in range(2, max_clusters + 1):
            kmeans = KMeans(n_clusters=k, random_state=42)
            kmeans.fit(scaled_data)
            inertia.append(kmeans.inertia_)
        
        # Calculate inertia differences
        inertia_diffs = [inertia[i] - inertia[i+1] for i in range(len(inertia)-1)]
        
        # Find optimal k using elbow method
        optimal_k = 2
        if inertia_diffs:
            # Find where the rate of improvement slows down
            for i, diff in enumerate(inertia_diffs):
                if i > 0 and diff < inertia_diffs[i-1] * 0.5:
                    optimal_k = i + 2  # +2 because we started with k=2
                    break
        
        # Perform clustering with optimal k
        kmeans = KMeans(n_clusters=optimal_k, random_state=42)
        clusters = kmeans.fit_predict(scaled_data)
        
        # Add cluster labels to original data
        numeric_data["cluster"] = clusters
        
        # Calculate cluster statistics
        cluster_stats = {}
        for cluster_id in range(optimal_k):
            cluster_data = numeric_data[numeric_data["cluster"] == cluster_id]
            
            # Calculate statistics for each numeric column
            col_stats = {}
            for col in numeric_cols:
                col_stats[col] = {
                    "mean": cluster_data[col].mean(),
                    "median": cluster_data[col].median(),
                    "min": cluster_data[col].min(),
                    "max": cluster_data[col].max()
                }
            
            cluster_stats[f"cluster_{cluster_id}"] = {
                "size": len(cluster_data),
                "percentage": len(cluster_data) / len(numeric_data) * 100,
                "column_stats": col_stats
            }
        
        # Perform PCA for visualization
        if len(numeric_cols) > 2:
            pca = PCA(n_components=2)
            pca_result = pca.fit_transform(scaled_data)
            
            # Create PCA visualization data
            pca_data = []
            for i in range(len(pca_result)):
                pca_data.append({
                    "x": float(pca_result[i, 0]),
                    "y": float(pca_result[i, 1]),
                    "cluster": int(clusters[i])
                })
        else:
            pca_data = None
        
        # Create analysis results
        analysis_results = {
            "optimal_clusters": optimal_k,
            "cluster_stats": cluster_stats,
            "inertia_values": inertia,
            "pca_data": pca_data,
            "columns_used": numeric_cols
        }
        
        return analysis_results

    async def _perform_predictive_analysis(self, data: pd.DataFrame, context: AgentProcessingContext) -> Dict[str, Any]:
        """
        Perform predictive analysis on the data.

        Args:
            data: DataFrame to analyze
            context: AgentProcessingContext object

        Returns:
            Dictionary of analysis results
        """
        try:
            from sklearn.model_selection import train_test_split
            from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
            from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, classification_report
        except ImportError:
            # Log error and inform user through context
            error_msg = "Required libraries (scikit-learn) not installed for predictive analysis."
            logger.error(error_msg)
            context.add_error(self.name, "library_missing_sklearn", {"message": error_msg})
            # It's better to return a dict that the main process can handle, or raise an exception
            # For now, returning a dict that indicates error, similar to other error paths.
            return { 
                "error": error_msg,
                "suggestion": "Please install scikit-learn to use predictive analysis."
            }
        
        # Get target variable from context metadata
        target_variable = context.metadata.get("target_variable")
        
        # If no target variable specified, try to infer it
        if not target_variable:
            # Check if there's a column that looks like a target
            for col in data.columns:
                if any(term in col.lower() for term in ["target", "label", "class", "outcome", "result"]):
                    target_variable = col
                    break
            
            # If still no target, use the last column
            if not target_variable and len(data.columns) > 0:
                target_variable = data.columns[-1]
        
        # Check if target variable exists in data
        if target_variable not in data.columns:
            return {
                "error": f"Target variable '{target_variable}' not found in data",
                "suggestion": f"Please specify a valid target variable. Available columns: {', '.join(data.columns)}"
            }
        
        # Get feature columns (all except target)
        feature_cols = [col for col in data.columns if col != target_variable]
        
        # Filter to only numeric features
        numeric_features = data[feature_cols].select_dtypes(include=["number"]).columns.tolist()
        
        # Need at least 1 numeric feature
        if len(numeric_features) == 0:
            return {
                "error": "No numeric feature columns found for predictive analysis",
                "suggestion": "Please provide a dataset with numeric feature columns"
            }
        
        # Prepare data
        X = data[numeric_features].copy()
        y = data[target_variable].copy()
        
        # Drop rows with missing values
        valid_rows = ~(X.isnull().any(axis=1) | y.isnull())
        X = X[valid_rows]
        y = y[valid_rows]
        
        # If no data left, return error
        if len(X) == 0:
            return {
                "error": "No complete rows found for predictive analysis",
                "suggestion": "Please clean your data to remove missing values"
            }
        
        # Determine if regression or classification
        is_regression = True
        if y.dtype == "object" or y.dtype.name == "category" or y.nunique() < 10:
            is_regression = False
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
        
        # Train model
        if is_regression:
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X_train, y_train)
            
            # Make predictions
            y_pred = model.predict(X_test)
            
            # Calculate metrics
            mse = mean_squared_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            # Get feature importance
            feature_importance = dict(zip(numeric_features, model.feature_importances_))
            
            # Sort feature importance
            feature_importance = {k: v for k, v in sorted(feature_importance.items(), key=lambda item: item[1], reverse=True)}
            
            # Create model results
            model_results = {
                "model_type": "regression",
                "target_variable": target_variable,
                "metrics": {
                    "mean_squared_error": mse,
                    "r2_score": r2,
                    "rmse": np.sqrt(mse)
                },
                "feature_importance": feature_importance
            }
        else:
            # For classification
            model = RandomForestClassifier(n_estimators=100, random_state=42)
            model.fit(X_train, y_train)
            
            # Make predictions
            y_pred = model.predict(X_test)
            
            # Calculate metrics
            accuracy = accuracy_score(y_test, y_pred)
            
            # Get classification report
            try:
                report = classification_report(y_test, y_pred, output_dict=True)
            except:
                report = {"error": "Could not generate classification report"}
            
            # Get feature importance
            feature_importance = dict(zip(numeric_features, model.feature_importances_))
            
            # Sort feature importance
            feature_importance = {k: v for k, v in sorted(feature_importance.items(), key=lambda item: item[1], reverse=True)}
            
            # Create model results
            model_results = {
                "model_type": "classification",
                "target_variable": target_variable,
                "metrics": {
                    "accuracy": accuracy,
                    "classification_report": report
                },
                "feature_importance": feature_importance,
                "classes": list(model.classes_)
            }
        
        # Create analysis results
        analysis_results = {
            "model_results": model_results,
            "features_used": numeric_features,
            "num_training_samples": len(X_train),
            "num_testing_samples": len(X_test)
        }
        
        return analysis_results
