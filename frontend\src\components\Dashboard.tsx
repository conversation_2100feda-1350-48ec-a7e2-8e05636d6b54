
import { Card } from "@/components/ui/card";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

const data = [
  { name: "<PERSON>", value: 400 },
  { name: "Feb", value: 300 },
  { name: "<PERSON>", value: 600 },
  { name: "Apr", value: 800 },
  { name: "May", value: 700 },
];

export const Dashboard = () => {
  return (
    <div className="space-y-6">
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-2xl font-bold mb-6">Dashboard</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* KPI Cards */}
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <h3 className="text-sm font-medium text-gray-500">Revenue</h3>
            <p className="text-2xl font-bold text-brand-600">$24,000</p>
            <span className="text-sm text-green-500">+12% from last month</span>
          </Card>
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <h3 className="text-sm font-medium text-gray-500">Users</h3>
            <p className="text-2xl font-bold text-brand-600">1,234</p>
            <span className="text-sm text-green-500">+5% from last month</span>
          </Card>
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <h3 className="text-sm font-medium text-gray-500">Conversion</h3>
            <p className="text-2xl font-bold text-brand-600">15.2%</p>
            <span className="text-sm text-orange-500">-2% from last month</span>
          </Card>
        </div>
      </motion.div>

      {/* Chart */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Performance Overview</h3>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#9b87f5"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};
