import { useEffect, useRef } from 'react';
import { BarChart4, Image as ImageIcon } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { VisualizationData } from '@/utils/visualization';
import { SaveToDashboardButton } from './SaveToDashboardButton';

// Note: In a real implementation, you would use a charting library like Chart.js, Recharts, or D3.js
// This is a simplified implementation for demonstration purposes

interface ChartVisualizationProps {
  visualization: VisualizationData;
  className?: string;
}

export const ChartVisualization = ({ visualization, className = '' }: ChartVisualizationProps) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // In a real implementation, you would initialize your chart library here
    // For example, with Chart.js:
    // const chart = new Chart(chartRef.current, {
    //   type: visualization.config?.type || 'bar',
    //   data: visualization.data,
    //   options: visualization.config?.options
    // });

    // For this demo, we'll just render a simple bar chart with CSS

    // Clean up function
    return () => {
      // In a real implementation, you would destroy your chart here
      // For example, with Chart.js:
      // chart.destroy();
    };
  }, [visualization]);

  const { data, title, description } = visualization;

  // Check if this is an image-based visualization
  if (data.image) {
    return (
      <Card className={`overflow-hidden ${className}`}>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5 text-brand-500" />
            <CardTitle className="text-lg">{title || 'Visualization'}</CardTitle>
          </div>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div className="flex justify-center">
            <img
              src={data.image}
              alt={title || 'Visualization'}
              className="max-w-full max-h-[500px] object-contain"
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-end border-t border-gray-100 bg-gray-50 py-3 px-4">
          <SaveToDashboardButton visualization={visualization} />
        </CardFooter>
      </Card>
    );
  }

  // For standard chart visualization
  const { labels, datasets } = data;

  // Make sure we have valid data
  if (!labels || !datasets || !Array.isArray(labels) || !Array.isArray(datasets)) {
    return (
      <Card className={`overflow-hidden ${className}`}>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <BarChart4 className="h-5 w-5 text-brand-500" />
            <CardTitle className="text-lg">{title || 'Chart'}</CardTitle>
          </div>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div className="p-4 text-center text-gray-500">
            Invalid chart data format
          </div>
        </CardContent>
      </Card>
    );
  }

  // Find the maximum value for scaling
  const maxValue = Math.max(...datasets.flatMap(dataset => dataset.data));

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <BarChart4 className="h-5 w-5 text-brand-500" />
          <CardTitle className="text-lg">{title || 'Chart'}</CardTitle>
        </div>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="h-60" ref={chartRef}>
          <div className="flex h-full items-end gap-2">
            {labels.map((label, index) => (
              <div key={label} className="flex flex-col items-center flex-1">
                <div className="w-full flex items-end justify-center gap-1">
                  {datasets.map((dataset, datasetIndex) => {
                    const height = `${(dataset.data[index] / maxValue) * 100}%`;
                    return (
                      <div
                        key={`${label}-${datasetIndex}`}
                        className="w-full max-w-12 rounded-t-sm"
                        style={{
                          height,
                          backgroundColor: dataset.backgroundColor || `hsl(${(datasetIndex * 60) % 360}, 70%, 60%)`,
                        }}
                      />
                    );
                  })}
                </div>
                <div className="text-xs mt-2 text-gray-500">{label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Legend */}
        <div className="flex flex-wrap gap-4 mt-4 justify-center">
          {datasets.map((dataset, index) => (
            <div key={index} className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-sm"
                style={{
                  backgroundColor: dataset.backgroundColor || `hsl(${(index * 60) % 360}, 70%, 60%)`,
                }}
              />
              <span className="text-sm">{dataset.label}</span>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end border-t border-gray-100 bg-gray-50 py-3 px-4">
        <SaveToDashboardButton visualization={visualization} />
      </CardFooter>
    </Card>
  );
};
