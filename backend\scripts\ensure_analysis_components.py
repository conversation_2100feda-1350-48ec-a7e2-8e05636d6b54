"""
<PERSON><PERSON><PERSON> to ensure all analysis components are registered.

This script is designed to be run at server startup to ensure that all components
required by the composable analysis agent are properly registered in the component registry.
"""

import os
import sys
import logging
import importlib
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import the agent modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def ensure_analysis_components():
    """
    Ensure all analysis components are registered in the component registry.
    """
    try:
        # Import the component registry
        from agents.components.registry import ComponentRegistry
        
        # Import the analysis components
        from agents.analysis_agent.components import (
            AnalysisLLMComponent,
            AnalysisParserComponent,
            DataLoaderComponent,
            AnalysisExecutorComponent
        )
        
        # Import the MCP server component
        from agents.components.mcp_server import MCPServerComponent
        
        # Log the current state
        logger.info("Current registered components: %s", ComponentRegistry.list_registered_components())
        
        # Register the analysis components if they're not already registered
        analysis_components = {
            "analysis_llm": AnalysisLLMComponent,
            "analysis_parser": AnalysisParserComponent,
            "data_loader": DataLoaderComponent,
            "analysis_executor": AnalysisExecutorComponent,
            "mcp_server": MCPServerComponent
        }
        
        for component_name, component_class in analysis_components.items():
            if component_name not in ComponentRegistry.list_registered_components():
                ComponentRegistry.register(component_name, component_class)
                logger.info("Registered %s component", component_name)
            else:
                logger.info("Component %s already registered", component_name)
        
        # Log the updated state
        logger.info("Updated registered components: %s", ComponentRegistry.list_registered_components())
        
        # Ensure MCP tools are registered
        from agents.tools.mcp.registry import MCPToolRegistry
        from agents.tools.mcp.data_analysis import DataAnalysisTool
        from agents.tools.mcp.data_cleaning import DataCleaningTool
        from agents.tools.mcp.data_visualization import DataVisualizationTool
        from agents.tools.mcp.data_querying import DataQueryingTool
        from agents.tools.mcp.advanced_query import AdvancedQueryTool
        from agents.tools.mcp.data_filtering import DataFilteringTool
        from agents.tools.mcp.sentiment_analysis import SentimentAnalysisTool
        from agents.tools.mcp.text_processing import TextProcessingTool
        from agents.tools.mcp.content_generation import ContentGenerationTool
        from agents.tools.mcp.document_embedding import DocumentEmbeddingTool
        
        # Log the current state
        logger.info("Current registered MCP tools: %s", MCPToolRegistry.list_registered_tools())
        
        # Register the MCP tools if they're not already registered
        mcp_tools = {
            "data_analysis": DataAnalysisTool,
            "data_cleaning": DataCleaningTool,
            "data_visualization": DataVisualizationTool,
            "data_querying": DataQueryingTool,
            "advanced_query": AdvancedQueryTool,
            "data_filtering": DataFilteringTool,
            "sentiment_analysis": SentimentAnalysisTool,
            "text_processing": TextProcessingTool,
            "content_generation": ContentGenerationTool,
            "document_embedding": DocumentEmbeddingTool
        }
        
        for tool_name, tool_class in mcp_tools.items():
            if tool_name not in MCPToolRegistry.list_registered_tools():
                MCPToolRegistry.register(tool_name, tool_class)
                logger.info("Registered %s MCP tool", tool_name)
            else:
                logger.info("MCP tool %s already registered", tool_name)
        
        # Log the updated state
        logger.info("Updated registered MCP tools: %s", MCPToolRegistry.list_registered_tools())
        
        return True
    except Exception as e:
        logger.error("Error ensuring analysis components: %s", str(e), exc_info=True)
        return False

if __name__ == "__main__":
    success = ensure_analysis_components()
    if success:
        logger.info("Successfully ensured analysis components are registered")
    else:
        logger.error("Failed to ensure analysis components are registered")
        sys.exit(1)
