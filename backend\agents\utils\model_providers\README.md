# Model Provider System

This directory contains the model provider system for the Datagenius backend. The system provides a unified interface for working with different AI model providers, including:

- Groq
- OpenAI
- Anthropic
- Google Gemini
- OpenRouter
- Requesty
- Ollama (local models)

## Architecture

The system is designed to be modular and extensible, with the following components:

- **BaseModelProvider**: Abstract base class for model providers
- **Provider Implementations**: Concrete implementations for each provider
- **Registry**: Central registry for managing provider instances
- **Utilities**: Helper functions for working with providers
- **Configuration**: Centralized configuration for providers
- **Caching**: Response caching system
- **Monitoring**: Usage tracking and monitoring

## Usage

To use the model provider system, you can use the `get_model` function:

```python
from agents.utils.model_providers.utils import get_model

# Get a model from a specific provider
model = await get_model("groq", "llama3-70b-8192", {"temperature": 0.7})

# Get a model using the default provider and model
model = await get_model()

# Use the model
response = await model.ainvoke("Hello, world!")
```

## Configuration

The system is configured using environment variables and the `config.py` module. The following environment variables are supported:

- `GROQ_ENDPOINT`: Groq API endpoint
- `OPENAI_ENDPOINT`: OpenAI API endpoint
- `GEMINI_ENDPOINT`: Google Gemini API endpoint
- `OPENROUTER_ENDPOINT`: OpenRouter API endpoint
- `OLLAMA_ENDPOINT`: Ollama API endpoint
- `REQUESTY_ENDPOINT`: Requesty API endpoint
- `ANTHROPIC_ENDPOINT`: Anthropic API endpoint

- `GROQ_API_KEY`: Groq API key
- `OPENAI_API_KEY`: OpenAI API key
- `GEMINI_API_KEY`: Google Gemini API key
- `OPENROUTER_API_KEY`: OpenRouter API key
- `REQUESTY_API_KEY`: Requesty API key
- `ANTHROPIC_API_KEY`: Anthropic API key

## Adding a New Provider

To add a new provider, follow these steps:

1. Create a new file `<provider_id>_provider.py` in this directory
2. Implement the `BaseModelProvider` interface
3. Update `register.py` to register the new provider
4. Update `config.py` to add configuration for the new provider

## Dependencies

The system depends on the following packages:

- langchain-core
- langchain-openai
- langchain-groq
- langchain-anthropic
- langchain-ollama
- langchain-google-genai
- requests

You can install these dependencies using the provided `requirements.txt` file:

```bash
pip install -r requirements.txt
```
