# PandasAI v3 Implementation Plan

This document outlines the plan for upgrading our current PandasAI implementation to the newer v3 API and enhancing our data analysis capabilities in the composable analyst agent.

## Current Status

Our system currently uses PandasAI v2 with:
- `PandasAI` class for most tools
- `SmartDataframe` class for data visualization
- Limited LLM provider support (primarily OpenAI)
- No semantic layer or training capabilities

## Implementation Goals

1. Upgrade to PandasAI v3 API
2. Implement semantic layer for enhanced data understanding
3. Add support for multiple LLM providers
4. Implement training capabilities for few-shot learning
5. Integrate with vector databases for improved data retrieval
6. Enhance error handling and fallback mechanisms

## Phase 1: Setup and Infrastructure (Week 1)

### 1.1 Update Dependencies
- Ensure PandasAI v3.0.0b2 or newer is in requirements.txt
- Update any related dependencies
- Create a virtual environment for testing

```bash
# Update requirements.txt
pip install "pandasai>=3.0.0b2" --upgrade
pip freeze > requirements.txt.new
# Review and merge changes
```

### 1.2 Create PandasAI v3 Wrapper Module
- Create a new module `backend/agents/tools/pandasai_v3/`
- Implement a wrapper class for PandasAI v3 Agent
- Create utility functions for common operations

```python
# backend/agents/tools/pandasai_v3/wrapper.py
import logging
import os
import pandas as pd
from typing import Dict, Any, Optional, Union, List

import pandasai as pai
from pandasai import Agent

logger = logging.getLogger(__name__)

class PandasAIWrapper:
    """Wrapper for PandasAI v3 Agent class."""
    
    def __init__(self):
        """Initialize the PandasAI wrapper."""
        self.api_key = None
        self.agent = None
        self.df = None
        self.config = {}
        
    def initialize(self, api_key: str, provider: str = "openai"):
        """Initialize PandasAI with API key."""
        self.api_key = api_key
        pai.api_key.set(api_key)
        logger.info(f"Initialized PandasAI with {provider} API key")
        
    def load_dataframe(self, file_path: str) -> bool:
        """Load a dataframe from a file path."""
        try:
            if file_path.endswith('.csv'):
                self.df = pai.read_csv(file_path)
            elif file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                self.df = pai.read_excel(file_path)
            elif file_path.endswith('.json'):
                self.df = pai.read_json(file_path)
            else:
                logger.error(f"Unsupported file format: {file_path}")
                return False
                
            logger.info(f"Successfully loaded dataframe from {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error loading dataframe from {file_path}: {e}", exc_info=True)
            return False
            
    def create_agent(self, df: Optional[pd.DataFrame] = None) -> bool:
        """Create a PandasAI Agent instance."""
        try:
            if df is not None:
                self.df = df
                
            if self.df is None:
                logger.error("No dataframe loaded")
                return False
                
            self.agent = Agent(self.df)
            logger.info("Created PandasAI Agent instance")
            return True
        except Exception as e:
            logger.error(f"Error creating PandasAI Agent: {e}", exc_info=True)
            return False
            
    def chat(self, query: str) -> Dict[str, Any]:
        """Chat with the PandasAI Agent."""
        try:
            if self.agent is None:
                logger.error("PandasAI Agent not initialized")
                return {"error": "Agent not initialized"}
                
            response = self.agent.chat(query)
            logger.info(f"PandasAI chat response type: {type(response)}")
            
            # Process and format the response
            return self._format_response(response)
        except Exception as e:
            logger.error(f"Error in PandasAI chat: {e}", exc_info=True)
            return {"error": str(e)}
            
    def _format_response(self, response: Any) -> Dict[str, Any]:
        """Format the response from PandasAI."""
        # Handle different response types
        if isinstance(response, pd.DataFrame):
            return {
                "type": "dataframe",
                "data": response.to_dict(orient="records"),
                "columns": response.columns.tolist()
            }
        elif isinstance(response, (int, float)):
            return {
                "type": "number",
                "value": response
            }
        elif hasattr(response, "image_path") and response.image_path:
            # Handle chart response
            return {
                "type": "chart",
                "image_path": response.image_path
            }
        else:
            # Default to text response
            return {
                "type": "text",
                "text": str(response)
            }
            
    def train(self, instructions: Optional[str] = None, 
              queries: Optional[List[str]] = None, 
              codes: Optional[List[str]] = None) -> bool:
        """Train the PandasAI Agent."""
        try:
            if self.agent is None:
                logger.error("PandasAI Agent not initialized")
                return False
                
            if instructions:
                self.agent.train(docs=instructions)
                logger.info("Trained PandasAI Agent with instructions")
                
            if queries and codes and len(queries) == len(codes):
                self.agent.train(queries=queries, codes=codes)
                logger.info(f"Trained PandasAI Agent with {len(queries)} Q/A pairs")
                
            return True
        except Exception as e:
            logger.error(f"Error training PandasAI Agent: {e}", exc_info=True)
            return False
```

### 1.3 Create Semantic Layer Utilities
- Implement utilities for creating and managing semantic layers
- Create schema templates for common data types

```python
# backend/agents/tools/pandasai_v3/semantic_layer.py
import logging
import os
import pandas as pd
from typing import Dict, Any, List, Optional

import pandasai as pai

logger = logging.getLogger(__name__)

class SemanticLayerManager:
    """Manager for PandasAI semantic layers."""
    
    def __init__(self, base_path: str = "data/semantic_layers"):
        """Initialize the semantic layer manager."""
        self.base_path = base_path
        os.makedirs(base_path, exist_ok=True)
        
    def create_layer(self, df: pd.DataFrame, name: str, 
                    description: str, columns: Optional[List[Dict[str, Any]]] = None) -> str:
        """Create a semantic layer for a dataframe."""
        try:
            # Generate path
            path = f"datagenius/{name}"
            
            # Create semantic layer
            layer = pai.create(
                path=path,
                df=df,
                description=description,
                columns=columns
            )
            
            logger.info(f"Created semantic layer: {path}")
            return path
        except Exception as e:
            logger.error(f"Error creating semantic layer: {e}", exc_info=True)
            return ""
            
    def load_layer(self, path: str):
        """Load a semantic layer."""
        try:
            layer = pai.load(path)
            logger.info(f"Loaded semantic layer: {path}")
            return layer
        except Exception as e:
            logger.error(f"Error loading semantic layer: {e}", exc_info=True)
            return None
            
    def push_layer(self, path: str) -> bool:
        """Push a semantic layer to the platform."""
        try:
            layer = self.load_layer(path)
            if layer:
                layer.push()
                logger.info(f"Pushed semantic layer: {path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error pushing semantic layer: {e}", exc_info=True)
            return False
            
    def infer_columns(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Infer column schemas from a dataframe."""
        columns = []
        for col in df.columns:
            col_type = str(df[col].dtype)
            
            # Map pandas dtypes to semantic layer types
            if col_type.startswith('int'):
                type_name = "integer"
            elif col_type.startswith('float'):
                type_name = "float"
            elif col_type.startswith('datetime'):
                type_name = "datetime"
            elif col_type.startswith('bool'):
                type_name = "boolean"
            else:
                type_name = "string"
                
            columns.append({
                "name": col,
                "type": type_name,
                "description": f"The {col} column"
            })
            
        return columns
```

## Phase 2: Tool Implementation (Week 2)

### 2.1 Create New MCP Tools Using PandasAI v3
- Implement new MCP tools that use the PandasAI v3 wrapper
- Create tools for data analysis, visualization, and querying

```python
# backend/agents/tools/mcp/pandasai_analysis.py
import logging
import os
import pandas as pd
from typing import Dict, Any, Optional

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper

logger = logging.getLogger(__name__)

class PandasAIAnalysisTool(BaseMCPTool):
    """Tool for analyzing data using PandasAI v3."""
    
    def __init__(self):
        """Initialize the PandasAI analysis tool."""
        super().__init__(
            name="pandasai_analysis",
            description="Analyze data using PandasAI v3",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "query": {"type": "string"},
                    "api_key": {"type": "string"},
                    "provider": {"type": "string", "default": "openai"}
                },
                "required": ["file_path", "query", "api_key"]
            }
        )
        self.pandasai = PandasAIWrapper()
        
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the PandasAI analysis tool."""
        file_path = arguments.get("file_path")
        query = arguments.get("query")
        api_key = arguments.get("api_key")
        provider = arguments.get("provider", "openai")
        
        try:
            # Initialize PandasAI
            self.pandasai.initialize(api_key, provider)
            
            # Load dataframe
            if not self.pandasai.load_dataframe(file_path):
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error loading dataframe from {file_path}"}]
                }
                
            # Create agent
            if not self.pandasai.create_agent():
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": "Error creating PandasAI Agent"}]
                }
                
            # Chat with agent
            result = self.pandasai.chat(query)
            
            # Handle error
            if "error" in result:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error in PandasAI chat: {result['error']}"}]
                }
                
            # Format response based on result type
            if result["type"] == "dataframe":
                return {
                    "isError": False,
                    "content": [
                        {"type": "text", "text": f"Analysis results for: {query}"},
                        {"type": "table", "data": result["data"], "columns": result["columns"]}
                    ]
                }
            elif result["type"] == "chart":
                # Read image file and convert to base64
                import base64
                with open(result["image_path"], "rb") as image_file:
                    encoded_image = base64.b64encode(image_file.read()).decode("utf-8")
                    
                return {
                    "isError": False,
                    "content": [
                        {"type": "text", "text": f"Visualization for: {query}"},
                        {"type": "image", "src": f"data:image/png;base64,{encoded_image}"}
                    ]
                }
            else:
                return {
                    "isError": False,
                    "content": [{"type": "text", "text": result["text"]}]
                }
                
        except Exception as e:
            logger.error(f"Error executing PandasAI analysis tool: {e}", exc_info=True)
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Error: {str(e)}"}]
            }
```

### 2.2 Update MCP Tool Registry
- Register the new PandasAI v3 tools with the MCP tool registry
- Update the register.py file

```python
# Update backend/agents/tools/mcp/register.py
from .pandasai_analysis import PandasAIAnalysisTool
from .pandasai_visualization import PandasAIVisualizationTool
from .pandasai_query import PandasAIQueryTool

# In register_mcp_tools function:
MCPToolRegistry.register("pandasai_analysis", PandasAIAnalysisTool)
logger.info("Registered PandasAIAnalysisTool with MCP tool registry")

MCPToolRegistry.register("pandasai_visualization", PandasAIVisualizationTool)
logger.info("Registered PandasAIVisualizationTool with MCP tool registry")

MCPToolRegistry.register("pandasai_query", PandasAIQueryTool)
logger.info("Registered PandasAIQueryTool with MCP tool registry")
```

### 2.3 Create LLM Provider Adapters
- Implement adapters for different LLM providers
- Support Groq, OpenAI, Google Gemini, etc.

```python
# backend/agents/tools/pandasai_v3/llm_providers.py
import logging
import os
from typing import Dict, Any, Optional

import pandasai as pai
from pandasai.llm.openai import OpenAI
# Import other providers as needed

logger = logging.getLogger(__name__)

class LLMProviderFactory:
    """Factory for creating PandasAI LLM providers."""
    
    @staticmethod
    def create_provider(provider: str, api_key: str, model: Optional[str] = None) -> Any:
        """Create a PandasAI LLM provider."""
        provider = provider.lower()
        
        try:
            if provider == "openai":
                return OpenAI(api_token=api_key, model=model or "gpt-3.5-turbo")
            elif provider == "groq":
                # Implement Groq provider
                # This will require creating a custom LLM class for PandasAI
                from .custom_providers import GroqLLM
                return GroqLLM(api_key=api_key, model=model or "llama3-70b-8192")
            elif provider == "google":
                # Implement Google Gemini provider
                from .custom_providers import GeminiLLM
                return GeminiLLM(api_key=api_key, model=model or "gemini-pro")
            else:
                logger.error(f"Unsupported LLM provider: {provider}")
                raise ValueError(f"Unsupported LLM provider: {provider}")
        except ImportError as e:
            logger.error(f"Missing dependency for {provider}: {e}")
            raise ValueError(f"Missing dependency for {provider}: {e}")
        except Exception as e:
            logger.error(f"Error creating LLM provider {provider}: {e}", exc_info=True)
            raise ValueError(f"Error creating LLM provider {provider}: {e}")
```

## Phase 3: Integration with Composable Analyst Agent (Week 3)

### 3.1 Update Composable Analyst Agent
- Modify the ComposableAnalysisAgent to use the new PandasAI v3 tools
- Update the process_message method to handle semantic layers

```python
# Update backend/agents/analysis_agent/composable_agent.py
# In the process_message method:

# For natural language queries using PandasAI v3
if is_query_request:
    nlq_result = await mcp_server.call_tool("pandasai_query", {
        "file_path": file_path,
        "query": message,
        "api_key": self.config.get("api_key"),
        "provider": self.config.get("provider", "openai")
    })
    
    if not nlq_result.get("isError", False):
        data_analysis_results = nlq_result
        ctx["metadata"]["query_result"] = nlq_result
        ctx["metadata"]["task_type"] = "natural_language_query"
        logger.info(f"Processed natural language query using PandasAI v3: {message}")
```

### 3.2 Implement Semantic Layer Management
- Create a component for managing semantic layers
- Add methods for creating, loading, and updating semantic layers

```python
# backend/agents/analysis_agent/components.py
# Add a new component:

class SemanticLayerComponent(AgentComponent):
    """Component for managing semantic layers."""
    
    def __init__(self):
        """Initialize the semantic layer component."""
        super().__init__()
        self.manager = None
        
    async def _initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the component with configuration."""
        from agents.tools.pandasai_v3.semantic_layer import SemanticLayerManager
        self.manager = SemanticLayerManager()
        logger.info("Initialized SemanticLayerComponent")
        
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a request using this component."""
        # Check if we need to create a semantic layer
        if "create_semantic_layer" in context:
            file_path = context["create_semantic_layer"]["file_path"]
            name = context["create_semantic_layer"]["name"]
            description = context["create_semantic_layer"]["description"]
            
            # Load dataframe
            try:
                if file_path.endswith('.csv'):
                    df = pd.read_csv(file_path)
                elif file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                    df = pd.read_excel(file_path)
                else:
                    context["response"] = f"Unsupported file format: {file_path}"
                    return context
                    
                # Infer columns
                columns = self.manager.infer_columns(df)
                
                # Create semantic layer
                layer_path = self.manager.create_layer(df, name, description, columns)
                
                if layer_path:
                    context["semantic_layer_path"] = layer_path
                    context["response"] = f"Created semantic layer: {layer_path}"
                else:
                    context["response"] = "Failed to create semantic layer"
            except Exception as e:
                logger.error(f"Error creating semantic layer: {e}", exc_info=True)
                context["response"] = f"Error creating semantic layer: {str(e)}"
                
        return context
```

### 3.3 Implement Training System
- Create a component for training PandasAI agents
- Add methods for managing training examples

```python
# backend/agents/analysis_agent/components.py
# Add a new component:

class PandasAITrainingComponent(AgentComponent):
    """Component for training PandasAI agents."""
    
    def __init__(self):
        """Initialize the training component."""
        super().__init__()
        self.wrapper = None
        
    async def _initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the component with configuration."""
        from agents.tools.pandasai_v3.wrapper import PandasAIWrapper
        self.wrapper = PandasAIWrapper()
        logger.info("Initialized PandasAITrainingComponent")
        
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a request using this component."""
        # Check if we need to train a PandasAI agent
        if "train_pandasai" in context:
            file_path = context["train_pandasai"]["file_path"]
            instructions = context["train_pandasai"].get("instructions")
            queries = context["train_pandasai"].get("queries")
            codes = context["train_pandasai"].get("codes")
            api_key = context["train_pandasai"].get("api_key")
            provider = context["train_pandasai"].get("provider", "openai")
            
            # Initialize PandasAI
            self.wrapper.initialize(api_key, provider)
            
            # Load dataframe
            if not self.wrapper.load_dataframe(file_path):
                context["response"] = f"Error loading dataframe from {file_path}"
                return context
                
            # Create agent
            if not self.wrapper.create_agent():
                context["response"] = "Error creating PandasAI Agent"
                return context
                
            # Train agent
            success = self.wrapper.train(instructions, queries, codes)
            
            if success:
                context["response"] = "Successfully trained PandasAI Agent"
            else:
                context["response"] = "Failed to train PandasAI Agent"
                
        return context
```

## Phase 4: Testing and Optimization (Week 4)

### 4.1 Create Test Suite
- Implement unit tests for the PandasAI v3 wrapper
- Create integration tests for the MCP tools
- Test with different data types and queries

```python
# backend/tests/test_pandasai_v3.py
import unittest
import pandas as pd
import os
from agents.tools.pandasai_v3.wrapper import PandasAIWrapper

class TestPandasAIWrapper(unittest.TestCase):
    """Test cases for PandasAI v3 wrapper."""
    
    def setUp(self):
        """Set up test environment."""
        self.wrapper = PandasAIWrapper()
        self.test_csv = "test_data.csv"
        
        # Create test CSV
        df = pd.DataFrame({
            "A": [1, 2, 3, 4, 5],
            "B": [10, 20, 30, 40, 50],
            "C": ["a", "b", "c", "d", "e"]
        })
        df.to_csv(self.test_csv, index=False)
        
    def tearDown(self):
        """Clean up test environment."""
        if os.path.exists(self.test_csv):
            os.remove(self.test_csv)
            
    def test_load_dataframe(self):
        """Test loading a dataframe."""
        result = self.wrapper.load_dataframe(self.test_csv)
        self.assertTrue(result)
        self.assertIsNotNone(self.wrapper.df)
        
    # Add more test cases...
```

### 4.2 Optimize Performance
- Implement caching for PandasAI responses
- Optimize dataframe loading and processing
- Add parallel processing for large datasets

```python
# backend/agents/tools/pandasai_v3/cache.py
import logging
import os
import json
import hashlib
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class ResponseCache:
    """Cache for PandasAI responses."""
    
    def __init__(self, cache_dir: str = "cache/pandasai"):
        """Initialize the response cache."""
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        
    def _get_cache_key(self, file_path: str, query: str, provider: str) -> str:
        """Generate a cache key."""
        # Create a hash of the file path, query, and provider
        key = f"{file_path}:{query}:{provider}"
        return hashlib.md5(key.encode()).hexdigest()
        
    def get(self, file_path: str, query: str, provider: str) -> Optional[Dict[str, Any]]:
        """Get a cached response."""
        key = self._get_cache_key(file_path, query, provider)
        cache_file = os.path.join(self.cache_dir, f"{key}.json")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, "r") as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error reading cache file: {e}", exc_info=True)
                return None
        return None
        
    def set(self, file_path: str, query: str, provider: str, response: Dict[str, Any]) -> bool:
        """Set a cached response."""
        key = self._get_cache_key(file_path, query, provider)
        cache_file = os.path.join(self.cache_dir, f"{key}.json")
        
        try:
            with open(cache_file, "w") as f:
                json.dump(response, f)
            return True
        except Exception as e:
            logger.error(f"Error writing cache file: {e}", exc_info=True)
            return False
```

### 4.3 Implement Error Handling and Fallbacks
- Add robust error handling for PandasAI operations
- Implement fallback mechanisms for failed queries
- Create a system for logging and analyzing errors

```python
# backend/agents/tools/pandasai_v3/error_handler.py
import logging
import traceback
from typing import Dict, Any, Callable, Optional

logger = logging.getLogger(__name__)

class ErrorHandler:
    """Error handler for PandasAI operations."""
    
    @staticmethod
    def handle_error(func: Callable, fallback: Optional[Callable] = None, *args, **kwargs) -> Dict[str, Any]:
        """Handle errors in PandasAI operations."""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error in PandasAI operation: {e}", exc_info=True)
            
            # Log detailed error information
            error_info = {
                "error": str(e),
                "traceback": traceback.format_exc(),
                "args": args,
                "kwargs": kwargs
            }
            logger.debug(f"Error details: {error_info}")
            
            # Try fallback if provided
            if fallback:
                try:
                    logger.info(f"Attempting fallback for failed PandasAI operation")
                    return fallback(*args, **kwargs)
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed: {fallback_error}", exc_info=True)
                    
            # Return error response
            return {"error": str(e)}
```

## Phase 5: Documentation and Deployment (Week 5)

### 5.1 Create Documentation
- Write comprehensive documentation for the PandasAI v3 implementation
- Create examples and tutorials for common use cases
- Document the semantic layer and training systems

### 5.2 Update Configuration
- Update the composable-analyst.yaml file to include the new components
- Configure default settings for PandasAI v3

```yaml
# Update backend/personas/composable-analyst.yaml
components:
  # Add new components
  - type: semantic_layer
    name: semantic_layer_manager
    
  - type: pandasai_training
    name: pandasai_trainer
    
  # Update MCP server component
  - type: mcp_server
    name: analysis_tools
    server_name: datagenius-analysis-tools
    server_version: 1.0.0
    tools:
      # Add new tools
      - type: pandasai_analysis
      - type: pandasai_visualization
      - type: pandasai_query
      # Keep existing tools
      - type: content_generation
      - type: data_analysis
      # ...
```

### 5.3 Deploy and Monitor
- Deploy the updated system
- Monitor performance and error rates
- Collect user feedback for further improvements

## Future Enhancements

1. **Advanced Training System**
   - Implement a UI for managing training examples
   - Create a feedback loop for improving PandasAI responses
   - Develop a system for sharing training data across users

2. **Enhanced Semantic Layer Management**
   - Create a UI for managing semantic layers
   - Implement automatic schema detection and enhancement
   - Develop a system for sharing semantic layers across users

3. **Multi-Modal Analysis**
   - Extend PandasAI to handle images and other data types
   - Implement cross-modal analysis capabilities
   - Create visualizations that combine multiple data sources

4. **Automated Insights**
   - Implement automatic insight generation
   - Create a system for tracking and updating insights
   - Develop a recommendation system for data analysis

## Conclusion

This implementation plan provides a comprehensive roadmap for upgrading our current PandasAI implementation to the newer v3 API and enhancing our data analysis capabilities in the composable analyst agent. By following this plan, we will be able to leverage the full power of PandasAI v3, including the semantic layer, training capabilities, and improved data analysis features.
