"""
Migration to add provider settings to users table.

This migration adds provider settings columns to the users table.
"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.sql import table, column
import datetime

# revision identifiers, used by Alembic
revision = 'provider_settings_001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Upgrade database schema."""
    # Add provider settings columns to users table
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('default_provider', sa.String(50), nullable=True))
        batch_op.add_column(sa.Column('use_local_llm', sa.<PERSON>(), nullable=True, server_default='0'))


def downgrade():
    """Downgrade database schema."""
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_column('use_local_llm')
        batch_op.drop_column('default_provider')
