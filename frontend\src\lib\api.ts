/**
 * API service for interacting with the backend.
 */
import { cache } from '@/utils/cache';
import { requestQueue } from '@/utils/requestQueue';
import { authApi } from './authApi';
import { isTokenExpired, shouldRefreshToken } from '@/utils/jwt';
import { providerApi, type Provider, type ProviderModel } from './providerApi'; // Import types

// Re-export providerApi and types for use in components
export { providerApi };
export type { Provider, ProviderModel }; // Use export type

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// Types
export interface Conversation {
  id: string;
  user_id: number;
  persona_id: string;
  title: string;
  created_at: string;
  updated_at: string;
  messages: Message[];
}

export interface Message {
  id: string;
  conversation_id: string;
  sender: 'user' | 'ai';
  content: string;
  metadata?: any;
  created_at: string;
}

export interface SendMessageRequest {
  conversation_id: string;
  message: string;
  context?: any;
}

export interface SendMessageResponse {
  conversation_id: string;
  user_message: Message;
  ai_message: Message;
}

export interface ConversationListResponse {
  conversations: Conversation[];
  total: number;
}

export interface Persona {
  id: string;
  name: string;
  description: string;
  industry: string;
  skills: string[];
  rating: number;
  reviewCount: number;
  imageUrl: string;
  isAvailable: boolean;
  isPurchased?: boolean;
  capabilities?: string[];
}

// Helper function to get the auth token
const getToken = (): string | null => {
  return localStorage.getItem('token');
};

// Removed CustomRequestOptions and ApiRequestOptions to simplify and avoid type conflicts

// Track if a token refresh is in progress
let isRefreshing = false;
let refreshPromise: Promise<boolean> | null = null;

// Store pending requests that are waiting for token refresh
const pendingRequests: Array<() => void> = [];

// Helper function to handle authentication errors
const handleAuthError = async () => {
  const refreshToken = localStorage.getItem('refresh_token');

  // If no refresh token is available, log out immediately
  if (!refreshToken) {
    // Clear tokens
    localStorage.removeItem('token');
    localStorage.removeItem('refresh_token');

    // Throw an error that will be caught by the component
    throw new Error('Authentication failed. Your session has expired. Please sign in again.');
  }

  // If a refresh is already in progress, wait for it to complete
  if (isRefreshing) {
    return refreshPromise;
  }

  // Start the refresh process
  isRefreshing = true;

  try {
    // Create a new promise for the refresh operation
    refreshPromise = new Promise(async (resolve) => {
      try {
        // Attempt to refresh the token
        const response = await authApi.refreshToken(refreshToken);

        // Store the new tokens
        localStorage.setItem('token', response.access_token);
        if (response.refresh_token) {
          localStorage.setItem('refresh_token', response.refresh_token);
        }

        // Resolve all pending requests
        pendingRequests.forEach(callback => callback());
        pendingRequests.length = 0;

        resolve(true);
      } catch (error) {
        // If refresh fails, clear tokens and reject
        localStorage.removeItem('token');
        localStorage.removeItem('refresh_token');

        // Reject all pending requests
        pendingRequests.length = 0;

        resolve(false);
        throw new Error('Authentication failed. Your session has expired. Please sign in again.');
      } finally {
        isRefreshing = false;
        refreshPromise = null;
      }
    });

    return await refreshPromise;
  } catch (error) {
    throw error;
  }
};

// Define custom options interface for our API requests
interface CustomRequestOptions extends Omit<RequestInit, 'cache'> {
  cache?: boolean; // Our custom cache flag, not the standard RequestCache
  cacheTtl?: number; // Cache time-to-live in milliseconds
}

// Helper function for API requests with custom options
export const apiRequest = async (endpoint: string, options: CustomRequestOptions = {}) => {
  // Extract custom options
  const { cache: useCache, cacheTtl, ...standardOptions } = options;

  // Create the request function
  const executeRequestWithAuth = async (): Promise<any> => {
    let token = getToken();

    // Check if token exists and needs refreshing before making the request
    if (token && shouldRefreshToken(token)) {
      try {
        // Try to refresh the token proactively
        await handleAuthError();
        // Get the new token after refresh
        token = getToken();
      } catch (error) {
        // If refresh fails, continue with the current token
        console.warn('Proactive token refresh failed, continuing with current token');
      }
    }

    const headers = {
      'Content-Type': 'application/json',
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      ...standardOptions.headers, // Include original headers
    };

    // Check if we should use cache
    if (useCache) {
      const cacheKey = `${endpoint}`;
      const cachedData = cache.get(cacheKey);
      if (cachedData) {
        console.log(`Using cached data for ${endpoint}`);
        return cachedData;
      }
    }

    try {
      // Log simplified options
      console.log('API Request options:', { endpoint, method: standardOptions.method || 'GET' });

      // Make the request with standard fetch options only
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        ...standardOptions, // Pass only standard fetch options
        headers,
      });

      // Handle authentication errors
      if (response.status === 401) {
        console.log('Authentication error, session expired');

        // Try to refresh the token
        const refreshSuccessful = await handleAuthError();

        if (refreshSuccessful) {
          // If refresh was successful, retry the original request
          return executeRequestWithAuth();
        } else {
          // If refresh failed, throw an error
          throw new Error('Authentication failed. Your session has expired. Please sign in again.');
        }
      }

      if (!response.ok) {
        const error = await response.json().catch(() => ({ detail: 'An error occurred' }));
        throw new Error(error.detail || 'An error occurred');
      }

      const data = await response.json();

      // Store in cache if caching is enabled
      if (useCache) {
        const cacheKey = `${endpoint}`;
        cache.set(cacheKey, data, { ttl: cacheTtl });
        console.log(`Cached data for ${endpoint} with TTL ${cacheTtl}ms`);
      }

      return data;
    } catch (error) {
      console.error(`API request error for ${endpoint}:`, error);
      throw error;
    }
  };

  // Execute the request directly
  try {
    return await executeRequestWithAuth(); // Ensure await here
  } catch (error) {
    console.error(`API request error for ${endpoint}:`, error);
    throw error;
  }
};

// Chat API functions
export const chatApi = {
  // Create a new conversation
  createConversation: async (persona_id: string, title?: string): Promise<Conversation> => {
    return apiRequest('/chat/conversations', {
      method: 'POST',
      body: JSON.stringify({ persona_id, title }),
    });
  },

  // Get a list of conversations
  getConversations: async (skip = 0, limit = 100): Promise<ConversationListResponse> => {
    // Removed cache, queue, priority options
    return apiRequest(`/chat/conversations?skip=${skip}&limit=${limit}`);
  },

  // Get a specific conversation with messages
  getConversation: async (conversation_id: string): Promise<Conversation> => {
    // Removed cache, queue, priority options
    return apiRequest(`/chat/conversations/${conversation_id}`);
  },

  // Update a conversation's title
  updateConversation: async (conversation_id: string, title: string): Promise<Conversation> => {
    return apiRequest(`/chat/conversations/${conversation_id}`, {
      method: 'PUT',
      body: JSON.stringify({ title }),
    });
  },

  // Delete a conversation
  deleteConversation: async (conversation_id: string): Promise<{ message: string }> => {
    return apiRequest(`/chat/conversations/${conversation_id}`, {
      method: 'DELETE',
    });
  },

  // Send a message to an agent
  sendMessage: async (request: SendMessageRequest): Promise<SendMessageResponse> => {
    return apiRequest('/chat/send', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },
};

// Agent API functions
export const agentApi = {
  // Invoke an agent directly
  invokeAgent: async (persona_id: string, request: {
    message: string;
    conversation_id: string;
    context?: any;
    config?: any;
  }): Promise<any> => {
    return apiRequest(`/agents/${persona_id}/invoke`, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },

  // Get agent capabilities
  getAgentCapabilities: async (persona_id: string): Promise<{ capabilities: string[] }> => {
    return apiRequest(`/agents/${persona_id}/capabilities`);
  },
};

// File API functions
export const fileApi = {
  // Get a list of all files
  getFiles: async (skip = 0, limit = 100): Promise<{ files: any[] }> => {
    return apiRequest(`/files?skip=${skip}&limit=${limit}`);
  },

  // Get a specific file
  getFile: async (fileId: string): Promise<any> => {
    return apiRequest(`/files/${fileId}`);
  },

  // Delete a file
  deleteFile: async (fileId: string): Promise<{ message: string }> => {
    return apiRequest(`/files/${fileId}`, {
      method: 'DELETE',
    });
  },

  // Upload a file
  uploadFile: async (file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);

    // We need to use fetch directly here because FormData requires special handling
    const token = localStorage.getItem('token'); // Using the correct token key name

    if (!token) {
      throw new Error('Authentication token not found. Please log in again.');
    }

    const response = await fetch(`${API_BASE_URL}/files`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      // Handle different error status codes
      if (response.status === 401) {
        throw new Error('Authentication failed. Please log in again.');
      }

      try {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to upload file');
      } catch (e) {
        // If parsing JSON fails, use the status text
        throw new Error(`Failed to upload file: ${response.statusText}`);
      }
    }

    return response.json();
  },
};

// Persona API functions
export const personaApi = {
  // Get a list of all personas
  getPersonas: async (industry?: string): Promise<{ personas: Persona[] }> => {
    const endpoint = industry ? `/personas?industry=${industry}` : '/personas';
    // Removed cache, queue, priority options
    return apiRequest(endpoint);
  },

  // Get a specific persona
  getPersona: async (persona_id: string): Promise<Persona> => {
    // Removed cache, queue, priority options
    return apiRequest(`/personas/${persona_id}`);
  },

  // Check if a user has access to a persona
  checkPersonaAccess: async (persona_id: string): Promise<{ has_access: boolean, is_purchased: boolean }> => {
    // Removed cache, queue, priority options
    return apiRequest(`/personas/access/${persona_id}`);
  },

  // Get a list of purchased personas
  getPurchasedPersonas: async (): Promise<string[]> => {
    try {
      // Removed cache, queue, priority options
      const result = await apiRequest('/personas/purchased');

      console.log('Purchased personas response:', result);

      // Handle both array and non-array responses
      if (Array.isArray(result)) {
        return result;
      } else {
        console.warn('Unexpected response format from purchased personas endpoint:', result);
        return [];
      }
    } catch (error) {
      console.error('Error fetching purchased personas:', error);
      // Return empty array as fallback
      return [];
    }
  },

  // Get a list of all industries
  getIndustries: async (): Promise<{ industries: string[] }> => {
    // Removed cache, queue, priority options
    return apiRequest('/personas/industries');
  },
};

// WebSocket connection for real-time chat
export class ChatWebSocket {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: number = 1000; // Start with 1 second
  private pingInterval: number | null = null;
  private token: string | null = null;
  private conversationId: string | null = null;
  private messageHandlers: ((data: any) => void)[] = [];
  private statusHandlers: ((status: 'connecting' | 'connected' | 'disconnected' | 'error') => void)[] = [];
  private deliveryHandlers: ((messageId: string, status: 'delivered' | 'failed') => void)[] = [];
  private typingHandlers: ((isTyping: boolean) => void)[] = [];

  constructor() {
    this.token = getToken();
  }

  // Connect to the WebSocket server
  connect(conversationId: string): void {
    if (!this.token) {
      this.notifyStatus('error');
      console.error('No authentication token available');
      return;
    }

    this.conversationId = conversationId;
    this.notifyStatus('connecting');

    // Use the API_BASE_URL to determine the WebSocket URL
    const apiBaseUrl = API_BASE_URL || '';
    const wsBaseUrl = apiBaseUrl.replace(/^http/, 'ws');

    // Ensure the conversation ID is properly encoded
    const encodedConversationId = encodeURIComponent(conversationId);
    const encodedToken = encodeURIComponent(this.token);

    // If there's a custom API base URL, use it; otherwise, fall back to the current host
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

    // Construct the WebSocket URL with the token as a query parameter
    let wsUrl: string;
    if (wsBaseUrl) {
      wsUrl = `${wsBaseUrl}/chat/ws/${encodedConversationId}?token=${encodedToken}`;
    } else {
      wsUrl = `${wsProtocol}//${window.location.host}/chat/ws/${encodedConversationId}?token=${encodedToken}`;
    }

    // Log the WebSocket URL for debugging
    console.log(`Connecting to WebSocket URL: ${wsUrl}`);

    try {
      console.log(`Creating new WebSocket connection to: ${wsUrl}`);
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log(`WebSocket connection established for conversation: ${conversationId}`);
        console.log(`WebSocket readyState: ${this.ws ? this.ws.readyState : 'null'}`);
        this.notifyStatus('connected');
        this.reconnectAttempts = 0;
        this.startPingInterval();

        // Send reconnect message if reconnecting
        if (this.reconnectAttempts > 0) {
          console.log(`Sending reconnect message for conversation: ${conversationId}`);
          this.send({ reconnect: true });
        }
      };

      this.ws.onmessage = (event) => {
        try {
          console.log('Raw WebSocket message received:', event.data.substring(0, 200));
          const data = JSON.parse(event.data);
          console.log('Parsed WebSocket message:', data);
          console.log(`Message type: ${data.type}, conversation: ${this.conversationId}`);

          if (data.type === 'user_message' || data.type === 'ai_message' ||
              data.type === 'ai_message_start' || data.type === 'ai_message_complete') {
            console.log(`Message ID: ${data.message?.id}, sender: ${data.message?.sender}, content length: ${data.message?.content?.length || 0}`);
          }

          this.notifyMessageHandlers(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          console.error('Raw message that caused error:', event.data.substring(0, 200));
        }
      };

      this.ws.onclose = (event) => {
        console.log(`WebSocket connection closed for conversation: ${conversationId}`);
        console.log(`Close code: ${event.code}, reason: ${event.reason || 'No reason provided'}`);
        this.notifyStatus('disconnected');
        this.stopPingInterval();

        // Only attempt to reconnect if the close wasn't clean (code 1000)
        if (event.code !== 1000) {
          console.log(`Attempting to reconnect due to abnormal closure (code: ${event.code})`);
          this.attemptReconnect();
        } else {
          console.log('Clean WebSocket closure, not attempting to reconnect');
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        // Log more detailed information about the WebSocket
        console.error('WebSocket readyState:', this.ws ? this.ws.readyState : 'null');
        console.error('WebSocket URL:', wsUrl);
        console.error('Conversation ID:', conversationId);
        this.notifyStatus('error');

        // Attempt to reconnect automatically on error
        this.attemptReconnect();
      };
    } catch (error) {
      console.error('Error creating WebSocket:', error);
      this.notifyStatus('error');
    }
  }

  // Disconnect from the WebSocket server
  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.stopPingInterval();
    this.notifyStatus('disconnected');
  }

  // Send a message through the WebSocket
  send(data: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('Sending data through WebSocket:', data);
      this.ws.send(JSON.stringify(data));
    } else {
      console.error('WebSocket is not connected, readyState:', this.ws ? this.ws.readyState : 'null');
      this.notifyStatus('disconnected');
    }
  }

  // Send a chat message
  sendChatMessage(message: string, metadata: any = {}, context: any = {}): void {
    console.log(`Sending chat message through WebSocket to conversation ${this.conversationId}`);
    console.log(`Message content: "${message.substring(0, 30)}${message.length > 30 ? '...' : ''}"`);
    console.log('Message metadata:', metadata);
    console.log('Message context:', context);

    this.send({
      message,
      metadata,
      context,
    });
  }

  // Add a message handler
  onMessage(handler: (data: any) => void): () => void {
    this.messageHandlers.push(handler);
    return () => {
      this.messageHandlers = this.messageHandlers.filter(h => h !== handler);
    };
  }

  // Add a status handler
  onStatus(handler: (status: 'connecting' | 'connected' | 'disconnected' | 'error') => void): () => void {
    this.statusHandlers.push(handler);
    return () => {
      this.statusHandlers = this.statusHandlers.filter(h => h !== handler);
    };
  }

  // Add a delivery confirmation handler
  onDelivery(handler: (messageId: string, status: 'delivered' | 'failed') => void): () => void {
    this.deliveryHandlers.push(handler);
    return () => {
      this.deliveryHandlers = this.deliveryHandlers.filter(h => h !== handler);
    };
  }

  // Add a typing indicator handler
  onTyping(handler: (isTyping: boolean) => void): () => void {
    this.typingHandlers.push(handler);
    return () => {
      this.typingHandlers = this.typingHandlers.filter(h => h !== handler);
    };
  }

  // Notify all message handlers
  private notifyMessageHandlers(data: any): void {
    // Process special message types
    if (data.type === 'typing_indicator' && this.typingHandlers.length > 0) {
      this.notifyTyping(data.is_typing);
    }

    if (data.type === 'message_delivered' && this.deliveryHandlers.length > 0) {
      this.notifyDelivery(data.message_id, 'delivered');
    }

    if (data.type === 'message_failed' && this.deliveryHandlers.length > 0) {
      this.notifyDelivery(data.message_id, 'failed');
    }

    // Handle streaming response chunks
    if (data.type === 'stream_start') {
      // Stream start event - create an empty message that will be updated
      console.log('Stream started for message:', data.message_id);
    }

    if (data.type === 'stream_chunk') {
      // Stream chunk event - update the message with the new content
      console.log('Stream chunk received for message:', data.message_id);
    }

    if (data.type === 'stream_end') {
      // Stream end event - finalize the message
      console.log('Stream ended for message:', data.message_id);
    }

    // Notify all general message handlers
    this.messageHandlers.forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error('Error in message handler:', error);
      }
    });
  }

  // Notify all status handlers
  private notifyStatus(status: 'connecting' | 'connected' | 'disconnected' | 'error'): void {
    this.statusHandlers.forEach(handler => {
      try {
        handler(status);
      } catch (error) {
        console.error('Error in status handler:', error);
      }
    });
  }

  // Notify all delivery handlers
  private notifyDelivery(messageId: string, status: 'delivered' | 'failed'): void {
    this.deliveryHandlers.forEach(handler => {
      try {
        handler(messageId, status);
      } catch (error) {
        console.error('Error in delivery handler:', error);
      }
    });
  }

  // Notify all typing handlers
  private notifyTyping(isTyping: boolean): void {
    this.typingHandlers.forEach(handler => {
      try {
        handler(isTyping);
      } catch (error) {
        console.error('Error in typing handler:', error);
      }
    });
  }

  // Start the ping interval to keep the connection alive
  private startPingInterval(): void {
    this.stopPingInterval();
    this.pingInterval = window.setInterval(() => {
      this.send({ ping: true });
    }, 30000); // Send ping every 30 seconds
  }

  // Stop the ping interval
  private stopPingInterval(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  // Attempt to reconnect to the WebSocket server
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Maximum reconnect attempts reached');
      this.notifyStatus('error');
      return;
    }

    this.reconnectAttempts++;
    const timeout = this.reconnectTimeout * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Attempting to reconnect in ${timeout}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    this.notifyStatus('connecting');

    setTimeout(() => {
      if (this.conversationId) {
        this.connect(this.conversationId);
      }
    }, timeout);
  }
}
