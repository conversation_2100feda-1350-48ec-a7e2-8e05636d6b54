"""
Composable analysis agent for the Datagenius backend.

This module provides a composable implementation of the analysis agent
that uses the component-based architecture.
"""

import logging
import os
import json # Added json import
from typing import Dict, Any, Optional, List

from agents.composable import ComposableAgent
from agents.components.mcp_server import MC<PERSON>erverComponent
# Attempt to import formatting utilities, or define simple ones if not available
try:
    from agents.utils.template_processor import format_number, format_data_types, format_memory_usage
except ImportError:
    logger.warning("Could not import formatting utilities from template_processor. Using basic formatting.")
    def format_number(n, *args, **kwargs): return str(n) if n is not None else "N/A"
    def format_data_types(d, *args, **kwargs): return str(d) if d else "N/A"
    def format_memory_usage(m, *args, **kwargs): return str(m) if m is not None else "N/A"

from .components import (
    AnalysisLLMComponent,
    AnalysisParserComponent,
    DataLoaderComponent,
    AnalysisExecutorComponent,
    <PERSON><PERSON><PERSON><PERSON><PERSON>er<PERSON>omponent,
    PandasAITrainingComponent
)

# Configure logging
logger = logging.getLogger(__name__)


class ComposableAnalysisAgent(ComposableAgent):
    """Composable implementation of the analysis agent."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the composable analysis agent.

        Args:
            config: Configuration dictionary for the agent
        """
        logger.info("Initializing Composable Analysis Agent")

        # Call the parent initialization to set up the base components
        await super()._initialize(config)

        # If no components were configured, set up the default components
        if not self.components:
            logger.info("No components configured, setting up default analysis components")

            # Create and initialize the LLM component
            llm_component = AnalysisLLMComponent()

            # Get provider and model from config
            provider = config.get("provider", "")
            model = config.get("model", "")
            api_key = config.get("api_key", "")

            # Initialize the LLM component with the centralized model provider system
            await llm_component.initialize({
                "name": "analysis_llm",
                "provider": provider,
                "model": model,
                "api_key": api_key
            })
            self.components.append(llm_component)

            # Create and initialize the parser component
            parser_component = AnalysisParserComponent()
            await parser_component.initialize({
                "name": "analysis_parser"
            })
            self.components.append(parser_component)

            # Create and initialize the data loader component
            loader_component = DataLoaderComponent()
            await loader_component.initialize({
                "name": "data_loader",
                "data_dir": config.get("data_dir", "data")
            })
            self.components.append(loader_component)

            # Create and initialize the analysis executor component
            executor_component = AnalysisExecutorComponent()
            await executor_component.initialize({
                "name": "analysis_executor"
            })
            self.components.append(executor_component)

            # Create and initialize the semantic layer component
            semantic_layer_component = SemanticLayerComponent()
            await semantic_layer_component.initialize({
                "name": "semantic_layer_manager"
            })
            self.components.append(semantic_layer_component)

            # Create and initialize the PandasAI training component
            pandasai_training_component = PandasAITrainingComponent()
            await pandasai_training_component.initialize({
                "name": "pandasai_trainer"
            })
            self.components.append(pandasai_training_component)

            # Create and initialize the MCP server component with all data analysis tools
            from agents.components import create_mcp_server_with_essential_tools

            # Create MCP server with essential tools first
            mcp_server_component = await create_mcp_server_with_essential_tools({
                "name": "analysis_tools",
                "server_name": "datagenius-analysis-tools",
                "server_version": "1.0.0",
                "tools": [
                    {"type": "data_analysis"},
                    {"type": "data_cleaning"},
                    {"type": "data_visualization"},
                    {"type": "data_querying"},
                    {"type": "advanced_query"},
                    {"type": "data_filtering"},
                    {"type": "sentiment_analysis"},
                    {"type": "text_processing"},
                    {"type": "document_embedding"},
                    # Phase 2 Enhancements: Add new tool types
                    {"type": "advanced_visualization"}, # For heatmaps, 3D plots, network graphs, geospatial maps
                    {"type": "machine_learning"},      # For predictive analytics, pattern recognition, feature importance
                    {"type": "statistical_analysis"},  # For advanced tests, anomaly detection, time series
                    {"type": "natural_language_query"},# For enhanced context-aware query processing
                    {"type": "data_storytelling"},     # For generating narrative explanations
                    # PandasAI v3 tools
                    {"type": "pandasai_analysis"},     # For data analysis using PandasAI v3
                    {"type": "pandasai_visualization"},# For data visualization using PandasAI v3
                    {"type": "pandasai_query"}         # For natural language queries using PandasAI v3
                ]
            })
            self.components.append(mcp_server_component)
            logger.info("Added MCP server component with comprehensive and enhanced data analysis tools")

            logger.info(f"Initialized {len(self.components)} default analysis components")

    def _extract_text_from_content_items(self, content_items: List[Dict[str, Any]]) -> str:
        """Helper to extract text from a list of content items."""
        if not content_items:
            return "Not available"
        return "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"]).strip()

    def _construct_data_profile_summary(
        self,
        file_name: str,
        info_metadata: Dict[str, Any],
        preview_content_items: List[Dict[str, Any]],
        profile_content_items: List[Dict[str, Any]]
    ) -> str:
        """
        Constructs a data profile summary string directly from metadata and content.
        """
        summary_parts = [f"### Data Profile for: {file_name}\n"]

        # Basic Info
        rows = info_metadata.get("shape", [None, None])[0]
        cols = info_metadata.get("shape", [None, None])[1]
        summary_parts.append(f"**Overview:**")
        summary_parts.append(f"- **Rows:** {format_number(rows)}")
        summary_parts.append(f"- **Columns:** {format_number(cols)}")

        dtypes = info_metadata.get("dtypes", {})
        if dtypes:
            summary_parts.append(f"- **Data Types:** {format_data_types(dtypes)}")

        memory = info_metadata.get("memory_usage", {}).get("total") # Assuming memory_usage is a dict with 'total'
        if memory is not None:
             summary_parts.append(f"- **Memory Usage:** {format_memory_usage(memory)}")
        else: # Fallback if structure is different
            memory_raw = info_metadata.get("memory_usage")
            if memory_raw is not None:
                summary_parts.append(f"- **Memory Usage:** {format_memory_usage(memory_raw)}")


        # Data Preview
        preview_text = self._extract_text_from_content_items(preview_content_items)
        summary_parts.append(f"\n**Data Preview (first few rows):**\n```\n{preview_text}\n```")

        # Statistical Profile
        profile_text = self._extract_text_from_content_items(profile_content_items)
        summary_parts.append(f"\n**Statistical Summary:**\n```\n{profile_text}\n```")

        # Data Quality (Missing Values)
        missing_values_text = "Information not available" # Default if summary object or keys are missing
        if "missing_values_summary" in info_metadata:
            mv_summary = info_metadata["missing_values_summary"]
            # Ensure mv_summary is a dictionary and contains the expected keys
            if isinstance(mv_summary, dict) and \
               "total_missing_values" in mv_summary and \
               "columns_with_missing_values" in mv_summary:
                total_missing = mv_summary["total_missing_values"]
                cols_with_missing = mv_summary["columns_with_missing_values"]

                # Ensure these are numbers before comparison
                if isinstance(total_missing, (int, float)) and total_missing > 0:
                    missing_values_text = f"{format_number(total_missing)} missing values across {format_number(cols_with_missing)} columns."
                elif isinstance(total_missing, (int, float)) and total_missing == 0:
                    missing_values_text = "No missing values detected."
            # If mv_summary is not a dict or keys are missing, missing_values_text remains "Information not available"

        summary_parts.append(f"\n**Data Quality:**\n- Missing Values: {missing_values_text}")

        summary_parts.append("\n\nWhat would you like to explore or analyze in this dataset?")
        return "\n".join(summary_parts)

    async def process_message(self,
                             user_id: int,
                             message: str,
                             conversation_id: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message using the agent's components.
        """
        logger.info(f"=== ANALYSIS AGENT PROCESS_MESSAGE START ===")
        logger.info(f"User ID: {user_id}, Message: '{message}', Conversation ID: {conversation_id}")
        logger.info(f"Context received: {context}")
        logger.info(f"Agent config: {self.config}")

        ctx = {
            "user_id": user_id,
            "message": message,
            "conversation_id": conversation_id,
            "context": context or {},
            "agent_config": self.config,
            "agent_components": self.components,
            "response": "",
            "metadata": {}
        }

        # Attempt to find a structured data_source object in the context
        data_source_obj_from_context = None
        if context:
            if "data_source" in context:
                data_source_obj_from_context = context["data_source"]
                logger.info(f"Found 'data_source' directly in context: {data_source_obj_from_context}")
            elif "message" in context and isinstance(context["message"], dict) and "metadata" in context["message"] and "data_source" in context["message"]["metadata"]:
                data_source_obj_from_context = context["message"]["metadata"]["data_source"]
                logger.info(f"Found 'data_source' in message metadata: {data_source_obj_from_context}")
            elif "conversation" in context and isinstance(context["conversation"], dict) and "metadata" in context["conversation"] and "data_source" in context["conversation"]["metadata"]:
                data_source_obj_from_context = context["conversation"]["metadata"]["data_source"]
                logger.info(f"Found 'data_source' in conversation metadata: {data_source_obj_from_context}")
            elif "file_attachment" in context:
                data_source_obj_from_context = context["file_attachment"]
                logger.info(f"Found 'file_attachment' in context: {data_source_obj_from_context}")
            elif "attachments" in context and context["attachments"]:
                if isinstance(context["attachments"], list) and len(context["attachments"]) > 0:
                    data_source_obj_from_context = context["attachments"][0]
                    logger.info(f"Found 'data_source' in attachments list: {data_source_obj_from_context}")
                elif isinstance(context["attachments"], dict):
                    data_source_obj_from_context = context["attachments"]
                    logger.info(f"Found 'data_source' in attachments dict: {data_source_obj_from_context}")
        
        if data_source_obj_from_context:
            ctx["data_source"] = data_source_obj_from_context # Store for later use in prompt if needed
            try:
                logger.info(f"Full data_source object from context: {json.dumps(data_source_obj_from_context, indent=2, default=str)}")
            except Exception as e:
                logger.error(f"Error serializing data_source_obj_from_context for logging: {e}. Raw: {data_source_obj_from_context}")

        # Initialize file_path. It will be set if a file is successfully located.
        file_path: Optional[str] = None
        file_id: Optional[str] = None
        file_name: str = "your data" # Default file name

        if data_source_obj_from_context:
            logger.info(f"=== DATA_SOURCE OBJECT FOUND IN CONTEXT. ATTEMPTING TO DETERMINE FILE PATH ===")
            logger.info(f"Type of data_source_obj_from_context: {type(data_source_obj_from_context)}")
            if isinstance(data_source_obj_from_context, dict):
                logger.info(f"Keys in data_source_obj_from_context (dict): {list(data_source_obj_from_context.keys())}")
            
            try:
                # Extract file_id and file_name from the data_source_obj_from_context
                if isinstance(data_source_obj_from_context, dict):
                    file_id = data_source_obj_from_context.get("id")
                    logger.info(f"Attempt 1 (direct get 'id'): file_id = {file_id}")
                    file_name = data_source_obj_from_context.get("name", file_name)
                    file_path_direct = data_source_obj_from_context.get("file_path") # Keep direct path if provided

                    if not file_id and "source_metadata" in data_source_obj_from_context:
                        source_metadata = data_source_obj_from_context.get("source_metadata", {})
                        if isinstance(source_metadata, dict):
                            logger.info(f"Checking in source_metadata: {source_metadata}")
                            file_id = source_metadata.get("file_id") or source_metadata.get("id")
                            logger.info(f"Attempt 2 (source_metadata): file_id = {file_id}")
                            if not file_name or file_name == "your data": # Update file_name if found here
                                file_name = source_metadata.get("file_name") or source_metadata.get("name", file_name)
                    
                    if not file_id and "metadata" in data_source_obj_from_context: # Check 'metadata' key within data_source_obj
                        metadata_ds = data_source_obj_from_context.get("metadata", {})
                        if isinstance(metadata_ds, dict):
                            logger.info(f"Checking in data_source_obj.metadata: {metadata_ds}")
                            file_id = metadata_ds.get("file_id") or metadata_ds.get("id")
                            logger.info(f"Attempt 3 (data_source_obj.metadata): file_id = {file_id}")
                            if not file_name or file_name == "your data": # Update file_name if found here
                                file_name = metadata_ds.get("file_name") or metadata_ds.get("name", file_name)

                elif isinstance(data_source_obj_from_context, (str, int)): 
                    file_id = str(data_source_obj_from_context)
                    logger.info(f"Attempt 4 (data_source_obj is str/int): file_id = {file_id}")
                
                # Log final extracted values before DB lookup
                logger.info(f"Final extracted values before DB/FS lookup: file_id='{file_id}', file_name='{file_name}', direct_path_from_context='{file_path_direct if 'file_path_direct' in locals() and file_path_direct else 'Not Provided'}'")

                # 1. Attempt to get file_path from database if file_id is available
                if file_id:
                    try:
                        from app.database import get_db, get_file
                        from sqlalchemy.orm import Session
                        db_gen = get_db()
                        db: Session = next(db_gen)
                        try:
                            db_file = get_file(db, file_id)
                            if db_file and db_file.file_path:
                                file_path = db_file.file_path
                                logger.info(f"Found file path in database: '{file_path}'. Will attempt to use this path.")
                            elif db_file:
                                logger.warning(f"File ID '{file_id}' found in database, but its file_path is empty.")
                            else:
                                logger.warning(f"File ID '{file_id}' not found in database.")
                        finally:
                            db.close()
                    except Exception as e:
                        logger.error(f"Error looking up file ID '{file_id}' in database: {str(e)}")
                
                # 2. If database lookup failed or no file_id, attempt filesystem search
                if not file_path:
                    logger.info(f"Database did not yield a file path (or no file_id). Attempting filesystem search for file_name='{file_name}', file_id='{file_id}'.")
                    possible_file_paths = []
                    # Use direct path from data_source if available
                    if 'file_path_direct' in locals() and file_path_direct:
                        possible_file_paths.append(file_path_direct)
                        if not os.path.isabs(file_path_direct):
                            possible_file_paths.extend([
                                os.path.join("data", file_path_direct), os.path.join("uploads", file_path_direct), os.path.join("backend/data", file_path_direct)
                            ])
                    
                    # Construct paths based on file_id
                    if file_id:
                        for ext in ['.csv', '.xlsx', '.xls', '']: # Empty ext for names that might include it
                            base_names = [file_id, f"file_{file_id}"]
                            if file_name and file_name != "your data" and file_id not in file_name: # Add file_name if distinct and useful
                                base_names.append(file_name)
                            for bn in base_names:
                                possible_file_paths.append(f"{bn}{ext}") # Relative to CWD
                                for d in ["data", "uploads", "backend/data", "temp_uploads"]:
                                    possible_file_paths.append(os.path.join(d, f"{bn}{ext}"))
                    
                    # Construct paths based on file_name (if not already covered by file_id logic)
                    if file_name and file_name != "your data":
                        for ext in ['.csv', '.xlsx', '.xls', '']:
                             fn_to_check = file_name if ext == '' and '.' in file_name else f"{file_name}{ext}"
                             if fn_to_check not in possible_file_paths: # Avoid duplicates
                                possible_file_paths.append(fn_to_check)
                                for d in ["data", "uploads", "backend/data", "temp_uploads"]:
                                    possible_file_paths.append(os.path.join(d, fn_to_check))

                    possible_file_paths = list(dict.fromkeys(p for p in possible_file_paths if p)) # Unique, non-empty
                    logger.info(f"Checking possible file paths: {possible_file_paths}")
                    for p in possible_file_paths:
                        if os.path.exists(p):
                            file_path = p
                            logger.info(f"Found data file via filesystem search at path: '{file_path}'")
                            break
                        # else: logger.debug(f"Path does not exist: {p}") # Too verbose for INFO

                    if not file_path: # Last resort: search common upload dirs for most recent
                        logger.warning(f"Filesystem search failed. Searching common upload directories for most recent data file potentially matching '{file_name}'.")
                        from app.config import UPLOAD_DIR # Assuming UPLOAD_DIR is configured
                        upload_dirs_to_check = list(dict.fromkeys([UPLOAD_DIR, "data", "uploads", "backend/data", "temp_uploads", "."]))
                        recent_files_found = []
                        for directory in upload_dirs_to_check:
                            if os.path.exists(directory) and os.path.isdir(directory):
                                try:
                                    all_files = [os.path.join(directory, f) for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]
                                    data_files_in_dir = [f for f in all_files if f.endswith(('.csv', '.xlsx', '.xls'))]
                                    if file_name and file_name != "your data":
                                        named_matches = [f for f in data_files_in_dir if file_name.lower() in os.path.basename(f).lower()]
                                        if named_matches: recent_files_found.extend(sorted(named_matches, key=os.path.getmtime, reverse=True))
                                    recent_files_found.extend(sorted(data_files_in_dir, key=os.path.getmtime, reverse=True))
                                except Exception as e_fs: logger.error(f"Error listing files in {directory}: {e_fs}")
                        if recent_files_found:
                            file_path = recent_files_found[0] # Take the most recent overall or named match
                            logger.info(f"Using most recently modified/matched file from searched directories: '{file_path}'")
            
            except Exception as e_outer:
                logger.error(f"Outer error during file path determination from data_source_obj: {e_outer}", exc_info=True)
                file_path = None # Ensure file_path is None on any error in this block
        
        else: # No data_source_obj_from_context
            logger.warning("No structured data_source object found in context. File analysis will be skipped.")
            # file_path remains None

        logger.info(f"Final determined file_path: '{file_path}' (Will be None if not found)")

        # Store determined file_id and file_name in ctx for prompt consistency
        ctx["file_id"] = file_id
        ctx["file_name"] = file_name if file_name and file_name != "your data" else (os.path.basename(file_path) if file_path else "your data")


        mcp_server = next((comp for comp in self.components if isinstance(comp, MCPServerComponent)), None)
        if not mcp_server:
            ctx["response"] = "I'm sorry, I'm not properly configured to respond. MCP server component missing."
            logger.error("No MCP server component found.")
            return {"message": ctx["response"], "metadata": ctx["metadata"]}

        try:
            # Handle "send_file_to_persona" flow separately if active (usually for initial display)
            if context and context.get("send_file_to_persona") and data_source_obj_from_context:
                logger.info("Processing file for persona (send_file_to_persona is true)")
                # This flow might use data_source_obj_from_context directly
                # It's often for displaying a preview and might not need full file_path resolution if tool handles it
                # For simplicity, we assume it uses the data_source_obj_from_context
                persona_result = await mcp_server.call_tool("data_access", {
                    "operation": "send_to_persona", "data_source": data_source_obj_from_context,
                    "params": {"sample_size": 10, "include_table": True}
                })
                if not persona_result.get("isError", False):
                    ctx["response"] = self._extract_text_from_content_items(persona_result.get("content", []))
                    ctx["metadata"]["file_processed_for_persona"] = True
                    # If this is the only action, return early
                    if not message or message.strip() == "": return {"message": ctx["response"], "metadata": ctx["metadata"]}
                else:
                    err_msg = self._extract_text_from_content_items(persona_result.get("content", [])) or "Error processing file for persona."
                    logger.error(f"Error in send_file_to_persona: {err_msg}")
                    ctx["response"] = err_msg
                    ctx["metadata"]["file_error"] = True
                    # If this is the only action and it failed, return early
                    if not message or message.strip() == "": return {"message": ctx["response"], "metadata": ctx["metadata"]}


            data_profile_summary_response = None
            data_analysis_results = None # For results from specific analysis tools

            if file_path: # All data operations depend on a valid file_path
                logger.info(f"Proceeding with analysis for file: '{file_path}'")
                
                # Determine if a data profile should be generated
                should_profile_data = False
                profile_keywords = ["analyze", "profile", "describe", "summarize", "statistics", "overview", "show me", "tell me about"]
                if message.strip().lower() in ["", "hi", "hello"] or any(keyword in message.lower() for keyword in profile_keywords):
                    # Profile if message is empty/greeting OR contains profiling keywords
                    # This also handles the case where a file is attached with a generic "analyze this" message.
                    if not message.strip() or any(keyword in message.lower() for keyword in profile_keywords):
                         logger.info(f"Data profiling requested (empty message or keyword found).")
                         should_profile_data = True

                # Prepare data_source object for tools, now using the resolved file_path
                tool_data_source = {"id": file_id, "name": ctx["file_name"], "file_path": file_path}

                # Get data preview, info, and profile
                preview_res = await mcp_server.call_tool("data_access", {"data_source": tool_data_source, "operation": "head", "params": {"n": 10}})
                info_res = await mcp_server.call_tool("data_access", {"data_source": tool_data_source, "operation": "info"})
                describe_res = await mcp_server.call_tool("data_access", {"data_source": tool_data_source, "operation": "describe"})

                if not preview_res.get("isError"): ctx["metadata"]["data_preview"] = preview_res
                if not info_res.get("isError"): ctx["metadata"]["data_info"] = info_res
                if not describe_res.get("isError"): ctx["metadata"]["data_profile"] = describe_res
                
                # If errors occurred in essential data access, log them and potentially stop
                if preview_res.get("isError") or info_res.get("isError") or describe_res.get("isError"):
                    logger.error(f"Error accessing data for profiling. Preview: {preview_res.get('isError')}, Info: {info_res.get('isError')}, Describe: {describe_res.get('isError')}")
                    # Construct an error message if essential data access failed
                    error_parts = []
                    if preview_res.get("isError"): error_parts.append(f"Preview failed: {self._extract_text_from_content_items(preview_res.get('content', []))}")
                    if info_res.get("isError"): error_parts.append(f"Info failed: {self._extract_text_from_content_items(info_res.get('content', []))}")
                    if describe_res.get("isError"): error_parts.append(f"Describe failed: {self._extract_text_from_content_items(describe_res.get('content', []))}")
                    
                    ctx["response"] = f"I encountered an issue while trying to load basic information about your data file ('{ctx['file_name']}'):\n" + "\n".join(error_parts) + "\nPlease ensure the file is accessible and in a supported format."
                    ctx["metadata"]["analysis_error"] = "data_access_failure"
                    # Return early if basic data access fails
                    return {"message": ctx["response"], "metadata": ctx["metadata"]}


                if should_profile_data:
                    data_profile_summary_response = self._construct_data_profile_summary(
                        ctx["file_name"],
                        info_res.get("metadata", {}),
                        preview_res.get("content", []),
                        describe_res.get("content", [])
                    )
                    ctx["metadata"]["task_type"] = "data_profile_summary"
                    # If only profiling was requested (e.g. empty message with attachment), this is the main response
                    if not message.strip() or message.lower() in profile_keywords: # Simplified condition
                        ctx["response"] = data_profile_summary_response
                        return {"message": ctx["response"], "metadata": ctx["metadata"]}
                
                # If there's a specific message beyond just profiling
                if message.strip() and not (should_profile_data and (not message.strip() or message.lower() in profile_keywords)) :
                    logger.info(f"Handling specific user query: '{message}'")
                    # Request Type Detection (simplified for brevity, full logic was here)
                    visualization_keywords = ["visualize", "chart", "plot", "graph"] # etc.
                    is_visualization_request = any(k in message.lower() for k in visualization_keywords)
                    
                    if is_visualization_request:
                        # Example: Call pandasai_visualization
                        viz_prompt = f"{message}. Create an appropriate plot for {ctx['file_name']}."
                        data_analysis_results = await mcp_server.call_tool("pandasai_visualization", {
                            "data_source": tool_data_source, "prompt": viz_prompt,
                            "api_key": self.config.get("api_key"), "provider": self.config.get("provider", "openai")
                        })
                        ctx["metadata"]["task_type"] = "pandasai_visualization"
                    else: # Default to pandasai_query for other questions
                        data_analysis_results = await mcp_server.call_tool("pandasai_query", {
                            "data_source": tool_data_source, "query": message,
                            "api_key": self.config.get("api_key"), "provider": self.config.get("provider", "openai")
                        })
                        ctx["metadata"]["task_type"] = "pandasai_query"

                    if data_analysis_results and data_analysis_results.get("isError"):
                        logger.error(f"Error from analysis tool ({ctx['metadata']['task_type']}): {self._extract_text_from_content_items(data_analysis_results.get('content',[]))}")
                        # Fallback or error message construction
            
            # Construct final response prompt for LLM
            prompt_parts = [
                "You are Composable Analyst, an advanced AI assistant specializing in data analysis, visualization, machine learning, and data storytelling.",
                f"The user asked: '{message}'."
            ]
            if file_path:
                prompt_parts.append(f"They are working with the data source: '{ctx['file_name']}'.")
                if data_profile_summary_response and not data_analysis_results : # If profile was generated and no other analysis
                     prompt_parts.append(f"Here is an initial profile of their data:\n{data_profile_summary_response}")
                if data_analysis_results and not data_analysis_results.get("isError"):
                    analysis_text = self._extract_text_from_content_items(data_analysis_results.get("content", []))
                    prompt_parts.append(f"I performed the requested '{ctx['metadata'].get('task_type', 'analysis')}' and found:\n{analysis_text.strip()}")
                    # If visualization, mention it's in metadata
                    if "visualization" in ctx["metadata"].get("task_type", ""):
                        prompt_parts.append("The generated visualization details are available in the analysis panel.")
                elif data_analysis_results and data_analysis_results.get("isError"):
                    error_text = self._extract_text_from_content_items(data_analysis_results.get('content',[]))
                    prompt_parts.append(f"I tried to perform '{ctx['metadata'].get('task_type', 'analysis')}' but encountered an error: {error_text}")
                prompt_parts.append("Provide a helpful, insightful response. If analysis was done, explain it. Suggest next steps if appropriate.")
            else: # No file_path
                prompt_parts.append("No data source is currently available for analysis. This could be because no data file was attached, or an attached file could not be located or accessed.")
                prompt_parts.append("To proceed with analysis, please ensure a valid data file (e.g., CSV, Excel) is attached using the 'Attach Data' button.")
            
            final_prompt = "\n\n".join(prompt_parts)
            
            # Generate content using the MCP server
            llm_response_result = await mcp_server.call_tool("generate_content", {
                "content_type": "analysis_response", "prompt_override": final_prompt,
                "provider": self.config.get("provider", "groq"), "model": self.config.get("model", "llama3-70b-8192"),
                "temperature": 0.7
            })

            if not llm_response_result.get("isError", False):
                ctx["response"] = self._extract_text_from_content_items(llm_response_result.get("content", []))
                if not ctx["response"].strip() and file_path : # If LLM gave empty response but analysis was done
                    ctx["response"] = "I have processed your request. Please check the analysis panel for detailed results or visualizations."
            else:
                error_content = self._extract_text_from_content_items(llm_response_result.get("content", [])) or "Unknown error generating response."
                ctx["response"] = f"I'm sorry, I encountered an error while generating the final response: {error_content}"
                logger.error(f"Error generating LLM response: {error_content}")

        except Exception as e_global:
            ctx["response"] = f"I'm sorry, I encountered an unexpected error: {str(e_global)}"
            logger.error(f"Global error in process_message: {str(e_global)}", exc_info=True)
            ctx["metadata"]["process_error"] = str(e_global)

        if not ctx.get("response"): # Fallback if response is still empty
            ctx["response"] = "I'm sorry, I wasn't able to process your request properly. Please try again."
            if "process_error" not in ctx["metadata"] and "analysis_error" not in ctx["metadata"]:
                 ctx["metadata"]["final_error"] = "no_response_generated"
        
        logger.info(f"=== ANALYSIS AGENT PROCESS_MESSAGE END ===")
        logger.info(f"Final response preview: {ctx.get('response', '')[:200]}...")
        logger.info(f"Final metadata keys: {list(ctx.get('metadata', {}).keys())}")
        return {"message": ctx.get("response", ""), "metadata": ctx.get("metadata", {})}
