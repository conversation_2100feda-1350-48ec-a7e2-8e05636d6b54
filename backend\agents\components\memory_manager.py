"""
Component for managing memory operations using mem0ai.

This component provides memory management capabilities for AI personas,
enabling personalized user experiences, improved context retention, and
more efficient token usage. It leverages the mem0ai integration with Qdrant
for vector storage and knowledge graph operations.
"""

import logging
import os
import time
from typing import Dict, Any, List, Optional

from .base import AgentComponent
from ..utils.memory_service import MemoryService
from ..utils.vector_service import VectorService
from ..utils.knowledge_graph_service import KnowledgeGraphService

logger = logging.getLogger(__name__)


class MemoryManagerComponent(AgentComponent):
    """
    Manages memory operations for AI personas using mem0ai.

    This component provides a standardized interface for memory operations
    across all AI personas in the Datagenius application. It handles:
    - Adding memories from conversations
    - Retrieving relevant memories for context
    - Managing memory retention policies
    - Filtering memories based on relevance
    - Document embedding and semantic search
    - Knowledge graph operations
    """

    def __init__(self):
        """Initialize the MemoryManagerComponent."""
        super().__init__()
        self.memory_service = MemoryService()
        self.vector_service = VectorService()
        self.kg_service = KnowledgeGraphService()
        self.memory_ttl = 2592000  # 30 days in seconds (default)
        self.max_memories = 1000  # Default maximum memories per user
        self.memory_threshold = 0.7  # Default relevance threshold
        self.enable_cross_session_memory = True  # Default cross-session memory setting
        self.enable_document_embedding = True  # Enable document embedding by default
        self.enable_knowledge_graph = True  # Enable knowledge graph by default

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info(f"MemoryManagerComponent '{self.name}' initializing...")

        # Set configuration values
        self.memory_ttl = config.get("memory_ttl", self.memory_ttl)
        self.max_memories = config.get("max_memories", self.max_memories)
        self.memory_threshold = config.get("memory_threshold", self.memory_threshold)
        self.enable_cross_session_memory = config.get("enable_cross_session_memory", self.enable_cross_session_memory)
        self.enable_document_embedding = config.get("enable_document_embedding", self.enable_document_embedding)
        self.enable_knowledge_graph = config.get("enable_knowledge_graph", self.enable_knowledge_graph)

        # Additional configuration
        self.memory_types = config.get("memory_types", ["conversation", "insight", "preference", "document", "entity", "relationship"])
        self.persona_id = config.get("persona_id", "unknown")

        # Initialize vector and knowledge graph services if enabled
        if self.enable_document_embedding:
            logger.info(f"Document embedding enabled for MemoryManagerComponent '{self.name}'")

        if self.enable_knowledge_graph:
            logger.info(f"Knowledge graph enabled for MemoryManagerComponent '{self.name}'")

        logger.info(f"MemoryManagerComponent '{self.name}' initialized with TTL: {self.memory_ttl}s, " +
                   f"max memories: {self.max_memories}, threshold: {self.memory_threshold}")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the context to manage memories.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary with memory-enhanced data
        """
        user_id = str(context.get("user_id", "anonymous"))
        message = context.get("message", "")
        conversation_id = context.get("conversation_id", "unknown")
        persona_id = context.get("persona_id", self.persona_id)

        logger.debug(f"MemoryManagerComponent processing for user {user_id}, conversation {conversation_id}")

        # Initialize memory context if not present
        if "memory" not in context:
            context["memory"] = {
                "relevant_memories": [],
                "memory_added": False,
                "memory_metadata": {}
            }

        # Retrieve relevant memories for the current context
        if message and self.enable_cross_session_memory:
            await self._retrieve_memories(context, user_id, message, persona_id)

        # Store conversation in memory if appropriate
        conversation_history = context.get("conversation_history", [])
        if conversation_history and len(conversation_history) > 1:  # Only store if there's an actual conversation
            await self._store_conversation(context, user_id, conversation_history, persona_id)

        # Store any insights or preferences if present
        if "insights" in context:
            await self._store_insights(context, user_id, persona_id)

        if "preferences" in context:
            await self._store_preferences(context, user_id, persona_id)

        # Process document if present and document embedding is enabled
        if self.enable_document_embedding and "file_path" in context:
            await self._process_document(context, user_id, persona_id)

        # Process knowledge graph operations if enabled
        if self.enable_knowledge_graph and "knowledge_graph" in context:
            await self._process_knowledge_graph(context, user_id, persona_id)

        return context

    async def _retrieve_memories(self, context: Dict[str, Any], user_id: str, message: str, persona_id: str) -> None:
        """
        Retrieve relevant memories for the current context.

        Args:
            context: The current context dictionary
            user_id: The user ID
            message: The current message
            persona_id: The current persona ID
        """
        try:
            # Search for relevant memories
            metadata_filter = {"persona_id": persona_id} if persona_id != "unknown" else None

            memories = self.memory_service.search_memories(
                query=message,
                user_id=user_id,
                limit=5,  # Retrieve top 5 memories
                metadata_filter=metadata_filter
            )

            # Filter memories by relevance threshold
            relevant_memories = []
            if memories and "results" in memories:
                for memory in memories["results"]:
                    if memory.get("relevance", 0) >= self.memory_threshold:
                        relevant_memories.append(memory)

            # Add relevant memories to context
            if relevant_memories:
                context["memory"]["relevant_memories"] = relevant_memories
                memory_texts = [m.get("memory", "") for m in relevant_memories]

                # Add memory texts to context for LLM
                context["memory_context"] = "\n\n".join([
                    f"Previous memory: {memory}" for memory in memory_texts
                ])

                logger.info(f"Retrieved {len(relevant_memories)} relevant memories for user {user_id}")
            else:
                logger.debug(f"No relevant memories found for user {user_id}")

        except Exception as e:
            logger.error(f"Error retrieving memories: {e}")
            # Continue processing even if memory retrieval fails

    async def _store_conversation(self, context: Dict[str, Any], user_id: str,
                                conversation_history: List[Dict[str, Any]], persona_id: str) -> None:
        """
        Store the current conversation in memory.

        Args:
            context: The current context dictionary
            user_id: The user ID
            conversation_history: The conversation history
            persona_id: The current persona ID
        """
        try:
            # Only store if we have a meaningful conversation
            if len(conversation_history) < 2:
                return

            # Create metadata
            metadata = {
                "type": "conversation",
                "persona_id": persona_id,
                "conversation_id": context.get("conversation_id", "unknown"),
                "timestamp": time.time(),
                "message_count": len(conversation_history)
            }

            # Add the conversation to memory
            result = self.memory_service.add_conversation(
                messages=conversation_history,
                user_id=user_id,
                metadata=metadata
            )

            if result:
                context["memory"]["memory_added"] = True
                context["memory"]["memory_metadata"] = metadata
                logger.info(f"Stored conversation with {len(conversation_history)} messages for user {user_id}")
            else:
                logger.warning(f"Failed to store conversation for user {user_id}")

        except Exception as e:
            logger.error(f"Error storing conversation: {e}")
            # Continue processing even if memory storage fails

    async def _store_insights(self, context: Dict[str, Any], user_id: str, persona_id: str) -> None:
        """
        Store insights in memory.

        Args:
            context: The current context dictionary
            user_id: The user ID
            persona_id: The current persona ID
        """
        try:
            insights = context.get("insights", [])
            if not insights:
                return

            for insight in insights:
                if not isinstance(insight, str) or not insight.strip():
                    continue

                # Create metadata
                metadata = {
                    "type": "insight",
                    "persona_id": persona_id,
                    "conversation_id": context.get("conversation_id", "unknown"),
                    "timestamp": time.time()
                }

                # Add the insight to memory
                self.memory_service.add_memory(
                    content=insight,
                    user_id=user_id,
                    metadata=metadata
                )

            logger.info(f"Stored {len(insights)} insights for user {user_id}")

        except Exception as e:
            logger.error(f"Error storing insights: {e}")
            # Continue processing even if memory storage fails

    async def _store_preferences(self, context: Dict[str, Any], user_id: str, persona_id: str) -> None:
        """
        Store user preferences in memory.

        Args:
            context: The current context dictionary
            user_id: The user ID
            persona_id: The current persona ID
        """
        try:
            preferences = context.get("preferences", {})
            if not preferences:
                return

            # Convert preferences dictionary to a string
            preferences_str = "\n".join([f"{key}: {value}" for key, value in preferences.items()])

            # Create metadata
            metadata = {
                "type": "preference",
                "persona_id": persona_id,
                "conversation_id": context.get("conversation_id", "unknown"),
                "timestamp": time.time(),
                "preference_keys": list(preferences.keys())
            }

            # Add the preferences to memory
            self.memory_service.add_memory(
                content=preferences_str,
                user_id=user_id,
                metadata=metadata
            )

            logger.info(f"Stored preferences with {len(preferences)} items for user {user_id}")

        except Exception as e:
            logger.error(f"Error storing preferences: {e}")
            # Continue processing even if memory storage fails

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.

        Returns:
            List of capability strings
        """
        capabilities = ["memory_management"]

        if self.enable_document_embedding:
            capabilities.append("document_embedding")

        if self.enable_knowledge_graph:
            capabilities.append("knowledge_graph")

        return self.config.get("capabilities", capabilities)

    async def _process_document(self, context: Dict[str, Any], user_id: str, persona_id: str) -> None:
        """
        Process a document for embedding and semantic search.

        Args:
            context: The current context dictionary
            user_id: The user ID
            persona_id: The current persona ID
        """
        try:
            file_path = context.get("file_path")
            if not file_path or not os.path.exists(file_path):
                logger.warning(f"Invalid file path for document processing: {file_path}")
                return

            # Check if we need to embed the document
            embed_document = context.get("embed_document", True)
            if embed_document:
                # Create metadata with user and persona information
                metadata = {
                    "user_id": user_id,
                    "persona_id": persona_id,
                    "conversation_id": context.get("conversation_id", "unknown")
                }

                # Embed the document using the vector service
                vector_store_id, file_info = self.vector_service.embed_document(
                    file_path=file_path,
                    chunk_size=context.get("chunk_size", 1000),
                    chunk_overlap=context.get("chunk_overlap", 200)
                )

                # Update file_info with user and persona information
                file_info.update(metadata)

                # Add document info to context
                if "memory" not in context:
                    context["memory"] = {}

                if "documents" not in context["memory"]:
                    context["memory"]["documents"] = []

                context["memory"]["documents"].append({
                    "vector_store_id": vector_store_id,
                    "file_path": file_path,
                    "file_info": file_info
                })

                logger.info(f"Embedded document {file_path} with vector store ID: {vector_store_id}")

            # Check if we need to query the document
            query = context.get("document_query")
            if query and "memory" in context and "documents" in context["memory"]:
                # Get the most recent document
                document = context["memory"]["documents"][-1]
                vector_store_id = document.get("vector_store_id")

                if vector_store_id:
                    # Query the document using the vector service
                    results = self.vector_service.search_document(
                        vector_store_id=vector_store_id,
                        query=query,
                        limit=context.get("query_limit", 5)
                    )

                    # Add results to context
                    if "document_results" not in context["memory"]:
                        context["memory"]["document_results"] = []

                    context["memory"]["document_results"].append({
                        "query": query,
                        "results": results
                    })

                    logger.info(f"Queried document with vector store ID {vector_store_id} for: {query}")
        except Exception as e:
            logger.error(f"Error processing document: {e}")

    async def _process_knowledge_graph(self, context: Dict[str, Any], user_id: str, persona_id: str) -> None:
        """
        Process knowledge graph operations.

        Args:
            context: The current context dictionary
            user_id: The user ID
            persona_id: The current persona ID
        """
        try:
            kg_operations = context.get("knowledge_graph", {})
            operation = kg_operations.get("operation")

            if not operation:
                logger.warning("No knowledge graph operation specified")
                return

            # Initialize knowledge graph context if not present
            if "memory" not in context:
                context["memory"] = {}

            if "knowledge_graph" not in context["memory"]:
                context["memory"]["knowledge_graph"] = {
                    "user_id": user_id,
                    "persona_id": persona_id,
                    "conversation_id": context.get("conversation_id", "unknown")
                }

            # Process the operation
            if operation == "create_graph":
                # Create a new knowledge graph
                name = kg_operations.get("name", "New Knowledge Graph")
                description = kg_operations.get("description", "")
                metadata = kg_operations.get("metadata", {})

                # Add persona ID to metadata
                metadata["persona_id"] = persona_id

                # Create the graph
                graph_id = self.kg_service.create_graph(
                    name=name,
                    description=description,
                    metadata=metadata
                )

                # Add graph info to context
                context["memory"]["knowledge_graph"]["current_graph_id"] = graph_id
                context["memory"]["knowledge_graph"]["graph_info"] = {
                    "name": name,
                    "description": description,
                    "metadata": metadata
                }

                logger.info(f"Created knowledge graph '{name}' with ID: {graph_id}")

            elif operation == "add_entity":
                # Add an entity to the knowledge graph
                graph_id = kg_operations.get("graph_id", context["memory"]["knowledge_graph"].get("current_graph_id"))

                if not graph_id:
                    logger.warning("No graph ID specified for add_entity operation")
                    return

                entity_type = kg_operations.get("entity_type")
                name = kg_operations.get("name")
                properties = kg_operations.get("properties", {})
                description = kg_operations.get("description", "")

                if not entity_type or not name:
                    logger.warning("Missing required parameters for add_entity operation")
                    return

                # Add the entity
                entity_id = self.kg_service.add_entity(
                    graph_id=graph_id,
                    entity_type=entity_type,
                    name=name,
                    properties=properties,
                    description=description
                )

                # Add entity info to context
                if "entities" not in context["memory"]["knowledge_graph"]:
                    context["memory"]["knowledge_graph"]["entities"] = []

                context["memory"]["knowledge_graph"]["entities"].append({
                    "id": entity_id,
                    "type": entity_type,
                    "name": name,
                    "properties": properties,
                    "description": description
                })

                logger.info(f"Added entity '{name}' of type '{entity_type}' to graph {graph_id}")

            elif operation == "add_relationship":
                # Add a relationship to the knowledge graph
                graph_id = kg_operations.get("graph_id", context["memory"]["knowledge_graph"].get("current_graph_id"))

                if not graph_id:
                    logger.warning("No graph ID specified for add_relationship operation")
                    return

                relationship_type = kg_operations.get("relationship_type")
                source_id = kg_operations.get("source_id")
                target_id = kg_operations.get("target_id")
                properties = kg_operations.get("properties", {})
                description = kg_operations.get("description", "")

                if not relationship_type or not source_id or not target_id:
                    logger.warning("Missing required parameters for add_relationship operation")
                    return

                # Add the relationship
                relationship_id = self.kg_service.add_relationship(
                    graph_id=graph_id,
                    relationship_type=relationship_type,
                    source_id=source_id,
                    target_id=target_id,
                    properties=properties,
                    description=description
                )

                # Add relationship info to context
                if "relationships" not in context["memory"]["knowledge_graph"]:
                    context["memory"]["knowledge_graph"]["relationships"] = []

                context["memory"]["knowledge_graph"]["relationships"].append({
                    "id": relationship_id,
                    "type": relationship_type,
                    "source_id": source_id,
                    "target_id": target_id,
                    "properties": properties,
                    "description": description
                })

                logger.info(f"Added relationship '{relationship_type}' from {source_id} to {target_id} in graph {graph_id}")

            elif operation == "query_graph":
                # Query the knowledge graph
                graph_id = kg_operations.get("graph_id", context["memory"]["knowledge_graph"].get("current_graph_id"))

                if not graph_id:
                    logger.warning("No graph ID specified for query_graph operation")
                    return

                query = kg_operations.get("query")

                if not query:
                    logger.warning("No query specified for query_graph operation")
                    return

                # Get entities that match the query
                entities = self.kg_service.get_entities(graph_id)
                matching_entities = []
                for entity in entities:
                    if (query.lower() in entity.get("name", "").lower() or
                        query.lower() in entity.get("content", "").lower()):
                        matching_entities.append(entity)

                # Get relationships that match the query
                relationships = self.kg_service.get_relationships(graph_id)
                matching_relationships = []
                for relationship in relationships:
                    if query.lower() in relationship.get("content", "").lower():
                        matching_relationships.append(relationship)

                # Add results to context
                if "query_results" not in context["memory"]["knowledge_graph"]:
                    context["memory"]["knowledge_graph"]["query_results"] = []

                context["memory"]["knowledge_graph"]["query_results"].append({
                    "query": query,
                    "matching_entities": matching_entities,
                    "matching_relationships": matching_relationships
                })

                logger.info(f"Queried graph {graph_id} for: {query}")
        except Exception as e:
            logger.error(f"Error processing knowledge graph operation: {e}")
