"""
Component for managing memory operations using mem0ai.

This component provides memory management capabilities for AI personas,
enabling personalized user experiences, improved context retention, and
more efficient token usage. It leverages the mem0ai integration with Qdrant
for vector storage and knowledge graph operations.
"""

import logging
import os
import time
from typing import Dict, Any, List, Optional

from backend.schemas.agent_config_schemas import AgentPro<PERSON>ing<PERSON>ontext # Added
from .base import AgentComponent
from ..utils.memory_service import MemoryService
from ..utils.vector_service import VectorService
from ..utils.knowledge_graph_service import KnowledgeGraphService

logger = logging.getLogger(__name__)


class MemoryManagerComponent(AgentComponent):
    """
    Manages memory operations for AI personas using mem0ai.

    This component provides a standardized interface for memory operations
    across all AI personas in the Datagenius application. It handles:
    - Adding memories from conversations
    - Retrieving relevant memories for context
    - Managing memory retention policies
    - Filtering memories based on relevance
    - Document embedding and semantic search
    - Knowledge graph operations
    """

    def __init__(self):
        """Initialize the MemoryManagerComponent."""
        super().__init__()
        self.memory_service = MemoryService()
        self.vector_service = VectorService()
        self.kg_service = KnowledgeGraphService()
        self.memory_ttl = 2592000  # 30 days in seconds (default)
        self.max_memories = 1000  # Default maximum memories per user
        self.memory_threshold = 0.7  # Default relevance threshold
        self.enable_cross_session_memory = True  # Default cross-session memory setting
        self.enable_document_embedding = True  # Enable document embedding by default
        self.enable_knowledge_graph = True  # Enable knowledge graph by default

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info(f"MemoryManagerComponent '{self.name}' initializing...")

        # Set configuration values
        self.memory_ttl = config.get("memory_ttl", self.memory_ttl)
        self.max_memories = config.get("max_memories", self.max_memories)
        self.memory_threshold = config.get("memory_threshold", self.memory_threshold)
        self.enable_cross_session_memory = config.get("enable_cross_session_memory", self.enable_cross_session_memory)
        self.enable_document_embedding = config.get("enable_document_embedding", self.enable_document_embedding)
        self.enable_knowledge_graph = config.get("enable_knowledge_graph", self.enable_knowledge_graph)

        # Additional configuration
        self.memory_types = config.get("memory_types", ["conversation", "insight", "preference", "document", "entity", "relationship"])
        self.persona_id = config.get("persona_id", "unknown")

        # Initialize vector and knowledge graph services if enabled
        if self.enable_document_embedding:
            logger.info(f"Document embedding enabled for MemoryManagerComponent '{self.name}'")

        if self.enable_knowledge_graph:
            logger.info(f"Knowledge graph enabled for MemoryManagerComponent '{self.name}'")

        logger.info(f"MemoryManagerComponent '{self.name}' initialized with TTL: {self.memory_ttl}s, " +
                   f"max memories: {self.max_memories}, threshold: {self.memory_threshold}")

    async def process(self, context: AgentProcessingContext) -> AgentProcessingContext:
        """
        Process the context to manage memories.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object with memory-enhanced data.
        """
        user_id = str(context.user_id or "anonymous")
        message = context.message or ""
        conversation_id = str(context.conversation_id or "unknown")
        persona_id = (context.agent_config.id if context.agent_config else self.persona_id)

        logger.debug(f"MemoryManagerComponent processing for user {user_id}, conversation {conversation_id}")

        # Initialize memory context in component_data if not present
        memory_data = context.component_data.setdefault(self.name, {}).setdefault("memory", {
            "relevant_memories": [],
            "memory_added": False,
            "memory_metadata": {}
        })

        # Retrieve relevant memories for the current context
        if message and self.enable_cross_session_memory:
            await self._retrieve_memories(context, user_id, message, persona_id) # Pass AgentProcessingContext

        # Store conversation in memory if appropriate
        conversation_history = context.initial_context.get("conversation_history", [])
        if conversation_history and len(conversation_history) > 1:
            await self._store_conversation(context, user_id, conversation_history, persona_id) # Pass AgentProcessingContext

        # Store any insights or preferences if present
        insights_from_meta = context.metadata.get("insights", [])
        if insights_from_meta: # Check if insights exist
            await self._store_insights(context, user_id, persona_id, insights_from_meta) # Pass AgentProcessingContext and insights

        preferences_from_meta = context.metadata.get("preferences", {})
        if preferences_from_meta: # Check if preferences exist
            await self._store_preferences(context, user_id, persona_id, preferences_from_meta) # Pass AgentProcessingContext and preferences

        # Process document if present and document embedding is enabled
        file_path_from_initial = context.initial_context.get("file_path")
        if self.enable_document_embedding and file_path_from_initial:
            await self._process_document(context, user_id, persona_id, file_path_from_initial) # Pass AgentProcessingContext and file_path

        # Process knowledge graph operations if enabled
        kg_operations_from_meta = context.metadata.get("knowledge_graph", {})
        if self.enable_knowledge_graph and kg_operations_from_meta:
            await self._process_knowledge_graph(context, user_id, persona_id, kg_operations_from_meta) # Pass AgentProcessingContext and kg_operations

        return context

    async def _retrieve_memories(self, context: AgentProcessingContext, user_id: str, message: str, persona_id: str) -> None:
        """
        Retrieve relevant memories for the current context.

        Args:
            context: The current AgentProcessingContext object.
            user_id: The user ID.
            message: The current message.
            persona_id: The current persona ID.
        """
        memory_data = context.component_data.setdefault(self.name, {}).setdefault("memory", {})
        try:
            metadata_filter = {"persona_id": persona_id} if persona_id != "unknown" else None
            memories = self.memory_service.search_memories(
                query=message, user_id=user_id, limit=5, metadata_filter=metadata_filter
            )
            relevant_memories = []
            if memories and "results" in memories:
                for memory_item in memories["results"]: # Renamed memory to memory_item
                    if memory_item.get("relevance", 0) >= self.memory_threshold:
                        relevant_memories.append(memory_item)
            
            if relevant_memories:
                memory_data["relevant_memories"] = relevant_memories
                memory_texts = [m.get("memory", "") for m in relevant_memories]
                context.metadata["memory_context_for_llm"] = "\n\n".join( # Store in metadata
                    [f"Previous memory: {mem_text}" for mem_text in memory_texts] # Renamed memory to mem_text
                )
                logger.info(f"Retrieved {len(relevant_memories)} relevant memories for user {user_id}")
            else:
                logger.debug(f"No relevant memories found for user {user_id}")
        except Exception as e:
            logger.error(f"Error retrieving memories: {e}", exc_info=True)
            context.add_error(self.name, "memory_retrieval_error", {"error_message": str(e)})


    async def _store_conversation(self, context: AgentProcessingContext, user_id: str,
                                conversation_history: List[Dict[str, Any]], persona_id: str) -> None:
        """
        Store the current conversation in memory.

        Args:
            context: The current AgentProcessingContext object.
            user_id: The user ID.
            conversation_history: The conversation history.
            persona_id: The current persona ID.
        """
        memory_data = context.component_data.setdefault(self.name, {}).setdefault("memory", {})
        try:
            if len(conversation_history) < 2:
                return
            metadata = {
                "type": "conversation",
                "persona_id": persona_id,
                "conversation_id": str(context.conversation_id or "unknown"),
                "timestamp": time.time(),
                "message_count": len(conversation_history)
            }
            result = self.memory_service.add_conversation(
                messages=conversation_history, user_id=user_id, metadata=metadata
            )
            if result:
                memory_data["memory_added"] = True
                memory_data["memory_metadata"] = metadata # This metadata is specific to this stored memory
                logger.info(f"Stored conversation with {len(conversation_history)} messages for user {user_id}")
            else:
                logger.warning(f"Failed to store conversation for user {user_id}")
                context.add_error(self.name, "conversation_store_failed", {"user_id": user_id})
        except Exception as e:
            logger.error(f"Error storing conversation: {e}", exc_info=True)
            context.add_error(self.name, "conversation_store_exception", {"error_message": str(e)})

    async def _store_insights(self, context: AgentProcessingContext, user_id: str, persona_id: str, insights: List[str]) -> None:
        """
        Store insights in memory.

        Args:
            context: The current AgentProcessingContext object.
            user_id: The user ID.
            persona_id: The current persona ID.
            insights: List of insights from context.metadata.
        """
        try:
            if not insights: return
            for insight_content in insights: # Renamed insight
                if not isinstance(insight_content, str) or not insight_content.strip():
                    continue
                metadata = {
                    "type": "insight", "persona_id": persona_id,
                    "conversation_id": str(context.conversation_id or "unknown"),
                    "timestamp": time.time()
                }
                self.memory_service.add_memory(content=insight_content, user_id=user_id, metadata=metadata)
            logger.info(f"Stored {len(insights)} insights for user {user_id}")
        except Exception as e:
            logger.error(f"Error storing insights: {e}", exc_info=True)
            context.add_error(self.name, "insights_store_exception", {"error_message": str(e)})

    async def _store_preferences(self, context: AgentProcessingContext, user_id: str, persona_id: str, preferences: Dict[str, Any]) -> None:
        """
        Store user preferences in memory.

        Args:
            context: The current AgentProcessingContext object.
            user_id: The user ID.
            persona_id: The current persona ID.
            preferences: Dictionary of preferences from context.metadata.
        """
        try:
            if not preferences: return
            preferences_str = "\n".join([f"{key}: {value}" for key, value in preferences.items()])
            metadata = {
                "type": "preference", "persona_id": persona_id,
                "conversation_id": str(context.conversation_id or "unknown"),
                "timestamp": time.time(), "preference_keys": list(preferences.keys())
            }
            self.memory_service.add_memory(content=preferences_str, user_id=user_id, metadata=metadata)
            logger.info(f"Stored preferences with {len(preferences)} items for user {user_id}")
        except Exception as e:
            logger.error(f"Error storing preferences: {e}", exc_info=True)
            context.add_error(self.name, "preferences_store_exception", {"error_message": str(e)})

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.

        Returns:
            List of capability strings
        """
        capabilities = ["memory_management"]
        if self.enable_document_embedding: capabilities.append("document_embedding")
        if self.enable_knowledge_graph: capabilities.append("knowledge_graph")
        return self.config.get("capabilities", capabilities)

    async def _process_document(self, context: AgentProcessingContext, user_id: str, persona_id: str, file_path: str) -> None:
        """
        Process a document for embedding and semantic search.

        Args:
            context: The current AgentProcessingContext object.
            user_id: The user ID.
            persona_id: The current persona ID.
            file_path: Path to the file from context.initial_context.
        """
        memory_data = context.component_data.setdefault(self.name, {}).setdefault("memory", {})
        try:
            if not file_path or not os.path.exists(file_path):
                logger.warning(f"Invalid file path for document processing: {file_path}")
                context.add_error(self.name, "invalid_document_path", {"path": file_path})
                return

            embed_document = context.metadata.get("embed_document", True)
            if embed_document:
                doc_metadata = { # Renamed metadata to avoid conflict
                    "user_id": user_id, "persona_id": persona_id,
                    "conversation_id": str(context.conversation_id or "unknown")
                }
                vector_store_id, file_info = self.vector_service.embed_document(
                    file_path=file_path,
                    chunk_size=context.metadata.get("chunk_size", 1000),
                    chunk_overlap=context.metadata.get("chunk_overlap", 200)
                )
                file_info.update(doc_metadata)
                
                memory_data.setdefault("documents", []).append({
                    "vector_store_id": vector_store_id, "file_path": file_path, "file_info": file_info
                })
                logger.info(f"Embedded document {file_path} with vector store ID: {vector_store_id}")

            query = context.metadata.get("document_query")
            if query and memory_data.get("documents"):
                document_to_query = memory_data["documents"][-1] # Get the most recent document
                vector_store_id_to_query = document_to_query.get("vector_store_id") # Renamed var

                if vector_store_id_to_query:
                    results = self.vector_service.search_document(
                        vector_store_id=vector_store_id_to_query, query=query,
                        limit=context.metadata.get("query_limit", 5)
                    )
                    memory_data.setdefault("document_results", []).append({"query": query, "results": results})
                    logger.info(f"Queried document with vector store ID {vector_store_id_to_query} for: {query}")
        except Exception as e:
            logger.error(f"Error processing document: {e}", exc_info=True)
            context.add_error(self.name, "document_processing_error", {"file_path": file_path, "error_message": str(e)})

    async def _process_knowledge_graph(self, context: AgentProcessingContext, user_id: str, persona_id: str, kg_operations: Dict[str, Any]) -> None:
        """
        Process knowledge graph operations.

        Args:
            context: The current AgentProcessingContext object.
            user_id: The user ID.
            persona_id: The current persona ID.
            kg_operations: KG operations from context.metadata.
        """
        memory_data = context.component_data.setdefault(self.name, {}).setdefault("memory", {})
        kg_data_in_memory = memory_data.setdefault("knowledge_graph", { # Renamed var
            "user_id": user_id, "persona_id": persona_id,
            "conversation_id": str(context.conversation_id or "unknown")
        })

        try:
            operation = kg_operations.get("operation")
            if not operation:
                logger.warning("No knowledge graph operation specified")
                return

            if operation == "create_graph":
                name = kg_operations.get("name", "New Knowledge Graph")
                description = kg_operations.get("description", "")
                op_metadata = kg_operations.get("metadata", {}) # Renamed metadata
                op_metadata["persona_id"] = persona_id
                graph_id = self.kg_service.create_graph(name=name, description=description, metadata=op_metadata)
                kg_data_in_memory["current_graph_id"] = graph_id
                kg_data_in_memory["graph_info"] = {"name": name, "description": description, "metadata": op_metadata}
                logger.info(f"Created knowledge graph '{name}' with ID: {graph_id}")

            elif operation in ["add_entity", "add_relationship", "query_graph"]:
                graph_id = kg_operations.get("graph_id", kg_data_in_memory.get("current_graph_id"))
                if not graph_id:
                    logger.warning(f"No graph ID for KG operation {operation}")
                    context.add_error(self.name, f"kg_op_no_graph_id_{operation}")
                    return

                if operation == "add_entity":
                    entity_type = kg_operations.get("entity_type")
                    name = kg_operations.get("name")
                    if not entity_type or not name:
                        logger.warning(f"Missing params for add_entity in KG {graph_id}")
                        context.add_error(self.name, "kg_add_entity_missing_params", {"graph_id": graph_id})
                        return
                    properties = kg_operations.get("properties", {})
                    description = kg_operations.get("description", "")
                    entity_id = self.kg_service.add_entity(graph_id=graph_id, entity_type=entity_type, name=name, properties=properties, description=description)
                    kg_data_in_memory.setdefault("entities", []).append({"id": entity_id, "type": entity_type, "name": name, "properties": properties, "description": description})
                    logger.info(f"Added entity '{name}' to KG {graph_id}")

                elif operation == "add_relationship":
                    rel_type = kg_operations.get("relationship_type") # Renamed var
                    source_id = kg_operations.get("source_id")
                    target_id = kg_operations.get("target_id")
                    if not rel_type or not source_id or not target_id:
                        logger.warning(f"Missing params for add_relationship in KG {graph_id}")
                        context.add_error(self.name, "kg_add_relationship_missing_params", {"graph_id": graph_id})
                        return
                    properties = kg_operations.get("properties", {})
                    description = kg_operations.get("description", "")
                    rel_id = self.kg_service.add_relationship(graph_id=graph_id, relationship_type=rel_type, source_id=source_id, target_id=target_id, properties=properties, description=description)
                    kg_data_in_memory.setdefault("relationships", []).append({"id": rel_id, "type": rel_type, "source_id": source_id, "target_id": target_id, "properties": properties, "description": description})
                    logger.info(f"Added relationship '{rel_type}' in KG {graph_id}")

                elif operation == "query_graph":
                    query = kg_operations.get("query")
                    if not query:
                        logger.warning(f"No query for KG {graph_id}")
                        context.add_error(self.name, "kg_query_missing", {"graph_id": graph_id})
                        return
                    
                    entities_result = self.kg_service.get_entities(graph_id) # Renamed var
                    matching_entities = [e for e in entities_result if query.lower() in e.get("name", "").lower() or query.lower() in e.get("content", "").lower()]
                    
                    relationships_result = self.kg_service.get_relationships(graph_id) # Renamed var
                    matching_relationships = [r for r in relationships_result if query.lower() in r.get("content", "").lower()]
                    
                    kg_data_in_memory.setdefault("query_results", []).append({"query": query, "matching_entities": matching_entities, "matching_relationships": matching_relationships})
                    logger.info(f"Queried KG {graph_id} for: {query}")
            else:
                logger.warning(f"Unknown KG operation: {operation}")
                context.add_error(self.name, "kg_unknown_operation", {"operation": operation})

        except Exception as e:
            logger.error(f"Error processing knowledge graph operation: {e}", exc_info=True)
            context.add_error(self.name, "kg_processing_error", {"operation": kg_operations.get("operation"), "error_message": str(e)})
