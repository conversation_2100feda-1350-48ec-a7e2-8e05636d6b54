import logging
logging.basicConfig(level=logging.DEBUG)

from app.database import get_db, get_user_data_sources

def main():
    db = next(get_db())
    data_sources = get_user_data_sources(db, 2)
    print(f'Number of data sources: {len(data_sources)}')
    
    for ds in data_sources:
        print(f'Data source: {ds.id}, Type: {ds.type}, Name: {ds.name}, Metadata: {ds.source_metadata}')

if __name__ == "__main__":
    main()
