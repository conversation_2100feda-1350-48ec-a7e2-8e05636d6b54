"""
MCP server component for the Datagenius agent system.

This module provides a component for integrating MCP-compatible tools with agents.
"""

import logging
import json
from typing import Dict, Any, List, Optional

from .base import AgentComponent
from ..tools.mcp import MCPTool, MCPToolRegistry

logger = logging.getLogger(__name__)


class MCPServerComponent(AgentComponent):
    """Component for integrating MCP-compatible tools with agents."""

    def __init__(self):
        """Initialize the MCP server component."""
        super().__init__()
        self.tools = {}
        self.server_name = "datagenius-mcp-server"
        self.server_version = "1.0.0"

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        self.server_name = config.get("server_name", self.server_name)
        self.server_version = config.get("server_version", self.server_version)

        # Initialize tools from configuration
        if "tools" in config:
            for tool_config in config["tools"]:
                tool_type = tool_config.get("type")
                if not tool_type:
                    logger.warning("Tool configuration missing 'type' field")
                    continue

                logger.info(f"Creating MCP tool of type '{tool_type}'")
                tool_class = MCPToolRegistry.get_tool_class(tool_type)
                if tool_class:
                    try:
                        tool = tool_class()
                        await tool.initialize(tool_config)
                        self.tools[tool.name] = tool
                        logger.info(f"Added MCP tool: {tool.name}")
                    except Exception as e:
                        logger.error(f"Error initializing MCP tool '{tool_type}': {e}")
                else:
                    logger.warning(f"MCP tool type '{tool_type}' not found in registry")

        logger.info(f"Initialized MCP server with {len(self.tools)} tools")

    async def list_tools(self) -> List[Dict[str, Any]]:
        """
        List all available tools in MCP format.

        Returns:
            List of tool definitions
        """
        return [tool.definition for tool in self.tools.values()]

    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call a tool with the provided arguments.

        Args:
            name: Name of the tool to call
            arguments: Arguments for tool execution

        Returns:
            Tool execution results
        """
        if name not in self.tools:
            logger.warning(f"Tool '{name}' not found")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Tool '{name}' not found"
                    }
                ]
            }

        try:
            # Log the full arguments for debugging
            logger.info(f"EXECUTING MCP TOOL '{name}'")
            logger.info(f"TOOL ARGUMENTS:")

            # Log specific arguments for content generation
            if name == "generate_content":
                logger.info(f"- content_type: '{arguments.get('content_type', '')}'")
                logger.info(f"- brand_description: '{arguments.get('brand_description', '')}'")
                logger.info(f"- target_audience: '{arguments.get('target_audience', '')}'")
                logger.info(f"- products_services: '{arguments.get('products_services', '')}'")
                logger.info(f"- marketing_goals: '{arguments.get('marketing_goals', '')}'")
                logger.info(f"- existing_content: '{arguments.get('existing_content', '')}'")
                logger.info(f"- keywords: '{arguments.get('keywords', '')}'")
                logger.info(f"- suggested_topics: '{arguments.get('suggested_topics', '')}'")
                logger.info(f"- tone: '{arguments.get('tone', '')}'")
                logger.info(f"- provider: '{arguments.get('provider', '')}'")
                logger.info(f"- model: '{arguments.get('model', '')}'")
            else:
                # For other tools, log a summary
                logger.info(f"Arguments summary: {json.dumps(arguments)[:200]}...")

            # Get the tool and execute it
            tool = self.tools[name]
            logger.info(f"Found tool: {tool.name} ({type(tool).__name__})")

            # Execute the tool
            result = await tool.execute(arguments)

            # Log the result
            if result.get("isError", False):
                logger.error(f"Tool execution failed: {result.get('content', [{'text': 'Unknown error'}])[0].get('text', 'Unknown error')}")
            else:
                logger.info(f"Tool execution succeeded")
                if "content" in result:
                    content_preview = str(result["content"])[:200] + "..." if len(str(result["content"])) > 200 else str(result["content"])
                    logger.info(f"Result content preview: {content_preview}")

            return result
        except Exception as e:
            logger.error(f"Error executing MCP tool '{name}': {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error executing tool '{name}': {str(e)}"
                    }
                ]
            }

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        # Check if a tool should be executed
        if "tool" in context:
            tool_name = context["tool"]
            tool_arguments = context.get("tool_arguments", {})

            logger.info(f"Executing tool '{tool_name}' from context")
            result = await self.call_tool(tool_name, tool_arguments)
            context["tool_result"] = result

            # If the tool execution was successful and generated content, add it to the response
            if not result.get("isError", False) and "content" in result:
                # Extract text content from the result
                text_content = []
                for content_item in result["content"]:
                    if content_item.get("type") == "text":
                        text_content.append(content_item.get("text", ""))

                if text_content:
                    tool_response = "\n".join(text_content)
                    context["response"] = tool_response
                    context["metadata"]["tool_used"] = tool_name

        # Check if we need to list available tools
        elif context.get("message", "").lower().strip() in ["list tools", "what tools do you have", "show tools"]:
            tools = await self.list_tools()
            tool_descriptions = [f"- {tool['name']}: {tool['description']}" for tool in tools]

            if tool_descriptions:
                context["response"] = "Available tools:\n" + "\n".join(tool_descriptions)
            else:
                context["response"] = "No tools are currently available."

            context["metadata"]["listed_tools"] = True

        return context

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.

        Returns:
            List of capability strings
        """
        # Each tool provides a capability
        return [f"tool:{tool.name}" for tool in self.tools.values()]
