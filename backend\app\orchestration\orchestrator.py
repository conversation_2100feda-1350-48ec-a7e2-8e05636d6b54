"""
Core Orchestrator for managing multi-agent workflows in Datagenius.
"""

import logging
from typing import Dict, Any, Optional

# Import necessary components/services
from sqlalchemy.orm import Session
from agents.registry import AgentRegistry # Changed to absolute import
from .routing_component import RoutingComponent
from ..database import get_conversation, update_conversation # Added DB imports
# from ..services.conversation_service import ConversationService
# from ..models.conversation import Conversation

logger = logging.getLogger(__name__)


class Orchestrator:
    """
    Manages the execution flow of tasks across multiple agents/personas.
    """

    def __init__(self):
        """Initialize the Orchestrator."""
        # Initialize necessary services or registries here
        self.agent_registry = AgentRegistry # Use the imported registry
        self.routing_component = RoutingComponent() # Instantiate routing component
        # self.conversation_service = ConversationService()
        logger.info("Orchestrator initialized.")

    async def handle_incoming_message(self, db: Session, user_id: int, conversation_id: str, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handles an incoming user message, routes it appropriately, and returns the final response.

        Args:
            db: Database session.
            user_id: The ID of the user.
            conversation_id: The ID of the conversation.
            message: The user's message text.
            context: Optional additional context (e.g., attached files, session info).

        Returns:
            A dictionary containing the response message and any metadata.
        """
        logger.info(f"Orchestrator handling message for conversation {conversation_id}")
        logger.info(f"Message: '{message[:100]}...', Context keys: {list(context.keys()) if context else []}")
        if context is None:
            context = {}

        # DEBUG: Log the raw context received
        logger.info(f"=== ORCHESTRATOR RECEIVED CONTEXT ===")
        logger.info(f"Raw context: {context}")
        logger.info(f"Context type: {type(context)}")
        logger.info(f"Context keys: {list(context.keys()) if context else 'No context'}")
        logger.info(f"=== END ORCHESTRATOR RECEIVED CONTEXT ===")

        # --- Get Current Conversation State ---
        conversation = get_conversation(db, conversation_id)
        if not conversation:
            logger.error(f"Conversation {conversation_id} not found during orchestration.")
            # Return an error response immediately
            return {
                "message": "Error: Conversation not found.",
                "metadata": {"error": "conversation_not_found", "conversation_id": conversation_id}
            }
        current_state = conversation.state
        logger.info(f"Current conversation state for {conversation_id}: {current_state}")
        # Add state to context for potential use by agents/components
        context["current_conversation_state"] = current_state
        # --- End Get State ---

        # 1. Determine the target agent/persona using the routing component
        # TODO: Pass current_state to determine_target_agent when implemented

        # DEBUG LOGGING: Log routing decision
        logger.info(f"=== ORCHESTRATOR ROUTING DEBUG ===")
        logger.info(f"Message: '{message}'")
        logger.info(f"Context keys: {list(context.keys()) if context else 'No context'}")
        logger.info(f"User ID: {user_id}, Conversation ID: {conversation_id}")

        target_persona_id = await self.routing_component.determine_target_agent(message, context)
        logger.info(f"Routing component determined target agent: {target_persona_id}")
        logger.info(f"=== END ORCHESTRATOR ROUTING DEBUG ===")

        # 2. Instantiate the target agent
        agent_instance = await self.agent_registry.create_agent_instance(target_persona_id)

        if not agent_instance:
             logger.error(f"Failed to create agent instance for persona ID: {target_persona_id}")
             return {
                 "message": f"Sorry, I could not find an agent for '{target_persona_id}'.",
                 "metadata": {"error": "agent_instantiation_failed", "persona_id": target_persona_id}
             }

        # 3. Call the agent's process_message method
        logger.debug(f"Calling process_message for agent {agent_instance.name}")

        # Call with standard arguments: message, user_id, conversation_id, context
        # The 'context' variable from handle_incoming_message already contains relevant session/task context.
        # 'user_id' and 'conversation_id' are passed directly.

        logger.info(f"Orchestrator calling agent_instance.process_message with message: '{message[:50]}...', user_id: {user_id}, conversation_id: {conversation_id}, context keys: {list(context.keys()) if context else None}")
        agent_response = await agent_instance.process_message(
            message=message,
            user_id=user_id,
            conversation_id=conversation_id,
            context=context
        )
        logger.debug(f"Agent {agent_instance.name} processing complete.")

        # 4. Update Conversation State (Example)
        new_state = "PROCESSING_COMPLETE" # Placeholder state
        try:
            update_conversation(db, conversation_id, update_data={"state": new_state})
            logger.info(f"Updated conversation {conversation_id} state to: {new_state}")
        except Exception as e:
            logger.error(f"Failed to update conversation state for {conversation_id}: {e}", exc_info=True)
            # Add error to metadata but continue returning the agent response
            agent_response["metadata"] = agent_response.get("metadata", {})
            agent_response["metadata"]["state_update_error"] = str(e)
        # --- End Update State ---

        # 5. Process the agent's response (Further steps can be added here later)
        #    - Could involve further steps, quality checks, or routing to other agents
        #      based on the response or updated context.

        # 6. Format and return the final response
        final_response = {
            "message": agent_response.get("message", "Sorry, I encountered an issue."),
            "metadata": agent_response.get("metadata", {})
        }

        logger.info(f"Orchestrator finished handling message for conversation {conversation_id}")
        return final_response

    # Removed _simulate_agent_processing as it's replaced by actual agent call

    # Add methods for task decomposition, state management, error handling etc. later

# Potentially create a singleton instance if needed application-wide
# orchestrator = Orchestrator()
