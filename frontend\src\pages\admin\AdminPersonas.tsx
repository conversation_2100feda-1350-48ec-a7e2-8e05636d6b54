import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { adminApi, AdminPersona } from '@/lib/adminApi';
import { providerApi, ProviderModel } from '@/lib/providerApi';
import AdminLayout from '@/components/admin/AdminLayout';
import ModelSelect from '@/components/admin/ModelSelect';
import ApiKeyTester from '@/components/admin/ApiKeyTester';
import ProviderIcon from '@/components/admin/ProviderIcon';
import ProviderSelect from '@/components/admin/ProviderSelect';
import IndustrySelect, { getIndustryColor } from '@/components/admin/IndustrySelect';
import SkillsInput from '@/components/admin/SkillsInput';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash,
  Loader2,
  AlertCircle,
  Check,
  X,
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { industries } from '@/data/aiPersonas';

// Define available providers
const providers = [
  { id: 'groq', name: 'Groq' },
  { id: 'openai', name: 'OpenAI' },
  { id: 'gemini', name: 'Google Gemini' },
  { id: 'openrouter', name: 'OpenRouter' },
  { id: 'anthropic', name: 'Anthropic' },
  { id: 'requesty', name: 'Requesty' },
];

const AdminPersonas = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [industryFilter, setIndustryFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPersona, setSelectedPersona] = useState<AdminPersona | null>(null);
  const [formData, setFormData] = useState<Partial<AdminPersona>>({
    id: '',
    name: '',
    description: '',
    industry: '',
    skills: [],
    rating: 4.5,
    review_count: 0,
    image_url: '/placeholder.svg',
    price: 10.0,
    provider: 'groq',
    model: '',
    is_active: true,
    age_restriction: 0,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [providerModels, setProviderModels] = useState<ProviderModel[]>([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  // Load models when provider changes
  useEffect(() => {
    const loadModels = async () => {
      if (formData.provider) {
        setIsLoadingModels(true);
        try {
          const response = await providerApi.getProviderModels(formData.provider);

          // Log the response for debugging
          console.log(`Loaded ${response.models.length} models for provider ${formData.provider}`);

          // Set the models
          setProviderModels(response.models);

          // If there's an error message but no models, show a toast
          if (response.message && response.models.length === 0) {
            toast({
              title: 'Error Loading Models',
              description: response.message,
              variant: 'destructive',
            });
          }
        } catch (error) {
          console.error('Error loading models:', error);
          setProviderModels([]);

          // Show error toast
          toast({
            title: 'Error Loading Models',
            description: error instanceof Error ? error.message : 'Failed to load models',
            variant: 'destructive',
          });
        } finally {
          setIsLoadingModels(false);
        }
      } else {
        setProviderModels([]);
      }
    };

    loadModels();
  }, [formData.provider, toast]);

  // Fetch personas
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['adminPersonas', industryFilter, statusFilter],
    queryFn: () => adminApi.getPersonas({
      industry: industryFilter && industryFilter !== 'all' ? industryFilter : undefined,
      is_active: statusFilter && statusFilter !== 'all' ? statusFilter === 'active' : undefined,
    }),
  });

  // Filter personas by search query
  const filteredPersonas = data?.personas.filter(persona => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      persona.id.toLowerCase().includes(query) ||
      persona.name.toLowerCase().includes(query) ||
      persona.description.toLowerCase().includes(query) ||
      persona.industry.toLowerCase().includes(query)
    );
  }) || [];

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Skills are now handled directly by the SkillsInput component

  // Reset form data
  const resetFormData = () => {
    setFormData({
      id: '',
      name: '',
      description: '',
      industry: '',
      skills: [],
      rating: 4.5,
      review_count: 0,
      image_url: '/placeholder.svg',
      price: 10.0,
      provider: 'groq',
      model: '',
      is_active: true,
      age_restriction: 0,
    });
  };

  // Open create dialog
  const openCreateDialog = () => {
    resetFormData();
    setIsCreateDialogOpen(true);
  };

  // Open edit dialog
  const openEditDialog = (persona: AdminPersona) => {
    setSelectedPersona(persona);
    setFormData({
      ...persona,
      skills: [...persona.skills], // Create a copy of the skills array
    });
    setIsEditDialogOpen(true);
  };

  // Open delete dialog
  const openDeleteDialog = (persona: AdminPersona) => {
    setSelectedPersona(persona);
    setIsDeleteDialogOpen(true);
  };

  // Handle create persona
  const handleCreatePersona = async () => {
    if (!formData.id || !formData.name || !formData.description || !formData.industry) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await adminApi.createPersona(formData as Omit<AdminPersona, 'created_at' | 'updated_at'>);
      toast({
        title: 'Persona Created',
        description: `${formData.name} has been created successfully.`,
      });
      setIsCreateDialogOpen(false);
      refetch();
    } catch (error) {
      console.error('Error creating persona:', error);
      toast({
        title: 'Error',
        description: 'Failed to create persona. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle update persona
  const handleUpdatePersona = async () => {
    if (!selectedPersona) return;

    setIsSubmitting(true);
    try {
      await adminApi.updatePersona(selectedPersona.id, formData);
      toast({
        title: 'Persona Updated',
        description: `${formData.name} has been updated successfully.`,
      });
      setIsEditDialogOpen(false);
      refetch();
    } catch (error) {
      console.error('Error updating persona:', error);
      toast({
        title: 'Error',
        description: 'Failed to update persona. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete persona
  const handleDeletePersona = async () => {
    if (!selectedPersona) return;

    setIsSubmitting(true);
    try {
      await adminApi.deletePersona(selectedPersona.id);
      toast({
        title: 'Persona Deleted',
        description: `${selectedPersona.name} has been deleted successfully.`,
      });
      setIsDeleteDialogOpen(false);
      refetch();
    } catch (error) {
      console.error('Error deleting persona:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete persona. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle toggle persona status
  const handleToggleStatus = async (persona: AdminPersona) => {
    try {
      await adminApi.updatePersona(persona.id, { is_active: !persona.is_active });
      toast({
        title: persona.is_active ? 'Persona Deactivated' : 'Persona Activated',
        description: `${persona.name} has been ${persona.is_active ? 'deactivated' : 'activated'} successfully.`,
      });
      refetch();
    } catch (error) {
      console.error('Error toggling persona status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update persona status. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <AlertCircle className="h-12 w-12 text-destructive mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Personas</h2>
          <p className="text-muted-foreground mb-4">
            There was a problem loading the personas data.
          </p>
          <Button onClick={() => refetch()}>Retry</Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">AI Personas</h1>
          <Button onClick={openCreateDialog}>
            <Plus className="h-4 w-4 mr-2" />
            Add Persona
          </Button>
        </div>

        <div className="flex flex-col md:flex-row gap-4 items-center">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search personas..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Select value={industryFilter || "all"} onValueChange={setIndustryFilter}>
            <SelectTrigger className="w-full md:w-40 bg-white">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-gray-500">
                  <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"></path>
                  <path d="M18 14h-8"></path>
                  <path d="M15 18h-5"></path>
                  <path d="M10 6h8v4h-8V6Z"></path>
                </svg>
                <SelectValue placeholder="Industry" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all" className="py-1.5">All Industries</SelectItem>
              {industries.map((industry) => (
                <SelectItem key={industry} value={industry} className="py-1.5">
                  <div className="flex items-center">
                    <span className={`h-2 w-2 rounded-full mr-2 ${getIndustryColor(industry)}`}></span>
                    {industry}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={statusFilter || "all"} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full md:w-40 bg-white">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-gray-500">
                  <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                  <path d="m9 12 2 2 4-4"></path>
                </svg>
                <SelectValue placeholder="Status" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all" className="py-1.5">All Status</SelectItem>
              <SelectItem value="active" className="py-1.5">
                <div className="flex items-center">
                  <span className="h-2 w-2 rounded-full mr-2 bg-green-500"></span>
                  Active
                </div>
              </SelectItem>
              <SelectItem value="inactive" className="py-1.5">
                <div className="flex items-center">
                  <span className="h-2 w-2 rounded-full mr-2 bg-gray-400"></span>
                  Inactive
                </div>
              </SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            className="ml-auto bg-white hover:bg-gray-50 border-gray-300"
            onClick={() => refetch()}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
              <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
              <path d="M3 3v5h5"></path>
              <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"></path>
              <path d="M16 21h5v-5"></path>
            </svg>
            Refresh
          </Button>
        </div>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Industry</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Provider</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPersonas.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    No personas found
                  </TableCell>
                </TableRow>
              ) : (
                filteredPersonas.map((persona) => (
                  <TableRow
                    key={persona.id}
                    className="hover:bg-gray-50 transition-colors"
                  >
                    <TableCell className="font-mono text-xs">{persona.id}</TableCell>
                    <TableCell className="font-medium">{persona.name}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <span className={`h-2 w-2 rounded-full mr-2 ${getIndustryColor(persona.industry)}`}></span>
                        {persona.industry}
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">${persona.price.toFixed(2)}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <ProviderIcon providerId={persona.provider} className="h-4 w-4 mr-1.5 text-gray-700" />
                        {persona.provider}
                      </div>
                    </TableCell>
                    <TableCell>
                      {persona.is_active ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <Check className="h-3 w-3 mr-1" />
                          Active
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <X className="h-3 w-3 mr-1" />
                          Inactive
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => openEditDialog(persona)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleToggleStatus(persona)}>
                            {persona.is_active ? (
                              <>
                                <X className="h-4 w-4 mr-2" />
                                Deactivate
                              </>
                            ) : (
                              <>
                                <Check className="h-4 w-4 mr-2" />
                                Activate
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => openDeleteDialog(persona)}
                          >
                            <Trash className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Create Persona Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <div className="flex items-center">
              <div className="mr-3 bg-blue-100 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                  <path d="M12 5v14M5 12h14"></path>
                </svg>
              </div>
              <div>
                <DialogTitle className="text-xl">Create New AI Persona</DialogTitle>
                <DialogDescription className="mt-1">
                  Add a new AI persona to the system. Fill in all the required fields.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="id">ID *</Label>
                <div className="relative">
                  <Input
                    id="id"
                    name="id"
                    placeholder="e.g., marketing-ai"
                    value={formData.id}
                    onChange={handleInputChange}
                    className="bg-white pl-7"
                  />
                  <div className="absolute inset-y-0 left-0 flex items-center pl-2.5 pointer-events-none text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 7h-3a2 2 0 0 1-2-2V2"></path>
                      <path d="M16 2H8a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                    </svg>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <div className="relative">
                  <Input
                    id="name"
                    name="name"
                    placeholder="e.g., Marketing AI"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="bg-white pl-7"
                  />
                  <div className="absolute inset-y-0 left-0 flex items-center pl-2.5 pointer-events-none text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20z"></path>
                      <path d="M12 16v-4"></path>
                      <path d="M12 8h.01"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <div className="relative">
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Describe what this AI persona does..."
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="bg-white resize-y min-h-[100px] pl-9 pt-2"
                />
                <div className="absolute top-2 left-2.5 text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                </div>
                <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                  {formData.description.length} characters
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="industry">Industry *</Label>
                <IndustrySelect
                  industries={industries}
                  value={formData.industry}
                  onChange={(value) => setFormData(prev => ({ ...prev, industry: value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="price">Price ($)</Label>
                <div className="relative">
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">$</span>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.price}
                    onChange={handleInputChange}
                    className="pl-7 bg-white"
                  />
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="skills">Skills</Label>
              <SkillsInput
                skills={formData.skills || []}
                onChange={(skills) => setFormData(prev => ({ ...prev, skills }))}
                placeholder="Add skills (e.g., Data Analysis, Visualization)..."
              />
            </div>
            <div className="border rounded-lg p-4 bg-gray-50">
              <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-blue-600">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="3.29 7 12 12 20.71 7"></polyline>
                  <line x1="12" y1="22" x2="12" y2="12"></line>
                </svg>
                AI Model Configuration
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between mb-1">
                    <Label htmlFor="provider" className="text-sm font-medium">Provider</Label>
                  </div>
                  <ProviderSelect
                    providers={providers}
                    value={formData.provider}
                    onChange={(value) => setFormData(prev => ({ ...prev, provider: value, model: "" }))}
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between mb-1">
                    <Label htmlFor="model" className="text-sm font-medium">Model <span className="text-gray-500 font-normal">(optional)</span></Label>
                    {formData.model && (
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600 mr-1">
                          <path d="M12 2v4m0 12v4M4.93 4.93l2.83 2.83m8.48 8.48 2.83 2.83M2 12h4m12 0h4M4.93 19.07l2.83-2.83m8.48-8.48 2.83-2.83"></path>
                        </svg>
                        <span className="text-xs font-medium text-blue-700">
                          {providerModels.find(m => m.id === formData.model)?.context_length?.toLocaleString() || ""} tokens
                        </span>
                      </div>
                    )}
                  </div>
                  <ModelSelect
                    isLoadingModels={isLoadingModels}
                    providerModels={providerModels}
                    value={formData.model || ""}
                    onChange={(value) => setFormData(prev => ({ ...prev, model: value }))}
                    inputId="model"
                    placeholder="e.g., llama3-8b-8192"
                  />
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="age_restriction">Age Restriction</Label>
                <div className="relative">
                  <Input
                    id="age_restriction"
                    name="age_restriction"
                    type="number"
                    min="0"
                    value={formData.age_restriction}
                    onChange={handleInputChange}
                    className="bg-white pr-12"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-500">
                    years+
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2 pt-8">
                <div className="flex items-center p-2 border rounded-md bg-white hover:bg-gray-50">
                  <Checkbox
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => handleCheckboxChange('is_active', checked as boolean)}
                    className="data-[state=checked]:bg-green-600"
                  />
                  <Label htmlFor="is_active" className="ml-2 cursor-pointer">
                    {formData.is_active ? 'Active' : 'Inactive'}
                  </Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsCreateDialogOpen(false)}
              className="border-gray-300 hover:bg-gray-100"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreatePersona}
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                    <path d="M12 5v14M5 12h14"></path>
                  </svg>
                  Create Persona
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Persona Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <div className="flex items-center">
              <div className="mr-3 bg-blue-100 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                  <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                  <polyline points="17 21 17 13 7 13 7 21"></polyline>
                  <polyline points="7 3 7 8 15 8"></polyline>
                </svg>
              </div>
              <div>
                <DialogTitle className="text-xl">Edit AI Persona</DialogTitle>
                <DialogDescription className="mt-1">
                  Update the AI persona details. ID cannot be changed.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-id">ID</Label>
                <div className="relative">
                  <Input
                    id="edit-id"
                    value={formData.id}
                    disabled
                    className="bg-gray-100 pl-7"
                  />
                  <div className="absolute inset-y-0 left-0 flex items-center pl-2.5 pointer-events-none text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 7h-3a2 2 0 0 1-2-2V2"></path>
                      <path d="M16 2H8a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                    </svg>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-name">Name *</Label>
                <div className="relative">
                  <Input
                    id="edit-name"
                    name="name"
                    placeholder="e.g., Marketing AI"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="bg-white pl-7"
                  />
                  <div className="absolute inset-y-0 left-0 flex items-center pl-2.5 pointer-events-none text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20z"></path>
                      <path d="M12 16v-4"></path>
                      <path d="M12 8h.01"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description *</Label>
              <div className="relative">
                <Textarea
                  id="edit-description"
                  name="description"
                  placeholder="Describe what this AI persona does..."
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="bg-white resize-y min-h-[100px] pl-9 pt-2"
                />
                <div className="absolute top-2 left-2.5 text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                </div>
                <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                  {formData.description.length} characters
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-industry">Industry *</Label>
                <IndustrySelect
                  industries={industries}
                  value={formData.industry}
                  onChange={(value) => setFormData(prev => ({ ...prev, industry: value }))}
                  name="edit-industry"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-price">Price ($)</Label>
                <div className="relative">
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">$</span>
                  <Input
                    id="edit-price"
                    name="price"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.price}
                    onChange={handleInputChange}
                    className="pl-7 bg-white"
                  />
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-skills">Skills</Label>
              <SkillsInput
                skills={formData.skills || []}
                onChange={(skills) => setFormData(prev => ({ ...prev, skills }))}
                placeholder="Add skills (e.g., Data Analysis, Visualization)..."
              />
            </div>
            <div className="border rounded-lg p-4 bg-gray-50">
              <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-blue-600">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="3.29 7 12 12 20.71 7"></polyline>
                  <line x1="12" y1="22" x2="12" y2="12"></line>
                </svg>
                AI Model Configuration
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between mb-1">
                    <Label htmlFor="edit-provider" className="text-sm font-medium">Provider</Label>
                  </div>
                  <ProviderSelect
                    providers={providers}
                    value={formData.provider}
                    onChange={(value) => setFormData(prev => ({ ...prev, provider: value, model: "" }))}
                    name="edit-provider"
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between mb-1">
                    <Label htmlFor="edit-model" className="text-sm font-medium">Model <span className="text-gray-500 font-normal">(optional)</span></Label>
                    {formData.model && (
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600 mr-1">
                          <path d="M12 2v4m0 12v4M4.93 4.93l2.83 2.83m8.48 8.48 2.83 2.83M2 12h4m12 0h4M4.93 19.07l2.83-2.83m8.48-8.48 2.83-2.83"></path>
                        </svg>
                        <span className="text-xs font-medium text-blue-700">
                          {providerModels.find(m => m.id === formData.model)?.context_length?.toLocaleString() || ""} tokens
                        </span>
                      </div>
                    )}
                  </div>
                  <ModelSelect
                    isLoadingModels={isLoadingModels}
                    providerModels={providerModels}
                    value={formData.model || ""}
                    onChange={(value) => setFormData(prev => ({ ...prev, model: value }))}
                    inputId="edit-model"
                    placeholder="e.g., llama3-8b-8192"
                  />
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-age_restriction">Age Restriction</Label>
                <div className="relative">
                  <Input
                    id="edit-age_restriction"
                    name="age_restriction"
                    type="number"
                    min="0"
                    value={formData.age_restriction}
                    onChange={handleInputChange}
                    className="bg-white pr-12"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-500">
                    years+
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2 pt-8">
                <div className="flex items-center p-2 border rounded-md bg-white hover:bg-gray-50">
                  <Checkbox
                    id="edit-is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => handleCheckboxChange('is_active', checked as boolean)}
                    className="data-[state=checked]:bg-green-600"
                  />
                  <Label htmlFor="edit-is_active" className="ml-2 cursor-pointer">
                    {formData.is_active ? 'Active' : 'Inactive'}
                  </Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              className="border-gray-300 hover:bg-gray-100"
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdatePersona}
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                    <polyline points="17 21 17 13 7 13 7 21"></polyline>
                    <polyline points="7 3 7 8 15 8"></polyline>
                  </svg>
                  Update Persona
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Persona Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <div className="flex items-center">
              <div className="mr-3 bg-red-100 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-red-600">
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
              </div>
              <div>
                <DialogTitle className="text-xl">Delete AI Persona</DialogTitle>
                <DialogDescription className="mt-1">
                  Are you sure you want to delete this AI persona? This action cannot be undone.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="py-4">
            <div className="flex items-center mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <div className="mr-4 bg-red-100 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-red-600">
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                  <line x1="12" y1="9" x2="12" y2="13"></line>
                  <line x1="12" y1="17" x2="12.01" y2="17"></line>
                </svg>
              </div>
              <div>
                <p className="font-medium text-red-800">This action cannot be undone</p>
                <p className="text-sm text-red-600 mt-1">
                  Once deleted, all data associated with this AI persona will be permanently removed.
                </p>
              </div>
            </div>
            <div className="p-4 border rounded-md">
              <h4 className="text-sm font-semibold text-gray-500 uppercase mb-2">Persona Details</h4>
              <p className="font-medium text-lg">{selectedPersona?.name}</p>
              <p className="text-sm text-muted-foreground mt-1">{selectedPersona?.description}</p>
              <div className="mt-3 flex items-center text-sm text-gray-500">
                <span className="mr-3">ID: <span className="font-mono">{selectedPersona?.id}</span></span>
                <span className="mr-3">Provider: {selectedPersona?.provider}</span>
                <span>Price: ${selectedPersona?.price}</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-gray-300 hover:bg-gray-100"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeletePersona}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                    <line x1="10" y1="11" x2="10" y2="17"></line>
                    <line x1="14" y1="11" x2="14" y2="17"></line>
                  </svg>
                  Delete Persona
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default AdminPersonas;
