
import { Search } from "lucide-react";

interface MarketplaceSearchProps {
  value: string;
  onChange: (value: string) => void;
}

export function MarketplaceSearch({ value, onChange }: MarketplaceSearchProps) {
  return (
    <div className="relative">
      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <Search className="h-4 w-4 text-muted-foreground" />
      </div>
      <input
        type="text"
        className="bg-background py-2 pl-10 pr-4 block w-full border rounded-md focus:ring-2 focus:ring-primary/20 focus:border-primary/80 transition-colors"
        placeholder="Search for AI assistants..."
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
    </div>
  );
}
