"""
Composable agent implementation for the Datagenius backend.

This module provides a composable agent implementation that can be built from
reusable components.
"""

import logging
from typing import Dict, Any, Optional, List

from .base import BaseAgent
from .components import AgentComponent, ComponentRegistry

logger = logging.getLogger(__name__)


class ComposableAgent(BaseAgent):
    """Agent composed of reusable components."""

    def __init__(self):
        """Initialize the composable agent."""
        super().__init__()
        self.components = []

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the composable agent.

        Args:
            config: Configuration dictionary for the agent
        """
        logger.info("Initializing Composable Agent")

        # Ensure the agent has a name based on its class if not set in config
        if self.name == "base-agent":
            # Get the class name and convert it to kebab-case for consistency
            class_name = self.__class__.__name__
            # Convert CamelCase to kebab-case (e.g., ComposableMarketingAgent -> composable-marketing-agent)
            kebab_name = ''.join(['-' + c.lower() if c.isupper() else c for c in class_name]).lstrip('-')
            self.name = kebab_name
            logger.info(f"Set agent name from class: {self.name}")

        # Initialize components from configuration
        if "components" in config:
            for component_config in config["components"]:
                component_name = component_config.get("type")
                if not component_name:
                    logger.warning("Component configuration missing 'type' field")
                    continue

                logger.info(f"Creating component of type '{component_name}'")
                logger.info(f"Available component types: {ComponentRegistry.list_registered_components()}")
                component_class = ComponentRegistry.get_component_class(component_name)
                if component_class:
                    try:
                        component = component_class()
                        await component.initialize(component_config)
                        self.components.append(component)
                        logger.info(f"Added component: {component.name}")
                    except Exception as e:
                        logger.error(f"Error initializing component '{component_name}': {e}", exc_info=True)
                else:
                    logger.warning(f"Component type '{component_name}' not found in registry")

        logger.info(f"Initialized {len(self.components)} components")

        # Ensure essential tools are available in the MCP server component
        await self._ensure_essential_tools()

    async def _ensure_essential_tools(self) -> None:
        """
        Ensure that the MCP server component has all essential tools.
        """
        from .components.mcp_server import MCPServerComponent
        from .components.essential_tools import ensure_essential_tools

        # Find the MCP server component
        mcp_server = next((comp for comp in self.components if isinstance(comp, MCPServerComponent)), None)
        if mcp_server:
            logger.info("Found MCP server component, ensuring essential tools")
            await ensure_essential_tools(mcp_server)
        else:
            logger.warning("No MCP server component found, cannot ensure essential tools")

    async def process_message(self,
                             user_id: int,
                             message: str,
                             conversation_id: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message using the agent's components.

        Args:
            user_id: The ID of the user sending the message
            message: The user's message text
            conversation_id: The ID of the conversation
            context: Additional context information

        Returns:
            Dict containing response text and any additional data
        """

        if not self.components:
            logger.warning("No components configured for this agent")
            return {
                "message": "This agent has no components configured. Please check the agent configuration.",
                "metadata": {
                    "error": "no_components_configured"
                }
            }

        # Create initial context
        ctx = {
            "user_id": user_id,
            "message": message,
            "conversation_id": conversation_id,
            "context": context or {},
            "agent_config": self.config,
            "agent_components": self.components,  # Add components to context for access
            "response": "",
            "metadata": {}
        }

        # Check if we need to process a file for the persona
        if context and context.get("send_file_to_persona") and context.get("data_source"):
            # Find MCP server component
            mcp_server = None
            for component in self.components:
                if component.__class__.__name__ == "MCPServerComponent":
                    mcp_server = component
                    break

            if mcp_server:
                logger.info("Found MCP server component, processing file for persona")
                try:
                    # Call the data_access tool with the send_to_persona operation
                    result = await mcp_server.call_tool("data_access", {
                        "operation": "send_to_persona",
                        "data_source": context.get("data_source"),
                        "params": {"sample_size": 10, "include_table": True}  # Show 10 rows with table visualization
                    })

                    # If successful, add the result to the context
                    if not result.get("isError", False):
                        logger.info("Successfully processed file for persona")
                        ctx["file_processed"] = True
                        ctx["file_data"] = result

                        # If no message was provided, generate a default response about the file
                        if not message or message.strip() == "":
                            # Extract text content from the result
                            text_content = []
                            for content_item in result.get("content", []):
                                if content_item.get("type") == "text":
                                    text_content.append(content_item.get("text", ""))

                            if text_content:
                                ctx["response"] = "\n".join(text_content)
                    else:
                        logger.error(f"Error processing file: {result}")
                        error_message = "I couldn't process the attached file. Please make sure it's a valid data file."
                        if result.get("content") and len(result["content"]) > 0:
                            error_message = result["content"][0].get("text", error_message)
                        ctx["response"] = error_message
                        ctx["metadata"]["file_error"] = True
                except Exception as e:
                    logger.error(f"Error calling data_access tool: {e}")
                    ctx["response"] = "I encountered an error while processing the attached file."
                    ctx["metadata"]["file_error"] = True

        # Process context through each component
        for component in self.components:
            try:
                logger.debug(f"Processing with component: {component.name}")
                ctx = await component.process(ctx)
            except Exception as e:
                logger.error(f"Error in component {component.name}: {e}")
                ctx["metadata"]["errors"] = ctx.get("metadata", {}).get("errors", []) + [
                    {"component": component.name, "error": str(e)}
                ]

        # Ensure we have a response
        if not ctx.get("response"):
            logger.warning("No response generated by components")
            ctx["response"] = "I'm sorry, I wasn't able to process your request properly."

        return {
            "message": ctx.get("response", ""),
            "metadata": ctx.get("metadata", {})
        }

    async def get_capabilities(self) -> List[str]:
        """
        Return a list of capabilities this agent supports.

        Returns:
            List of capability strings
        """
        # Combine capabilities from all components
        capabilities = set(self.capabilities)
        for component in self.components:
            capabilities.update(component.get_capabilities())
        return list(capabilities)
