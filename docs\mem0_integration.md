# mem0ai Integration with Qdrant

This document provides information about the integration of mem0ai with Qdrant for vector database and knowledge graph operations in the Datagenius application.

## Overview

The Datagenius application uses mem0ai as a unified memory, vector database, and knowledge graph solution. This integration provides the following capabilities:

1. **Memory Management**: Store and retrieve memories for AI personas
2. **Vector Database**: Store and query document embeddings for semantic search
3. **Knowledge Graph**: Create and query knowledge graphs for entity and relationship extraction

## Architecture

The integration consists of the following components:

1. **MemoryService**: A singleton service that provides a unified interface for memory operations
2. **VectorService**: A singleton service that provides a unified interface for vector database operations
3. **KnowledgeGraphService**: A singleton service that provides a unified interface for knowledge graph operations
4. **QdrantManager**: A utility class for managing the Qdrant vector database
5. **MCP Tools**: Model Context Protocol tools for interacting with the services

## Configuration

The integration is configured through environment variables:

```
# mem0ai configuration
MEM0_API_KEY=your-mem0-api-key
MEM0_ENDPOINT=
MEM0_SELF_HOSTED=true
MEM0_DEFAULT_TTL=2592000
MEM0_MAX_MEMORIES=1000
MEM0_MEMORY_THRESHOLD=0.7

# Qdrant configuration
QDRANT_HOST=qdrant
QDRANT_PORT=6333
```

## Self-Hosted Mode

When `MEM0_SELF_HOSTED` is set to `true`, the integration uses a local Qdrant instance for vector storage. The Qdrant instance can be run in Docker using the provided Docker Compose configuration.

## Hosted Mode

When `MEM0_SELF_HOSTED` is set to `false`, the integration uses the mem0ai hosted service. In this mode, you need to provide a valid `MEM0_API_KEY`.

## Docker Compose Configuration

The Docker Compose configuration includes a Qdrant service:

```yaml
qdrant:
  image: qdrant/qdrant:latest
  container_name: datagenius-qdrant
  restart: unless-stopped
  ports:
    - "6333:6333"  # REST API
    - "6334:6334"  # gRPC API
  volumes:
    - qdrant_data:/qdrant/storage
  environment:
    - QDRANT_ALLOW_CORS=true
    - QDRANT_LOG_LEVEL=INFO
  healthcheck:
    test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:6333/health"]
    interval: 5s
    timeout: 5s
    retries: 3
    start_period: 5s
```

## Services

### MemoryService

The `MemoryService` provides the following methods:

- `add_memory`: Add a memory for a specific user
- `search_memories`: Search for relevant memories for a user
- `add_conversation`: Add a conversation to memory
- `delete_memory`: Delete a specific memory
- `clear_user_memories`: Clear all memories for a specific user

### VectorService

The `VectorService` provides the following methods:

- `embed_document`: Embed a document using mem0ai
- `search_document`: Search a document using mem0ai

### KnowledgeGraphService

The `KnowledgeGraphService` provides the following methods:

- `create_graph`: Create a new knowledge graph
- `add_entity`: Add an entity to the knowledge graph
- `add_relationship`: Add a relationship to the knowledge graph
- `get_entities`: Get entities from the knowledge graph
- `get_relationships`: Get relationships from the knowledge graph

## MCP Tools

The integration includes the following MCP tools:

- `Mem0DocumentEmbeddingTool`: Tool for document embedding using mem0ai
- `KnowledgeGraphTool`: Tool for knowledge graph operations using mem0ai

## Usage Examples

### Memory Management

```python
from agents.utils.memory_service import MemoryService

# Initialize memory service
memory_service = MemoryService()

# Add a memory
memory = memory_service.add_memory(
    "This is a test memory",
    user_id="user123",
    metadata={"category": "test"}
)

# Search for memories
results = memory_service.search_memories(
    "test memory",
    user_id="user123",
    limit=5
)
```

### Vector Database

```python
from agents.utils.vector_service import VectorService

# Initialize vector service
vector_service = VectorService()

# Embed a document
vector_store_id, file_info = vector_service.embed_document("path/to/document.pdf")

# Search a document
results = vector_service.search_document(vector_store_id, "What is a vector database?")
```

### Knowledge Graph

```python
from agents.utils.knowledge_graph_service import KnowledgeGraphService

# Initialize knowledge graph service
kg_service = KnowledgeGraphService()

# Create a graph
graph_id = kg_service.create_graph("Test Graph", "A test graph")

# Add entities
entity1_id = kg_service.add_entity(
    graph_id=graph_id,
    entity_type="Person",
    name="John Doe",
    properties={"age": 30}
)

entity2_id = kg_service.add_entity(
    graph_id=graph_id,
    entity_type="Organization",
    name="Acme Corp",
    properties={"industry": "Technology"}
)

# Add relationship
relationship_id = kg_service.add_relationship(
    graph_id=graph_id,
    relationship_type="WORKS_AT",
    source_id=entity1_id,
    target_id=entity2_id
)

# Get entities
entities = kg_service.get_entities(graph_id)

# Get relationships
relationships = kg_service.get_relationships(graph_id)
```

## Testing

You can test the integration using the provided test script:

```bash
python -m backend.tests.test_mem0_integration
```

This script tests the memory service, vector service, and knowledge graph service.
