
import { useState, useEffect } from "react";
import { DashboardLayout } from "@/components/DashboardLayout";
import { AIPersonaCard } from "@/components/marketplace/AIPersonaCard";
import { MarketplaceFilters } from "@/components/marketplace/MarketplaceFilters";
import { MarketplaceSearch } from "@/components/marketplace/MarketplaceSearch";
import { AIPersona, industries } from "@/data/aiPersonas";
import { aiPersonas as defaultPersonas } from "@/data/aiPersonas";
import { motion } from "framer-motion";
import { personaApi } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { useProviderAvailability } from "@/hooks/use-provider-availability";
import { Loader2 } from "lucide-react";

const AIMarketplace = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [personas, setPersonas] = useState<AIPersona[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Get provider availability
  const { combinedAvailability, isLoading: isLoadingProviders, error: providerError } = useProviderAvailability();

  // Log provider availability for debugging
  useEffect(() => {
    console.log('Provider availability in AIMarketplace:', combinedAvailability);
    if (providerError) {
      console.error('Provider availability error:', providerError);
    }
  }, [combinedAvailability, providerError]);

  // Fetch personas from the backend
  useEffect(() => {
    const fetchPersonas = async () => {
      setIsLoading(true);
      try {
        // If we have only one industry selected, use the API's industry filter
        // Otherwise, fetch all personas and filter client-side
        const selectedIndustry = selectedIndustries.length === 1 ? selectedIndustries[0] : undefined;
        const response = await personaApi.getPersonas(selectedIndustry);

        // Update persona availability based on provider availability
        const updatedPersonas = response.personas.map(persona => {
          // Determine which provider this persona needs
          let requiredProvider = "groq"; // Default provider

          if (persona.id === "marketing-ai") {
            requiredProvider = "groq";
          } else if (persona.id === "analysis-ai") {
            requiredProvider = "groq";
          } else if (persona.id === "classifier-ai") {
            requiredProvider = "groq";
          }

          // Check if the required provider is available
          const isProviderAvailable = combinedAvailability[requiredProvider] || false;

          console.log(`Persona ${persona.id} requires provider ${requiredProvider}, available: ${isProviderAvailable}`);

          // Override the isAvailable property based on provider availability
          return {
            ...persona,
            isAvailable: isProviderAvailable
          };
        });

        setPersonas(updatedPersonas);
      } catch (error) {
        console.error("Failed to load personas:", error);
        toast({
          title: "Error",
          description: "Failed to load AI personas. Using default data instead.",
          variant: "destructive",
        });

        // Fallback to default personas, but update availability based on provider availability
        const updatedDefaultPersonas = defaultPersonas.map(persona => {
          // Determine which provider this persona needs
          let requiredProvider = "groq"; // Default provider

          if (persona.id === "marketing-ai") {
            requiredProvider = "groq";
          } else if (persona.id === "analysis-ai") {
            requiredProvider = "groq";
          } else if (persona.id === "classifier-ai") {
            requiredProvider = "groq";
          }

          // Check if the required provider is available
          const isProviderAvailable = combinedAvailability[requiredProvider] || false;

          // Override the isAvailable property based on provider availability
          return {
            ...persona,
            isAvailable: isProviderAvailable
          };
        });

        setPersonas(updatedDefaultPersonas);
      } finally {
        setIsLoading(false);
      }
    };

    // Only fetch personas when provider availability is loaded
    if (!isLoadingProviders) {
      fetchPersonas();
    }
  }, [selectedIndustries, toast, combinedAvailability, isLoadingProviders]);

  // Filter personas based on search query and filters
  const filteredPersonas = personas.filter((persona) => {
    // Filter by search query
    const matchesSearch =
      searchQuery === "" ||
      persona.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      persona.description.toLowerCase().includes(searchQuery.toLowerCase());

    // Filter by industry (if multiple industries are selected)
    const matchesIndustry =
      selectedIndustries.length === 0 ||
      selectedIndustries.includes(persona.industry);

    // Filter by rating
    const matchesRating =
      selectedRating === null || persona.rating >= selectedRating;

    return matchesSearch && matchesIndustry && matchesRating;
  });

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        <div className="flex flex-col space-y-2">
          <h1 className="text-2xl font-bold">AI Persona Marketplace</h1>
          <p className="text-muted-foreground">
            Find and deploy industry-specific AI assistants for your business needs
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-6 md:items-center">
          <div className="w-full md:w-1/2">
            <MarketplaceSearch value={searchQuery} onChange={setSearchQuery} />
          </div>
          <div className="w-full md:w-1/2">
            <MarketplaceFilters
              industries={industries}
              selectedIndustries={selectedIndustries}
              onIndustriesChange={setSelectedIndustries}
              selectedRating={selectedRating}
              onRatingChange={setSelectedRating}
            />
          </div>
        </div>

        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <h3 className="text-lg font-medium">Loading AI personas...</h3>
          </div>
        ) : filteredPersonas.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <h3 className="text-lg font-medium">No matching personas found</h3>
            <p className="text-muted-foreground mt-2">
              Try adjusting your search criteria or filters
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPersonas.map((persona) => (
              <AIPersonaCard key={persona.id} persona={persona} />
            ))}
          </div>
        )}
      </motion.div>
    </DashboardLayout>
  );
};

export default AIMarketplace;
