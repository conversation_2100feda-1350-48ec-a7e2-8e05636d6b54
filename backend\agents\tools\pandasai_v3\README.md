# PandasAI v3 Integration

This module provides integration with PandasAI v3 for the Datagenius backend, including a wrapper for the PandasAI Agent class, semantic layer utilities, and LLM provider adapters.

## Overview

PandasAI v3 is a powerful library that enables natural language interactions with pandas dataframes. This integration provides a standardized way to use PandasAI v3 in the Datagenius backend, with support for various LLM providers.

## Components

### PandasAIWrapper

The `PandasAIWrapper` class provides a wrapper for the PandasAI Agent class, making it easier to use PandasAI v3 in the Datagenius backend. It includes methods for:

- Loading dataframes from various file formats
- Creating PandasAI agents with custom LLM providers
- Chatting with agents
- Training agents with custom instructions and examples

### LLMProviderFactory

The `LLMProviderFactory` class provides a factory for creating PandasAI LLM providers for different AI providers. It uses custom provider implementations from the `custom_providers` module.

### Custom Providers

The `custom_providers` module provides custom LLM provider implementations for PandasAI v3, supporting various providers:

- `OpenAILLM`: For OpenAI models
- `GroqLLM`: For Groq models
- `GeminiLLM`: For Google Gemini models
- `OpenRouterLLM`: For OpenRouter models
- `RequestyLLM`: For Requesty models

### SemanticLayerManager

The `SemanticLayerManager` class provides utilities for creating and managing semantic layers in PandasAI v3.

### ResponseCache

The `ResponseCache` class provides a cache for PandasAI responses to improve performance.

### ErrorHandler

The `ErrorHandler` class provides error handling utilities for PandasAI operations.

## MCP Tools

The following MCP tools are provided for using PandasAI v3 in the Datagenius backend:

- `PandasAIAnalysisTool`: For analyzing data using PandasAI v3
- `PandasAIVisualizationTool`: For visualizing data using PandasAI v3
- `PandasAIQueryTool`: For querying data using PandasAI v3

## Usage

To use PandasAI v3 in the Datagenius backend, you can use the MCP tools directly or use the wrapper classes in your own code.

### Using MCP Tools

```python
# Example of using the PandasAI v3 query tool
result = await mcp_server.call_tool("pandasai_query", {
    "file_path": file_path,
    "query": "What is the average age?",
    "api_key": api_key,
    "provider": "openai",
    "model": "gpt-4"  # Optional
})
```

### Using Wrapper Classes

```python
# Example of using the PandasAIWrapper class
from agents.tools.pandasai_v3 import PandasAIWrapper

wrapper = PandasAIWrapper()
wrapper.initialize(api_key, provider="openai")
wrapper.load_dataframe("data/example.csv")
wrapper.create_agent(model="gpt-4")  # Optional model parameter
result = wrapper.chat("What is the average age?")
```

## Supported Providers

The following AI providers are supported:

- **OpenAI**: Using the OpenAI API
- **Groq**: Using the Groq API
- **Google Gemini**: Using the Google Gemini API
- **OpenRouter**: Using the OpenRouter API (provides access to multiple models)
- **Requesty**: Using the Requesty API

## Configuration

The PandasAI v3 integration can be configured using the following environment variables:

- `OPENAI_API_KEY`: API key for OpenAI
- `GROQ_API_KEY`: API key for Groq
- `GEMINI_API_KEY`: API key for Google Gemini
- `OPENROUTER_API_KEY`: API key for OpenRouter
- `REQUESTY_API_KEY`: API key for Requesty

## Dependencies

- `pandasai==3.0.0b17` or later
- `pandas`
- `numpy`
- `matplotlib`
- `seaborn`
- `plotly`
- `openai` (for OpenAI, OpenRouter, and Requesty providers)
- `groq` (for Groq provider)
- `google-generativeai` (for Google Gemini provider)
