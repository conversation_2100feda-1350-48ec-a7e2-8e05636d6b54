"""
Model provider registration for the Datagenius backend.

This module registers all available model providers with the registry.
"""

import logging
from .registry import ModelProviderRegistry
from .groq_provider import GroqProvider
from .openai_provider import OpenAIProvider
from .anthropic_provider import AnthropicProvider
from .ollama_provider import OllamaProvider
from .gemini_provider import GeminiProvider
from .openrouter_provider import OpenRouterProvider
from .requesty_provider import RequestyProvider
from .config import get_provider_config, PROVIDER_CONFIG

# Configure logging
logger = logging.getLogger(__name__)


def register_model_providers():
    """Register all model providers with the registry."""
    # Register Groq provider
    ModelProviderRegistry.register("groq", GroqProvider)
    logger.info("Registered Groq provider with model provider registry")

    # Register OpenAI provider
    ModelProviderRegistry.register("openai", OpenAIProvider)
    logger.info("Registered OpenAI provider with model provider registry")

    # Register Anthropic provider
    ModelProviderRegistry.register("anthropic", AnthropicProvider)
    logger.info("Registered Anthropic provider with model provider registry")

    # Register Ollama provider
    ModelProviderRegistry.register("ollama", OllamaProvider)
    logger.info("Registered Ollama provider with model provider registry")

    # Register Gemini provider
    ModelProviderRegistry.register("gemini", GeminiProvider)
    logger.info("Registered Gemini provider with model provider registry")

    # Register OpenRouter provider
    ModelProviderRegistry.register("openrouter", OpenRouterProvider)
    logger.info("Registered OpenRouter provider with model provider registry")

    # Register Requesty provider
    ModelProviderRegistry.register("requesty", RequestyProvider)
    logger.info("Registered Requesty provider with model provider registry")

    # Set default provider based on available API keys
    _set_default_provider()

    # Log the registered providers
    provider_ids = ModelProviderRegistry.list_registered_providers()
    logger.info(f"Registered model providers: {provider_ids}")


def _set_default_provider():
    """Set the default provider based on available API keys."""
    # Check for API keys in provider configuration
    if PROVIDER_CONFIG["groq"]["api_key"]:
        ModelProviderRegistry.set_default_provider("groq")
        logger.info("Set default provider to 'groq'")
    elif PROVIDER_CONFIG["openai"]["api_key"]:
        ModelProviderRegistry.set_default_provider("openai")
        logger.info("Set default provider to 'openai'")
    elif PROVIDER_CONFIG["anthropic"]["api_key"]:
        ModelProviderRegistry.set_default_provider("anthropic")
        logger.info("Set default provider to 'anthropic'")
    elif PROVIDER_CONFIG["gemini"]["api_key"]:
        ModelProviderRegistry.set_default_provider("gemini")
        logger.info("Set default provider to 'gemini'")
    elif PROVIDER_CONFIG["openrouter"]["api_key"]:
        ModelProviderRegistry.set_default_provider("openrouter")
        logger.info("Set default provider to 'openrouter'")
    elif PROVIDER_CONFIG["requesty"]["api_key"]:
        ModelProviderRegistry.set_default_provider("requesty")
        logger.info("Set default provider to 'requesty'")
    else:
        # Default to Ollama if no API keys are found
        ModelProviderRegistry.set_default_provider("ollama")
        logger.info("Set default provider to 'ollama' (no API keys found)")
