import { VisualizationData } from '@/utils/visualization';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface DataPreviewVisualizationProps {
  visualization: VisualizationData;
}

export const DataPreviewVisualization = ({ visualization }: DataPreviewVisualizationProps) => {
  const { data } = visualization;
  const { preview_data, columns, description, metadata } = data;

  return (
    <Card className="shadow-md border-gray-200">
      <CardHeader className="bg-gradient-to-r from-white to-gray-50 border-b border-gray-100">
        <CardTitle className="text-brand-700">{visualization.title || 'Data Preview'}</CardTitle>
        {visualization.description && <CardDescription>{visualization.description}</CardDescription>}
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="preview" className="w-full">
          <TabsList className="w-full border-b rounded-none px-4 bg-gray-50">
            <TabsTrigger value="preview">Data Preview</TabsTrigger>
            <TabsTrigger value="info">Data Info</TabsTrigger>
            {description && <TabsTrigger value="description">Description</TabsTrigger>}
          </TabsList>

          <TabsContent value="preview" className="p-4">
            {preview_data && preview_data.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b bg-gray-50">
                      {Object.keys(preview_data[0]).map((column, index) => (
                        <th key={index} className="text-left p-2 text-sm font-medium text-gray-700">
                          {column}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {preview_data.map((row, rowIndex) => (
                      <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        {Object.values(row).map((value, cellIndex) => (
                          <td key={cellIndex} className="p-2 text-sm text-gray-700 border-b border-gray-100">
                            {String(value)}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center p-4 text-gray-500">No preview data available</div>
            )}
          </TabsContent>

          <TabsContent value="info" className="p-4">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Data Shape</h3>
                <div className="bg-gray-50 p-2 rounded text-sm">
                  {metadata?.shape ? (
                    <p>Rows: {metadata.shape[0]}, Columns: {metadata.shape[1]}</p>
                  ) : (
                    <p>Shape information not available</p>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Columns</h3>
                <div className="bg-gray-50 p-2 rounded text-sm">
                  {columns && columns.length > 0 ? (
                    <ul className="list-disc pl-5 space-y-1">
                      {columns.map((column, index) => (
                        <li key={index}>
                          {column} {metadata?.dtypes && metadata.dtypes[column] ? `(${metadata.dtypes[column]})` : ''}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p>Column information not available</p>
                  )}
                </div>
              </div>

              {metadata?.missing_values && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Missing Values</h3>
                  <div className="bg-gray-50 p-2 rounded text-sm">
                    <ul className="list-disc pl-5 space-y-1">
                      {Object.entries(metadata.missing_values).map(([column, count], index) => (
                        <li key={index}>
                          {column}: {count} missing values
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {metadata?.description && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Statistical Summary</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse text-sm">
                      <thead>
                        <tr className="border-b bg-gray-50">
                          <th className="text-left p-2">Statistic</th>
                          {Object.keys(metadata.description).map((column, index) => (
                            <th key={index} className="text-left p-2">{column}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {Object.keys(Object.values(metadata.description)[0] || {}).map((stat, statIndex) => (
                          <tr key={statIndex} className={statIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                            <td className="p-2 font-medium border-b border-gray-100">{stat}</td>
                            {Object.keys(metadata.description).map((column, colIndex) => (
                              <td key={colIndex} className="p-2 border-b border-gray-100">
                                {metadata.description[column][stat]?.toFixed(2) || 'N/A'}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          {description && (
            <TabsContent value="description" className="p-4">
              <div className="prose prose-sm max-w-none">
                <pre className="whitespace-pre-wrap bg-gray-50 p-3 rounded text-sm">{description}</pre>
              </div>
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
};
