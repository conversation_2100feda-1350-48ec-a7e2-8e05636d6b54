# Purchased Personas Fix

This document describes the fix for the issue with purchased personas not showing up in the Data Chat.

## Problem

The frontend was making a request to `/personas/purchased` but the endpoint was returning a 404 error. This was happening because the route order in FastAPI matters, and the `/{persona_id}` route was being matched before the `/purchased` route.

The logs showed:

```
2025-04-24 21:35:06,385 - app.api.personas - INFO - User 4 requested persona purchased
2025-04-24 21:35:06,388 - app.api.personas - INFO - Persona purchased not found in database, checking registry
2025-04-24 21:35:06,388 - agents.registry - WARNING - No configuration found for persona ID 'purchased'
2025-04-24 21:35:06,388 - agents.registry - WARNING - No agent class found for persona ID 'purchased'
2025-04-24 21:35:06,388 - app.api.personas - ERROR - Persona purchased not found in database or registry
INFO:     127.0.0.1:53709 - "GET /personas/purchased HTTP/1.1" 404 Not Found
```

This indicates that the `/personas/purchased` endpoint was being interpreted as a request for a specific persona with ID "purchased" rather than a request for the list of purchased personas.

## Solution

To fix this issue, we:

1. Removed the `/personas/purchased` endpoint from the `personas.py` file
2. Added a dedicated endpoint directly in `main.py` to handle the `/personas/purchased` route

This ensures that the `/personas/purchased` route is matched correctly and doesn't conflict with the `/{persona_id}` route.

## Implementation

### 1. Added a dedicated endpoint in `main.py`

```python
# Create a special route for purchased personas
from fastapi import Depends
from sqlalchemy.orm import Session
from typing import List
from .database import get_db
from .auth import get_current_active_user
from .models.auth import User
from .services import persona_service

@app.get("/personas/purchased", response_model=List[str], tags=["Personas"])
async def get_purchased_personas(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a list of persona IDs that the user has purchased.
    """
    logger.info(f"User {current_user.id} requested purchased personas")
    try:
        # Get purchased personas using the service
        persona_ids = persona_service.get_user_purchased_personas(db, current_user.id)
        logger.info(f"Found {len(persona_ids)} purchased personas for user {current_user.id}: {persona_ids}")
        return persona_ids
    except Exception as e:
        logger.error(f"Error getting purchased personas for user {current_user.id}: {str(e)}", exc_info=True)
        # Return empty list instead of failing
        return []
```

### 2. Removed the duplicate endpoint from `personas.py`

```python
# Note: The /personas/purchased endpoint is now defined in main.py to avoid routing conflicts
```

### 3. Added a test script to create test purchases

We also created a script to add test purchases for testing purposes:

```python
def add_test_purchase(user_email: str, persona_id: str, price: float = 10.0):
    """
    Add a test purchase for a user.

    Args:
        user_email: Email of the user
        persona_id: ID of the persona to purchase
        price: Price of the persona
    """
    # Implementation details...
```

## Testing

To test this fix:

1. Start the backend server
2. Run the test script to add a test purchase:
   ```
   python -m backend.scripts.add_test_purchase <EMAIL> marketing-ai
   ```
3. Make a request to the `/personas/purchased` endpoint
4. Verify that the purchased persona appears in the Data Chat

## Conclusion

This fix ensures that the `/personas/purchased` endpoint works correctly and doesn't conflict with the `/{persona_id}` route. This allows the frontend to correctly fetch the list of purchased personas and display them in the Data Chat.
