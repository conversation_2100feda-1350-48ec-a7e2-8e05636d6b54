#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to directly add personas to the database.
This is a simplified version that directly executes SQL to ensure personas are added.
"""

import sys
import os
import json
import logging
import sqlite3
import psycopg2
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Default personas data
DEFAULT_PERSONAS = [
    {
        "id": "marketing-ai",
        "name": "Marketing AI",
        "description": "Strategic marketing assistant for campaign planning and content creation.",
        "industry": "Marketing",
        "skills": ["Marketing Strategy", "Campaign Planning", "Content Creation"],
        "rating": 4.6,
        "review_count": 92,
        "image_url": "/placeholder.svg",
        "price": 10.0,
        "provider": "groq",
        "model": "llama3-8b-8192",
        "is_active": True,
        "age_restriction": 0
    },
    {
        "id": "analysis-ai",
        "name": "Analysis AI",
        "description": "Data analysis specialist for exploring and visualizing datasets.",
        "industry": "Data Science",
        "skills": ["Data Cleaning", "Data Visualization", "Statistical Analysis"],
        "rating": 4.7,
        "review_count": 105,
        "image_url": "/placeholder.svg",
        "price": 10.0,
        "provider": "groq",
        "model": "llama3-8b-8192",
        "is_active": True,
        "age_restriction": 0
    },
    {
        "id": "classifier-ai",
        "name": "Classifier AI",
        "description": "Text classification specialist for organizing and categorizing content.",
        "industry": "Technology",
        "skills": ["Text Classification", "Document Organization", "Content Analysis"],
        "rating": 4.7,
        "review_count": 95,
        "image_url": "/placeholder.svg",
        "price": 10.0,
        "provider": "groq",
        "model": "llama3-8b-8192",
        "is_active": True,
        "age_restriction": 0
    }
]

def get_database_url():
    """Get the database URL from the environment or config file."""
    # Try to import from config
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from app import config
        return config.DATABASE_URL
    except ImportError:
        logger.warning("Could not import config, using environment variable.")
        return os.environ.get("DATABASE_URL", "sqlite:///./app.db")

def is_postgres(db_url):
    """Check if the database is PostgreSQL."""
    return db_url.startswith("postgresql://")

def add_personas_sqlite(db_path):
    """Add personas to SQLite database."""
    # Extract the path from sqlite:///./path.db format
    if db_path.startswith("sqlite:///"):
        db_path = db_path[10:]
    
    logger.info(f"Using SQLite database at {db_path}")
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check if personas table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='personas'")
    if not cursor.fetchone():
        logger.info("Creating personas table...")
        cursor.execute("""
        CREATE TABLE personas (
            id VARCHAR(50) PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            industry VARCHAR(50),
            skills JSON,
            rating FLOAT DEFAULT 4.5,
            review_count INTEGER DEFAULT 0,
            image_url VARCHAR(255) DEFAULT '/placeholder.svg',
            price FLOAT DEFAULT 10.0,
            provider VARCHAR(50) DEFAULT 'groq',
            model VARCHAR(100),
            is_active BOOLEAN DEFAULT 1,
            age_restriction INTEGER DEFAULT 0,
            content_filters JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
    
    # Add personas
    now = datetime.now().isoformat()
    for persona in DEFAULT_PERSONAS:
        logger.info(f"Adding persona {persona['id']}...")
        
        # Check if persona already exists
        cursor.execute("SELECT id FROM personas WHERE id = ?", (persona['id'],))
        if cursor.fetchone():
            logger.info(f"Persona {persona['id']} already exists, updating...")
            cursor.execute("""
            UPDATE personas SET
                name = ?,
                description = ?,
                industry = ?,
                skills = ?,
                rating = ?,
                review_count = ?,
                image_url = ?,
                price = ?,
                provider = ?,
                model = ?,
                is_active = ?,
                age_restriction = ?,
                updated_at = ?
            WHERE id = ?
            """, (
                persona['name'],
                persona['description'],
                persona['industry'],
                json.dumps(persona['skills']),
                persona['rating'],
                persona['review_count'],
                persona['image_url'],
                persona['price'],
                persona['provider'],
                persona['model'],
                1 if persona['is_active'] else 0,
                persona['age_restriction'],
                now,
                persona['id']
            ))
        else:
            cursor.execute("""
            INSERT INTO personas (
                id, name, description, industry, skills, rating, review_count,
                image_url, price, provider, model, is_active, age_restriction,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                persona['id'],
                persona['name'],
                persona['description'],
                persona['industry'],
                json.dumps(persona['skills']),
                persona['rating'],
                persona['review_count'],
                persona['image_url'],
                persona['price'],
                persona['provider'],
                persona['model'],
                1 if persona['is_active'] else 0,
                persona['age_restriction'],
                now,
                now
            ))
    
    # Commit and close
    conn.commit()
    conn.close()
    logger.info("Successfully added personas to SQLite database.")

def add_personas_postgres(db_url):
    """Add personas to PostgreSQL database."""
    logger.info(f"Using PostgreSQL database at {db_url}")
    
    # Connect to the database
    conn = psycopg2.connect(db_url)
    cursor = conn.cursor()
    
    # Check if personas table exists
    cursor.execute("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'personas')")
    if not cursor.fetchone()[0]:
        logger.info("Creating personas table...")
        cursor.execute("""
        CREATE TABLE personas (
            id VARCHAR(50) PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            industry VARCHAR(50),
            skills JSONB,
            rating FLOAT DEFAULT 4.5,
            review_count INTEGER DEFAULT 0,
            image_url VARCHAR(255) DEFAULT '/placeholder.svg',
            price FLOAT DEFAULT 10.0,
            provider VARCHAR(50) DEFAULT 'groq',
            model VARCHAR(100),
            is_active BOOLEAN DEFAULT TRUE,
            age_restriction INTEGER DEFAULT 0,
            content_filters JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
        """)
    
    # Add personas
    now = datetime.now().isoformat()
    for persona in DEFAULT_PERSONAS:
        logger.info(f"Adding persona {persona['id']}...")
        
        # Check if persona already exists
        cursor.execute("SELECT id FROM personas WHERE id = %s", (persona['id'],))
        if cursor.fetchone():
            logger.info(f"Persona {persona['id']} already exists, updating...")
            cursor.execute("""
            UPDATE personas SET
                name = %s,
                description = %s,
                industry = %s,
                skills = %s,
                rating = %s,
                review_count = %s,
                image_url = %s,
                price = %s,
                provider = %s,
                model = %s,
                is_active = %s,
                age_restriction = %s,
                updated_at = %s
            WHERE id = %s
            """, (
                persona['name'],
                persona['description'],
                persona['industry'],
                json.dumps(persona['skills']),
                persona['rating'],
                persona['review_count'],
                persona['image_url'],
                persona['price'],
                persona['provider'],
                persona['model'],
                persona['is_active'],
                persona['age_restriction'],
                now,
                persona['id']
            ))
        else:
            cursor.execute("""
            INSERT INTO personas (
                id, name, description, industry, skills, rating, review_count,
                image_url, price, provider, model, is_active, age_restriction,
                created_at, updated_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                persona['id'],
                persona['name'],
                persona['description'],
                persona['industry'],
                json.dumps(persona['skills']),
                persona['rating'],
                persona['review_count'],
                persona['image_url'],
                persona['price'],
                persona['provider'],
                persona['model'],
                persona['is_active'],
                persona['age_restriction'],
                now,
                now
            ))
    
    # Commit and close
    conn.commit()
    conn.close()
    logger.info("Successfully added personas to PostgreSQL database.")

def main():
    """Main function to add personas to the database."""
    try:
        # Get database URL
        db_url = get_database_url()
        logger.info(f"Database URL: {db_url}")
        
        # Add personas based on database type
        if is_postgres(db_url):
            add_personas_postgres(db_url)
        else:
            add_personas_sqlite(db_url)
        
        logger.info("Successfully added personas to the database.")
    except Exception as e:
        logger.error(f"Error adding personas: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
