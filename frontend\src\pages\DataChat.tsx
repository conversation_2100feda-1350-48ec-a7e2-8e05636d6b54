import { useState, useRef, useEffect, useMemo } from "react";
import React from 'react'; // Import React for JSX
import { motion, AnimatePresence } from "framer-motion";
import { DashboardLayout } from "@/components/DashboardLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card"; // Added CardContent
import { Input } from "@/components/ui/input";
import { MessageCircle, Send, BarChart4, Mic, MicOff, Upload, Loader2, Menu, X, Database, Link, User, ShoppingCart, FileText, RefreshCw, Settings, Info, ArrowLeft, Compass, ChevronLeft, ChevronRight, Paperclip } from "lucide-react"; // Added Paperclip, ChevronLeft, ChevronRight
import { useToast } from "@/hooks/use-toast";
import { useChat } from "@/hooks/use-chat"; // Removed Message type import from here
import { useProviderAvailability } from "@/hooks/use-provider-availability";
import { persona<PERSON>pi, chat<PERSON>pi, file<PERSON><PERSON>, <PERSON><PERSON>, Message } from "@/lib/api"; // Added Message type import from here
import { aiPersonas } from "@/data/aiPersonas";
import { generateWelcomeMessageId } from "@/utils/idUtils";
import { createReactSyncApp } from "@/utils/createReactSyncApp";
import { ConversationList } from "@/components/chat/ConversationList";
import { DataSourceSelector } from "@/components/chat/DataSourceSelector";
import { PersonaSelector } from "@/components/chat/PersonaSelector";
import { SpecializedComponentRenderer } from "@/components/chat/SpecializedComponentRenderer";
import { VisualizationRenderer } from "@/components/visualizations/VisualizationRenderer";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DataSource } from "@/lib/dataSourceApi";
import { useNavigate } from "react-router-dom";
import { ClassificationConfig } from "@/components/classification/ClassificationConfigForm";
import { ClassificationResult } from "@/components/classification/ClassificationResultsVisualization";
import { MarketingContentForm, MarketingContentFormData } from "@/components/marketing/MarketingContentForm"; // Import the component too
import { useProviders } from '@/hooks/useProviders'; // Import useProviders
import { WorkflowStageIndicator, WorkflowStage } from "@/components/chat/WorkflowStageIndicator";
import { WorkflowVisualization, createWorkflowSteps } from "@/components/chat/WorkflowVisualization"; // Added WorkflowVisualization and createWorkflowSteps
import { ReturnToConcierge } from "@/components/chat/ReturnToConcierge";
import { ConciergePanel } from "@/components/chat/ConciergePanel";
import { ConciergeHelpModal } from "@/components/chat/ConciergeHelpModal";
import { ConciergeOnboarding } from "@/components/chat/ConciergeOnboarding";
import { useConcierge, extractConciergeState } from "@/contexts/ConciergeContext";

// Convert backend message to frontend format
const convertMessage = (message: any) => {
  let visualization = null;

  // Check if message has visualization data in metadata
  if (message.metadata) {
    if (message.metadata.visualization === "chart") {
      visualization = "chart";
    } else if (message.metadata.visualization === "table") {
      visualization = "table";
    }

    // If metadata contains sample_results, it's a classification result
    if (message.metadata.sample_results) {
      visualization = "classification";
    }

    // If metadata contains generated_content and task_type, it's a marketing content result
    if (message.metadata.generated_content && message.metadata.task_type) {
      visualization = "marketing";
    }
  }

  return {
    id: message.id,
    content: message.content,
    sender: message.sender,
    timestamp: new Date(message.created_at),
    visualization,
    metadata: message.metadata,
    // Add missing required fields from the original message
    conversation_id: message.conversation_id,
    created_at: message.created_at,
  };
};

type MessageType = {
  id: string;
  content: string;
  sender: "user" | "ai";
  timestamp: Date;
  visualization?: "chart" | "table" | "classification" | "marketing" | null; // Added classification and marketing
  metadata?: any;
  attachment?: {
    name: string;
    type: string;
    size: number;
  } | null; // Added for file attachments
  // Align with Message type from @/lib/api
  conversation_id: string; // Make required
  created_at: string; // Should be string based on use-chat usage
};

// No longer need assertMessageTypeCompatible as types should align now

const DataChat = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [inputMessage, setInputMessage] = useState(""); // Renamed state variable
  const [attachedFile, setAttachedFile] = useState<File | null>(null); // State for the attached file
  const [isRecording, setIsRecording] = useState(false);
  const [selectedPersona, setSelectedPersona] = useState<Persona | null>(null);
  const [availablePersonas, setAvailablePersonas] = useState<Persona[]>([]);
  const [isLoadingPersonas, setIsLoadingPersonas] = useState(false);
  const [conversationsModalOpen, setConversationsModalOpen] = useState(false);
  const [dataSourceModalOpen, setDataSourceModalOpen] = useState(false);
  const [personaModalOpen, setPersonaModalOpen] = useState(false);
  const [selectedDataSource, setSelectedDataSource] = useState<DataSource | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const creationAttemptedForPersonaRef = useRef<string | null>(null); // Ref to track creation attempt
  const fileInputRef = useRef<HTMLInputElement>(null); // Ref for file input


  // Specialized component states
  const [showSpecializedComponent, setShowSpecializedComponent] = useState(false);
  const [isProcessingSpecializedRequest, setIsProcessingSpecializedRequest] = useState(false);

  // Classification states
  const [classificationConfig, setClassificationConfig] = useState<ClassificationConfig | null>(null);
  const [classificationResults, setClassificationResults] = useState<ClassificationResult[]>([]);

  // Marketing content states
  const [generatedMarketingContent, setGeneratedMarketingContent] = useState("");
  const [showMarketingForm, setShowMarketingForm] = useState(false); // State for marketing form visibility
  const [marketingFormDefaultValues, setMarketingFormDefaultValues] = useState<Partial<MarketingContentFormData>>({});
  const [shouldExtractFormData, setShouldExtractFormData] = useState(false); // Flag to trigger form data extraction
  const { providers } = useProviders(); // Get providers

  // Concierge-related states
  const {
    isConciergeActive,
    conciergeState,
    setConciergeActive,
    updateConciergeState,
    resetConciergeState,
    returnToConcierge,
    setOnReturnToConcierge
  } = useConcierge();
  const [showConciergePanel, setShowConciergePanel] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);

  // Create workflow steps for visualization
  const workflowSteps = useMemo(() => {
    console.log("DataChat: conciergeState inside workflowSteps useMemo:", conciergeState); // Added log for debugging
    if (!conciergeState) return [];
    return createWorkflowSteps(conciergeState);
  }, [conciergeState]);

  // We'll fetch models from the API instead of using mock data

  // Use our custom chat hook
  const {
    messages: chatMessages,
    isLoading,
    isTyping,
    connectionStatus,
    messageDeliveryStatus,
    sendMessage,
    createConversation,
    loadConversation,
    refreshConversation: hookRefreshConversation,
    isAutoRefreshing,
  } = useChat();

  // State for manual refresh loading
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Function to manually refresh the conversation
  const refreshConversation = async () => {
    if (!chatMessages.length) return;

    setIsRefreshing(true);
    try {
      await hookRefreshConversation();
    } catch (error) {
      console.error("Failed to refresh conversation:", error);
      toast({
        title: "Refresh Failed",
        description: "Could not refresh the conversation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Convert backend messages to frontend format with deduplication
  const messages: MessageType[] = useMemo(() => {
    if (chatMessages.length === 0) {
      return [];
    }
    const seenMessageIds = new Set<string>();
    const uniqueMessages: MessageType[] = [];
    chatMessages.forEach(message => {
      if (!seenMessageIds.has(message.id)) {
        seenMessageIds.add(message.id);
        uniqueMessages.push(convertMessage(message));
      } else {
        console.log(`Skipping duplicate message with ID ${message.id}`);
      }
    });
    console.log(`Processed ${chatMessages.length} messages, ${uniqueMessages.length} unique messages`);
    // Ensure messages are sorted by timestamp
    uniqueMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    return uniqueMessages;
  }, [chatMessages]);

  // Check if there are any available personas
  const hasAvailablePersonas = availablePersonas.some(p => p.isAvailable);

  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Get provider availability
  const { combinedAvailability, isLoading: isLoadingProviders } = useProviderAvailability();

  // Load available personas from the backend
  useEffect(() => {
    const loadPersonas = async () => {
      setIsLoadingPersonas(true);
      try {
        const response = await personaApi.getPersonas();
        let purchasedPersonas: string[] = [];
        try {
          purchasedPersonas = await personaApi.getPurchasedPersonas();
        } catch (purchaseError) { console.error('Error fetching purchased personas:', purchaseError); }

        const updatedPersonas = await Promise.all(response.personas.map(async persona => {
          let requiredProvider = "groq"; // Default
          // Add specific provider logic if needed based on persona.id
          const isProviderAvailable = combinedAvailability[requiredProvider] || false;
          const isPurchased = Array.isArray(purchasedPersonas) && purchasedPersonas.includes(persona.id);
          return { ...persona, isAvailable: isProviderAvailable && isPurchased, isPurchased };
        }));

        setAvailablePersonas(updatedPersonas);

        // Default selection logic - prioritize concierge agent
        const conciergePersona = updatedPersonas.find(p => p.id === 'concierge-agent' && p.isAvailable);
        if (conciergePersona) {
          // Set concierge as the default first agent
          setSelectedPersona(conciergePersona);
        } else {
          // Fall back to any available persona if concierge is not available
          const firstAvailable = updatedPersonas.find(p => p.isAvailable);
          if (firstAvailable) {
            setSelectedPersona(firstAvailable);
          } else if (updatedPersonas.length > 0 && !updatedPersonas.some(p => p.isAvailable)) {
            setSelectedPersona(null);
            toast({ title: "No Available Personas", description: "Purchase personas from the marketplace.", variant: "destructive" });
          }
        }

      } catch (error) {
        console.error("Failed to load personas:", error);
        toast({ title: "Error", description: "Failed to load AI personas.", variant: "destructive" });
        // Handle fallback if necessary
      } finally {
        setIsLoadingPersonas(false);
      }
    };
    if (!isLoadingProviders) loadPersonas();
  }, [toast, combinedAvailability, isLoadingProviders]); // Removed navigate dependency

  // Effect for initial conversation creation when a default persona is loaded
  useEffect(() => {
    // Only run if a persona is selected, no messages exist, and creation hasn't been attempted for this persona yet
    if (selectedPersona && messages.length === 0 && !isLoading && creationAttemptedForPersonaRef.current !== selectedPersona.id) {
      console.log(`Initial Load Effect: Conditions met for persona ${selectedPersona.id}. Attempting creation.`);
      creationAttemptedForPersonaRef.current = selectedPersona.id; // Mark attempt

      // Check if this is the concierge persona
      if (selectedPersona.id === 'concierge-agent') {
        setConciergeActive(true);
      } else {
        setConciergeActive(false);
      }

      createConversation(selectedPersona.id, `Conversation with ${selectedPersona.name}`)
        .catch(error => {
          console.error("Failed to create initial conversation:", error);
          // Optionally reset the ref if creation fails, allowing another attempt?
          // creationAttemptedForPersonaRef.current = null;
        });
    }
  // Run only when selectedPersona changes (initially) or if createConversation function reference changes (unlikely but safe)
  // Exclude messages.length and isLoading to prevent re-triggering on message updates or loading states
  }, [selectedPersona, createConversation, setConciergeActive]);

  // Effect to update concierge state based on messages
  useEffect(() => {
    if (isConciergeActive && messages.length > 0) {
      // Extract concierge state from messages
      const extractedState = extractConciergeState(messages);
      if (extractedState) {
        updateConciergeState(extractedState);
      }
    }
  }, [messages, isConciergeActive, updateConciergeState]);

  // Effect to show onboarding when concierge is activated
  useEffect(() => {
    if (isConciergeActive && conciergeState) {
      // Show onboarding after a short delay
      const timer = setTimeout(() => {
        setShowOnboarding(true);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isConciergeActive, conciergeState]);


  const handleSendMessage = async () => {
    if (!inputMessage.trim() && !attachedFile) return;

    let messageContent = inputMessage.trim();
    const context: any = selectedDataSource ? { data_source: { id: selectedDataSource.id, name: selectedDataSource.name, type: selectedDataSource.type } } : {};

    if (attachedFile) {
      try {
        // Upload the file first
        toast({
          title: "Uploading File",
          description: `Uploading ${attachedFile.name}...`,
        });

        const uploadedFile = await fileApi.uploadFile(attachedFile);

        // Create a data_source object similar to the "Attach Data" method
        context.data_source = {
          id: uploadedFile.id,
          name: uploadedFile.filename,
          type: "file",
          source_metadata: {
            file_id: uploadedFile.id,
            file_name: uploadedFile.filename,
            file_size: uploadedFile.file_size,
            num_rows: uploadedFile.num_rows,
            columns: uploadedFile.columns
          },
          metadata: {
            user_id: uploadedFile.user_id,
            upload_date: uploadedFile.created_at,
            updated_at: uploadedFile.updated_at
          }
        };

        // Update message content to indicate file attachment
        if (!messageContent) {
          messageContent = `I've attached the file "${attachedFile.name}". Please analyze this data.`;
        } else {
          messageContent = `${messageContent}\n\n(Attached file: ${attachedFile.name})`;
        }

        toast({
          title: "File Uploaded",
          description: `${attachedFile.name} uploaded successfully.`,
        });
      } catch (error) {
        console.error("Error uploading file:", error);
        toast({
          title: "Upload Failed",
          description: `Failed to upload ${attachedFile.name}. Please try again.`,
          variant: "destructive",
        });
        return; // Don't send the message if upload failed
      }
    }

    sendMessage(messageContent, Object.keys(context).length > 0 ? context : undefined);
    setInputMessage("");
    setAttachedFile(null); // Clear the attached file
    if (fileInputRef.current) {
      fileInputRef.current.value = ""; // Reset file input
    }
  };

  const handleAttachmentClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelected = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setAttachedFile(file);
      toast({
        title: "File Ready to Attach",
        description: `${file.name} will be sent with your next message.`,
      });
      // Don't reset fileInputRef.current.value here, so the user sees the selected file
    }
  };

  const toggleRecording = () => {
    setIsRecording(!isRecording);
    if (!isRecording) {
      toast({ title: "Voice recording started", description: "Speak clearly" });
    } else {
      toast({ title: "Voice recording stopped", description: "Processing..." });
      setTimeout(() => { setInputMessage("What were my top selling products last month?"); }, 1500); // Use renamed state variable
    }
  };

  const handleSelectConversation = async (conversationId: string) => {
    try {
      await loadConversation(conversationId);
      setConversationsModalOpen(false);
    } catch (error) {
      console.error("Failed to load conversation:", error);
      toast({ title: "Error", description: "Failed to load conversation.", variant: "destructive" });
    }
  };

  const handleSelectDataSource = (dataSource: DataSource) => {
    setSelectedDataSource(dataSource);
    setDataSourceModalOpen(false);
    toast({
      title: "Data Source Attached",
      description: `"${dataSource.name}" attached. The AI will analyze this data.`
    });

    // Send a message to the AI persona with the file data
    if (dataSource.type === 'file' && chatMessages.length > 0) {
      // Create a context object with the data source and a special flag to trigger file processing
      const context = {
        data_source: {
          id: dataSource.id,
          name: dataSource.name,
          type: dataSource.type,
          source_metadata: dataSource.source_metadata,
          metadata: dataSource.metadata
        },
        send_file_to_persona: true  // Special flag to indicate file should be sent to persona
      };

      // Send a message to the AI persona
      sendMessage(`I've attached the file "${dataSource.name}". Please analyze this data.`, context);
    }
  };

  const handleSelectPersona = (persona: Persona) => {
    if (!persona.isAvailable) {
      if (persona.isPurchased) {
        toast({ title: "Provider Not Available", description: `Provider for "${persona.name}" unavailable.`, variant: "destructive" });
      }
      return;
    }

    // Check if this is the concierge persona
    const isConcierge = persona.id === 'concierge-agent';

    if (selectedPersona && selectedPersona.id !== persona.id) {
      if (messages.length > 0 && !window.confirm(`Changing persona starts a new conversation. Continue?`)) {
        return;
      }
      // Reset state for the new persona
      creationAttemptedForPersonaRef.current = null;
      setSelectedPersona(persona);
      loadConversation(""); // Clear messages and WS connection via useChat hook
      setShowSpecializedComponent(false);

      // Update concierge state
      if (isConcierge) {
        setConciergeActive(true);
        resetConciergeState();
      } else {
        setConciergeActive(false);
      }

      // Explicitly create the new conversation *after* state updates
      console.log(`handleSelectPersona: Explicitly creating conversation for new persona ${persona.id}`);
      createConversation(persona.id, `Conversation with ${persona.name}`)
        .catch(error => console.error("Failed to create conversation from handleSelectPersona:", error));

    } else if (!selectedPersona) {
      // This is the initial selection when the component loads
      creationAttemptedForPersonaRef.current = null; // Ensure it's reset if no persona was selected before
      setSelectedPersona(persona);

      // Update concierge state
      if (isConcierge) {
        setConciergeActive(true);
        resetConciergeState();
      } else {
        setConciergeActive(false);
      }
    }
    setPersonaModalOpen(false);
    toast({ title: "AI Persona Selected", description: `"${persona.name}" will assist.` });
  };

  // Function to handle returning to the concierge
  const handleReturnToConcierge = () => {
    // Find the concierge persona
    const conciergePersona = availablePersonas.find(p => p.id === 'concierge-agent');
    if (conciergePersona && conciergePersona.isAvailable) {
      handleSelectPersona(conciergePersona);
    } else {
      toast({
        title: "Concierge Not Available",
        description: "The concierge persona is not available. Please purchase it from the marketplace.",
        variant: "destructive"
      });
    }
  };

  // Set the return to concierge callback
  useEffect(() => {
    setOnReturnToConcierge(handleReturnToConcierge);
    return () => setOnReturnToConcierge(undefined);
  }, [setOnReturnToConcierge, availablePersonas]);

  // Function to extract marketing form data from messages
  const extractMarketingFormData = () => {
    console.log("Extracting marketing form data from messages");
    let foundFormData = false;

    // Look through messages for form data
    for (const msg of messages) {
      if (msg.metadata) {
        // Try to extract marketing form data from the message metadata
        let formData = null;

        if (msg.metadata.marketing_form_data) {
          formData = msg.metadata.marketing_form_data;
          foundFormData = true;
        } else if (msg.metadata.context && msg.metadata.context.marketing_form_data) {
          formData = msg.metadata.context.marketing_form_data;
          foundFormData = true;
        }

        // If we found form data, store it in state
        if (formData) {
          console.log("Found marketing form data in messages:", formData);
          setMarketingFormDefaultValues(formData);
          break;
        }
      }
    }

    // If no form data was found in messages, try to get it from localStorage
    if (!foundFormData) {
      try {
        const storedFormData = localStorage.getItem('lastMarketingFormData');
        if (storedFormData) {
          const formData = JSON.parse(storedFormData);
          console.log("Using marketing form data from localStorage:", formData);
          setMarketingFormDefaultValues(formData);
        }
      } catch (e) {
        console.error("Error parsing stored form data:", e);
      }
    }

    // Reset the flag
    setShouldExtractFormData(false);
  };

  // Effect to extract marketing form data when needed
  useEffect(() => {
    if (shouldExtractFormData) {
      extractMarketingFormData();
    }
  }, [shouldExtractFormData, messages]);

  // Classification handler (still mock)
  const handleSubmitClassification = async (config: ClassificationConfig) => { console.log("Classification submitted:", config); };

  // Marketing content handler - real implementation
  const handleSubmitMarketingContent = async (data: MarketingContentFormData) => {
    console.log("Marketing content submitted:", data);

    // Set loading state
    setIsProcessingSpecializedRequest(true);

    try {
      // Get the current conversation ID
      const currentConversationId = chatMessages[0]?.conversation_id;
      if (!currentConversationId) {
        toast({
          title: "Error",
          description: "No active conversation found. Please start a conversation first.",
          variant: "destructive",
        });
        return;
      }

      // Prepare context with data source if available
      const context = {
        marketing_form_data: data,
        // Include provider and model explicitly at the top level
        provider: data.provider,
        model: data.model,
        data_source: selectedDataSource ? {
          id: selectedDataSource.id,
          name: selectedDataSource.name,
          type: selectedDataSource.type
        } : undefined
      };

      // Send a message to trigger the marketing content generation
      await sendMessage(`Generate ${data.content_type} content`, context);

      // Close the marketing form after submission
      setShowMarketingForm(false);

      // Show a toast notification
      toast({
        title: "Content Generation Started",
        description: "Your marketing content is being generated. Please wait a moment.",
      });

      // Store the form data in local storage for potential regeneration
      localStorage.setItem('lastMarketingFormData', JSON.stringify(data));

      // Store the form data in state for pre-filling the form when opened again
      setMarketingFormDefaultValues(data);
    } catch (error) {
      console.error("Error generating marketing content:", error);
      toast({
        title: "Error",
        description: "Failed to generate marketing content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessingSpecializedRequest(false);
    }
  };

  const toggleSpecializedComponent = () => { setShowSpecializedComponent(!showSpecializedComponent); };
  const generateMockClassificationResults = (type: 'llm' | 'huggingface'): ClassificationResult[] => { return []; };
  const generateMockMarketingContent = (contentType: string): string => { return `Mock content for ${contentType}`; };

  const renderVisualization = (msg: MessageType) => {
    if (!msg.metadata) return null;

    // Log the metadata for debugging
    console.log('Message metadata:', msg.metadata);

    // Check for image content in the message
    const hasImageContent = msg.metadata.content &&
                           Array.isArray(msg.metadata.content) &&
                           msg.metadata.content.some(item => item.type === 'image' && item.src);

    if (hasImageContent) {
      console.log('Found image content in message');
    }

    // Function to handle regeneration of content
    const handleRetry = async () => {
      // For marketing content, we need to regenerate using the same parameters
      if (msg.metadata?.generated_content && msg.metadata?.task_type) {
        try {
          // Extract the original form data
          // First check if it's directly in the metadata
          let formData = {};

          if (msg.metadata.marketing_form_data) {
            console.log("Using marketing_form_data from metadata", msg.metadata.marketing_form_data);
            formData = msg.metadata.marketing_form_data;
          } else if (msg.metadata.context && msg.metadata.context.marketing_form_data) {
            console.log("Using marketing_form_data from context", msg.metadata.context.marketing_form_data);
            formData = msg.metadata.context.marketing_form_data;
          } else {
            // Try to get the form data from local storage as a fallback
            try {
              const storedFormData = localStorage.getItem('lastMarketingFormData');
              if (storedFormData) {
                formData = JSON.parse(storedFormData);
                console.log("Using marketing_form_data from localStorage", formData);
              } else {
                console.log("No marketing_form_data found in localStorage, using empty object");
              }
            } catch (e) {
              console.error("Error parsing stored form data:", e);
              console.log("No marketing_form_data found, using empty object");
            }
          } // Moved the closing brace for the else block here

          // Ensure we have the content_type in the form data, checking if formData is not empty
          // Moved this check inside the try block
          if (typeof formData === 'object' && formData !== null && !('content_type' in formData) && msg.metadata.task_type) {
            (formData as any).content_type = msg.metadata.task_type;
          }

          // Get provider and model from the most reliable source
          const provider = msg.metadata.provider ||
                          (formData as any).provider ||
                          (msg.metadata.context && msg.metadata.context.provider);

          const model = msg.metadata.model ||
                       (formData as any).model ||
                       (msg.metadata.context && msg.metadata.context.model);

          // Prepare context with the same parameters
          const context = {
            marketing_form_data: formData,
            provider: provider,
            model: model,
            data_source: selectedDataSource ? {
              id: selectedDataSource.id,
              name: selectedDataSource.name,
              type: selectedDataSource.type
            } : undefined,
            is_regeneration: true // Flag to indicate this is a regeneration request
          };

          console.log("Regenerating content with context:", context);

          // Send a message to trigger regeneration
          await sendMessage(`Regenerate ${msg.metadata.task_type} content`, context);

          toast({
            title: "Regenerating Content",
            description: "Your request to regenerate content has been sent.",
          });
        } catch (error) {
          console.error("Error regenerating content:", error);
          toast({
            title: "Error",
            description: "Failed to regenerate content. Please try again.",
            variant: "destructive",
          });
        }
      }
    };

    // For marketing content, ensure we pass the response in the metadata
    if (msg.metadata.generated_content && msg.metadata.task_type) {
      // Make sure the response is included in the metadata for the visualization
      const enhancedMetadata = {
        ...msg.metadata,
        response: msg.metadata.response || msg.content
      };
      return <VisualizationRenderer metadata={enhancedMetadata} onRetry={handleRetry} />;
    }

    // For data preview/profile, ensure we pass the metadata properly
    if (msg.metadata.data_preview || msg.metadata.data_profile) {
      return <VisualizationRenderer metadata={msg.metadata} />;
    }

    // For query results, ensure we pass the metadata properly
    if (msg.metadata.query_result) {
      return <VisualizationRenderer metadata={msg.metadata} />;
    }

    // For visualization results, ensure we pass the metadata properly
    if (msg.metadata.visualization) {
      console.log('Found visualization in metadata:', msg.metadata.visualization);
      return <VisualizationRenderer metadata={msg.metadata} />;
    }

    // For messages with image content, create a visualization
    if (hasImageContent) {
      // Find the image content
      const imageContent = msg.metadata.content.find(item => item.type === 'image' && item.src);

      // Create a visualization object
      const visualizationMetadata = {
        ...msg.metadata,
        visualization: {
          type: 'chart',
          title: msg.metadata.prompt || 'Visualization',
          description: 'Generated visualization',
          data: {
            image: imageContent.src
          }
        }
      };

      return <VisualizationRenderer metadata={visualizationMetadata} />;
    }

    // For other visualizations, pass metadata as is
    return <VisualizationRenderer metadata={msg.metadata} />;
  };

  // Function to parse message content and render text/buttons, handling line breaks
  const renderMessageContent = (content: string): React.ReactNode[] => {
    // Split the original content by newlines first
    const lines = content.split('\n');
    const renderedLines: React.ReactNode[] = [];

    lines.forEach((line, lineIndex) => {
        const lineParts: React.ReactNode[] = [];
        let currentLineIndex = 0;
        const lineRegex = /\[([^\]]+)\]\(action:([^)]+)\)/g; // Need to re-apply regex per line
        let lineMatch;

        while ((lineMatch = lineRegex.exec(line)) !== null) {
             if (lineMatch.index > currentLineIndex) {
                 lineParts.push(<span key={`line-${lineIndex}-text-${currentLineIndex}`}>{line.substring(currentLineIndex, lineMatch.index)}</span>);
             }
             const buttonText = lineMatch[1];
             const actionKey = lineMatch[2];
             lineParts.push(
                <Button
                  key={`line-${lineIndex}-action-${lineMatch.index}-${actionKey}`}
                  variant="outline"
                  size="sm"
                  className="mx-1 my-0.5 h-auto py-1 px-2 text-sm whitespace-normal text-left inline-block align-middle" // Added align-middle
                  onClick={() => {
                    const finalActionKey = actionKey.trim().toLowerCase(); // Trim and convert to lower case for comparison
                    console.log(`Action button clicked (line loop): Original Key='${actionKey}', Processed Key='${finalActionKey}'`); // Updated log
                    // Define marketing action keys that should open the form (Updated based on observed behavior)
                    const marketingActionKeys = [
                      'marketing_strategy', // Added based on screenshot
                      'create_marketing_strategy',
                      'develop_campaign_plan', // Assuming this might be 'campaign_plan'
                      'campaign_plan',         // Added assumption
                      'generate_social_media_content', // Assuming this might be 'social_media_content'
                      'social_media_content',        // Added assumption
                      'craft_seo_optimized_content', // Assuming this might be 'seo_content'
                      'seo_content',                 // Added assumption
                      'initiate_marketing_content'
                    ];

                    // Determine if the form should be opened
                    let shouldOpenForm = false;
                    const seoComparisonResult = finalActionKey === 'seo_optimization'; // Direct comparison for SEO
                    console.log(`Comparing '${finalActionKey}' === 'seo_optimization': ${seoComparisonResult}`); // Log comparison result

                    // Check only the comparison result for seo_optimization for debugging
                    if (seoComparisonResult) {
                       shouldOpenForm = true;
                    } else {
                       // Check other keys only if seo_optimization didn't match
                       if (finalActionKey === 'marketing_strategy' ||
                           finalActionKey === 'campaign_strategy' ||
                           finalActionKey === 'social_media_content' ||
                           finalActionKey === 'initiate_marketing_content') {
                          shouldOpenForm = true;
                       }
                    }

                    // Act based on the determination
                    if (shouldOpenForm) {
                      console.log(`Opening marketing form for key: '${finalActionKey}'`);

                      // Set the flag to trigger form data extraction in the useEffect hook
                      setShouldExtractFormData(true);
                      setShowMarketingForm(true);
                      setShowSpecializedComponent(false);
                    } else {
                      console.log(`Sending message for key: '${finalActionKey}'`);
                      // Default behavior: Send the action key as a message
                      sendMessage(actionKey.trim()); // Send the original trimmed key
                    }
                  }}
                >
                  {buttonText}
                </Button>
             );
             currentLineIndex = lineRegex.lastIndex;
        }
         if (currentLineIndex < line.length) {
             lineParts.push(<span key={`line-${lineIndex}-text-${currentLineIndex}`}>{line.substring(currentLineIndex)}</span>);
         }

         // Use a div for each line to preserve line breaks, add margin between lines
         if (lineParts.length > 0 || line.trim() !== '') { // Avoid adding empty divs for consecutive newlines
             renderedLines.push(<div key={`line-${lineIndex}`} className="mb-1">{lineParts}</div>);
         }
    });


    return renderedLines; // Return array of line divs
  };

  // Calculate the effective dataSourceId directly before rendering the form section
  let effectiveDataSourceId: string | undefined = undefined;
  if (selectedDataSource) {
    if (selectedDataSource.type === "file") {
      // First try to get file_id from source_metadata (correct location based on model)
      if (selectedDataSource.source_metadata?.file_id) {
        effectiveDataSourceId = selectedDataSource.source_metadata.file_id;
      }
      // Try to get file_id directly from the data source ID for the attached file
      else if (selectedDataSource.id) {
        // For file data sources, the ID might be the file ID itself
        effectiveDataSourceId = selectedDataSource.id;
      }
      // Fallback to old method using metadata.file_path
      else {
        const filePath = selectedDataSource.metadata?.file_path;
        if (typeof filePath === 'string') {
          const parts = filePath.split('/');
          const filename = parts.pop(); // Get last part (filename)
          if (filename) {
            effectiveDataSourceId = filename.split('.')[0]; // Get part before extension
          }
        }
      }

      // Add a warning if the ID is still missing
      if (!effectiveDataSourceId) {
         console.warn("Could not extract file ID from data source");
      }
    } else {
      // For non-file data sources, use the data source ID directly
      effectiveDataSourceId = selectedDataSource.id;
    }
  }


  return (
    <DashboardLayout>
      {/* Modals */}
      <Dialog open={conversationsModalOpen} onOpenChange={setConversationsModalOpen}><DialogContent><DialogHeader><DialogTitle>Conversations</DialogTitle></DialogHeader><ConversationList onSelectConversation={handleSelectConversation} currentConversationId={chatMessages[0]?.conversation_id} /></DialogContent></Dialog>
      <Dialog open={dataSourceModalOpen} onOpenChange={setDataSourceModalOpen}><DialogContent><DialogHeader><DialogTitle>Attach Data</DialogTitle></DialogHeader><DataSourceSelector onSelectDataSource={handleSelectDataSource} currentDataSourceId={selectedDataSource?.id} /></DialogContent></Dialog>
      <Dialog open={personaModalOpen} onOpenChange={setPersonaModalOpen}><DialogContent><DialogHeader><DialogTitle>Select Persona</DialogTitle></DialogHeader><PersonaSelector onSelectPersona={handleSelectPersona} currentPersonaId={selectedPersona?.id} availablePersonas={availablePersonas.filter(p => p.isPurchased)} isLoading={isLoadingPersonas} onGoToMarketplace={() => { setPersonaModalOpen(false); navigate("/ai-marketplace"); }} /></DialogContent></Dialog>

      <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }} className="h-full relative">
        {/* Concierge Onboarding */}
        <ConciergeOnboarding
          isActive={isConciergeActive && showOnboarding}
          onDismiss={() => setShowOnboarding(false)}
        />
        <div className="flex-1">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            {/* Left Header */}
            <div className="flex items-center gap-3">
              <Button variant="ghost" size="icon" onClick={() => setConversationsModalOpen(true)} className="md:hidden"><Menu className="h-5 w-5" /></Button>
              <div>
                <h1 className="text-2xl font-bold">Data Chat</h1>
                <div className="md:hidden flex items-center text-sm text-gray-600 -mt-1 cursor-pointer" onClick={() => setPersonaModalOpen(true)}>
                  <User className="h-3 w-3 mr-1" />
                  {selectedPersona ? <span className="max-w-[150px] truncate">{selectedPersona.name}</span> : <span className="text-brand-500 font-medium">Select Persona</span>}
                </div>
              </div>
            </div>
            {/* Right Header (Desktop) */}
            <div className="hidden md:flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => setPersonaModalOpen(true)} className="flex items-center gap-2"><User className="h-4 w-4" />{selectedPersona ? <span className="flex items-center gap-1"><span className="max-w-[100px] truncate">{selectedPersona.name}</span></span> : "Select Persona"}</Button>
              <Button variant="outline" size="sm" onClick={() => setDataSourceModalOpen(true)} className="flex items-center gap-2"><Database className="h-4 w-4" />{selectedDataSource ? <span className="flex items-center gap-1"><span className="max-w-[100px] truncate">{selectedDataSource.name}</span><X className="h-3 w-3 text-gray-500 hover:text-gray-700" onClick={(e) => { e.stopPropagation(); setSelectedDataSource(null); toast({ title: "Data Source Detached" }); }} /></span> : "Attach Data"}</Button>
              <Button variant="outline" size="sm" onClick={() => setConversationsModalOpen(true)} className="flex items-center gap-2"><MessageCircle className="h-4 w-4" />Conversations</Button>
              {selectedPersona && <Button variant={showSpecializedComponent ? "default" : "outline"} size="sm" onClick={toggleSpecializedComponent} className="flex items-center gap-2"><Settings className="h-4 w-4" />Specialized Tools</Button>}
            </div>
             {/* Right Header (Mobile) */}
             <div className="flex md:hidden items-center gap-1">
                <Button variant="ghost" size="icon" onClick={() => setDataSourceModalOpen(true)} className="h-8 w-8" title="Attach Data"><Database className="h-4 w-4" /></Button>
                <Button variant="ghost" size="icon" onClick={() => setPersonaModalOpen(true)} className="h-8 w-8" title="Select Persona"><User className="h-4 w-4" /></Button>
                {selectedPersona && <Button variant="ghost" size="icon" onClick={toggleSpecializedComponent} className={`h-8 w-8 ${showSpecializedComponent ? 'bg-brand-100' : ''}`} title="Specialized Tools"><Settings className="h-4 w-4" /></Button>}
             </div>
          </div>

          {/* Chat Card */}
          <Card className="flex flex-col h-[calc(100vh-9rem)]">
            {/* Error/Info Banners */}
            {!hasAvailablePersonas && !isLoadingPersonas && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-t-md relative"><div className="flex items-center"><div className="flex-shrink-0"><ShoppingCart className="h-5 w-5 mr-2" /></div><div><p className="font-bold">No Available Personas</p><p className="text-sm">Purchase personas from the marketplace.</p></div><div className="ml-auto"><Button size="sm" onClick={() => navigate("/ai-marketplace")} className="bg-red-600 hover:bg-red-700 text-white">Go to Marketplace</Button></div></div></div>}
            {selectedDataSource && selectedDataSource.type === "file" && <div className="bg-blue-50 border-b border-blue-200 p-3 flex items-center"><FileText className="h-5 w-5 text-blue-500 mr-2" /><div className="flex-1"><p className="text-sm font-medium text-blue-700">File attached: <span className="font-semibold">{selectedDataSource.name}</span></p></div><Button variant="ghost" size="sm" className="text-blue-700 hover:text-blue-900 hover:bg-blue-100" onClick={() => { setSelectedDataSource(null); toast({ title: "File Detached" }); }}><X className="h-4 w-4" /></Button></div>}

            {/* Concierge Workflow Stage Indicator */}
            {isConciergeActive && conciergeState && (
              <div className="border-b border-gray-200 p-3 relative">
                <div className="absolute right-3 top-3">
                  <ConciergeHelpModal />
                </div>
                <WorkflowStageIndicator
                  currentStage={conciergeState.stage as WorkflowStage}
                  className="mx-auto max-w-2xl"
                />
              </div>
            )}

            {/* Return to Concierge Button (when not in concierge) */}
            {!isConciergeActive && selectedPersona && selectedPersona.id !== 'concierge-agent' && (
              <div className="border-b border-gray-200 p-2 flex justify-end">
                <ReturnToConcierge onReturn={handleReturnToConcierge} />
              </div>
            )}

            {/* Specialized Component Area - Now includes Marketing Form */}
            {showMarketingForm && (
              <motion.div initial={{ opacity: 0, height: 0 }} animate={{ opacity: 1, height: 'auto' }} exit={{ opacity: 0, height: 0 }} className="border-b p-4">
                {!selectedDataSource && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 text-yellow-500 mr-2">
                        <Info className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="font-medium text-yellow-800">No Data Source Attached</h4>
                        <p className="text-sm text-yellow-700 mt-1">
                          Attach a data source to auto-fill this form with relevant content.
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2 bg-white"
                          onClick={() => setDataSourceModalOpen(true)}
                        >
                          Attach Data Source
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
                {/* Show error only if file_id is missing from the correct location (source_metadata) */}
                {selectedDataSource?.type === "file" &&
                 !selectedDataSource?.source_metadata?.file_id && (
                  <div className="bg-orange-50 border border-orange-200 rounded-md p-4 mb-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 text-orange-500 mr-2">
                        <Info className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="font-medium text-orange-800">Invalid File Reference</h4>
                        <p className="text-sm text-orange-700 mt-1">
                          This data source references a file that doesn't exist. Please attach a different data source.
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2 bg-white"
                          onClick={() => setDataSourceModalOpen(true)}
                        >
                          Choose Different Data Source
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
                {/* Removed logging block from here */}
                <MarketingContentForm
                  onSubmit={handleSubmitMarketingContent} // Reuse existing handler
                  isLoading={isProcessingSpecializedRequest} // Reuse loading state
                  // Pass the calculated ID
                  dataSourceId={effectiveDataSourceId}
                  onClose={() => setShowMarketingForm(false)} // Add close handler
                  defaultValues={marketingFormDefaultValues} // Pass the default values
                />
              </motion.div>
            )}
            {showSpecializedComponent && !showMarketingForm && selectedPersona && ( // Render only if marketing form isn't shown
              <SpecializedComponentRenderer
                persona={selectedPersona}
                onClose={toggleSpecializedComponent}
                onSubmitClassification={handleSubmitClassification}
                onSubmitMarketingContent={handleSubmitMarketingContent} // Keep this? Maybe redundant now.
                classificationResults={classificationResults}
                classificationConfig={classificationConfig}
                generatedMarketingContent={generatedMarketingContent}
                isLoading={isProcessingSpecializedRequest}
              />
            )}

            {/* Messages Area with Concierge Panel */}
            <div className="flex-1 overflow-y-auto p-4 mb-4 relative flex">
              {/* Main Messages Area */}
              <div className="flex-1 relative">
                {/* Refresh Button */}
                {messages.length > 1 && <div className="absolute top-2 right-2 z-10"><Button variant="outline" size="sm" className="bg-white/80 backdrop-blur-sm hover:bg-white" onClick={refreshConversation} disabled={isRefreshing || isLoading || isAutoRefreshing}>{isRefreshing || isAutoRefreshing ? <Loader2 className="h-4 w-4 animate-spin mr-1" /> : <RefreshCw className="h-4 w-4 mr-1" />}{isRefreshing || isAutoRefreshing ? "Refreshing..." : "Refresh"}</Button></div>}
                {/* Initial Loading */}
                {messages.length === 0 && isLoading && <div className="flex flex-col items-center justify-center h-full"><Loader2 className="h-8 w-8 animate-spin text-brand-500 mb-4" /><p className="text-gray-500">Initializing conversation...</p></div>}
                {/* Auto-Refresh Indicator */}
                {isAutoRefreshing && <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md"><div className="flex items-start"><div className="flex-shrink-0 text-blue-500 mr-2"><Loader2 className="h-5 w-5 animate-spin" /></div><div><h4 className="font-medium text-blue-800">Auto-refreshing...</h4></div></div></div>}

                {/* Concierge Workflow Button (only when in concierge mode) */}
                {isConciergeActive && conciergeState && (
                  <div className="absolute top-2 right-2 z-10">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{
                        opacity: 1,
                        y: 0,
                        transition: {
                          type: "spring",
                          stiffness: 500,
                          damping: 30,
                          delay: 0.3
                        }
                      }}
                    >
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-white/80 backdrop-blur-sm hover:bg-white flex items-center gap-1 shadow-sm border-brand-100"
                        onClick={() => setShowConciergePanel(!showConciergePanel)}
                      >
                        <Compass className="h-4 w-4 text-brand-500" />
                        <span className="hidden md:inline">Workflow</span>
                        {showConciergePanel ? (
                          <ChevronLeft className="h-4 w-4 md:hidden" />
                        ) : (
                          <ChevronRight className="h-4 w-4 md:hidden" />
                        )}
                      </Button>
                    </motion.div>
                  </div>
                )}

                {/* Message List */}
                {messages.map((msg, index) => {
                  // Simplified filter: Only hide the generic "Processing..." message if needed
                  if (msg.sender === 'ai' && msg.content === "Processing your request..." && msg.metadata?.status === "processing" && msg.metadata?.processing === true) {
                    console.log(`Filtering out processing message ${msg.id}`);
                    return null;
                  }

                  // Check if this is a marketing content message
                  const isMarketingContent = msg.sender === 'ai' &&
                                            msg.metadata?.generated_content === true &&
                                            msg.metadata?.task_type;

                  const attachment = msg.metadata?.attachment || (msg as any).attachment; // Check both metadata and direct property

                  return (
                    <motion.div key={msg.id || `message-${index}`} initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} className={`mb-4 max-w-3xl ${msg.sender === 'user' ? 'ml-auto' : ''}`}>
                      <div className={`p-3 rounded-lg ${msg.sender === 'user' ? 'bg-brand-500 text-white' : 'bg-gray-100 text-gray-800'}`}>
                        <div className="flex justify-between items-start"> {/* Use items-start */}
                          <div className="flex-1 mr-2 prose prose-sm max-w-none"> {/* Added prose classes */}
                            {isMarketingContent ? (
                              <div className="flex items-center gap-2">
                                <BarChart4 className="h-4 w-4 text-brand-500" />
                                <span>I've generated your {msg.metadata.task_type.replace(/_/g, ' ')} content below.</span>
                              </div>
                            ) : (
                              msg.sender === 'ai' ? renderMessageContent(msg.content) : msg.content
                            )}
                            {attachment && (
                              <div className={`mt-2 p-2 rounded-md flex items-center gap-2 ${msg.sender === 'user' ? 'bg-brand-600' : 'bg-gray-200'}`}>
                                <Paperclip className={`h-4 w-4 ${msg.sender === 'user' ? 'text-white' : 'text-gray-600'}`} />
                                <span className={`text-sm ${msg.sender === 'user' ? 'text-white' : 'text-gray-700'}`}>
                                  {attachment.name} ({Math.round(attachment.size / 1024)} KB)
                                </span>
                              </div>
                            )}
                          </div>
                          {/* Delivery Status */}
                          {msg.sender === 'user' && messageDeliveryStatus[msg.id] && (
                            <div className="flex items-center self-start pt-1"> {/* Align to top */}
                              {messageDeliveryStatus[msg.id] === 'pending' && <span className="text-xs opacity-70">Sending...</span>}
                              {messageDeliveryStatus[msg.id] === 'delivered' && <span className="text-xs opacity-70">✓</span>}
                              {messageDeliveryStatus[msg.id] === 'failed' && <span className="text-xs text-red-300">Failed</span>}
                            </div>
                          )}
                        </div>
                      </div>
                      {renderVisualization(msg)}
                    </motion.div>
                  );
                })}
                {isTyping && <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="flex items-center gap-2 text-gray-500 p-3"><Loader2 className="h-4 w-4 animate-spin" /><span>AI is thinking...</span></motion.div>}
                <div ref={messagesEndRef} />
              </div>

              {/* Concierge Panel - Desktop */}
              <AnimatePresence>
                {isConciergeActive && showConciergePanel && conciergeState && (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{
                      opacity: 1,
                      x: 0,
                      transition: {
                        type: "spring",
                        stiffness: 300,
                        damping: 30
                      }
                    }}
                    exit={{
                      opacity: 0,
                      x: 20,
                      transition: {
                        duration: 0.2
                      }
                    }}
                    className="ml-4 w-80 hidden md:block"
                  >
                    <ConciergePanel />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Concierge Panel - Mobile (Fixed at bottom) */}
              <AnimatePresence>
                {isConciergeActive && showConciergePanel && conciergeState && (
                  <motion.div
                    initial={{ opacity: 0, y: 100 }}
                    animate={{
                      opacity: 1,
                      y: 0,
                      transition: {
                        type: "spring",
                        stiffness: 300,
                        damping: 30
                      }
                    }}
                    exit={{
                      opacity: 0,
                      y: 100,
                      transition: {
                        duration: 0.2
                      }
                    }}
                    className="fixed bottom-20 left-0 right-0 z-50 p-4 md:hidden"
                  >
                    <Card className="shadow-lg border-brand-100 max-h-[60vh] overflow-auto">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-base font-medium flex items-center gap-2 text-brand-700">
                            <Compass className="h-4 w-4 text-brand-500" />
                            Concierge Workflow
                          </h3>
                          <motion.div
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => setShowConciergePanel(false)}
                              className="h-8 w-8 text-gray-500"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </motion.div>
                        </div>
                        <WorkflowVisualization steps={workflowSteps} />
                      </CardContent>
                    </Card>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Input Area */}
            <div className="border-t p-4">
              {attachedFile && (
                <div className="mb-2 p-2 bg-blue-50 border border-blue-200 rounded-md flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Paperclip className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-blue-700">
                      {attachedFile.name} ({Math.round(attachedFile.size / 1024)} KB)
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 text-blue-600 hover:bg-blue-100"
                    onClick={() => {
                      setAttachedFile(null);
                      if (fileInputRef.current) fileInputRef.current.value = "";
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
              <div className="flex gap-2">
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileSelected}
                  className="hidden"
                  accept="*" // Allow all file types
                />
                <Button variant="outline" size="icon" onClick={handleAttachmentClick} disabled={isLoading || connectionStatus === 'connecting' || !hasAvailablePersonas} title="Attach file">
                  <Paperclip className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" className={isRecording ? 'bg-red-100 text-red-500' : ''} onClick={toggleRecording} disabled={isLoading || connectionStatus === 'connecting' || !hasAvailablePersonas} title={isRecording ? "Stop recording" : "Start recording"}>{isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}</Button>
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder={
                    attachedFile
                      ? `Message with ${attachedFile.name}`
                      : !hasAvailablePersonas
                        ? 'Purchase AI personas...'
                        : isLoading
                          ? 'Processing...'
                          : 'Ask about your data...'
                  }
                  className="flex-1"
                  disabled={isLoading || connectionStatus === 'connecting' || !hasAvailablePersonas}
                  onKeyDown={(e) => { if (e.key === 'Enter' && !isLoading && hasAvailablePersonas && (inputMessage.trim() || attachedFile)) { handleSendMessage(); } }}
                />
                <Button onClick={handleSendMessage} disabled={isLoading || connectionStatus === 'connecting' || !hasAvailablePersonas || (!inputMessage.trim() && !attachedFile)}>
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* Connection Status Footer */}
            {connectionStatus !== 'connected' && <div className={`text-xs p-2 text-center ${connectionStatus === 'connecting' ? 'bg-yellow-50 text-yellow-700' : 'bg-red-50 text-red-700'}`}>{connectionStatus === 'connecting' ? 'Connecting...' : 'Disconnected.'}</div>}
          </Card>
        </div>
      </motion.div>
    </DashboardLayout>
  );
};

export default DataChat;
