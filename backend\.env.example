# API Keys for LLM Providers
# Uncomment and add your API keys below

# Groq API Key
# GROQ_API_KEY=gsk_your_groq_api_key

# OpenAI API Key
# OPENAI_API_KEY=sk-your_openai_api_key

# Google Gemini API Key
# GEMINI_API_KEY=your_gemini_api_key

# OpenRouter API Key
# OPENROUTER_API_KEY=your_openrouter_api_key

# mem0ai Configuration
# MEM0_API_KEY=your_mem0ai_api_key
# MEM0_ENDPOINT=https://api.mem0.ai  # Only needed if using a custom endpoint
# MEM0_SELF_HOSTED=false  # Set to true if using self-hosted mem0ai
# MEM0_DEFAULT_TTL=2592000  # 30 days in seconds
# MEM0_MAX_MEMORIES=1000
# MEM0_MEMORY_THRESHOLD=0.7

# Qdrant Configuration
# QDRANT_HOST=localhost  # Use 'qdrant' when running inside Docker
# QDRANT_PORT=6333

# Optional: Override default endpoints if needed
# OLLAMA_ENDPOINT=http://localhost:11434
# GROQ_ENDPOINT=https://api.groq.com/openai/v1
# OPENAI_ENDPOINT=https://api.openai.com/v1
# GEMINI_ENDPOINT=https://generativelanguage.googleapis.com
# OPENROUTER_ENDPOINT=https://openrouter.ai/api/v1

# Database Configuration
# SQLite (default)
# DATABASE_URL=sqlite:///./classyweb.db

# PostgreSQL (for production)
# DATABASE_URL=postgresql://username:password@localhost/classyweb
# For Docker setup:
# DATABASE_URL=*********************************************************/datagenius_db

# MySQL/MariaDB
# DATABASE_URL=mysql+pymysql://username:password@localhost/classyweb

# Debug SQL statements (set to "true" to enable)
# DATABASE_ECHO=false

# Docker PostgreSQL Configuration
# POSTGRES_USER=datagenius
# POSTGRES_PASSWORD=datagenius_password
# POSTGRES_DB=datagenius_db

# Authentication Configuration
# JWT settings (generate a secure random key for production)
# JWT_SECRET_KEY=your-secret-key-for-development-only

# Redis Configuration (for token blacklisting and session management)
# REDIS_URL=redis://localhost:6379/0
# For Docker setup:
# REDIS_URL=redis://redis:6379/0


# Frontend URL for redirects
# FRONTEND_URL=http://localhost:5173

# Email settings
# EMAIL_ENABLED=false
# EMAIL_SENDER=<EMAIL>
# SMTP_SERVER=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_TLS=true
