
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Home, LayoutDashboard, Users, Settings, MessageSquare, BarChart4, Database, ShieldAlert } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";

const sidebarItems = [
  {
    href: "/",
    label: "Home",
    icon: Home,
  },
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
  },
  {
    href: "/ai-marketplace",
    label: "Marketplace",
    icon: Users,
  },
  {
    href: "/data-integration",
    label: "Data Integration",
    icon: Database,
  },
  {
    href: "/data-chat",
    label: "Data Chat",
    icon: MessageSquare,
  },
  {
    href: "/reports",
    label: "Reports",
    icon: BarChart4,
  },
  {
    href: "/settings",
    label: "Settings",
    icon: Settings,
  },
];

export const LeftNavbar = () => {
  const location = useLocation();
  const pathname = location.pathname;
  const [isExpanded, setIsExpanded] = useState(true);
  const { user } = useAuth();

  return (
    <div
      className={cn(
        "flex flex-col bg-white border-r w-60 py-4",
        isExpanded ? "w-60" : "w-16"
      )}
    >
      <div className="px-4 mb-6">
        <Link to="/" className="flex items-center space-x-2">
          <span className="font-bold">AI Toolkit</span>
        </Link>
      </div>

      <nav className="flex-1 px-2 space-y-1">
        {sidebarItems.map((item) => (
          <Link
            key={item.href}
            to={item.href}
            className={cn(
              "flex items-center space-x-3 py-2 px-3 hover:bg-gray-100 rounded-md transition-colors",
              pathname === item.href
                ? "bg-gray-100 font-medium"
                : "text-gray-600"
            )}
          >
            <item.icon className="h-4 w-4" />
            <span className={cn("text-sm", isExpanded ? "" : "hidden")}>
              {item.label}
            </span>
          </Link>
        ))}

        {/* Admin Link - Only shown for admin users */}
        {user?.is_superuser && (
          <Link
            to="/admin"
            className={cn(
              "flex items-center space-x-3 py-2 px-3 hover:bg-gray-100 rounded-md transition-colors mt-4 border-t pt-4",
              pathname.startsWith("/admin")
                ? "bg-gray-100 font-medium text-primary"
                : "text-gray-600"
            )}
          >
            <ShieldAlert className="h-4 w-4" />
            <span className={cn("text-sm", isExpanded ? "" : "hidden")}>
              Admin Panel
            </span>
          </Link>
        )}
      </nav>
    </div>
  );
};
