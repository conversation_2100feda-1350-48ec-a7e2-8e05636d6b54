# Datagenius Multi-Agent Architecture

This document provides a comprehensive overview of the multi-agent architecture implemented in the Datagenius project.

## Architecture Diagram

```mermaid
graph TB
    %% User Interface Layer
    User[👤 User] --> Frontend[🖥️ Frontend]
    Frontend --> API[🔌 API Gateway]
    
    %% Orchestration Layer
    API --> Orchestrator[🎯 Orchestrator]
    Orchestrator --> MessageQueue[📬 Message Queue]
    Orchestrator --> RoutingComponent[🧭 Routing Component]
    
    %% Agent Registry & Management
    RoutingComponent --> AgentRegistry[📋 Agent Registry]
    AgentRegistry --> ConciergeAgent[🤝 Concierge Agent]
    AgentRegistry --> MarketingAgent[📈 Marketing Agent]
    AgentRegistry --> AnalysisAgent[📊 Analysis Agent]
    AgentRegistry --> ClassificationAgent[🏷️ Classification Agent]
    
    %% Component System
    subgraph "Component System"
        ComponentRegistry[📦 Component Registry]
        LLMProcessor[🧠 LLM Processor]
        DataRetriever[📁 Data Retriever]
        MCPServer[⚙️ MCP Server]
        ContextManager[🗂️ Context Manager]
        PersonaCoordinator[🤝 Persona Coordinator]
        TeamManager[👥 Team Manager]
        AdvancedRouter[🔀 Advanced Router]
        BidirectionalComm[↔️ Bidirectional Communication]
        MemoryManager[🧠 Memory Manager]
    end
    
    %% MCP Tools System
    subgraph "MCP Tools"
        MCPToolRegistry[🛠️ MCP Tool Registry]
        DataAccessTool[📊 Data Access]
        PandasAITool[🐼 PandasAI Analysis]
        TextProcessingTool[📝 Text Processing]
        ContentGenerationTool[✍️ Content Generation]
        KnowledgeGraphTool[🕸️ Knowledge Graph]
        StatisticalAnalysisTool[📈 Statistical Analysis]
        VisualizationTool[📊 Visualization]
    end
    
    %% Data & Memory Layer
    subgraph "Data & Memory Layer"
        PostgreSQL[(🗄️ PostgreSQL)]
        QdrantDB[(🔍 Qdrant Vector DB)]
        Redis[(⚡ Redis Cache)]
        Mem0Service[🧠 Mem0 Memory Service]
        KnowledgeGraphStore[🕸️ Knowledge Graph Store]
    end
    
    %% AI Providers
    subgraph "AI Providers"
        OpenAI[🤖 OpenAI]
        Groq[⚡ Groq]
        OpenRouter[🔄 OpenRouter]
        Gemini[💎 Google Gemini]
        Ollama[🦙 Ollama]
        Requesty[📡 Requesty]
    end
    
    %% Workflow Management
    subgraph "Workflow Management"
        WorkflowManager[⚙️ Workflow Manager]
        TaskQueue[📋 Task Queue]
        QualityAssurance[✅ Quality Assurance]
    end
    
    %% Connections - Agent to Components
    ConciergeAgent --> ComponentRegistry
    MarketingAgent --> ComponentRegistry
    AnalysisAgent --> ComponentRegistry
    ClassificationAgent --> ComponentRegistry
    
    %% Component connections
    ComponentRegistry --> LLMProcessor
    ComponentRegistry --> DataRetriever
    ComponentRegistry --> MCPServer
    ComponentRegistry --> ContextManager
    ComponentRegistry --> PersonaCoordinator
    ComponentRegistry --> TeamManager
    ComponentRegistry --> AdvancedRouter
    ComponentRegistry --> BidirectionalComm
    ComponentRegistry --> MemoryManager
    
    %% MCP Server to Tools
    MCPServer --> MCPToolRegistry
    MCPToolRegistry --> DataAccessTool
    MCPToolRegistry --> PandasAITool
    MCPToolRegistry --> TextProcessingTool
    MCPToolRegistry --> ContentGenerationTool
    MCPToolRegistry --> KnowledgeGraphTool
    MCPToolRegistry --> StatisticalAnalysisTool
    MCPToolRegistry --> VisualizationTool
    
    %% Data connections
    DataRetriever --> PostgreSQL
    DataRetriever --> QdrantDB
    MemoryManager --> Mem0Service
    Mem0Service --> QdrantDB
    KnowledgeGraphTool --> KnowledgeGraphStore
    ContextManager --> Redis
    
    %% AI Provider connections
    LLMProcessor --> OpenAI
    LLMProcessor --> Groq
    LLMProcessor --> OpenRouter
    LLMProcessor --> Gemini
    LLMProcessor --> Ollama
    LLMProcessor --> Requesty
    
    %% Workflow connections
    Orchestrator --> WorkflowManager
    WorkflowManager --> TaskQueue
    WorkflowManager --> QualityAssurance
    
    %% Styling
    classDef userLayer fill:#e1f5fe
    classDef orchestrationLayer fill:#f3e5f5
    classDef agentLayer fill:#e8f5e8
    classDef componentLayer fill:#fff3e0
    classDef toolLayer fill:#fce4ec
    classDef dataLayer fill:#f1f8e9
    classDef aiLayer fill:#e3f2fd
    classDef workflowLayer fill:#fafafa
    
    class User,Frontend,API userLayer
    class Orchestrator,MessageQueue,RoutingComponent orchestrationLayer
    class AgentRegistry,ConciergeAgent,MarketingAgent,AnalysisAgent,ClassificationAgent agentLayer
    class ComponentRegistry,LLMProcessor,DataRetriever,MCPServer,ContextManager,PersonaCoordinator,TeamManager,AdvancedRouter,BidirectionalComm,MemoryManager componentLayer
    class MCPToolRegistry,DataAccessTool,PandasAITool,TextProcessingTool,ContentGenerationTool,KnowledgeGraphTool,StatisticalAnalysisTool,VisualizationTool toolLayer
    class PostgreSQL,QdrantDB,Redis,Mem0Service,KnowledgeGraphStore dataLayer
    class OpenAI,Groq,OpenRouter,Gemini,Ollama,Requesty aiLayer
    class WorkflowManager,TaskQueue,QualityAssurance workflowLayer
```

## Key Architecture Components

### 1. **Orchestrator**
- Central coordinator that manages message flow
- Routes incoming messages to appropriate agents
- Handles conversation context and state management
- Integrates with routing component for intelligent agent selection

### 2. **Agent Registry**
- Manages registration and instantiation of all agents
- Maps persona IDs to agent implementations
- Supports versioning and configuration management
- Enables dynamic agent loading

### 3. **Component System**
- Modular, reusable components shared across agents
- Component Registry manages all available components
- Components handle specific functionality (LLM processing, data retrieval, etc.)
- Enables composable agent architecture

### 4. **MCP Tools System**
- Standardized tools using Model Context Protocol
- Tool Registry manages all available tools
- Tools provide specific capabilities (data analysis, content generation, etc.)
- Enables consistent tool integration across agents

### 5. **Routing System**
- Intelligent routing based on message content and capabilities
- Supports multiple routing strategies (direct, capability-based, load-balanced, hierarchical)
- Fallback mechanisms for unavailable agents
- Advanced router component with sophisticated routing logic

### 6. **Memory & Knowledge Management**
- Mem0 service for persistent memory across conversations
- Qdrant vector database for semantic search and embeddings
- Knowledge graph for entity relationships and context
- Redis for session and cache management

## Agent Types

### **Concierge Agent**
- Default agent for user guidance and persona recommendation
- Handles routing requests and persona selection
- Provides data assistance and general help
- Manages user onboarding and navigation

### **Marketing Agent**
- Specialized in marketing content creation
- Campaign strategy development
- SEO optimization and social media content
- Brand messaging and promotional materials

### **Analysis Agent**
- Data analysis and visualization
- Statistical analysis and insights generation
- PandasAI integration for natural language data queries
- Machine learning and predictive analytics

### **Classification Agent**
- Text and data classification tasks
- Content categorization and tagging
- Sentiment analysis and entity recognition
- Document processing and organization

## Communication Patterns

### **Message Flow**
1. User sends message through frontend
2. API Gateway receives and validates request
3. Orchestrator determines target agent via routing component
4. Agent Registry creates appropriate agent instance
5. Agent processes message using its components
6. Components utilize MCP tools as needed
7. Response flows back through the system

### **Inter-Agent Communication**
- Persona Coordinator enables agent collaboration
- Bidirectional Communication for agent handoffs
- Team Manager for hierarchical agent teams
- Advanced Router for sophisticated routing decisions

### **Context Management**
- Enhanced Context Manager maintains conversation state
- Memory Manager provides persistent memory across sessions
- Knowledge Graph stores entity relationships
- Vector database enables semantic context retrieval

## Extensibility Features

### **Adding New Agents**
1. Create agent class extending BaseAgent or ComposableAgent
2. Register agent with AgentRegistry
3. Configure agent components and capabilities
4. Add routing rules for agent selection

### **Adding New Components**
1. Create component class extending AgentComponent
2. Register component with ComponentRegistry
3. Configure component in agent configurations
4. Components automatically available to all agents

### **Adding New Tools**
1. Create tool class extending BaseMCPTool
2. Register tool with MCPToolRegistry
3. Define tool schema and capabilities
4. Tools automatically available through MCP Server component

This architecture provides a highly extensible, modular system that supports easy addition of new agents, components, and tools while maintaining clean separation of concerns and standardized interfaces.
