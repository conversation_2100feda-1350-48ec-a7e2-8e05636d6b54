"""
Test script for the enhanced marketing persona.

This script tests the enhanced marketing persona by creating a conversation
and sending messages to simulate the task selection workflow.
"""

import asyncio
import logging
import sys
import os
import json
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import agent registry
from agents.registry import AgentRegistry
from agents.persona_manager import persona_manager


async def test_marketing_persona():
    """Test the enhanced marketing persona."""
    logger.info("Testing enhanced marketing persona")

    # Load agent configurations
    AgentRegistry.load_configurations("personas")

    # Check if the enhanced marketing persona is registered
    persona_id = "enhanced_marketing_agent"
    if persona_id not in AgentRegistry._registry:
        logger.error(f"Persona '{persona_id}' not found in registry")
        available_personas = list(AgentRegistry._registry.keys())
        logger.info(f"Available personas: {available_personas}")
        return

    logger.info(f"Creating agent instance for persona '{persona_id}'")

    # Create agent instance
    agent = await AgentRegistry.create_agent_instance(persona_id)

    if agent is None:
        logger.error(f"Failed to create agent for persona '{persona_id}'")
        return

    logger.info(f"Successfully created agent for persona '{persona_id}'")

    # Test the workflow
    conversation_id = "test_conversation"
    user_id = 1

    # Step 1: First message (should get task selection prompt)
    logger.info("Step 1: Sending first message")
    response1 = await agent.process_message(
        user_id=user_id,
        message="Hello",
        conversation_id=conversation_id,
        context={"is_first_message": True}
    )

    logger.info(f"Response 1: {response1.get('message')}")

    # Step 2: Select a task
    logger.info("Step 2: Selecting a task")
    response2 = await agent.process_message(
        user_id=user_id,
        message="Marketing Strategy",
        conversation_id=conversation_id,
        context={"is_first_message": False}
    )

    logger.info(f"Response 2: {response2.get('message')}")

    # Step 3: Provide some business information
    logger.info("Step 3: Providing business information")
    response3 = await agent.process_message(
        user_id=user_id,
        message="My business is a fitness app that helps people track their workouts and nutrition. Our target audience is health-conscious adults aged 25-45.",
        conversation_id=conversation_id,
        context={
            "is_first_message": False,
            "attached_files": [
                {
                    "name": "sample_data.txt",
                    "path": "test_data/sample_data.txt",
                    "type": "text"
                }
            ]
        }
    )

    logger.info(f"Response 3: {response3.get('message')}")

    # Step 4: Ask for specific recommendations
    logger.info("Step 4: Asking for specific recommendations")
    response4 = await agent.process_message(
        user_id=user_id,
        message="Can you suggest some marketing channels that would be effective for my fitness app?",
        conversation_id=conversation_id,
        context={"is_first_message": False}
    )

    logger.info(f"Response 4: {response4.get('message')}")


async def main():
    """Main function."""
    # Create test data directory if it doesn't exist
    os.makedirs("test_data", exist_ok=True)

    # Create a sample data file
    with open("test_data/sample_data.txt", "w") as f:
        f.write("""
Fitness App User Demographics:
- 60% female, 40% male
- Average age: 32
- Most active times: 6-8am and 5-7pm
- Top features used: workout tracking, meal planning, progress charts
- 70% of users are in urban areas
- 85% have premium subscriptions
- Average session length: 15 minutes
- User retention after 30 days: 65%
- Most common user goals: weight loss (45%), muscle gain (30%), general fitness (25%)
- Marketing channels with highest conversion: Instagram (35%), YouTube (25%), Referrals (20%)
        """)

    await test_marketing_persona()


if __name__ == "__main__":
    asyncio.run(main())
