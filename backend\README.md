# Datagenius Backend

This directory contains the backend code for the Datagenius application, a platform for chatting with AI agents (personas) that can access user-uploaded data.

## Directory Structure

```
backend/
├── agents/                   # AI agent implementations
│   ├── __init__.py
│   ├── base.py               # Base agent class
│   ├── registry.py           # Agent registry
│   ├── analysis_agent/       # Data analysis agent
│   ├── classification/       # Classification agent
│   ├── marketing_agent/      # Marketing agent
│   └── tools/                # Shared agent tools
├── app/                      # Core application
│   ├── __init__.py
│   ├── main.py               # FastAPI application entry point
│   ├── config.py             # Configuration settings
│   ├── database.py           # Database models and connections
│   ├── redis_client.py       # Redis client for token management
│   ├── api/                  # API routes
│   │   ├── __init__.py
│   │   ├── agents.py         # Agent invocation endpoints
│   │   ├── chat.py           # Chat endpoints
│   │   └── files.py          # File management endpoints
│   ├── auth/                 # Authentication utilities
│   │   └── __init__.py
│   └── models/               # Pydantic models
│       ├── __init__.py
│       ├── agent.py          # Agent-related models
│       ├── auth.py           # Authentication models
│       ├── chat.py           # Chat-related models
│       ├── file.py           # File-related models
│       └── task.py           # Task-related models
├── migrations/               # Database migrations
├── .env                      # Environment variables (not in version control)
├── .env.example              # Example environment variables
├── main.py                   # Legacy entry point (transitional)
├── server.py                 # Server script
└── requirements.txt          # Dependencies
```

## Setup

1. Create a virtual environment:
   ```
   python -m venv venv
   ```

2. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Unix/MacOS: `source venv/bin/activate`

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Create a `.env` file based on `.env.example` and configure your environment variables.

5. Initialize the database:
   ```
   python manage_db.py create
   ```

## Running the Server

```
python server.py
```

Or with custom options:
```
python server.py --host 0.0.0.0 --port 8000 --reload
```

## API Documentation

Once the server is running, you can access the API documentation at:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Agents

The backend supports multiple AI agents:

- **Analysis Agent**: Performs data analysis on CSV and Excel files
- **Marketing Agent**: Generates marketing content based on user requests
- **Classification Agent**: Classifies text using both Hugging Face and LLM models

## Authentication

The backend uses JWT-based authentication with refresh tokens. Redis is used for token blacklisting and session management, with an in-memory fallback if Redis is not available.

## Database

The application uses SQLAlchemy with SQLite by default, but can be configured to use PostgreSQL or MySQL for production.

## Environment Variables

See `.env.example` for a list of available environment variables and their descriptions.
