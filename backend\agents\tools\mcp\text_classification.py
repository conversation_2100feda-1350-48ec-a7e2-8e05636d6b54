"""
Text classification MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for classifying text using various models.
"""

import logging
import os
import json
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from pydantic import BaseModel, Field, create_model

from .base import BaseMCPTool
from agents.utils.model_providers.utils import get_model

logger = logging.getLogger(__name__)


class TextClassificationTool(BaseMCPTool):
    """Tool for classifying text using various models."""

    def __init__(self):
        """Initialize the text classification tool."""
        super().__init__(
            name="classify_text",
            description="Classify text using various models",
            input_schema={
                "type": "object",
                "properties": {
                    "texts": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of texts to classify"
                    },
                    "classification_type": {
                        "type": "string",
                        "enum": ["llm", "huggingface"],
                        "description": "Type of classification to perform"
                    },
                    "model_provider": {
                        "type": "string",
                        "description": "Provider for LLM classification (e.g., groq, openai, anthropic)"
                    },
                    "model_name": {
                        "type": "string",
                        "description": "Model name to use for classification"
                    },
                    "hf_model_name": {
                        "type": "string",
                        "description": "Hugging Face model name for huggingface classification"
                    },
                    "categories": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Categories for classification (for LLM classification)"
                    },
                    "hierarchy_levels": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Hierarchy levels for classification (e.g., ['theme', 'category'])"
                    },
                    "temperature": {
                        "type": "number",
                        "description": "Temperature for LLM classification"
                    },
                    "threshold": {
                        "type": "number",
                        "description": "Threshold for Hugging Face classification"
                    },
                    "sample_size": {
                        "type": "integer",
                        "description": "Number of samples to classify (for large datasets)"
                    }
                },
                "required": ["texts", "classification_type"]
            },
            annotations={
                "title": "Classify Text",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.hf_models = {}
        self.hf_tokenizers = {}

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # No specific initialization needed
        pass

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            texts = arguments["texts"]
            classification_type = arguments["classification_type"]

            if not texts:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "No texts provided for classification"
                        }
                    ]
                }

            # Sample texts if sample_size is provided
            sample_size = arguments.get("sample_size")
            if sample_size and len(texts) > sample_size:
                import random
                texts = random.sample(texts, sample_size)
                logger.info(f"Sampled {sample_size} texts from {len(texts)} total texts")

            # Perform classification based on the type
            if classification_type == "llm":
                result = await self._classify_with_llm(texts, arguments)
            elif classification_type == "huggingface":
                result = await self._classify_with_huggingface(texts, arguments)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported classification type: {classification_type}"
                        }
                    ]
                }

            # Convert the result to the MCP format
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Classification completed successfully. Classified {len(texts)} texts."
                    }
                ],
                "metadata": {
                    "classification_type": classification_type,
                    "results": result
                }
            }

        except Exception as e:
            logger.error(f"Error classifying text: {str(e)}", exc_info=True)
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error classifying text: {str(e)}"
                    }
                ]
            }

    async def _classify_with_llm(self, texts: List[str], arguments: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Classify texts using an LLM.

        Args:
            texts: List of texts to classify
            arguments: Additional arguments for classification

        Returns:
            List of classification results
        """
        provider_id = arguments.get("model_provider", "groq")
        model_name = arguments.get("model_name", "llama3-70b-8192")
        temperature = arguments.get("temperature", 0.1)
        hierarchy_levels = arguments.get("hierarchy_levels", ["category"])
        categories = arguments.get("categories", [])

        # Initialize LLM client using the model provider system
        try:
            llm = await get_model(provider_id, model_name, {"temperature": temperature})
            logger.info(f"Successfully initialized model from provider '{provider_id}'")
        except Exception as e:
            logger.error(f"Error initializing model: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to initialize model: {str(e)}")

        # Create a dynamic Pydantic model for the output
        fields = {}
        for level in hierarchy_levels:
            fields[level] = (str, ...)
        fields["reasoning"] = (str, ...)

        DynamicCategorizationResult = create_model(
            "DynamicCategorizationResult",
            **fields
        )

        # Create output parser
        from langchain.output_parsers import PydanticOutputParser
        from langchain.output_parsers.fix import OutputFixingParser

        parser = PydanticOutputParser(pydantic_object=DynamicCategorizationResult)
        fixing_parser = OutputFixingParser.from_llm(parser=parser, llm=llm)

        # Create prompt template
        from langchain.prompts import PromptTemplate

        # Build hierarchy description
        if categories:
            hierarchy_description = "Categories: " + ", ".join(categories)
        else:
            hierarchy_description = "Use appropriate categories based on the content of the text."

        template = """
        You are an expert text classifier. Your task is to categorize the following text according to a hierarchical classification system.

        Hierarchical Classification System:
        {hierarchy_description}

        Text to classify:
        "{text}"

        Provide your classification in the following format:
        {format_instructions}

        Think step by step and provide a brief reasoning for your classification.
        """

        prompt = PromptTemplate(
            template=template,
            input_variables=["text", "hierarchy_description"],
            partial_variables={"format_instructions": parser.get_format_instructions()}
        )

        # Process each text
        results = []
        for text in texts:
            try:
                # Format the prompt
                formatted_prompt = prompt.format(
                    text=text[:1000],  # Limit text length
                    hierarchy_description=hierarchy_description
                )

                # Get LLM response
                llm_response = await llm.agenerate_text(formatted_prompt)

                # Parse the response
                try:
                    parsed_response = fixing_parser.parse(llm_response)
                    result = parsed_response.dict()
                    result["text"] = text[:100] + "..." if len(text) > 100 else text
                    results.append(result)
                except Exception as parse_error:
                    logger.warning(f"Error parsing LLM response: {str(parse_error)}")
                    results.append({
                        "text": text[:100] + "..." if len(text) > 100 else text,
                        "error": str(parse_error),
                        "raw_response": llm_response
                    })
            except Exception as text_error:
                logger.warning(f"Error processing text: {str(text_error)}")
                results.append({
                    "text": text[:100] + "..." if len(text) > 100 else text,
                    "error": str(text_error)
                })

        return results

    async def _classify_with_huggingface(self, texts: List[str], arguments: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Classify texts using a Hugging Face model.

        Args:
            texts: List of texts to classify
            arguments: Additional arguments for classification

        Returns:
            List of classification results
        """
        model_name = arguments.get("hf_model_name", "facebook/bart-large-mnli")
        threshold = arguments.get("threshold", 0.5)

        # Load or get cached model and tokenizer
        if model_name not in self.hf_models:
            try:
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                model = AutoModelForSequenceClassification.from_pretrained(model_name)
                self.hf_tokenizers[model_name] = tokenizer
                self.hf_models[model_name] = model
                logger.info(f"Loaded Hugging Face model: {model_name}")
            except Exception as e:
                logger.error(f"Error loading Hugging Face model: {str(e)}", exc_info=True)
                raise ValueError(f"Failed to load Hugging Face model: {str(e)}")
        else:
            tokenizer = self.hf_tokenizers[model_name]
            model = self.hf_models[model_name]

        # Get candidate labels
        candidate_labels = arguments.get("categories", ["positive", "negative", "neutral"])

        # Batch process texts for efficiency
        batch_size = 16
        all_results = []

        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i+batch_size]
            batch_results = []

            for text in batch_texts:
                # Process each text with the model
                try:
                    # Tokenize input
                    inputs = tokenizer(text, candidate_labels, padding=True, truncation=True, return_tensors="pt")

                    # Get model predictions
                    with torch.no_grad():
                        outputs = model(**inputs)
                        logits = outputs.logits
                        probs = torch.nn.functional.softmax(logits, dim=1)

                    # Get the highest probability label
                    max_idx = torch.argmax(probs, dim=1).item()
                    max_prob = probs[0, max_idx].item()
                    predicted_label = candidate_labels[max_idx]

                    # Apply threshold
                    if max_prob < threshold:
                        predicted_label = "unknown"

                    # Add to results
                    result = {
                        "text": text[:100] + "..." if len(text) > 100 else text,
                        "label": predicted_label,
                        "confidence": max_prob,
                        "all_labels": {label: probs[0, i].item() for i, label in enumerate(candidate_labels)}
                    }
                    batch_results.append(result)
                except Exception as text_error:
                    logger.warning(f"Error processing text with Hugging Face: {str(text_error)}")
                    batch_results.append({
                        "text": text[:100] + "..." if len(text) > 100 else text,
                        "error": str(text_error)
                    })

            all_results.extend(batch_results)

        return all_results
