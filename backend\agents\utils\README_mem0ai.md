# mem0ai Integration for Datagenius

This directory contains the core services for integrating mem0ai with the Datagenius application.

## Overview

mem0ai is a powerful memory system that provides vector database and knowledge graph capabilities. It integrates with Qdrant for vector storage and offers a unified interface for memory operations.

## Core Services

### MemoryService

The `MemoryService` class provides a unified interface for memory operations using mem0ai. It handles:

- Adding memories
- Retrieving memories
- Searching memories
- Managing memory retention policies

### VectorService

The `VectorService` class provides vector database operations using mem0ai and Qdrant. It handles:

- Document embedding
- Semantic search
- Vector storage and retrieval

### KnowledgeGraphService

The `KnowledgeGraphService` class provides knowledge graph operations using mem0ai. It handles:

- Entity extraction and storage
- Relationship management
- Graph queries and traversal

### QdrantManager

The `QdrantManager` class manages the Qdrant vector database used by mem0ai for local memory storage. It handles:

- Checking if Qdrant is running
- Starting Qdrant if needed
- Managing Qdrant lifecycle

## Configuration

The mem0ai integration can be configured in two modes:

### Self-Hosted Mode

In self-hosted mode, Datagenius uses a local Qdrant instance for vector storage. This is configured in the `.env` file:

```
MEM0_SELF_HOSTED=True
QDRANT_HOST=localhost
QDRANT_PORT=6333
```

### Hosted Mode

In hosted mode, Datagenius uses the mem0ai service for vector storage. This is configured in the `.env` file:

```
MEM0_SELF_HOSTED=False
MEM0_API_KEY=your_api_key
```

## Usage

### Adding a Memory

```python
from agents.utils.memory_service import MemoryService

memory_service = MemoryService()
memory = memory_service.add_memory(
    content="This is a memory",
    user_id="user123",
    metadata={"category": "test"}
)
```

### Embedding a Document

```python
from agents.utils.vector_service import VectorService

vector_service = VectorService()
vector_store_id, file_info = vector_service.embed_document(
    file_path="document.pdf",
    chunk_size=1000,
    chunk_overlap=200
)
```

### Searching a Document

```python
from agents.utils.vector_service import VectorService

vector_service = VectorService()
results = vector_service.search_document(
    vector_store_id="vector_store_123",
    query="What is artificial intelligence?",
    limit=5
)
```

### Creating a Knowledge Graph

```python
from agents.utils.knowledge_graph_service import KnowledgeGraphService

kg_service = KnowledgeGraphService()
graph_id = kg_service.create_graph(
    name="My Graph",
    description="A knowledge graph",
    metadata={"category": "test"}
)
```

### Adding Entities and Relationships

```python
from agents.utils.knowledge_graph_service import KnowledgeGraphService

kg_service = KnowledgeGraphService()
entity_id = kg_service.add_entity(
    graph_id="graph_123",
    entity_type="Person",
    name="John Doe",
    properties={"age": 30}
)

relationship_id = kg_service.add_relationship(
    graph_id="graph_123",
    relationship_type="WORKS_AT",
    source_id="entity_123",
    target_id="entity_456",
    properties={"since": 2020}
)
```

### Ensuring Qdrant is Running

```python
from agents.utils.qdrant_manager import QdrantManager

if QdrantManager.ensure_qdrant_running():
    print("Qdrant is running")
else:
    print("Failed to ensure Qdrant is running")
```

## Integration with Components

The mem0ai integration is used by the following components:

### MemoryManagerComponent

The `MemoryManagerComponent` uses mem0ai for:

- Memory management across AI personas
- Document embedding and semantic search
- Knowledge graph operations

### MCP Tools

The following MCP tools use mem0ai:

- `Mem0DocumentEmbeddingTool`: For document embedding and semantic search
- `DataAccessTool`: For document processing
- `KnowledgeGraphTool`: For knowledge graph operations
- `PandasAIAnalysisTool`: For storing analysis results
- `PandasAIVisualizationTool`: For storing visualizations
- `PandasAIQueryTool`: For storing query results

## Testing

The integration includes a comprehensive test script (`test_mem0_integration.py`) that tests:

1. Memory Service
2. Vector Service
3. Knowledge Graph Service
4. MCP Tools
5. Data Access Tool

To run the tests:

```bash
python -m backend.tests.test_mem0_integration
```

## Registration

To register the mem0ai integration with all components:

```bash
python -m backend.scripts.register_mem0_integration
```

This script:

1. Ensures all necessary components are registered
2. Updates component configurations to use mem0ai
3. Verifies the integration is working

## Documentation

For more information, see the [mem0ai Integration Documentation](../../docs/mem0ai_integration.md).
