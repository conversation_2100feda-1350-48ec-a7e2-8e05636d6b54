"""
Data visualization MCP tool using PandasAI.

This module provides an MCP-compatible tool for visualizing data by interpreting
natural language prompts using PandasAI.
"""

import logging
import os
import io
import base64
import pandas as pd
from typing import Dict, Any, Optional

# PandasAI imports
import pandasai as pai
from pandasai import Agent


from .base import BaseMCPTool

logger = logging.getLogger(__name__)

# Helper function to load dataframe (can be shared or kept within tool)
def _try_load_dataframe(file_path: str) -> Optional[pd.DataFrame]:
    """Attempts to load a dataframe from the given path."""
    try:
        if file_path.lower().endswith(".csv"):
            df = pd.read_csv(file_path)
        elif file_path.lower().endswith((".xls", ".xlsx")):
            df = pd.read_excel(file_path)
        elif file_path.lower().endswith(".json"):
             # Basic JSON loading, might need adjustments based on structure
             df = pd.read_json(file_path)
        else:
            logger.warning(f"Unsupported file type for PandasAI visualization: {file_path}")
            return None
        if df.empty:
             logger.warning(f"Loaded dataframe from {file_path} is empty.")
             return None
        logger.info(f"Successfully loaded dataframe from {file_path} for PandasAI visualization")
        return df
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        return None
    except pd.errors.EmptyDataError:
        logger.warning(f"File is empty: {file_path}")
        return None
    except Exception as e:
        logger.error(f"Error loading dataframe from {file_path}: {e}", exc_info=True)
        return None

# Helper function to instantiate LLM (can be shared or kept within tool)
def _get_pandasai_llm(provider: Optional[str], api_key: Optional[str], model: Optional[str] = None):
    """Instantiates a PandasAI LLM based on provider and API key."""
    from ..pandasai_v3.llm_providers import LLMProviderFactory

    provider = provider.lower() if provider else "openai"
    if not api_key:
        logger.error(f"API key missing for LLM provider: {provider}")
        raise ValueError(f"API key required for {provider}")

    try:
        return LLMProviderFactory.create_provider(provider=provider, api_key=api_key, model=model)
    except Exception as e:
        logger.error(f"Error instantiating LLM for {provider}: {e}", exc_info=True)
        raise ValueError(f"Could not instantiate LLM for {provider}: {e}")


class DataVisualizationTool(BaseMCPTool):
    """Tool for visualizing data using PandasAI based on natural language prompts."""

    def __init__(self):
        """Initialize the PandasAI data visualization tool."""
        super().__init__(
            # Tool name should likely match the 'type' used in the agent config
            name="data_visualization",
            description="Generates data visualizations (charts, plots) based on a natural language prompt using PandasAI.",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the data file (CSV, Excel, JSON supported)."
                    },
                    "prompt": {
                        "type": "string",
                        "description": "Natural language prompt describing the desired visualization (e.g., 'Plot histogram of Sales', 'Scatter plot of GDP vs Happiness Index', 'Bar chart showing average Salary by Department')."
                    },
                    "provider": {
                        "type": "string",
                        "description": "LLM provider for PandasAI (e.g., 'openai')."
                    },
                    "model": {
                        "type": "string",
                        "description": "Optional: Specific LLM model name."
                    },
                    "api_key": {
                        "type": "string",
                        "description": "API key for the selected LLM provider."
                    }
                    # Removed specific plot params like x_column, plot_type etc.
                },
                "required": ["file_path", "prompt", "provider", "api_key"]
            },
            # Output schema might need adjustment based on how PandasAI returns plots
            output_schema={
                 "type": "object",
                 "properties": {
                     "tool_name": {"type": "string"},
                     "status": {"type": "string"},
                     "isError": {"type": "boolean"},
                     "content": {
                         "type": "array",
                         "items": {
                             "type": "object",
                             "properties": {
                                 "type": {"type": "string", "enum": ["text", "image"]},
                                 "text": {"type": "string"},
                                 "src": {"type": "string", "description": "Base64 encoded image data URI"}
                             },
                             "required": ["type"]
                         }
                     },
                     "metadata": {
                         "type": "object",
                         "properties": {
                             "visualization": {
                                 "type": "object",
                                 "properties": {
                                     "type": {"type": "string"},
                                     "title": {"type": "string"},
                                     "description": {"type": "string"},
                                     "data": {
                                         "type": "object",
                                         "properties": {
                                             "image": {"type": "string"}
                                         }
                                     }
                                 }
                             }
                         }
                     }
                 }
            },
            annotations={ # Keep annotations if they are standard for your MCP setup
                "title": "Visualize Data (PandasAI)",
                "readOnlyHint": True,
                "openWorldHint": False # This tool now relies on external LLM
            }
        )
        self.data_dir = "data" # Default data directory

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration (e.g., data directory).

        Args:
            config: Configuration dictionary for the tool
        """
        if "data_dir" in config:
            self.data_dir = config["data_dir"]
            logger.info(f"Set data directory to: {self.data_dir}")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments using PandasAI.

        Args:
            arguments: Arguments for tool execution (file_path, prompt, provider, api_key, model?)

        Returns:
            Tool execution results in MCP format, potentially including a base64 image.
        """
        file_path_arg = arguments.get("file_path")
        prompt = arguments.get("prompt")
        provider = arguments.get("provider")
        api_key = arguments.get("api_key")
        model = arguments.get("model") # Optional

        # --- Input Validation ---
        if not file_path_arg or not prompt or not provider or not api_key:
             missing = [k for k, v in arguments.items() if k in ["file_path", "prompt", "provider", "api_key"] and not v]
             return {
                 "tool_name": self.name, "status": "error", "isError": True,
                 "content": [{"type": "text", "text": f"Error: Missing required arguments: {', '.join(missing)}"}],
                 "metadata": {"status": "error", "error_type": "missing_arguments"}
             }

        # --- Resolve File Path ---
        if not os.path.isabs(file_path_arg):
            file_path = os.path.join(self.data_dir, file_path_arg)
        else:
            file_path = file_path_arg

        # --- Load Data ---
        df = _try_load_dataframe(file_path)
        if df is None:
            return {
                "tool_name": self.name, "status": "error", "isError": True,
                "content": [{"type": "text", "text": f"Error: Could not load or data is empty in {file_path}."}],
                "metadata": {"file_path": file_path, "prompt": prompt, "status": "error", "error_type": "load_error"}
            }

        results_content = []
        results_metadata = {}
        is_error = False
        status = "success"

        try:
            # --- Instantiate PandasAI ---
            llm = _get_pandasai_llm(provider, api_key, model)
            # Ensure the default export path exists or handle potential errors
            export_path = "exports/charts"
            os.makedirs(export_path, exist_ok=True)
            # Create agent with config
            config = {
                "llm": llm,
                "conversational": False,
                "verbose": False, # Set to True for debugging PandasAI steps
                "enforce_privacy": False,
                "save_charts": True,
                "save_charts_path": export_path
            }
            agent = Agent(df, config=config)

            # --- Run Query ---
            logger.info(f"Running PandasAI visualization prompt: '{prompt}'")
            # Use the chat method for Agent
            result = agent.chat(prompt)
            logger.info(f"PandasAI visualization execution finished. Result: {result} (Type: {type(result)})")

            # --- Process Result ---
            image_path = None
            # Check if the result is a string and looks like a path PandasAI might save to
            if isinstance(result, str) and "exports/charts" in result and result.lower().endswith(".png"):
                 # Check if the file actually exists (PandasAI might return path even if saving failed)
                 if os.path.exists(result):
                      image_path = result
                 else:
                      logger.warning(f"PandasAI returned image path '{result}' but file does not exist.")
                      results_content.append({"type": "text", "text": "Chart generation was attempted, but the output file was not found."})
                      status = "partial_success" # Or error? Depends on expectation.

            elif isinstance(result, str): # Handle cases where it returns text instead of a plot path
                 results_content.append({"type": "text", "text": f"PandasAI response: {result}"})
                 status = "success_text_response" # Indicate it wasn't an image

            elif result is None:
                 results_content.append({"type": "text", "text": "Visualization request processed, but no specific chart was generated or returned."})
                 status = "success_no_output"

            else: # Unexpected result type
                 results_content.append({"type": "text", "text": f"PandasAI returned an unexpected result type: {type(result).__name__}. Result: {str(result)}"})
                 status = "unexpected_result"


            # If an image path was found, load and encode it
            if image_path:
                try:
                    with open(image_path, "rb") as f:
                        img_bytes = f.read()
                    img_base64 = base64.b64encode(img_bytes).decode("utf-8")
                    # Add image content in a format that can be properly displayed in the frontend
                    results_content.append({
                        "type": "image",
                        "src": f"data:image/png;base64,{img_base64}"
                    })

                    # Also add visualization metadata for the frontend to render
                    results_metadata["visualization"] = {
                        "type": "chart",
                        "title": f"Visualization for: {prompt}",
                        "description": "Generated using PandasAI",
                        "data": {
                            "image": f"data:image/png;base64,{img_base64}"
                        }
                    }
                    results_content.append({ # Add context text
                         "type": "text",
                         "text": f"Generated visualization based on prompt: '{prompt}'"
                    })
                    results_metadata["image_path"] = image_path # Store path for reference
                    # Optionally delete the temp file: os.remove(image_path)
                except Exception as img_e:
                    logger.error(f"Error reading or encoding image file {image_path}: {img_e}", exc_info=True)
                    results_content.append({"type": "text", "text": f"Error processing generated image file: {img_e}"})
                    status = "error"
                    is_error = True
                    results_metadata["error_type"] = "image_processing_error"


        except ValueError as e: # Catch errors from _get_pandasai_llm or PandasAI init
            logger.error(f"Configuration or instantiation error for PandasAI: {e}")
            results_content = [{"type": "text", "text": f"Error setting up visualization engine: {e}"}]
            results_metadata = {"status": "error", "error_type": "setup_error", "details": str(e)}
            is_error = True
            status = "error"
        except Exception as e:
            # Catch errors during pandas_ai.run()
            logger.error(f"Error during PandasAI visualization execution: {e}", exc_info=True)
            error_detail = str(e)
            results_content = [{"type": "text", "text": f"An unexpected error occurred while generating the visualization with PandasAI: {error_detail}"}]
            results_metadata = {"status": "error", "error_type": "pandasai_execution_error", "details": error_detail}
            is_error = True
            status = "error"

        # Ensure content is not empty
        if not results_content:
             results_content.append({"type": "text", "text": "No specific output generated."})
             if status == "success": status = "success_no_output"


        return {
            "tool_name": self.name,
            "status": status,
            "isError": is_error,
            "content": results_content,
            "metadata": {
                "file_path": file_path,
                "prompt": prompt,
                "implementation": "pandasai",
                **results_metadata
            }
        }

# Note: If this file is directly registered (not via registry.py),
# you might need a TOOL_METADATA dict here like the standalone tools.
# However, given the class structure, registration likely happens elsewhere.
