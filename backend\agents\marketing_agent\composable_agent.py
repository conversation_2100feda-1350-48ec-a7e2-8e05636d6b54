"""
Composable marketing agent for the Datagenius backend.

This module provides a composable implementation of the marketing agent
that uses the component-based architecture with MCP tools.
"""

import logging
from typing import Dict, Any, Optional

from agents.composable import ComposableAgent
from .components import (
    MarketingParserComponent,
    MCPContentGeneratorComponent
)
from agents.components.mcp_server import MCPServerComponent

# Configure logging
logger = logging.getLogger(__name__)


class ComposableMarketingAgent(ComposableAgent):
    """Composable implementation of the marketing agent using MCP tools."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the composable marketing agent.

        Args:
            config: Configuration dictionary for the agent
        """
        logger.info("Initializing Composable Marketing Agent with MCP tools")
        logger.info(f"Agent configuration: {config}")

        # Log available components in registry
        from agents.components.registry import ComponentRegistry
        available_components = ComponentRegistry.list_registered_components()
        logger.info(f"Available components in registry: {available_components}")

        # IMPORTANT: We're going to initialize our components directly instead of relying on the parent
        # This ensures we have the components we need regardless of registry state

        # Initialize our components list
        self.components = []

        # Check if components are configured in the YAML
        has_configured_components = "components" in config and len(config["components"]) > 0
        logger.info(f"Has configured components: {has_configured_components}")

        if has_configured_components:
            # Process components from configuration
            logger.info("Processing components from configuration")
            for component_config in config["components"]:
                component_type = component_config.get("type")
                component_name = component_config.get("name", component_type)

                logger.info(f"Processing component: type={component_type}, name={component_name}")

                if component_type == "marketing_parser":
                    # Create and initialize the parser component
                    parser_component = MarketingParserComponent()
                    await parser_component.initialize(component_config)
                    self.components.append(parser_component)
                    logger.info(f"Added marketing parser component: {parser_component.name}")

                elif component_type == "marketing_content_generator":
                    # Create and initialize the content generator component
                    generator_component = MCPContentGeneratorComponent()
                    await generator_component.initialize(component_config)
                    self.components.append(generator_component)
                    logger.info(f"Added marketing content generator component: {generator_component.name}")

                elif component_type == "mcp_server":
                    # Create and initialize the MCP server component
                    mcp_server_component = MCPServerComponent()
                    await mcp_server_component.initialize(component_config)
                    self.components.append(mcp_server_component)
                    logger.info(f"Added MCP server component: {mcp_server_component.name}")

                else:
                    logger.warning(f"Unknown component type: {component_type}")
        else:
            # If no components were configured, set up the default components
            logger.info("No components configured, setting up default marketing components")

            # Create and initialize the parser component
            parser_component = MarketingParserComponent()
            await parser_component.initialize({
                "name": "marketing_parser"
            })
            self.components.append(parser_component)
            logger.info(f"Added default marketing parser component: {parser_component.name}")

            # Create and initialize the MCP server component with essential tools
            from agents.components import create_mcp_server_with_essential_tools

            # Create MCP server with essential tools first
            mcp_server_component = await create_mcp_server_with_essential_tools({
                "name": "marketing_tools",
                "server_name": "datagenius-marketing-tools",
                "server_version": "1.0.0",
                "tools": [
                    {
                        "type": "content_generation",
                        "prompt_templates": {
                            "marketing_strategy": config.get("system_prompts", {}).get("marketing_strategy", ""),
                            "social_media": config.get("system_prompts", {}).get("social_media", "")
                        }
                    }
                ]
            })
            self.components.append(mcp_server_component)
            logger.info(f"Added default MCP server component: {mcp_server_component.name}")

            # Create and initialize the content generator component
            generator_component = MCPContentGeneratorComponent()
            await generator_component.initialize({
                "name": "content_generator"
            })
            self.components.append(generator_component)
            logger.info(f"Added default marketing content generator component: {generator_component.name}")

        logger.info(f"Initialized {len(self.components)} marketing components")

    async def process_message(self,
                             user_id: int,
                             message: str,
                             conversation_id: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message using the agent's components.

        Args:
            user_id: The ID of the user sending the message
            message: The user's message text
            conversation_id: The ID of the conversation
            context: Additional context information

        Returns:
            Dict containing response text and any additional data
        """
        try:
            logger.info(f"Processing message for user {user_id} in conversation {conversation_id}")
            logger.info(f"Message: {message[:50]}...")
            logger.info(f"Number of components available: {len(self.components)}")

            if not self.components:
                logger.error("No components available for processing message")
                return {
                    "message": "This agent has no components configured. Please check the agent configuration.",
                    "metadata": {
                        "error": "no_components_configured"
                    }
                }

            # Log the components that will be used
            for i, component in enumerate(self.components):
                logger.info(f"Component {i+1}: {component.name} ({component.__class__.__name__})")

            # Create initial context
            ctx = {
                "user_id": user_id,
                "message": message,
                "conversation_id": conversation_id,
                "context": context or {},
                "agent_config": self.config,
                "agent_components": self.components,
                "response": "",
                "metadata": {}
                # Removed is_first_message logic, now handled by queue task type
            }

            # Check if we need to process a file for the persona
            if context and context.get("send_file_to_persona") and context.get("data_source"):
                # Find MCP server component
                mcp_server = None
                for component in self.components:
                    if component.__class__.__name__ == "MCPServerComponent":
                        mcp_server = component
                        break

                if mcp_server:
                    logger.info("Found MCP server component, processing file for persona")
                    try:
                        # Call the data_access tool with the send_to_persona operation
                        result = await mcp_server.call_tool("data_access", {
                            "operation": "send_to_persona",
                            "data_source": context.get("data_source"),
                            "params": {"sample_size": 10, "include_table": True}  # Show 10 rows with table visualization
                        })

                        # If successful, add the result to the context
                        if not result.get("isError", False):
                            logger.info("Successfully processed file for persona")
                            ctx["file_processed"] = True
                            ctx["file_data"] = result

                            # If no message was provided, generate a default response about the file
                            if not message or message.strip() == "":
                                # Extract text content from the result
                                text_content = []
                                for content_item in result.get("content", []):
                                    if content_item.get("type") == "text":
                                        text_content.append(content_item.get("text", ""))

                                if text_content:
                                    ctx["response"] = "\n".join(text_content)
                        else:
                            logger.error(f"Error processing file: {result}")
                            error_message = "I couldn't process the attached file. Please make sure it's a valid data file."
                            if result.get("content") and len(result["content"]) > 0:
                                error_message = result["content"][0].get("text", error_message)
                            ctx["response"] = error_message
                            ctx["metadata"]["file_error"] = True
                    except Exception as e:
                        logger.error(f"Error calling data_access tool: {e}", exc_info=True)
                        ctx["response"] = f"I encountered an error while processing the attached file: {str(e)}"
                        ctx["metadata"]["file_error"] = True
                        ctx["metadata"]["error_details"] = str(e)

            # Process context through each component
            # This loop now handles all non-initiation messages
            logger.info("Processing message through component chain.")
            for component in self.components:
                try:
                    logger.info(f"Processing with component: {component.name}")
                    ctx = await component.process(ctx)
                    logger.info(f"After component {component.name}, response length: {len(ctx.get('response', ''))}")
                except Exception as e:
                    logger.error(f"Error in component {component.name}: {str(e)}", exc_info=True)

                    # Initialize metadata if it doesn't exist
                    if "metadata" not in ctx:
                        ctx["metadata"] = {}

                    # Initialize errors array if it doesn't exist
                    if "errors" not in ctx["metadata"]:
                        ctx["metadata"]["errors"] = []

                    # Add the error to the errors array
                    ctx["metadata"]["errors"].append({
                        "component": component.name,
                        "error": str(e),
                        "error_type": e.__class__.__name__
                    })

                    # Set a user-friendly error message
                    ctx["response"] = f"I encountered an error while processing your request. Please try again or try a different request."
                    ctx["metadata"]["error"] = "component_error"
                    ctx["metadata"]["error_details"] = str(e)

            # Ensure we have a response
            if not ctx.get("response"):
                logger.warning("No response generated by components")
                ctx["response"] = "I'm sorry, I wasn't able to process your request properly."

            logger.info(f"Final response length: {len(ctx.get('response', ''))}")

            # Get the response and metadata
            response = ctx.get("response", "")
            metadata = ctx.get("metadata", {})

            # Ensure the response is included in the metadata for visualization
            if response and "generated_content" in metadata and metadata.get("generated_content") == True:
                metadata["response"] = response
                metadata["content"] = response

            # Add diagnostic information to help troubleshoot issues
            if "errors" in metadata and metadata["errors"]:
                logger.error(f"Errors occurred during processing: {metadata['errors']}")

            # Log the final response and metadata for debugging
            logger.info(f"Final response: {response[:100]}...")
            logger.info(f"Final metadata keys: {list(metadata.keys())}")

            if "error" in metadata:
                logger.error(f"Error in metadata: {metadata['error']}")
                if "error_details" in metadata:
                    logger.error(f"Error details: {metadata['error_details']}")

            return {
                "message": response,
                "metadata": metadata
            }
        except Exception as e:
            # Catch any uncaught exceptions in the process_message method
            logger.error(f"Uncaught exception in process_message: {str(e)}", exc_info=True)
            
            # Check for connection errors
            if "connection" in str(e).lower() or "timeout" in str(e).lower() or "socket" in str(e).lower() or "websocket" in str(e).lower():
                return {
                    "message": "I'm having trouble connecting to the AI provider. Please try again in a moment.",
                    "metadata": {
                        "error": "connection_error",
                        "error_details": str(e)
                    }
                }
            else:
                return {
                    "message": "I encountered an unexpected error while processing your message. Please try again.",
                    "metadata": {
                        "error": "unexpected_error",
                        "error_details": str(e)
                    }
                }
