"""
Utility functions for initializing models in agents.

This module provides utility functions for initializing LLM models in agents
based on persona configuration.
"""

import logging
from typing import Dict, Any, Optional, Union

# Import LangChain models with compatibility for different versions
try:
    # Try newer LangChain structure
    from langchain_core.language_models.base import BaseLanguageModel
    from langchain_core.language_models.chat_models import BaseChatModel
except ImportError:
    try:
        # Try older LangChain structure
        from langchain.schema.language_model import BaseLanguageModel
        from langchain.schema.chat_model import BaseChatModel
    except ImportError:
        # Fallback to even older structure
        from langchain.base_language import BaseLanguageModel
        from langchain.chat_models.base import BaseChatModel

# Import the model provider system
from .model_providers.utils import get_model

# Configure logging
logger = logging.getLogger(__name__)


async def initialize_agent_model(
    config: Dict[str, Any],
    default_provider: str = "groq",
    default_model: Optional[str] = None
) -> Union[BaseLanguageModel, BaseChatModel]:
    """
    Initialize an LLM model for an agent based on configuration.

    This function uses the centralized model provider system to initialize
    a model based on the agent's configuration.

    Args:
        config: Agent configuration dictionary
        default_provider: Default provider to use if not specified in config
        default_model: Default model to use if not specified in config

    Returns:
        Initialized LLM model
    """
    # Get provider from config or use default
    provider_id = config.get("provider", default_provider).lower()

    # Get model from config or use default
    model_id = config.get("model", default_model)

    # Get model configuration
    model_config = {
        "temperature": config.get("temperature", 0.7),
    }

    # Add any additional configuration
    if "max_tokens" in config:
        model_config["max_tokens"] = config.get("max_tokens")

    if "top_p" in config:
        model_config["top_p"] = config.get("top_p")

    # Initialize the model using the model provider system
    try:
        logger.info(f"Initializing model from provider '{provider_id}', model '{model_id}'")
        model = await get_model(provider_id, model_id, model_config)
        logger.info(f"Successfully initialized model from provider '{provider_id}'")
        return model
    except Exception as e:
        logger.error(f"Error initializing model from provider '{provider_id}': {str(e)}", exc_info=True)
        raise ValueError(f"Failed to initialize model: {str(e)}")


async def initialize_component_model(
    component_config: Dict[str, Any],
    agent_config: Dict[str, Any],
    default_provider: str = "groq",
    default_model: Optional[str] = None
) -> Union[BaseLanguageModel, BaseChatModel]:
    """
    Initialize an LLM model for a component based on configuration.

    This function uses the centralized model provider system to initialize
    a model based on the component's configuration, falling back to the
    agent's configuration if needed.

    Args:
        component_config: Component configuration dictionary
        agent_config: Agent configuration dictionary
        default_provider: Default provider to use if not specified in config
        default_model: Default model to use if not specified in config

    Returns:
        Initialized LLM model
    """
    # First check component config
    provider_id = component_config.get("provider")
    model_id = component_config.get("model")

    # If not in component config, check agent config
    if not provider_id:
        provider_id = agent_config.get("provider", default_provider)

    if not model_id:
        model_id = agent_config.get("model", default_model)

    # Ensure provider_id is lowercase
    provider_id = provider_id.lower()

    # Get model configuration
    model_config = {
        "temperature": component_config.get("temperature", agent_config.get("temperature", 0.7)),
    }

    # Add any additional configuration
    if "max_tokens" in component_config:
        model_config["max_tokens"] = component_config.get("max_tokens")
    elif "max_tokens" in agent_config:
        model_config["max_tokens"] = agent_config.get("max_tokens")

    if "top_p" in component_config:
        model_config["top_p"] = component_config.get("top_p")
    elif "top_p" in agent_config:
        model_config["top_p"] = agent_config.get("top_p")

    # Initialize the model using the model provider system
    try:
        logger.info(f"Initializing component model from provider '{provider_id}', model '{model_id}'")
        model = await get_model(provider_id, model_id, model_config)
        logger.info(f"Successfully initialized component model from provider '{provider_id}'")
        return model
    except Exception as e:
        logger.error(f"Error initializing component model from provider '{provider_id}': {str(e)}", exc_info=True)
        raise ValueError(f"Failed to initialize component model: {str(e)}")
