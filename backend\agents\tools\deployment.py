"""
Deployment workflow compatibility module.

This module provides a compatibility layer for the old DeploymentWorkflow class,
redirecting calls to the new MCP DeploymentTool.
"""

import logging
from typing import Dict, Any, List, Optional

from .mcp.registry import MCPToolRegistry

logger = logging.getLogger(__name__)


class DeploymentWorkflow:
    """
    Compatibility class for the old DeploymentWorkflow.
    
    This class redirects calls to the new MCP DeploymentTool.
    """
    
    @staticmethod
    def create_version(persona_id: str, version: str, config: Dict[str, Any], personas_dir: str = "personas") -> bool:
        """
        Create a new version file for a persona.
        
        Args:
            persona_id: ID of the persona
            version: Version string
            config: Configuration dictionary
            personas_dir: Directory containing persona configurations
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get the MCP tool
            deployment_tool = MCPToolRegistry.get_tool_instance("deployment")
            if not deployment_tool:
                logger.error("DeploymentTool not found in MCP registry")
                return False
                
            # Call the MCP tool
            result = deployment_tool.execute({
                "operation": "create_version",
                "persona_id": persona_id,
                "version": version,
                "config": config,
                "personas_dir": personas_dir
            })
            
            # Check if the operation was successful
            if result.get("isError", False):
                logger.error(f"Error creating version: {result.get('content', [{'text': 'Unknown error'}])[0]['text']}")
                return False
                
            return True
        except Exception as e:
            logger.error(f"Error creating version: {e}", exc_info=True)
            return False
    
    @staticmethod
    def activate_version(persona_id: str, version: str, personas_dir: str = "personas") -> bool:
        """
        Activate a version by copying its configuration to the main persona file.
        
        Args:
            persona_id: ID of the persona
            version: Version string
            personas_dir: Directory containing persona configurations
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get the MCP tool
            deployment_tool = MCPToolRegistry.get_tool_instance("deployment")
            if not deployment_tool:
                logger.error("DeploymentTool not found in MCP registry")
                return False
                
            # Call the MCP tool
            result = deployment_tool.execute({
                "operation": "activate_version",
                "persona_id": persona_id,
                "version": version,
                "personas_dir": personas_dir
            })
            
            # Check if the operation was successful
            if result.get("isError", False):
                logger.error(f"Error activating version: {result.get('content', [{'text': 'Unknown error'}])[0]['text']}")
                return False
                
            return True
        except Exception as e:
            logger.error(f"Error activating version: {e}", exc_info=True)
            return False
    
    @staticmethod
    def rollback_version(persona_id: str, version: str, personas_dir: str = "personas") -> bool:
        """
        Roll back to a previous version.
        
        Args:
            persona_id: ID of the persona
            version: Version string
            personas_dir: Directory containing persona configurations
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get the MCP tool
            deployment_tool = MCPToolRegistry.get_tool_instance("deployment")
            if not deployment_tool:
                logger.error("DeploymentTool not found in MCP registry")
                return False
                
            # Call the MCP tool
            result = deployment_tool.execute({
                "operation": "rollback_version",
                "persona_id": persona_id,
                "version": version,
                "personas_dir": personas_dir
            })
            
            # Check if the operation was successful
            if result.get("isError", False):
                logger.error(f"Error rolling back version: {result.get('content', [{'text': 'Unknown error'}])[0]['text']}")
                return False
                
            return True
        except Exception as e:
            logger.error(f"Error rolling back version: {e}", exc_info=True)
            return False
    
    @staticmethod
    def list_versions(persona_id: str, personas_dir: str = "personas") -> List[str]:
        """
        List all available versions for a persona.
        
        Args:
            persona_id: ID of the persona
            personas_dir: Directory containing persona configurations
            
        Returns:
            List of version strings
        """
        try:
            # Get the MCP tool
            deployment_tool = MCPToolRegistry.get_tool_instance("deployment")
            if not deployment_tool:
                logger.error("DeploymentTool not found in MCP registry")
                return []
                
            # Call the MCP tool
            result = deployment_tool.execute({
                "operation": "list_versions",
                "persona_id": persona_id,
                "personas_dir": personas_dir
            })
            
            # Check if the operation was successful
            if result.get("isError", False):
                logger.error(f"Error listing versions: {result.get('content', [{'text': 'Unknown error'}])[0]['text']}")
                return []
                
            # Extract the versions from the result
            versions = result.get("metadata", {}).get("versions", [])
            return versions
        except Exception as e:
            logger.error(f"Error listing versions: {e}", exc_info=True)
            return []
    
    @staticmethod
    def delete_version(persona_id: str, version: str, personas_dir: str = "personas") -> bool:
        """
        Delete a version file.
        
        Args:
            persona_id: ID of the persona
            version: Version string
            personas_dir: Directory containing persona configurations
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get the MCP tool
            deployment_tool = MCPToolRegistry.get_tool_instance("deployment")
            if not deployment_tool:
                logger.error("DeploymentTool not found in MCP registry")
                return False
                
            # Call the MCP tool
            result = deployment_tool.execute({
                "operation": "delete_version",
                "persona_id": persona_id,
                "version": version,
                "personas_dir": personas_dir
            })
            
            # Check if the operation was successful
            if result.get("isError", False):
                logger.error(f"Error deleting version: {result.get('content', [{'text': 'Unknown error'}])[0]['text']}")
                return False
                
            return True
        except Exception as e:
            logger.error(f"Error deleting version: {e}", exc_info=True)
            return False
    
    @staticmethod
    def compare_versions(persona_id: str, version1: str, version2: str, personas_dir: str = "personas") -> Dict[str, Any]:
        """
        Compare two versions of a persona.
        
        Args:
            persona_id: ID of the persona
            version1: First version string
            version2: Second version string
            personas_dir: Directory containing persona configurations
            
        Returns:
            Dictionary with differences between the versions
        """
        try:
            # Get the MCP tool
            deployment_tool = MCPToolRegistry.get_tool_instance("deployment")
            if not deployment_tool:
                logger.error("DeploymentTool not found in MCP registry")
                return {}
                
            # Call the MCP tool
            result = deployment_tool.execute({
                "operation": "compare_versions",
                "persona_id": persona_id,
                "version": version1,
                "version2": version2,
                "personas_dir": personas_dir
            })
            
            # Check if the operation was successful
            if result.get("isError", False):
                logger.error(f"Error comparing versions: {result.get('content', [{'text': 'Unknown error'}])[0]['text']}")
                return {}
                
            # Extract the differences from the result
            differences = result.get("metadata", {}).get("differences", {})
            return differences
        except Exception as e:
            logger.error(f"Error comparing versions: {e}", exc_info=True)
            return {}
