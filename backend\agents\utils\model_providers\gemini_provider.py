"""
Google Gemini model provider for the Datagenius backend.

This module provides a model provider implementation for Google Gemini.
"""

import logging
import requests
from typing import Dict, Any, List, Union, Optional
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.language_models.chat_models import BaseChatModel


from .base import BaseModelProvider
from .exceptions import ModelInitializationError, ModelNotFoundError
from .config import get_provider_config

# Configure logging
logger = logging.getLogger(__name__)


class GeminiProvider(BaseModelProvider):
    """Model provider implementation for Google Gemini."""

    @property
    def provider_id(self) -> str:
        """Get the provider ID."""
        return "gemini"

    @property
    def provider_name(self) -> str:
        """Get the provider name."""
        return "Google Gemini"

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the provider with configuration.

        Args:
            config: Configuration dictionary for the provider
        """
        await super().initialize(config)

        # Get provider configuration
        provider_config = get_provider_config("gemini")

        # Set default model if not specified
        if not self._default_model_id:
            self._default_model_id = provider_config.get("default_model", "gemini-pro")

        # Set default endpoint if not specified
        if not self._endpoint:
            self._endpoint = provider_config.get("endpoint", "https://generativelanguage.googleapis.com/v1")

        # Set API key from configuration if not specified
        if not self._api_key:
            self._api_key = provider_config.get("api_key", "")

        # Check if we have an API key
        if not self._api_key:
            logger.warning("No Google Gemini API key provided")

    async def _initialize_model(self, model_id: str, config: Dict[str, Any]) -> Union[BaseLanguageModel, BaseChatModel]:
        """
        Initialize a model instance.

        Args:
            model_id: ID of the model to initialize
            config: Configuration for the model

        Returns:
            Initialized model instance

        Raises:
            ModelInitializationError: If there's an error initializing the model
            ModelNotFoundError: If the model is not found
        """
        try:
            # Check if the model exists
            models = await self.list_models()
            model_exists = any(model["id"] == model_id for model in models)

            if not model_exists:
                # If the model doesn't exist but is a known model, we'll try anyway
                known_models = ["gemini-pro", "gemini-pro-vision", "gemini-ultra"]
                if model_id not in known_models:
                    raise ModelNotFoundError(f"Model '{model_id}' not found in Google Gemini")

            # Import here to avoid hard dependencies
            try:
                from langchain_google_genai import ChatGoogleGenerativeAI
            except ImportError:
                logger.warning("langchain-google-genai not installed, attempting to install...")
                import subprocess
                subprocess.check_call(["pip", "install", "langchain-google-genai"])
                from langchain_google_genai import ChatGoogleGenerativeAI

            # Initialize the model
            model = ChatGoogleGenerativeAI(
                temperature=config.get("temperature", 0.7),
                model=model_id,
                google_api_key=self._api_key
            )

            logger.info(f"Initialized Google Gemini model '{model_id}'")
            return model

        except ImportError as e:
            raise ModelInitializationError(f"Error importing Google Gemini: {str(e)}")
        except Exception as e:
            raise ModelInitializationError(f"Error initializing Google Gemini model '{model_id}': {str(e)}")

    async def _fetch_models(self) -> List[Dict[str, Any]]:
        """
        Fetch available models from Google Gemini.

        Returns:
            List of model metadata dictionaries
        """
        # Google Gemini doesn't have a models endpoint, so we return a static list
        return [
            {
                "id": "gemini-pro",
                "name": "Gemini Pro",
                "description": "Google's Gemini Pro model for text generation",
                "context_length": 32768,
                "provider": "gemini"
            },
            {
                "id": "gemini-pro-vision",
                "name": "Gemini Pro Vision",
                "description": "Google's Gemini Pro model with vision capabilities",
                "context_length": 32768,
                "provider": "gemini"
            },
            {
                "id": "gemini-ultra",
                "name": "Gemini Ultra",
                "description": "Google's most powerful Gemini model",
                "context_length": 32768,
                "provider": "gemini"
            }
        ]

    async def is_available(self) -> bool:
        """
        Check if the Google Gemini API is available.

        Returns:
            True if the API is available, False otherwise
        """
        if not self._api_key:
            return False

        try:
            # Make a simple request to check if the API key is valid
            headers = {
                "Content-Type": "application/json"
            }

            # Use a minimal prompt to check if the API is working
            # Use the standard 'gemini-pro' model identifier for the check
            url = f"{self._endpoint}/models/gemini-pro:generateContent?key={self._api_key}"
            data = {
                "contents": [{"parts": [{"text": "Hello"}]}]
            }

            response = requests.post(
                url,
                headers=headers,
                json=data,
                timeout=5
            )

            if response.status_code == 200:
                logger.info("Gemini API check successful.")
                return True
            else:
                # Log failure details
                logger.warning(f"Gemini API check failed with status {response.status_code}. Response: {response.text}")
                return False
        except Exception as e:
            # Log the full exception
            logger.warning(f"Error during Gemini API availability check: {str(e)}", exc_info=True)
            return False
