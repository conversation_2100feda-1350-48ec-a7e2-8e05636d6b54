import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { authApi, LoginRequest, RegisterRequest } from '@/lib/authApi';
import { decodeToken, getTokenTimeRemaining, isTokenExpired, shouldRefreshToken } from '@/utils/jwt';
import IdleTimer from '@/utils/idleTimer';

// Define the User type based on the backend model
export interface User {
  id: number;
  email: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  profile_picture?: string;
  is_active: boolean;
  is_verified: boolean;
  oauth_provider?: string;
  created_at: string;
  last_login?: string;
  bio?: string;
  job_title?: string;
  company?: string;
  website?: string;
  location?: string;
  theme_preference?: string;
}

// Define the auth context type
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => Promise<void>;
  refreshToken: (silent?: boolean) => Promise<boolean>;
  updateIdleTimeout: (timeoutMinutes: number) => void;
  googleLogin: () => void;
  handleGoogleCallback: (code: string, state?: string) => Promise<void>;
}

// Define the register data type
interface RegisterData {
  email: string;
  password: string;
  username?: string;
  first_name?: string;
  last_name?: string;
}

// Create the auth context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => {},
  register: async () => {},
  logout: () => {},
  updateUser: async () => {},
  refreshToken: async () => false,
  updateIdleTimeout: () => {},
  googleLogin: () => {},
  handleGoogleCallback: async () => {},
});

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Create the auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Check if the user is authenticated
  const isAuthenticated = !!user;

  // Reference to the refresh timer
  const refreshTimerRef = useRef<number | null>(null);

  // Reference to the idle timer
  const idleTimerRef = useRef<IdleTimer | null>(null);

  // Default idle timeout (30 minutes)
  const [idleTimeout, setIdleTimeout] = useState<number>(30 * 60 * 1000);

  // Function to set up the token refresh timer
  const setupRefreshTimer = () => {
    console.log('Setting up refresh timer...');
    // Clear any existing timer
    if (refreshTimerRef.current) {
      console.log('Clearing existing refresh timer');
      window.clearTimeout(refreshTimerRef.current);
      refreshTimerRef.current = null;
    }

    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('No token found in localStorage, cannot set up refresh timer');
      return;
    }

    // Decode and log token info
    try {
      const decodedToken = JSON.parse(atob(token.split('.')[1]));
      console.log('Token info:', {
        sub: decodedToken.sub,
        exp: new Date(decodedToken.exp * 1000).toLocaleString(),
        iat: new Date(decodedToken.iat * 1000).toLocaleString(),
      });
    } catch (e) {
      console.error('Error decoding token:', e);
    }

    // Calculate when to refresh (5 minutes before expiration)
    const timeRemaining = getTokenTimeRemaining(token);
    console.log(`Token time remaining: ${Math.round(timeRemaining / 1000)} seconds`);
    const refreshThreshold = 5 * 60 * 1000; // 5 minutes in milliseconds

    if (timeRemaining <= 0) {
      console.warn('Token is already expired, attempting immediate refresh');
      // Token is already expired, try to refresh now
      refreshToken(true).catch((e) => {
        console.error('Immediate token refresh failed:', e);
      });
      return;
    }

    // Schedule refresh for 5 minutes before expiration or halfway through the remaining time,
    // whichever is sooner (but not less than 30 seconds)
    const refreshTime = Math.max(
      Math.min(timeRemaining - refreshThreshold, timeRemaining / 2),
      30 * 1000 // At least 30 seconds from now
    );

    console.log(`Scheduling token refresh in ${Math.round(refreshTime / 1000)} seconds`);

    // Set the timer
    refreshTimerRef.current = window.setTimeout(() => {
      console.log('Executing scheduled token refresh');
      refreshToken(true) // Use silent mode for background refresh
        .then(success => {
          if (success) {
            console.log('Token refreshed successfully');
            // Set up the next refresh timer
            setupRefreshTimer();
          } else {
            console.warn('Token refresh returned false, user may be logged out soon');
          }
        })
        .catch(error => {
          console.error('Scheduled token refresh failed:', error);
        });
    }, refreshTime);
  };

  // Initialize the idle timer
  const setupIdleTimer = () => {
    // Clean up any existing timer
    if (idleTimerRef.current) {
      idleTimerRef.current.stop();
    }

    // Only set up the idle timer if the user is authenticated
    if (isAuthenticated) {
      // Create a new idle timer
      idleTimerRef.current = new IdleTimer(() => {
        // When the user becomes idle, log them out
        toast({
          title: 'Session Expired',
          description: 'You have been logged out due to inactivity.',
          variant: 'destructive',
        });
        logout();
      }, idleTimeout);

      // Start the idle timer
      idleTimerRef.current.start();
    }
  };

  // Function to update the idle timeout
  const updateIdleTimeout = (timeoutMinutes: number) => {
    const timeoutMs = timeoutMinutes * 60 * 1000;
    setIdleTimeout(timeoutMs);

    // Update the idle timer if it exists
    if (idleTimerRef.current) {
      idleTimerRef.current.updateTimeout(timeoutMs);
    }
  };

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      console.log('Initializing auth state...');
      // Clear any existing timers first
      if (refreshTimerRef.current) {
        window.clearTimeout(refreshTimerRef.current);
        refreshTimerRef.current = null;
      }

      const token = localStorage.getItem('token');
      console.log('Token in localStorage:', token ? 'present' : 'not found');

      if (token) {
        try {
          // Check if token is expired
          const isExpired = isTokenExpired(token);
          console.log('Token expired check:', isExpired ? 'expired' : 'valid');

          if (isExpired) {
            console.log('Token is expired, attempting to refresh...');
            const refreshed = await refreshToken(true); // Silent refresh

            if (!refreshed) {
              console.warn('Token refresh failed, clearing auth state');
              // If refresh fails, clear auth state
              localStorage.removeItem('token');
              localStorage.removeItem('refresh_token');
              setUser(null);
              setIsLoading(false);
              console.log('Auth initialization complete - user logged out due to expired token');
              return;
            }

            console.log('Token refreshed successfully during initialization');
            // No need to fetch user data or setup timer here as refreshToken does that
          } else {
            try {
              console.log('Token is valid, fetching user data...');
              // Validate token and get user data
              const userData = await fetchUserData();
              console.log('User data fetched successfully:', userData);
              setUser(userData);

              // Set up the refresh timer
              setupRefreshTimer();
            } catch (error) {
              console.error('Error fetching user data:', error);
              // If token is invalid, try to refresh it
              console.log('Error occurred, attempting to refresh token...');
              const refreshed = await refreshToken(true); // Silent refresh

              if (!refreshed) {
                console.warn('Token refresh failed, clearing auth state');
                // If refresh fails, clear auth state
                localStorage.removeItem('token');
                localStorage.removeItem('refresh_token');
                setUser(null);
              } else {
                console.log('Token refreshed successfully after error');
                // No need to fetch user data or setup timer here as refreshToken does that
              }
            }
          }
        } catch (error) {
          console.error('Error during token validation:', error);
          // If there's an error validating the token, clear auth state
          localStorage.removeItem('token');
          localStorage.removeItem('refresh_token');
          setUser(null);
        }
      } else {
        console.log('No token found, user is not authenticated');
      }

      setIsLoading(false);
      console.log('Auth initialization complete');
    };

    initializeAuth();

    // Clean up the timers when the component unmounts
    return () => {
      if (refreshTimerRef.current) {
        window.clearTimeout(refreshTimerRef.current);
      }
      if (idleTimerRef.current) {
        idleTimerRef.current.stop();
      }
    };
  }, []);

  // Set up or tear down the idle timer when authentication state changes
  useEffect(() => {
    setupIdleTimer();

    return () => {
      if (idleTimerRef.current) {
        idleTimerRef.current.stop();
      }
    };
  }, [isAuthenticated, idleTimeout]);

  // Fetch user data from the API
  const fetchUserData = async (): Promise<User> => {
    console.log('Fetching user data from API...');
    try {
      const token = localStorage.getItem('token');
      console.log('Using token:', token ? token.substring(0, 15) + '...' : 'none');

      const userData = await authApi.getCurrentUser();
      console.log('User data fetched successfully:', userData);
      return userData;
    } catch (error) {
      console.error('Error fetching user data:', error);
      throw new Error('Failed to fetch user data');
    }
  };

  // Login function
  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true);

    try {
      const loginData: LoginRequest = { email, password };
      const data = await authApi.login(loginData);

      // Store tokens in localStorage
      localStorage.setItem('token', data.access_token);
      if (data.refresh_token) {
        localStorage.setItem('refresh_token', data.refresh_token);
      }

      // Fetch user data
      const userData = await fetchUserData();
      setUser(userData);

      // Set up the token refresh timer
      setupRefreshTimer();

      // Show success toast
      toast({
        title: 'Login Successful',
        description: `Welcome back${userData.first_name ? ', ' + userData.first_name : ''}!`,
      });

      // Redirect to dashboard
      navigate('/dashboard');
    } catch (error) {
      toast({
        title: 'Login Failed',
        description: error instanceof Error ? error.message : 'An error occurred during login',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData: RegisterData): Promise<void> => {
    setIsLoading(true);

    try {
      const registerData: RegisterRequest = {
        email: userData.email,
        password: userData.password,
        username: userData.username,
        first_name: userData.first_name,
        last_name: userData.last_name,
      };

      await authApi.register(registerData);

      // Show success toast
      toast({
        title: 'Registration Successful',
        description: 'Your account has been created. Please check your email for verification.',
      });

      // Login the user automatically
      await login(userData.email, userData.password);
    } catch (error) {
      toast({
        title: 'Registration Failed',
        description: error instanceof Error ? error.message : 'An error occurred during registration',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      // Clear the refresh timer
      if (refreshTimerRef.current) {
        window.clearTimeout(refreshTimerRef.current);
        refreshTimerRef.current = null;
      }

      await authApi.logout();

      // Clear local storage
      localStorage.removeItem('token');
      localStorage.removeItem('refresh_token');

      // Clear user state
      setUser(null);

      // Show success toast
      toast({
        title: 'Logged Out',
        description: 'You have been successfully logged out.',
      });

      // Redirect to home page
      navigate('/');
    } catch (error) {
      console.error('Error during logout:', error);

      // Clear the refresh timer
      if (refreshTimerRef.current) {
        window.clearTimeout(refreshTimerRef.current);
        refreshTimerRef.current = null;
      }

      // Even if the API call fails, we should still clear local state
      localStorage.removeItem('token');
      localStorage.removeItem('refresh_token');
      setUser(null);
      navigate('/');
    }
  };

  // Update user function
  const updateUser = async (userData: Partial<User>): Promise<void> => {
    try {
      const updatedUserData = await authApi.updateUser(userData);
      setUser(updatedUserData);

      toast({
        title: 'Profile Updated',
        description: 'Your profile has been successfully updated.',
      });
    } catch (error) {
      toast({
        title: 'Update Failed',
        description: error instanceof Error ? error.message : 'An error occurred while updating your profile',
        variant: 'destructive',
      });
      throw error;
    }
  };

  // Refresh token function
  const refreshToken = async (silent: boolean = false): Promise<boolean> => {
    console.log('Refreshing token...');
    const refresh = localStorage.getItem('refresh_token');

    if (!refresh) {
      console.error('No refresh token found in localStorage');
      return false;
    }

    try {
      console.log('Calling refreshToken API...');
      const data = await authApi.refreshToken(refresh);
      console.log('Token refresh successful, received new tokens');

      // Store new tokens
      localStorage.setItem('token', data.access_token);
      console.log('New access token stored in localStorage');

      if (data.refresh_token) {
        localStorage.setItem('refresh_token', data.refresh_token);
        console.log('New refresh token stored in localStorage');
      } else {
        console.log('No new refresh token received, keeping existing one');
      }

      // Fetch and update user data
      console.log('Fetching user data after token refresh...');
      const userData = await fetchUserData();
      console.log('User data fetched successfully after token refresh');
      setUser(userData);

      // Set up a new refresh timer
      console.log('Setting up new refresh timer after token refresh');
      setupRefreshTimer();

      // If not silent, show a toast notification
      if (!silent) {
        toast({
          title: 'Session Extended',
          description: 'Your session has been refreshed.',
          duration: 3000,
        });
      }

      return true;
    } catch (error) {
      console.error('Error refreshing token:', error);

      // If refresh fails and we're not in silent mode, show an error
      if (!silent) {
        toast({
          title: 'Session Refresh Failed',
          description: 'Failed to refresh your session. You may need to log in again.',
          variant: 'destructive',
          duration: 5000,
        });
      }

      return false;
    }
  };

  // Google login function
  const googleLogin = () => {
    authApi.googleLogin();
  };

  // Handle Google callback function
  const handleGoogleCallback = async (code: string, state?: string): Promise<void> => {
    setIsLoading(true);
    console.log('Google callback initiated with code:', code.substring(0, 10) + '...');

    try {
      // Clear any existing tokens first to prevent conflicts
      localStorage.removeItem('token');
      localStorage.removeItem('refresh_token');
      console.log('Cleared existing tokens from localStorage');

      // Clear any existing refresh timer
      if (refreshTimerRef.current) {
        window.clearTimeout(refreshTimerRef.current);
        refreshTimerRef.current = null;
        console.log('Cleared existing refresh timer');
      }

      console.log('Calling googleCallback API...');
      const data = await authApi.googleCallback(code, state);
      console.log('Received token response:', {
        access_token: data.access_token ? data.access_token.substring(0, 10) + '...' : 'none',
        refresh_token: data.refresh_token ? 'present' : 'none',
        expires_in: data.expires_in
      });

      if (!data.access_token) {
        throw new Error('No access token received from server');
      }

      // Store tokens in localStorage
      localStorage.setItem('token', data.access_token);
      if (data.refresh_token) {
        localStorage.setItem('refresh_token', data.refresh_token);
      }
      console.log('Tokens stored in localStorage');

      // Validate the token we just received
      try {
        const decodedToken = decodeToken(data.access_token);
        if (decodedToken && decodedToken.exp) {
          const expirationTime = new Date(decodedToken.exp * 1000);
          console.log(`Token will expire at: ${expirationTime.toLocaleString()}`);
        } else {
          console.error('Could not decode token or token has no expiration');
        }
      } catch (e) {
        console.error('Error decoding token:', e);
      }

      // Fetch user data
      console.log('Fetching user data...');
      let userData;
      try {
        userData = await fetchUserData();
        console.log('User data received:', userData);
        setUser(userData);
      } catch (e) {
        console.error('Error fetching user data:', e);
        throw new Error('Failed to fetch user data after authentication');
      }

      // Set up the token refresh timer
      console.log('Setting up refresh timer...');
      setupRefreshTimer();

      // Show success toast
      toast({
        title: 'Login Successful',
        description: `Welcome${userData?.first_name ? ', ' + userData.first_name : ''}!`,
        duration: 3000,
      });

      // Navigate to dashboard
      console.log('Navigating to dashboard...');
      navigate('/dashboard');
    } catch (error) {
      console.error('Google login error:', error);

      // Clear any tokens that might have been set
      localStorage.removeItem('token');
      localStorage.removeItem('refresh_token');
      setUser(null);

      toast({
        title: 'Google Login Failed',
        description: error instanceof Error ? error.message : 'An error occurred during Google login',
        variant: 'destructive',
        duration: 5000,
      });

      // Navigate back to login page after a short delay
      setTimeout(() => {
        navigate('/login');
      }, 2000);

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Create the context value
  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    updateUser,
    refreshToken,
    updateIdleTimeout,
    googleLogin,
    handleGoogleCallback,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);
