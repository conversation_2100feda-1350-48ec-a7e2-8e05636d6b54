"""
Data cleaning MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for cleaning data.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, List, Optional

from .base import BaseMCPTool

logger = logging.getLogger(__name__)


class DataCleaningTool(BaseMCPTool):
    """Tool for cleaning data."""

    def __init__(self):
        """Initialize the data cleaning tool."""
        super().__init__(
            name="clean_data",
            description="Clean data by handling missing values and other issues",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "drop_columns": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Columns to drop from the dataset"
                    },
                    "drop_rows": {
                        "type": "boolean",
                        "description": "Whether to drop rows with missing values"
                    },
                    "fill_method": {
                        "type": "string",
                        "enum": ["mean", "median", "mode", "custom"],
                        "description": "Method to use for filling missing values"
                    },
                    "custom_value": {
                        "type": ["string", "number", "null"],
                        "description": "Custom value to use when fill_method is 'custom'"
                    }
                },
                "required": ["file_path"]
            },
            annotations={
                "title": "Clean Data",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.data_dir = "data"

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        if "data_dir" in config:
            self.data_dir = config["data_dir"]
        logger.info(f"Initialized data cleaning tool with data_dir={self.data_dir}")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the data cleaning tool.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            file_path = arguments["file_path"]
            drop_columns = arguments.get("drop_columns", [])
            drop_rows = arguments.get("drop_rows", False)
            fill_method = arguments.get("fill_method", None)
            custom_value = arguments.get("custom_value", None)

            # Check if the path is relative and prepend the data directory
            if not os.path.isabs(file_path):
                file_path = os.path.join(self.data_dir, file_path)

            # Check if the file exists
            if not os.path.exists(file_path):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"File not found: {file_path}"
                        }
                    ]
                }

            # Load the data
            if file_path.endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ]
                }

            # Make a copy of the data to avoid modifying the original
            cleaned_data = df.copy()
            
            # Drop columns if specified
            if drop_columns:
                cleaned_data = cleaned_data.drop(columns=[col for col in drop_columns if col in cleaned_data.columns])
            
            # Drop rows with missing values if specified
            if drop_rows:
                cleaned_data = cleaned_data.dropna(axis=0)
            
            # Fill missing values if specified
            if fill_method:
                if fill_method == "mean":
                    # Only apply to numeric columns
                    numeric_cols = cleaned_data.select_dtypes(include=['number']).columns
                    for col in numeric_cols:
                        cleaned_data[col] = cleaned_data[col].fillna(cleaned_data[col].mean())
                elif fill_method == "median":
                    # Only apply to numeric columns
                    numeric_cols = cleaned_data.select_dtypes(include=['number']).columns
                    for col in numeric_cols:
                        cleaned_data[col] = cleaned_data[col].fillna(cleaned_data[col].median())
                elif fill_method == "mode":
                    # Apply to all columns
                    for col in cleaned_data.columns:
                        cleaned_data[col] = cleaned_data[col].fillna(cleaned_data[col].mode()[0] if not cleaned_data[col].mode().empty else None)
                elif fill_method == "custom" and custom_value is not None:
                    cleaned_data = cleaned_data.fillna(custom_value)
            
            # Generate a summary of the cleaning operation
            summary = {
                "original_shape": df.shape,
                "cleaned_shape": cleaned_data.shape,
                "dropped_columns": drop_columns,
                "dropped_rows": drop_rows,
                "fill_method": fill_method,
                "missing_values_before": df.isnull().sum().sum(),
                "missing_values_after": cleaned_data.isnull().sum().sum()
            }
            
            # Save the cleaned data to a new file
            output_path = file_path.replace(".", "_cleaned.")
            if file_path.endswith(".csv"):
                cleaned_data.to_csv(output_path, index=False)
            elif file_path.endswith((".xls", ".xlsx")):
                cleaned_data.to_excel(output_path, index=False)
            elif file_path.endswith(".json"):
                cleaned_data.to_json(output_path, orient="records")
            
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Data cleaning completed successfully.\n\nSummary:\n- Original shape: {summary['original_shape']}\n- Cleaned shape: {summary['cleaned_shape']}\n- Missing values before: {summary['missing_values_before']}\n- Missing values after: {summary['missing_values_after']}\n\nCleaned data saved to: {output_path}"
                    }
                ],
                "metadata": {
                    "summary": summary,
                    "output_path": output_path
                }
            }
            
        except Exception as e:
            logger.error(f"Error cleaning data: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error cleaning data: {str(e)}"
                    }
                ]
            }
