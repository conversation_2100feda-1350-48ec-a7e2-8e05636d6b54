import { apiRequest } from './api';

// Types
export interface AdminDashboardStats {
  total_users: number;
  active_users: number;
  total_personas: number;
  active_personas: number;
  total_purchases: number;
  total_revenue: number;
  recent_purchases: number;
  recent_revenue: number;
}

export interface AdminPersona {
  id: string;
  name: string;
  description: string;
  industry: string;
  skills: string[];
  rating: number;
  review_count: number;
  image_url: string;
  price: number;
  provider: string;
  model?: string;
  is_active: boolean;
  age_restriction: number;
  content_filters?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface AdminUser {
  id: number;
  email: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  is_active: boolean;
  is_verified: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
}

export interface AdminActivityLog {
  id: number;
  admin_id: number;
  action: string;
  entity_type: string;
  entity_id?: string;
  details?: Record<string, any>;
  created_at: string;
}

export interface AdminAnalyticsDataPoint {
  date: string;
  value: number;
}

export interface AdminAnalyticsResponse {
  data: AdminAnalyticsDataPoint[];
  total: number;
  average: number;
  min: number;
  max: number;
}

export interface AdminAnalyticsRequest {
  timeframe: {
    start_date: string;
    end_date: string;
  };
  group_by?: string;
}

// Admin API functions
export const adminApi = {
  // Dashboard
  getDashboardStats: async (): Promise<AdminDashboardStats> => {
    return apiRequest('/admin/dashboard/stats');
  },

  // Personas
  getPersonas: async (params?: { skip?: number; limit?: number; industry?: string; is_active?: boolean }): Promise<{ personas: AdminPersona[]; total: number }> => {
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.industry) queryParams.append('industry', params.industry);
    if (params?.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());
    
    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    return apiRequest(`/admin/personas${queryString}`);
  },

  getPersona: async (personaId: string): Promise<{ persona: AdminPersona }> => {
    return apiRequest(`/admin/personas/${personaId}`);
  },

  createPersona: async (personaData: Omit<AdminPersona, 'created_at' | 'updated_at'>): Promise<{ persona: AdminPersona }> => {
    return apiRequest('/admin/personas', {
      method: 'POST',
      body: JSON.stringify(personaData),
    });
  },

  updatePersona: async (personaId: string, personaData: Partial<AdminPersona>): Promise<{ persona: AdminPersona }> => {
    return apiRequest(`/admin/personas/${personaId}`, {
      method: 'PUT',
      body: JSON.stringify(personaData),
    });
  },

  deletePersona: async (personaId: string): Promise<{ message: string }> => {
    return apiRequest(`/admin/personas/${personaId}`, {
      method: 'DELETE',
    });
  },

  // Users
  getUsers: async (params?: { skip?: number; limit?: number; search?: string; is_active?: boolean; is_superuser?: boolean }): Promise<{ users: AdminUser[]; total: number }> => {
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());
    if (params?.is_superuser !== undefined) queryParams.append('is_superuser', params.is_superuser.toString());
    
    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    return apiRequest(`/admin/users${queryString}`);
  },

  getUser: async (userId: number): Promise<AdminUser> => {
    return apiRequest(`/admin/users/${userId}`);
  },

  updateUser: async (userId: number, userData: Partial<AdminUser>): Promise<AdminUser> => {
    return apiRequest(`/admin/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  },

  // Activity Logs
  getActivityLogs: async (params?: { skip?: number; limit?: number; action?: string; entity_type?: string; admin_id?: number }): Promise<{ logs: AdminActivityLog[]; total: number }> => {
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.action) queryParams.append('action', params.action);
    if (params?.entity_type) queryParams.append('entity_type', params.entity_type);
    if (params?.admin_id) queryParams.append('admin_id', params.admin_id.toString());
    
    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    return apiRequest(`/admin/activity-logs${queryString}`);
  },

  // Analytics
  getPurchaseAnalytics: async (request: AdminAnalyticsRequest): Promise<AdminAnalyticsResponse> => {
    return apiRequest('/admin/analytics/purchases', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },

  getUserAnalytics: async (request: AdminAnalyticsRequest): Promise<AdminAnalyticsResponse> => {
    return apiRequest('/admin/analytics/users', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },

  getPersonaAnalytics: async (request: AdminAnalyticsRequest): Promise<AdminAnalyticsResponse> => {
    return apiRequest('/admin/analytics/personas', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },
};
