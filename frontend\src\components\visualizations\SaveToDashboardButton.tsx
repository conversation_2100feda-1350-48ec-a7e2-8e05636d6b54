import { useState } from 'react';
import { Save, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { VisualizationData } from '@/utils/visualization';
import { useDashboardStore } from '@/stores/dashboard-store';

interface SaveToDashboardButtonProps {
  visualization: VisualizationData;
  className?: string;
}

export const SaveToDashboardButton = ({ visualization, className = '' }: SaveToDashboardButtonProps) => {
  const { toast } = useToast();
  const [isSaved, setIsSaved] = useState(false);
  const { addWidget } = useDashboardStore();

  const handleSaveToDashboard = () => {
    try {
      // Add the visualization to the dashboard
      addWidget({
        id: `widget-${Date.now()}`,
        type: visualization.type,
        title: visualization.title || 'Visualization',
        data: visualization.data,
        config: visualization.config || {},
        position: { x: 0, y: 0, w: 2, h: 2 }
      });

      // Show success state
      setIsSaved(true);
      toast({
        title: 'Saved to dashboard',
        description: 'Visualization has been added to your dashboard',
      });

      // Reset after 2 seconds
      setTimeout(() => {
        setIsSaved(false);
      }, 2000);
    } catch (err) {
      console.error('Failed to save to dashboard: ', err);
      toast({
        title: 'Save failed',
        description: 'Failed to add visualization to dashboard',
        variant: 'destructive',
      });
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleSaveToDashboard}
      className={`flex items-center gap-1 bg-white ${className} ${
        isSaved ? 'text-green-600 border-green-200' : ''
      }`}
    >
      {isSaved ? <Check className="h-4 w-4" /> : <Save className="h-4 w-4" />}
      {isSaved ? 'Saved' : 'Save to Dashboard'}
    </Button>
  );
};
