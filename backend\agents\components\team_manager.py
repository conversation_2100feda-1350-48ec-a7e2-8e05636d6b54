"""
Component for managing hierarchical agent teams.
"""

import logging
import json
import time
import uuid
from typing import Dict, Any, List, Optional, Tuple, Set

from .base import AgentComponent
from ..registry import AgentRegistry

logger = logging.getLogger(__name__)


class TeamRole:
    """Defines a role within a hierarchical agent team."""

    MANAGER = "manager"
    SPECIALIST = "specialist"
    ASSISTANT = "assistant"
    COORDINATOR = "coordinator"
    EXECUTOR = "executor"
    REVIEWER = "reviewer"
    FALLBACK = "fallback"


class TeamManagerComponent(AgentComponent):
    """
    Manages hierarchical agent teams, enabling complex workflows with specialized roles,
    delegation, oversight, and fallback mechanisms.
    """

    def __init__(self):
        """Initialize the TeamManagerComponent."""
        super().__init__()
        self.teams = {}  # Store team configurations
        self.active_teams = {}  # Track active teams by conversation
        self.role_assignments = {}  # Track role assignments
        self.task_registry = {}  # Track tasks assigned to team members

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"TeamManagerComponent '{self.name}' initialized.")
        self.agent_registry = AgentRegistry
        self.team_ttl = config.get("team_ttl", 3600)  # Default TTL: 1 hour
        self.max_team_size = config.get("max_team_size", 5)
        self.enable_auto_team_formation = config.get("enable_auto_team_formation", True)
        self.team_formation_threshold = config.get("team_formation_threshold", 0.7)

        # Load team templates from configuration
        self.team_templates = config.get("team_templates", {})
        if self.team_templates:
            logger.info(f"Loaded {len(self.team_templates)} team templates")

        # Load role capabilities from configuration
        self.role_capabilities = config.get("role_capabilities", {
            TeamRole.MANAGER: ["oversight", "delegation", "planning"],
            TeamRole.SPECIALIST: ["domain_expertise", "specialized_tasks"],
            TeamRole.ASSISTANT: ["support", "information_gathering"],
            TeamRole.COORDINATOR: ["coordination", "communication"],
            TeamRole.EXECUTOR: ["task_execution", "implementation"],
            TeamRole.REVIEWER: ["quality_control", "verification"],
            TeamRole.FALLBACK: ["error_handling", "recovery"]
        })

        # Load persona role mappings from configuration
        self.persona_role_mappings = config.get("persona_role_mappings", {
            "concierge-agent": [TeamRole.MANAGER, TeamRole.COORDINATOR],
            "composable-analysis-ai": [TeamRole.SPECIALIST, TeamRole.EXECUTOR],
            "composable-marketing-ai": [TeamRole.SPECIALIST, TeamRole.EXECUTOR],
            "composable-classifier-ai": [TeamRole.SPECIALIST, TeamRole.ASSISTANT]
        })

        # Initialize cleanup timer
        self._schedule_cleanup()

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the context to manage hierarchical agent teams.

        Args:
            context: Context dictionary containing request data.

        Returns:
            Updated context dictionary with team management capabilities.
        """
        user_message = context.get("message", "")
        conversation_id = context.get("conversation_id")
        current_persona = context.get("persona_id", "unknown")

        logger.debug(f"TeamManagerComponent processing for conversation {conversation_id}")

        # Initialize team context if not present
        if "team" not in context:
            context["team"] = {
                "team_id": None,
                "role": None,
                "tasks": [],
                "hierarchy": {},
                "team_members": []
            }

        # Check if this conversation has an active team
        active_team = self._get_active_team(conversation_id)
        if active_team:
            # Update context with team information
            context["team"]["team_id"] = active_team["team_id"]
            context["team"]["role"] = self._get_persona_role(current_persona, active_team)
            context["team"]["tasks"] = self._get_persona_tasks(conversation_id, current_persona)
            context["team"]["hierarchy"] = active_team["hierarchy"]
            context["team"]["team_members"] = active_team["members"]

            # Add team information to metadata
            context["metadata"] = context.get("metadata", {})
            context["metadata"]["team"] = {
                "team_id": active_team["team_id"],
                "role": context["team"]["role"],
                "team_name": active_team["name"]
            }

            logger.debug(f"Updated context with active team {active_team['team_id']} for conversation {conversation_id}")

        # Check for team formation commands
        if self._is_team_formation_command(user_message):
            # Extract team formation parameters
            team_template, task_description = self._extract_team_formation_info(user_message)
            if team_template:
                # Form a team
                team_id = await self._form_team(conversation_id, current_persona, team_template, task_description)

                # Update context with the new team
                active_team = self._get_active_team(conversation_id)
                if active_team:
                    context["team"]["team_id"] = active_team["team_id"]
                    context["team"]["role"] = self._get_persona_role(current_persona, active_team)
                    context["team"]["hierarchy"] = active_team["hierarchy"]
                    context["team"]["team_members"] = active_team["members"]

                    # Add team formation information to metadata
                    context["metadata"] = context.get("metadata", {})
                    context["metadata"]["team_formation"] = {
                        "success": True,
                        "team_id": team_id,
                        "team_name": active_team["name"],
                        "message": f"I've formed a team to help with {task_description}."
                    }

                    logger.info(f"Formed team {team_id} for conversation {conversation_id}")
                else:
                    # Team formation failed
                    context["metadata"] = context.get("metadata", {})
                    context["metadata"]["team_formation"] = {
                        "success": False,
                        "message": "I couldn't form a team with the specified parameters."
                    }

                    logger.warning(f"Team formation failed for conversation {conversation_id}")
            else:
                logger.warning(f"Team formation command detected but no template found: {user_message}")

        # Check for task assignment commands
        if self._is_task_assignment_command(user_message):
            # Extract task assignment parameters
            target_persona, task_description = self._extract_task_assignment_info(user_message)
            if target_persona and active_team:
                # Assign the task
                task_id = await self._assign_task(conversation_id, current_persona, target_persona, task_description)

                # Add task assignment information to metadata
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["task_assignment"] = {
                    "success": True,
                    "task_id": task_id,
                    "target_persona": target_persona,
                    "message": f"I've assigned the task to {target_persona}."
                }

                logger.info(f"Assigned task {task_id} to {target_persona} in conversation {conversation_id}")
            else:
                # Task assignment failed
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["task_assignment"] = {
                    "success": False,
                    "message": "I couldn't assign the task. Please make sure a team is formed and the target persona is valid."
                }

                logger.warning(f"Task assignment failed for conversation {conversation_id}")

        # Check for task status update commands
        if self._is_task_status_update_command(user_message):
            # Extract task status update parameters
            task_id, status, result = self._extract_task_status_update_info(user_message)
            if task_id and active_team:
                # Update the task status
                success = await self._update_task_status(conversation_id, current_persona, task_id, status, result)

                # Add task status update information to metadata
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["task_status_update"] = {
                    "success": success,
                    "task_id": task_id,
                    "status": status,
                    "message": f"Task status updated to {status}."
                }

                logger.info(f"Updated task {task_id} status to {status} in conversation {conversation_id}")
            else:
                # Task status update failed
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["task_status_update"] = {
                    "success": False,
                    "message": "I couldn't update the task status. Please make sure a team is formed and the task ID is valid."
                }

                logger.warning(f"Task status update failed for conversation {conversation_id}")

        # Check for automatic team formation
        if self.enable_auto_team_formation and not active_team and not self._is_team_formation_command(user_message):
            team_needed, template_name, reason = self._detect_team_need(user_message, context)
            if team_needed:
                # Add automatic team formation suggestion to metadata
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["auto_team_suggestion"] = {
                    "template": template_name,
                    "reason": reason,
                    "message": f"This task might benefit from a team approach. Would you like me to form a {template_name} team to help with this?"
                }

                logger.info(f"Automatic team formation suggestion for {template_name} in conversation {conversation_id}")

        return context

    def _is_team_formation_command(self, message: str) -> bool:
        """
        Determine if the message is a command to form a team.

        Args:
            message: The user message.

        Returns:
            True if this is a team formation command, False otherwise.
        """
        # Simple keyword-based detection
        formation_keywords = [
            "form a team", "create a team", "assemble a team",
            "put together a team", "organize a team", "build a team"
        ]

        return any(keyword in message.lower() for keyword in formation_keywords)

    def _extract_team_formation_info(self, message: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract the team template and task description from a team formation command.

        Args:
            message: The user message.

        Returns:
            A tuple containing the team template name and the task description.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Check for template names
        template_name = None
        for name in self.team_templates.keys():
            if name.lower() in message_lower:
                template_name = name
                break

        # If no specific template is mentioned, use a default template
        if not template_name and self.team_templates:
            template_name = list(self.team_templates.keys())[0]

        # Extract task description (simple heuristic - everything after "for" or "to")
        task_description = None
        if "for" in message_lower:
            task_description = message_lower.split("for", 1)[1].strip()
        elif "to" in message_lower:
            task_description = message_lower.split("to", 1)[1].strip()
        else:
            # Default task description
            task_description = message_lower

        return template_name, task_description

    async def _form_team(self, conversation_id: str, initiator_persona: str, template_name: str, task_description: str) -> str:
        """
        Form a team based on a template.

        Args:
            conversation_id: The ID of the conversation.
            initiator_persona: The persona initiating the team formation.
            template_name: The name of the team template to use.
            task_description: The description of the task for the team.

        Returns:
            The ID of the formed team.
        """
        # Generate a unique team ID
        team_id = str(uuid.uuid4())

        # Get the template
        template = self.team_templates.get(template_name, {
            "roles": {
                TeamRole.MANAGER: 1,
                TeamRole.SPECIALIST: 2,
                TeamRole.ASSISTANT: 1
            },
            "hierarchy": {
                TeamRole.MANAGER: [TeamRole.SPECIALIST, TeamRole.ASSISTANT],
                TeamRole.SPECIALIST: [],
                TeamRole.ASSISTANT: []
            }
        })

        # Create the team structure
        team = {
            "team_id": team_id,
            "name": f"{template_name.capitalize()} Team",
            "initiator": initiator_persona,
            "created_at": time.time(),
            "task_description": task_description,
            "members": [],
            "roles": template["roles"].copy(),
            "hierarchy": template["hierarchy"].copy(),
            "conversation_id": conversation_id
        }

        # Assign the initiator as the manager by default
        initiator_role = self._get_best_role_for_persona(initiator_persona, template["roles"].keys())
        team["members"].append({
            "persona_id": initiator_persona,
            "role": initiator_role,
            "joined_at": time.time()
        })

        # Reduce the count for the assigned role
        if initiator_role in team["roles"]:
            team["roles"][initiator_role] -= 1
            if team["roles"][initiator_role] <= 0:
                del team["roles"][initiator_role]

        # Store the team
        self.teams[team_id] = team

        # Associate the team with the conversation
        if conversation_id not in self.active_teams:
            self.active_teams[conversation_id] = []

        self.active_teams[conversation_id].append(team_id)

        logger.info(f"Formed team {team_id} for conversation {conversation_id}")

        return team_id

    def _get_best_role_for_persona(self, persona_id: str, available_roles: Set[str]) -> str:
        """
        Determine the best role for a persona based on its capabilities.

        Args:
            persona_id: The ID of the persona.
            available_roles: The set of available roles.

        Returns:
            The best role for the persona.
        """
        # Get the roles this persona can fulfill
        persona_roles = self.persona_role_mappings.get(persona_id, [])

        # Find the first role that matches both the persona's capabilities and available roles
        for role in persona_roles:
            if role in available_roles:
                return role

        # If no match is found, return a default role
        for role in available_roles:
            return role

        # If no roles are available, return the manager role
        return TeamRole.MANAGER

    def _get_active_team(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the active team for a conversation.

        Args:
            conversation_id: The ID of the conversation.

        Returns:
            The active team if found, None otherwise.
        """
        # Get the team IDs for this conversation
        team_ids = self.active_teams.get(conversation_id, [])
        if not team_ids:
            return None

        # Get the most recently created team
        team_id = team_ids[-1]
        team = self.teams.get(team_id)

        return team

    def _get_persona_role(self, persona_id: str, team: Dict[str, Any]) -> Optional[str]:
        """
        Get the role of a persona in a team.

        Args:
            persona_id: The ID of the persona.
            team: The team dictionary.

        Returns:
            The role of the persona if found, None otherwise.
        """
        for member in team["members"]:
            if member["persona_id"] == persona_id:
                return member["role"]

        return None

    def _is_task_assignment_command(self, message: str) -> bool:
        """
        Determine if the message is a command to assign a task.

        Args:
            message: The user message.

        Returns:
            True if this is a task assignment command, False otherwise.
        """
        # Simple keyword-based detection
        assignment_keywords = [
            "assign task", "give task", "delegate task",
            "assign to", "give to", "delegate to"
        ]

        return any(keyword in message.lower() for keyword in assignment_keywords)

    def _extract_task_assignment_info(self, message: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract the target persona and task description from a task assignment command.

        Args:
            message: The user message.

        Returns:
            A tuple containing the target persona and the task description.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Check for persona names
        personas = ["analyst", "marketer", "classifier", "concierge", "analysis", "marketing", "classification"]
        target_persona = None

        for persona in personas:
            if persona in message_lower:
                target_persona = persona
                break

        if not target_persona:
            return None, None

        # Extract task description (simple heuristic - everything after "to" or ":")
        task_description = None
        if "to" in message_lower:
            parts = message_lower.split("to", 1)
            if len(parts) > 1 and target_persona in parts[0]:
                task_description = parts[1].strip()
            else:
                task_description = message_lower
        elif ":" in message_lower:
            task_description = message_lower.split(":", 1)[1].strip()
        else:
            # Default task description
            task_description = message_lower

        return target_persona, task_description

    async def _assign_task(self, conversation_id: str, assigner_persona: str, target_persona: str, task_description: str) -> str:
        """
        Assign a task to a team member.

        Args:
            conversation_id: The ID of the conversation.
            assigner_persona: The persona assigning the task.
            target_persona: The persona to assign the task to.
            task_description: The description of the task.

        Returns:
            The ID of the assigned task.
        """
        # Generate a unique task ID
        task_id = str(uuid.uuid4())

        # Get the active team
        team = self._get_active_team(conversation_id)
        if not team:
            logger.warning(f"Cannot assign task: No active team for conversation {conversation_id}")
            return ""

        # Check if the target persona is a member of the team
        target_is_member = False
        for member in team["members"]:
            if member["persona_id"] == target_persona:
                target_is_member = True
                break

        # If the target is not a member, try to add them
        if not target_is_member:
            # Find an available role for the target
            available_roles = set(team["roles"].keys())
            if available_roles:
                target_role = self._get_best_role_for_persona(target_persona, available_roles)

                # Add the target to the team
                team["members"].append({
                    "persona_id": target_persona,
                    "role": target_role,
                    "joined_at": time.time()
                })

                # Reduce the count for the assigned role
                if target_role in team["roles"]:
                    team["roles"][target_role] -= 1
                    if team["roles"][target_role] <= 0:
                        del team["roles"][target_role]

                logger.info(f"Added {target_persona} to team {team['team_id']} with role {target_role}")
            else:
                logger.warning(f"Cannot assign task: No available roles for {target_persona} in team {team['team_id']}")
                return ""

        # Create the task
        task = {
            "task_id": task_id,
            "team_id": team["team_id"],
            "assigner": assigner_persona,
            "assignee": target_persona,
            "description": task_description,
            "status": "assigned",
            "created_at": time.time(),
            "updated_at": time.time(),
            "result": None,
            "conversation_id": conversation_id
        }

        # Store the task
        if conversation_id not in self.task_registry:
            self.task_registry[conversation_id] = {}

        self.task_registry[conversation_id][task_id] = task

        logger.info(f"Assigned task {task_id} to {target_persona} in conversation {conversation_id}")

        return task_id

    def _get_persona_tasks(self, conversation_id: str, persona_id: str) -> List[Dict[str, Any]]:
        """
        Get the tasks assigned to a persona in a conversation.

        Args:
            conversation_id: The ID of the conversation.
            persona_id: The ID of the persona.

        Returns:
            A list of tasks assigned to the persona.
        """
        tasks = []

        # Get the tasks for this conversation
        conversation_tasks = self.task_registry.get(conversation_id, {})

        # Filter for tasks assigned to this persona
        for task_id, task in conversation_tasks.items():
            if task["assignee"] == persona_id:
                tasks.append(task)

        return tasks

    def _is_task_status_update_command(self, message: str) -> bool:
        """
        Determine if the message is a command to update a task status.

        Args:
            message: The user message.

        Returns:
            True if this is a task status update command, False otherwise.
        """
        # Simple keyword-based detection
        status_keywords = [
            "task complete", "completed task", "finished task",
            "task status", "update task", "task update"
        ]

        return any(keyword in message.lower() for keyword in status_keywords)

    def _extract_task_status_update_info(self, message: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        Extract the task ID, status, and result from a task status update command.

        Args:
            message: The user message.

        Returns:
            A tuple containing the task ID, status, and result.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Extract task ID (simple heuristic - look for "task" followed by an ID-like string)
        task_id = None
        if "task" in message_lower:
            parts = message_lower.split("task", 1)[1].strip().split()
            if parts:
                task_id = parts[0]

        # If no task ID is found, return None
        if not task_id:
            return None, None, None

        # Determine status
        status = "in_progress"
        if "complete" in message_lower or "completed" in message_lower or "finished" in message_lower:
            status = "completed"
        elif "failed" in message_lower or "failure" in message_lower or "error" in message_lower:
            status = "failed"

        # Extract result (simple heuristic - everything after "result" or ":")
        result = None
        if "result" in message_lower:
            result = message_lower.split("result", 1)[1].strip()
        elif ":" in message_lower:
            result = message_lower.split(":", 1)[1].strip()
        else:
            # Default result
            result = message_lower

        return task_id, status, result

    async def _update_task_status(self, conversation_id: str, updater_persona: str, task_id: str, status: str, result: Optional[str]) -> bool:
        """
        Update the status of a task.

        Args:
            conversation_id: The ID of the conversation.
            updater_persona: The persona updating the task status.
            task_id: The ID of the task.
            status: The new status of the task.
            result: The result of the task.

        Returns:
            True if the update was successful, False otherwise.
        """
        # Get the task
        if conversation_id not in self.task_registry or task_id not in self.task_registry[conversation_id]:
            logger.warning(f"Cannot update task: Task {task_id} not found in conversation {conversation_id}")
            return False

        task = self.task_registry[conversation_id][task_id]

        # Check if the updater is the assignee or the assigner
        if updater_persona != task["assignee"] and updater_persona != task["assigner"]:
            logger.warning(f"Cannot update task: {updater_persona} is not the assignee or assigner of task {task_id}")
            return False

        # Update the task
        task["status"] = status
        task["updated_at"] = time.time()
        if result:
            task["result"] = result

        # Store the updated task
        self.task_registry[conversation_id][task_id] = task

        logger.info(f"Updated task {task_id} status to {status} in conversation {conversation_id}")

        return True

    def _detect_team_need(self, message: str, context: Dict[str, Any]) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Detect if a team approach would be beneficial based on the message content.

        Args:
            message: The user message.
            context: The current context.

        Returns:
            A tuple containing a boolean indicating if a team is needed, the template name, and the reason.
        """
        # Simple rule-based detection - could be enhanced with NLP
        message_lower = message.lower()

        # Check for complex task indicators
        complex_task_keywords = [
            "complex", "complicated", "multi-step", "multiple", "comprehensive",
            "detailed", "collaborative", "team", "project", "campaign"
        ]
        is_complex_task = any(keyword in message_lower for keyword in complex_task_keywords)

        # Check for multi-domain indicators
        domains = []

        # Analysis domain
        analysis_keywords = ["analyze", "analysis", "data", "chart", "graph", "visualization"]
        if any(keyword in message_lower for keyword in analysis_keywords):
            domains.append("analysis")

        # Marketing domain
        marketing_keywords = ["marketing", "campaign", "content", "social media", "advertisement"]
        if any(keyword in message_lower for keyword in marketing_keywords):
            domains.append("marketing")

        # Classification domain
        classification_keywords = ["classify", "classification", "categorize", "sort", "group"]
        if any(keyword in message_lower for keyword in classification_keywords):
            domains.append("classification")

        # Determine if a team is needed
        if is_complex_task and len(domains) > 1:
            # Complex task spanning multiple domains
            return True, "cross_functional", f"complex task spanning {', '.join(domains)}"
        elif is_complex_task:
            # Complex task in a single domain
            return True, "specialized", f"complex {domains[0] if domains else 'task'}"
        elif len(domains) > 1:
            # Simple task spanning multiple domains
            return True, "collaborative", f"task spanning {', '.join(domains)}"

        return False, None, None

    def _schedule_cleanup(self) -> None:
        """Schedule periodic cleanup of expired teams and tasks."""
        # This would typically use a background task or timer
        # For simplicity, we'll just log that it would happen
        logger.info("Team cleanup scheduled")

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", [
            "team_management",
            "hierarchical_teams",
            "role_assignment",
            "task_delegation"
        ])
