"""
Diagnose issues with personas not showing up in Data Chat.

This script checks various components of the persona system to identify issues.
"""

import os
import sys
import logging
import json

# Add the parent directory to sys.path to allow importing from app
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def diagnose_persona_issue(user_email: str, persona_id: str):
    """
    Diagnose issues with a specific persona for a user.
    
    Args:
        user_email: Email of the user
        persona_id: ID of the persona to diagnose
    """
    logger.info(f"Diagnosing persona issue for user {user_email}, persona {persona_id}")
    
    try:
        # Import necessary modules
        from sqlalchemy.orm import Session
        from app.database import get_db, get_user_by_email, get_persona
        from app.database import has_user_purchased_persona, get_user_purchased_personas
        from agents.registry import AgentRegistry
        
        # Get database session
        db = next(get_db())
        
        # Check if the user exists
        user = get_user_by_email(db, user_email)
        if not user:
            logger.error(f"User with email {user_email} not found")
            return
        logger.info(f"Found user: id={user.id}, email={user.email}")
        
        # Check if the persona exists in the database
        db_persona = get_persona(db, persona_id)
        if db_persona:
            logger.info(f"Persona found in database: id={db_persona.id}, name={db_persona.name}")
            logger.info(f"Persona details: provider={db_persona.provider}, model={db_persona.model}, is_active={db_persona.is_active}")
        else:
            logger.error(f"Persona {persona_id} not found in database")
        
        # Check if the user has purchased the persona
        is_purchased = has_user_purchased_persona(db, user.id, persona_id)
        logger.info(f"Has user purchased persona: {is_purchased}")
        
        # Get all purchased personas for the user
        purchased_personas = get_user_purchased_personas(db, user.id)
        logger.info(f"All purchased personas for user: {purchased_personas}")
        
        # Check purchase records
        from sqlalchemy import text
        result = db.execute(text(f"""
            SELECT p.id, p.payment_status, p.created_at, pi.persona_id
            FROM purchases p
            JOIN purchased_items pi ON p.id = pi.purchase_id
            WHERE p.user_id = {user.id} AND pi.persona_id = '{persona_id}'
        """))
        purchases = [dict(row._mapping) for row in result]
        logger.info(f"Purchase records for persona: {json.dumps(purchases, default=str)}")
        
        # Check if the persona is registered in the agent registry
        config = AgentRegistry.get_configuration(persona_id)
        if config:
            logger.info(f"Persona configuration found in registry: {json.dumps(config)}")
        else:
            logger.error(f"Persona configuration not found in registry")
        
        # Check if the agent class is registered
        agent_class = AgentRegistry.get_agent_class(persona_id)
        if agent_class:
            logger.info(f"Agent class found in registry: {agent_class.__name__}")
            
            # Try to instantiate the agent
            try:
                agent = agent_class()
                logger.info(f"Successfully instantiated agent: {agent}")
            except Exception as e:
                logger.error(f"Error instantiating agent: {str(e)}")
        else:
            logger.error(f"Agent class not found in registry")
        
        # Check registered personas in the registry
        registered_personas = AgentRegistry.list_registered_personas()
        logger.info(f"All registered personas: {registered_personas}")
        
        # Check persona files
        personas_dir = os.path.join(parent_dir, "personas")
        if os.path.exists(personas_dir):
            persona_files = os.listdir(personas_dir)
            logger.info(f"Persona files in {personas_dir}: {persona_files}")
        else:
            logger.error(f"Personas directory not found: {personas_dir}")
            
        # Check agent modules
        agents_dir = os.path.join(parent_dir, "agents")
        if os.path.exists(agents_dir):
            agent_files = [f for f in os.listdir(agents_dir) if f.endswith(".py") or os.path.isdir(os.path.join(agents_dir, f))]
            logger.info(f"Agent files in {agents_dir}: {agent_files}")
            
            # Check for marketing agent specifically
            marketing_dir = os.path.join(agents_dir, "marketing")
            if os.path.exists(marketing_dir) and os.path.isdir(marketing_dir):
                marketing_files = os.listdir(marketing_dir)
                logger.info(f"Marketing agent files: {marketing_files}")
            else:
                logger.warning(f"Marketing agent directory not found: {marketing_dir}")
        else:
            logger.error(f"Agents directory not found: {agents_dir}")
            
    except Exception as e:
        logger.error(f"Error diagnosing persona issue: {str(e)}", exc_info=True)

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python diagnose_persona_issue.py <user_email> <persona_id>")
        sys.exit(1)
        
    user_email = sys.argv[1]
    persona_id = sys.argv[2]
    
    diagnose_persona_issue(user_email, persona_id)
