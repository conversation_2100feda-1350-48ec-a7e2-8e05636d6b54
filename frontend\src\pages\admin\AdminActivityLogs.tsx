import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { adminApi, AdminActivityLog } from '@/lib/adminApi';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Search,
  Loader2,
  AlertCircle,
  Info,
  RefreshCw,
  Eye,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

const AdminActivityLogs = () => {
  const [actionFilter, setActionFilter] = useState<string>('');
  const [entityTypeFilter, setEntityTypeFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedLog, setSelectedLog] = useState<AdminActivityLog | null>(null);
  const pageSize = 20;

  // Fetch activity logs
  const {
    data,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['adminActivityLogs', currentPage, actionFilter, entityTypeFilter],
    queryFn: () => adminApi.getActivityLogs({
      skip: (currentPage - 1) * pageSize,
      limit: pageSize,
      action: actionFilter || undefined,
      entity_type: entityTypeFilter || undefined,
    }),
  });

  const logs = data?.logs || [];
  const totalLogs = data?.total || 0;
  const totalPages = Math.ceil(totalLogs / pageSize);

  // Get action badge color
  const getActionColor = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'update':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'delete':
        return 'bg-red-50 text-red-700 border-red-200';
      case 'activate':
        return 'bg-emerald-50 text-emerald-700 border-emerald-200';
      case 'deactivate':
        return 'bg-amber-50 text-amber-700 border-amber-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  // Get entity type badge color
  const getEntityTypeColor = (entityType: string) => {
    switch (entityType.toLowerCase()) {
      case 'user':
        return 'bg-purple-50 text-purple-700 border-purple-200';
      case 'persona':
        return 'bg-indigo-50 text-indigo-700 border-indigo-200';
      case 'purchase':
        return 'bg-orange-50 text-orange-700 border-orange-200';
      case 'setting':
        return 'bg-cyan-50 text-cyan-700 border-cyan-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  // Open details dialog
  const openDetailsDialog = (log: AdminActivityLog) => {
    setSelectedLog(log);
    setIsDetailsDialogOpen(true);
  };

  // Format JSON for display
  const formatJSON = (json: any) => {
    return JSON.stringify(json, null, 2);
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <AlertCircle className="h-12 w-12 text-destructive mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Activity Logs</h2>
          <p className="text-muted-foreground mb-4">
            There was a problem loading the activity logs.
          </p>
          <Button onClick={() => refetch()}>Retry</Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Activity Logs</h1>
          <Button onClick={() => refetch()} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search logs..."
              className="pl-8"
              disabled
            />
          </div>
          <Select
            value={actionFilter}
            onValueChange={setActionFilter}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Action" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Actions</SelectItem>
              <SelectItem value="create">Create</SelectItem>
              <SelectItem value="update">Update</SelectItem>
              <SelectItem value="delete">Delete</SelectItem>
              <SelectItem value="activate">Activate</SelectItem>
              <SelectItem value="deactivate">Deactivate</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={entityTypeFilter}
            onValueChange={setEntityTypeFilter}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Entity Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Entity Types</SelectItem>
              <SelectItem value="user">User</SelectItem>
              <SelectItem value="persona">Persona</SelectItem>
              <SelectItem value="purchase">Purchase</SelectItem>
              <SelectItem value="setting">Setting</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Admin Activity Logs</CardTitle>
            <CardDescription>
              Track all administrative actions performed in the system.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Time</TableHead>
                  <TableHead>Admin ID</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Entity Type</TableHead>
                  <TableHead>Entity ID</TableHead>
                  <TableHead className="text-right">Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {logs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                      No activity logs found
                    </TableCell>
                  </TableRow>
                ) : (
                  logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="font-medium">
                        {format(new Date(log.created_at), 'MMM d, yyyy HH:mm:ss')}
                      </TableCell>
                      <TableCell>{log.admin_id}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={getActionColor(log.action)}>
                          {log.action}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={getEntityTypeColor(log.entity_type)}>
                          {log.entity_type}
                        </Badge>
                      </TableCell>
                      <TableCell>{log.entity_id || 'N/A'}</TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openDetailsDialog(log)}
                          disabled={!log.details}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>

            {totalPages > 1 && (
              <div className="flex items-center justify-end space-x-2 py-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <div className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Log Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Activity Log Details</DialogTitle>
            <DialogDescription>
              Detailed information about this activity log.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium mb-1">Time</h4>
                <p className="text-sm">
                  {selectedLog && format(new Date(selectedLog.created_at), 'MMM d, yyyy HH:mm:ss')}
                </p>
              </div>
              <div>
                <h4 className="text-sm font-medium mb-1">Admin ID</h4>
                <p className="text-sm">{selectedLog?.admin_id}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium mb-1">Action</h4>
                <Badge variant="outline" className={selectedLog ? getActionColor(selectedLog.action) : ''}>
                  {selectedLog?.action}
                </Badge>
              </div>
              <div>
                <h4 className="text-sm font-medium mb-1">Entity Type</h4>
                <Badge variant="outline" className={selectedLog ? getEntityTypeColor(selectedLog.entity_type) : ''}>
                  {selectedLog?.entity_type}
                </Badge>
              </div>
              <div>
                <h4 className="text-sm font-medium mb-1">Entity ID</h4>
                <p className="text-sm">{selectedLog?.entity_id || 'N/A'}</p>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="text-sm font-medium mb-2">Details</h4>
              {selectedLog?.details ? (
                <ScrollArea className="h-[200px] rounded-md border p-4">
                  <pre className="text-xs">{formatJSON(selectedLog.details)}</pre>
                </ScrollArea>
              ) : (
                <div className="flex items-center justify-center h-[200px] rounded-md border">
                  <div className="text-center text-muted-foreground">
                    <Info className="h-8 w-8 mx-auto mb-2" />
                    <p>No details available</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default AdminActivityLogs;
