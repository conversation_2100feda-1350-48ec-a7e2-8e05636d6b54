"""
Document embedding and vector database tool for the Datagenius backend.

This module provides an MCP tool for processing documents, creating embeddings,
and storing them in a vector database for semantic search and retrieval.
"""

import logging
import os
import sys # Added for path manipulation
from typing import Dict, Any, Optional
import uuid
from pathlib import Path

from ..mcp.base import BaseMCPTool
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader, TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from langchain_core.vectorstores import VectorStore

# Add yaml_utils import relative to backend directory
# Assuming this script is run from a context where 'backend' is the root or in PYTHONPATH
try:
    from app.utils.yaml_utils import load_yaml, save_yaml
except ImportError:
    # Attempt relative import if direct fails (might happen in certain execution contexts)
    try:
        # Calculate path to backend/app/utils relative to this file's location
        current_dir = os.path.dirname(os.path.abspath(__file__))
        utils_dir = os.path.abspath(os.path.join(current_dir, '..', '..', 'app', 'utils'))
        if utils_dir not in sys.path:
            sys.path.insert(0, os.path.dirname(utils_dir)) # Add parent of utils ('app')
        from utils.yaml_utils import load_yaml, save_yaml
    except ImportError as e:
        print(f"Error importing yaml_utils: {e}. Ensure backend/app/utils is accessible.")
        # Define dummy functions to avoid NameError, but functionality will be broken
        def load_yaml(*args, **kwargs): raise NotImplementedError("yaml_utils not loaded")
        def save_yaml(*args, **kwargs): raise NotImplementedError("yaml_utils not loaded")


logger = logging.getLogger(__name__)


class DocumentEmbeddingTool(BaseMCPTool):
    """Tool for embedding documents and storing them in a vector database."""

    def __init__(self):
        """Initialize the document embedding tool."""
        super().__init__(
            name="document_embedding", # This name must match the 'name' field in the YAML schema
            description="Process documents, create embeddings, and store in vector database", # This can be overridden by YAML
            schema_path="document_embedding.yaml" # Relative to backend/schemas/tools/
            # input_schema and annotations will be loaded from the YAML file.
        )
        self.data_dir = "data"
        self.vector_db_dir = "vector_db"
        self.default_chunk_size = 1000
        self.default_chunk_overlap = 200
        self.default_embeddings_model = "all-MiniLM-L6-v2"

        # Marketing field queries
        self.marketing_field_queries = {
            "brand_description": "Based on the provided context, write a concise brand description. Extract information about the company's mission, values, and unique selling points.",
            "target_audience": "Based on the provided context, identify and describe the target audience or customer segments for this business. Include demographics, psychographics, and key characteristics.",
            "products_services": "Based on the provided context, list and briefly describe the main products and/or services offered by the business.",
            "marketing_goals": "Based on the provided context, identify the key marketing goals or objectives for this business. If not explicitly stated, suggest reasonable goals based on the business type and information provided.",
            "existing_content": "Based on the provided context, summarize any existing marketing content, campaigns, or channels mentioned in the document.",
            "keywords": "Based on the provided context, generate a list of 10-15 relevant keywords for this business that could be used for marketing purposes. Format as a comma-separated list.",
            "suggested_topics": "Based on the provided context, suggest 5-7 content topics that would be relevant for this business's marketing strategy. Present as a numbered list."
        }

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # Set data directory
        self.data_dir = config.get("data_dir", self.data_dir)

        # Set vector database directory
        self.vector_db_dir = config.get("vector_db_dir", self.vector_db_dir)

        # Create vector database directory if it doesn't exist
        os.makedirs(self.vector_db_dir, exist_ok=True)

        # Set default chunk size and overlap
        self.default_chunk_size = config.get("chunk_size", self.default_chunk_size)
        self.default_chunk_overlap = config.get("chunk_overlap", self.default_chunk_overlap)

        # Set default embeddings model
        self.default_embeddings_model = config.get("embeddings_model", self.default_embeddings_model)

        logger.info(f"Initialized document embedding tool with vector_db_dir: {self.vector_db_dir}")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            file_path = arguments["file_path"]
            operation = arguments["operation"]

            # Make sure we have an absolute path
            file_path = os.path.abspath(file_path)

            # Log the file path we're trying to access
            logger.info(f"Attempting to access file: {file_path}")

            # Check if the file exists
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"File not found: {file_path}"
                        }
                    ]
                }

            # Get file extension
            file_extension = Path(file_path).suffix.lower()

            # Check if the file is supported
            if file_extension not in [".pdf", ".docx", ".txt"]:
                logger.warning(f"Potentially unsupported file extension: {file_extension}, but will attempt to process as PDF")
                # Force PDF for unknown extensions as a fallback
                file_extension = ".pdf"

            # Get chunk size and overlap
            chunk_size = arguments.get("chunk_size", self.default_chunk_size)
            chunk_overlap = arguments.get("chunk_overlap", self.default_chunk_overlap)

            # Get embeddings model
            embeddings_model = arguments.get("embeddings_model", self.default_embeddings_model)

            # Initialize embeddings
            embeddings = HuggingFaceEmbeddings(model_name=embeddings_model)

            # Execute the operation
            if operation == "embed":
                # Process the document and create embeddings
                vector_store, file_info = await self._process_document(
                    file_path=file_path,
                    file_extension=file_extension,
                    embeddings=embeddings,
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap
                )

                # Save the vector store
                vector_store_id = str(uuid.uuid4())
                vector_store_path = os.path.join(self.vector_db_dir, vector_store_id)
                vector_store.save_local(vector_store_path)

                # Save file info
                file_info["vector_store_id"] = vector_store_id
                file_info["vector_store_path"] = vector_store_path

                # Save file info as YAML
                info_yaml_path = f"{vector_store_path}_info.yaml"
                logger.info(f"Saving vector store metadata to: {info_yaml_path}")
                if not save_yaml(file_info, info_yaml_path):
                    logger.error(f"Failed to save metadata file: {info_yaml_path}")
                    # Decide how to handle failure - maybe return error?
                    # For now, log and continue, but the metadata won't be saved.

                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Document processed and embedded successfully. Vector store ID: {vector_store_id}"
                        }
                    ],
                    "metadata": {
                        "vector_store_id": vector_store_id,
                        "file_info": file_info
                    }
                }

            elif operation == "query":
                # Get query
                query = arguments.get("query")
                if not query:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Query is required for 'query' operation"
                            }
                        ]
                    }

                # Load the vector store
                vector_store = await self._load_vector_store_for_file(file_path, embeddings)
                if not vector_store:
                    # If vector store doesn't exist, create it
                    vector_store, _ = await self._process_document(
                        file_path=file_path,
                        file_extension=file_extension,
                        embeddings=embeddings,
                        chunk_size=chunk_size,
                        chunk_overlap=chunk_overlap
                    )

                # Query the vector store
                results = vector_store.similarity_search(query, k=5)

                # Format the results
                formatted_results = []
                for doc in results:
                    formatted_results.append({
                        "content": doc.page_content,
                        "metadata": doc.metadata
                    })

                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Query results:\n\n{results[0].page_content if results else 'No results found.'}"
                        }
                    ],
                    "metadata": {
                        "query": query,
                        "results": formatted_results
                    }
                }

            elif operation == "query_marketing_fields":
                # Load the vector store
                vector_store = await self._load_vector_store_for_file(file_path, embeddings)
                if not vector_store:
                    # If vector store doesn't exist, create it
                    vector_store, _ = await self._process_document(
                        file_path=file_path,
                        file_extension=file_extension,
                        embeddings=embeddings,
                        chunk_size=chunk_size,
                        chunk_overlap=chunk_overlap
                    )

                # Query the vector store for each marketing field
                marketing_fields = {}
                for field, query in self.marketing_field_queries.items():
                    results = vector_store.similarity_search(query, k=3)
                    if results:
                        # Combine the results
                        context = "\n\n".join([doc.page_content for doc in results])

                        # Use the context to generate a response for the field
                        from langchain_core.prompts import ChatPromptTemplate
                        from langchain_groq import ChatGroq

                        # Initialize the LLM
                        llm = ChatGroq(
                            temperature=0.3,
                            model_name="llama3-70b-8192",
                            groq_api_key=os.getenv("GROQ_API_KEY", "")
                        )

                        # Create the prompt
                        prompt = ChatPromptTemplate.from_template(
                            """You are an AI assistant that helps extract information from documents.

                            Context from document:
                            {context}

                            Task: {query}

                            Provide a concise, well-formatted response based only on the information in the context.
                            If the context doesn't contain relevant information, provide a reasonable response based on the type of business or organization mentioned.
                            """
                        )

                        # Create the chain
                        chain = prompt | llm

                        # Execute the chain
                        response = await chain.ainvoke({"context": context, "query": query})

                        # Store the response
                        marketing_fields[field] = response.content
                    else:
                        marketing_fields[field] = ""

                return {
                    "content": [
                        {
                            "type": "text",
                            "text": "Marketing fields extracted successfully."
                        }
                    ],
                    "metadata": {
                        "marketing_fields": marketing_fields
                    }
                }

            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported operation: {operation}"
                        }
                    ]
                }

        except Exception as e:
            logger.error(f"Error executing document embedding tool: {e}", exc_info=True)
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error executing document embedding tool: {str(e)}"
                    }
                ]
            }

    async def _process_document(
        self,
        file_path: str,
        file_extension: str,
        embeddings: Any,
        chunk_size: int,
        chunk_overlap: int
    ) -> tuple[VectorStore, Dict[str, Any]]:
        """
        Process a document and create a vector store.

        Args:
            file_path: Path to the file
            file_extension: File extension
            embeddings: Embeddings model
            chunk_size: Size of text chunks
            chunk_overlap: Overlap between chunks

        Returns:
            Tuple of (vector_store, file_info)
        """
        try:
            # Load the document based on extension
            if file_extension == ".pdf":
                logger.info(f"Loading PDF document: {file_path}")
                loader = PyPDFLoader(file_path)
            elif file_extension == ".docx":
                logger.info(f"Loading DOCX document: {file_path}")
                loader = Docx2txtLoader(file_path)
            elif file_extension == ".txt":
                logger.info(f"Loading TXT document: {file_path}")
                loader = TextLoader(file_path)
            else:
                logger.error(f"Unsupported file extension: {file_extension}")
                raise ValueError(f"Unsupported file extension: {file_extension}")

            # Load the document
            documents = loader.load()
            logger.info(f"Successfully loaded document with {len(documents)} pages/sections")
        except Exception as e:
            logger.error(f"Error loading document: {str(e)}", exc_info=True)
            # Try a fallback approach for PDFs
            if file_extension == ".pdf":
                try:
                    from langchain_community.document_loaders import UnstructuredPDFLoader
                    logger.info(f"Trying fallback PDF loader (UnstructuredPDFLoader) for: {file_path}")
                    fallback_loader = UnstructuredPDFLoader(file_path)
                    documents = fallback_loader.load()
                    logger.info(f"Successfully loaded document with fallback loader: {len(documents)} pages/sections")
                except Exception as e2:
                    logger.error(f"Fallback loader also failed: {str(e2)}", exc_info=True)
                    raise ValueError(f"Could not load document with any available loader: {str(e)} / {str(e2)}")
            else:
                raise

        # Split the document into chunks
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len
        )
        chunks = text_splitter.split_documents(documents)

        # Create vector store
        vector_store = FAISS.from_documents(chunks, embeddings)

        # Create file info
        file_info = {
            "file_path": file_path,
            "file_type": file_extension.lstrip("."),
            "num_chunks": len(chunks),
            "chunk_size": chunk_size,
            "chunk_overlap": chunk_overlap,
            "embeddings_model": embeddings.model_name
        }

        return vector_store, file_info

    async def _load_vector_store_for_file(self, file_path: str, embeddings: Any) -> Optional[VectorStore]:
        """
        Load the vector store for a file.

        Args:
            file_path: Path to the file
            embeddings: Embeddings model

        Returns:
            Vector store if found, None otherwise
        """
        logger.debug(f"Searching for vector store metadata for file: {file_path} in {self.vector_db_dir}")
        # Check if vector store exists for this file
        for filename in os.listdir(self.vector_db_dir):
            base_name, ext = os.path.splitext(filename)
            if not ext.lower() in ['.yaml', '.yml'] or not base_name.endswith("_info"):
                continue # Skip non-YAML files or directories

            info_path = os.path.join(self.vector_db_dir, filename)
            file_info = None
            vector_store_path_from_info = None

            try:
                logger.debug(f"Loading YAML metadata: {info_path}")
                file_info = load_yaml(info_path)
                vector_store_path_from_info = file_info.get("vector_store_path")
            except Exception as e:
                logger.warning(f"Could not load or parse metadata file {info_path}: {e}")
                continue # Skip this file if loading fails

            if file_info and file_info.get("file_path") == file_path:
                logger.info(f"Found matching metadata for {file_path} in {info_path}")
                # Use the vector_store_path derived from the filename base if not in metadata
                # (This assumes the directory name matches the base name of the info file)
                vector_store_dir_name = base_name[:-5] # Remove '_info'
                potential_vector_store_path = os.path.join(self.vector_db_dir, vector_store_dir_name)

                # Prefer path from metadata if available and valid, otherwise use derived path
                vector_store_path = vector_store_path_from_info if vector_store_path_from_info else potential_vector_store_path

                if vector_store_path and os.path.exists(vector_store_path):
                    logger.info(f"Loading vector store from: {vector_store_path}")
                    if vector_store_path and os.path.exists(vector_store_path):
                        # Load the vector store
                        vector_store = FAISS.load_local(vector_store_path, embeddings, allow_dangerous_deserialization=True)
                        return vector_store

        return None
