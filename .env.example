# Database configuration
DATABASE_URL=*********************************************************/datagenius
DATABASE_ECHO=false

# JWT configuration
JWT_SECRET_KEY=your-secret-key-for-development-only
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Security configuration
MAX_REFRESH_COUNT=30
MAX_CONCURRENT_SESSIONS=5
ENFORCE_IP_VALIDATION=false
IP_CHANGE_LOCKOUT=false

# Redis configuration
REDIS_URL=redis://redis:6379/0

# File upload configuration
UPLOAD_DIR=temp_uploads
MAX_UPLOAD_SIZE=10485760

# LLM provider configuration
GROQ_API_KEY=your-groq-api-key
OPENAI_API_KEY=your-openai-api-key
GEMINI_API_KEY=your-gemini-api-key
OPENROUTER_API_KEY=your-openrouter-api-key

# Google OAuth configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:5173/auth/google/callback

# LLM endpoint configuration
GROQ_ENDPOINT=https://api.groq.com/openai/v1
OPENAI_ENDPOINT=https://api.openai.com/v1
GEMINI_ENDPOINT=https://generativelanguage.googleapis.com
OPENROUTER_ENDPOINT=https://openrouter.ai/api/v1
OLLAMA_ENDPOINT=http://localhost:11434
REQUESTY_ENDPOINT=https://router.requesty.ai/v1

# mem0ai configuration
MEM0_API_KEY=your-mem0-api-key
MEM0_ENDPOINT=
MEM0_SELF_HOSTED=true
MEM0_DEFAULT_TTL=2592000
MEM0_MAX_MEMORIES=1000
MEM0_MEMORY_THRESHOLD=0.7

# Qdrant configuration
QDRANT_HOST=qdrant
QDRANT_PORT=6333

# Frontend URL for redirects
FRONTEND_URL=http://localhost:5173

# Email configuration
EMAIL_ENABLED=false
EMAIL_SENDER=<EMAIL>
EMAIL_SMTP_SERVER=smtp.example.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USER=
EMAIL_SMTP_PASSWORD=
EMAIL_USE_TLS=true

# CORS configuration
CORS_ORIGINS=*

# Debug configuration
DEBUG=true

# Application configuration
APP_NAME=Datagenius
APP_VERSION=1.0.0
APP_DESCRIPTION=AI-powered data analysis platform

# PostgreSQL configuration for Docker Compose
POSTGRES_USER=datagenius
POSTGRES_PASSWORD=datagenius_password
POSTGRES_DB=datagenius
