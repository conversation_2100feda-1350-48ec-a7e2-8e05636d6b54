"""
Knowledge Graph Store for the Datagenius backend.

This module provides a store for the knowledge graph, with methods for adding,
querying, and manipulating entities and relationships.
"""

import logging
import os
import sys
import uuid
from typing import Dict, Any, List, Optional
import networkx as nx
from networkx.readwrite import json_graph
import numpy as np

from .schema import (
    Entity, Relationship, KnowledgeGraph,
    KnowledgeGraphQuery, KnowledgeGraphQueryResult
)

# Add yaml_utils import relative to backend directory
try:
    # Adjust path relative to this file's location (knowledge_graph -> app/utils)
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Assuming store.py is in backend/agents/knowledge_graph/
    # Path to backend/app/utils
    utils_dir = os.path.abspath(os.path.join(current_dir, '..', '..', 'app', 'utils'))
    if utils_dir not in sys.path:
        sys.path.insert(0, os.path.dirname(utils_dir)) # Add 'app' to path
    from app.utils.yaml_utils import load_yaml, save_yaml
except ImportError as e:
    print(f"Error importing yaml_utils in knowledge_graph.store: {e}. Ensure backend/app/utils is accessible.")
    # Define dummy functions
    def load_yaml(file_path): raise NotImplementedError("yaml_utils not loaded")
    def save_yaml(data, file_path): raise NotImplementedError("yaml_utils not loaded")


logger = logging.getLogger(__name__)


class KnowledgeGraphStore:
    """Store for the knowledge graph."""

    def __init__(self, graph_dir: str = "knowledge_graphs"):
        """
        Initialize the knowledge graph store.

        Args:
            graph_dir: Directory to store knowledge graphs.
        """
        self.graph_dir = graph_dir
        os.makedirs(graph_dir, exist_ok=True)
        self.graph = nx.DiGraph()
        self.current_graph_id = None
        logger.info(f"Initialized knowledge graph store in {graph_dir}")

    def create_graph(self, metadata: Dict[str, Any] = None) -> str:
        """
        Create a new knowledge graph.

        Args:
            metadata: Metadata for the graph.

        Returns:
            ID of the created graph.
        """
        # Generate a unique ID for the graph
        graph_id = str(uuid.uuid4())
        self.current_graph_id = graph_id

        # Create a new graph
        self.graph = nx.DiGraph()

        # Add metadata to the graph
        if metadata:
            for key, value in metadata.items():
                self.graph.graph[key] = value

        # Add creation timestamp
        import datetime
        self.graph.graph["created_at"] = datetime.datetime.now().isoformat()
        self.graph.graph["id"] = graph_id

        logger.info(f"Created new knowledge graph with ID: {graph_id}")
        return graph_id

    def load_graph(self, graph_id: str) -> bool:
        """
        Load a knowledge graph from disk.

        Args:
            graph_id: ID of the graph to load.

        Returns:
            True if the graph was loaded successfully, False otherwise.
        """
        yaml_path = os.path.join(self.graph_dir, f"{graph_id}.yaml")
        data = None

        if os.path.exists(yaml_path):
            logger.info(f"Loading YAML graph: {yaml_path}")
            try:
                # load_yaml from our utils returns a dict, json_graph expects dict
                data = load_yaml(yaml_path)
            except Exception as e:
                logger.error(f"Failed to load YAML graph {yaml_path}: {e}")
                return False
        else:
            logger.error(f"Graph {graph_id} not found. YAML file does not exist: {yaml_path}")
            return False

        try:
            self.graph = json_graph.node_link_graph(data) # networkx handles dict from json.load or yaml.safe_load
            self.current_graph_id = graph_id
            logger.info(f"Loaded knowledge graph {graph_id} with {len(self.graph.nodes)} entities and {len(self.graph.edges)} relationships")
            return True
        except Exception as e:
            logger.error(f"Error constructing graph {graph_id} from loaded data: {str(e)}")
            return False

    def save_graph(self, graph_id: Optional[str] = None) -> bool:
        """
        Save the current knowledge graph to disk.

        Args:
            graph_id: ID of the graph to save. If None, use the current graph ID.

        Returns:
            True if the graph was saved successfully, False otherwise.
        """
        if graph_id is None:
            graph_id = self.current_graph_id

        if graph_id is None:
            graph_id = self.current_graph_id

        if graph_id is None:
            logger.error("No graph ID specified and no current graph ID for saving.")
            return False

        # Save as YAML
        graph_path_yaml = os.path.join(self.graph_dir, f"{graph_id}.yaml")
        try:
            # Convert the graph to a serializable format (dict)
            data = json_graph.node_link_data(self.graph)

            if save_yaml(data, graph_path_yaml):
                logger.info(f"Saved knowledge graph {graph_id} to {graph_path_yaml}")
                return True
            else:
                logger.error(f"Failed to save graph {graph_id} to {graph_path_yaml} using save_yaml.")
                return False
        except Exception as e:
            logger.error(f"Error preparing or saving graph {graph_id} as YAML: {str(e)}")
            return False

    def add_entity(self, entity: Entity) -> bool:
        """
        Add an entity to the knowledge graph.

        Args:
            entity: Entity to add.

        Returns:
            True if the entity was added successfully, False otherwise.
        """
        if entity.id in self.graph.nodes:
            logger.warning(f"Entity {entity.id} already exists in the graph")
            return False

        # Convert the entity to a dictionary for storage in the graph
        entity_dict = entity.model_dump()

        # Add the entity to the graph
        self.graph.add_node(entity.id, **entity_dict)
        logger.info(f"Added entity {entity.id} to the graph")
        return True

    def add_relationship(self, relationship: Relationship) -> bool:
        """
        Add a relationship to the knowledge graph.

        Args:
            relationship: Relationship to add.

        Returns:
            True if the relationship was added successfully, False otherwise.
        """
        if relationship.source_id not in self.graph.nodes:
            logger.error(f"Source entity {relationship.source_id} not found in the graph")
            return False

        if relationship.target_id not in self.graph.nodes:
            logger.error(f"Target entity {relationship.target_id} not found in the graph")
            return False

        # Convert the relationship to a dictionary for storage in the graph
        relationship_dict = relationship.model_dump()

        # Add the relationship to the graph
        self.graph.add_edge(
            relationship.source_id,
            relationship.target_id,
            id=relationship.id,
            **relationship_dict
        )
        logger.info(f"Added relationship {relationship.id} to the graph")
        return True

    def get_entity(self, entity_id: str) -> Optional[Entity]:
        """
        Get an entity from the knowledge graph.

        Args:
            entity_id: ID of the entity to get.

        Returns:
            The entity if found, None otherwise.
        """
        if entity_id not in self.graph.nodes:
            logger.warning(f"Entity {entity_id} not found in the graph")
            return None

        entity_dict = self.graph.nodes[entity_id]
        return Entity(**entity_dict)

    def get_relationship(self, relationship_id: str) -> Optional[Relationship]:
        """
        Get a relationship from the knowledge graph.

        Args:
            relationship_id: ID of the relationship to get.

        Returns:
            The relationship if found, None otherwise.
        """
        for source_id, target_id, edge_data in self.graph.edges(data=True):
            if edge_data.get("id") == relationship_id:
                return Relationship(**edge_data)

        logger.warning(f"Relationship {relationship_id} not found in the graph")
        return None

    def query(self, query: KnowledgeGraphQuery) -> KnowledgeGraphQueryResult:
        """
        Query the knowledge graph.

        Args:
            query: Query to execute.

        Returns:
            Result of the query.
        """
        if query.query_type == "entity":
            return self._query_entity(query)
        elif query.query_type == "relationship":
            return self._query_relationship(query)
        elif query.query_type == "path":
            return self._query_path(query)
        elif query.query_type == "neighbors":
            return self._query_neighbors(query)
        elif query.query_type == "similarity":
            return self._query_similarity(query)
        else:
            logger.error(f"Unknown query type: {query.query_type}")
            return KnowledgeGraphQueryResult(
                query=query,
                entities=[],
                relationships=[],
                metadata={"error": f"Unknown query type: {query.query_type}"}
            )

    def _query_entity(self, query: KnowledgeGraphQuery) -> KnowledgeGraphQueryResult:
        """
        Query entities in the knowledge graph.

        Args:
            query: Query to execute.

        Returns:
            Result of the query.
        """
        entities = []

        # Get parameters
        entity_type = query.parameters.get("type")
        name_contains = query.parameters.get("name_contains")
        property_filters = query.parameters.get("properties", {})

        # Apply filters
        for node_id, node_data in self.graph.nodes(data=True):
            # Filter by entity type
            if entity_type and node_data.get("type") != entity_type:
                continue

            # Filter by name
            if name_contains and name_contains.lower() not in node_data.get("name", "").lower():
                continue

            # Filter by properties
            if property_filters:
                properties = {prop.get("name"): prop.get("value") for prop in node_data.get("properties", [])}
                if not all(properties.get(k) == v for k, v in property_filters.items()):
                    continue

            # Add entity to results
            entities.append(Entity(**node_data))

        # Apply limit and offset
        if query.offset is not None:
            entities = entities[query.offset:]
        if query.limit is not None:
            entities = entities[:query.limit]

        return KnowledgeGraphQueryResult(
            query=query,
            entities=entities,
            relationships=[],
            metadata={"count": len(entities)}
        )

    def _query_relationship(self, query: KnowledgeGraphQuery) -> KnowledgeGraphQueryResult:
        """
        Query relationships in the knowledge graph.

        Args:
            query: Query to execute.

        Returns:
            Result of the query.
        """
        relationships = []

        # Get parameters
        relationship_type = query.parameters.get("type")
        source_id = query.parameters.get("source_id")
        target_id = query.parameters.get("target_id")
        property_filters = query.parameters.get("properties", {})

        # Apply filters
        for source, target, edge_data in self.graph.edges(data=True):
            # Filter by relationship type
            if relationship_type and edge_data.get("type") != relationship_type:
                continue

            # Filter by source and target
            if source_id and source != source_id:
                continue
            if target_id and target != target_id:
                continue

            # Filter by properties
            if property_filters:
                properties = {prop.get("name"): prop.get("value") for prop in edge_data.get("properties", [])}
                if not all(properties.get(k) == v for k, v in property_filters.items()):
                    continue

            # Add relationship to results
            relationships.append(Relationship(**edge_data))

        # Apply limit and offset
        if query.offset is not None:
            relationships = relationships[query.offset:]
        if query.limit is not None:
            relationships = relationships[:query.limit]

        return KnowledgeGraphQueryResult(
            query=query,
            entities=[],
            relationships=relationships,
            metadata={"count": len(relationships)}
        )

    def _query_path(self, query: KnowledgeGraphQuery) -> KnowledgeGraphQueryResult:
        """
        Query paths between entities in the knowledge graph.

        Args:
            query: Query to execute.

        Returns:
            Result of the query.
        """
        # Get parameters
        source_id = query.parameters.get("source_id")
        target_id = query.parameters.get("target_id")
        max_length = query.parameters.get("max_length", 5)

        if not source_id or not target_id:
            logger.error("Source and target IDs are required for path queries")
            return KnowledgeGraphQueryResult(
                query=query,
                entities=[],
                relationships=[],
                metadata={"error": "Source and target IDs are required for path queries"}
            )

        # Find all paths between source and target
        try:
            paths = list(nx.all_simple_paths(self.graph, source_id, target_id, cutoff=max_length))
        except nx.NetworkXNoPath:
            logger.info(f"No path found between {source_id} and {target_id}")
            return KnowledgeGraphQueryResult(
                query=query,
                entities=[],
                relationships=[],
                metadata={"error": f"No path found between {source_id} and {target_id}"}
            )

        # Get entities and relationships in the paths
        entities = []
        relationships = []
        entity_ids = set()
        relationship_ids = set()

        for path in paths:
            # Add entities
            for node_id in path:
                if node_id not in entity_ids:
                    entity_ids.add(node_id)
                    entities.append(Entity(**self.graph.nodes[node_id]))

            # Add relationships
            for i in range(len(path) - 1):
                source = path[i]
                target = path[i + 1]
                edge_data = self.graph.get_edge_data(source, target)
                relationship_id = edge_data.get("id")
                if relationship_id and relationship_id not in relationship_ids:
                    relationship_ids.add(relationship_id)
                    relationships.append(Relationship(**edge_data))

        return KnowledgeGraphQueryResult(
            query=query,
            entities=entities,
            relationships=relationships,
            metadata={"paths": paths, "count": len(paths)}
        )

    def _query_neighbors(self, query: KnowledgeGraphQuery) -> KnowledgeGraphQueryResult:
        """
        Query neighbors of an entity in the knowledge graph.

        Args:
            query: Query to execute.

        Returns:
            Result of the query.
        """
        # Get parameters
        entity_id = query.parameters.get("entity_id")
        direction = query.parameters.get("direction", "both")  # "in", "out", or "both"
        relationship_type = query.parameters.get("relationship_type")

        if not entity_id:
            logger.error("Entity ID is required for neighbor queries")
            return KnowledgeGraphQueryResult(
                query=query,
                entities=[],
                relationships=[],
                metadata={"error": "Entity ID is required for neighbor queries"}
            )

        # Get neighbors
        entities = []
        relationships = []
        entity_ids = set()
        relationship_ids = set()

        # Outgoing edges
        if direction in ["out", "both"]:
            for _, target, edge_data in self.graph.out_edges(entity_id, data=True):
                # Filter by relationship type
                if relationship_type and edge_data.get("type") != relationship_type:
                    continue

                # Add target entity
                if target not in entity_ids:
                    entity_ids.add(target)
                    entities.append(Entity(**self.graph.nodes[target]))

                # Add relationship
                relationship_id = edge_data.get("id")
                if relationship_id and relationship_id not in relationship_ids:
                    relationship_ids.add(relationship_id)
                    relationships.append(Relationship(**edge_data))

        # Incoming edges
        if direction in ["in", "both"]:
            for source, _, edge_data in self.graph.in_edges(entity_id, data=True):
                # Filter by relationship type
                if relationship_type and edge_data.get("type") != relationship_type:
                    continue

                # Add source entity
                if source not in entity_ids:
                    entity_ids.add(source)
                    entities.append(Entity(**self.graph.nodes[source]))

                # Add relationship
                relationship_id = edge_data.get("id")
                if relationship_id and relationship_id not in relationship_ids:
                    relationship_ids.add(relationship_id)
                    relationships.append(Relationship(**edge_data))

        return KnowledgeGraphQueryResult(
            query=query,
            entities=entities,
            relationships=relationships,
            metadata={"count": len(entities)}
        )

    def _query_similarity(self, query: KnowledgeGraphQuery) -> KnowledgeGraphQueryResult:
        """
        Query entities similar to a given entity or embedding.

        Args:
            query: Query to execute.

        Returns:
            Result of the query.
        """
        # Get parameters
        entity_id = query.parameters.get("entity_id")
        embedding = query.parameters.get("embedding")
        top_k = query.parameters.get("top_k", 10)

        if not entity_id and not embedding:
            logger.error("Either entity_id or embedding is required for similarity queries")
            return KnowledgeGraphQueryResult(
                query=query,
                entities=[],
                relationships=[],
                metadata={"error": "Either entity_id or embedding is required for similarity queries"}
            )

        # Get the reference embedding
        if entity_id:
            entity = self.get_entity(entity_id)
            if not entity or not entity.embedding:
                logger.error(f"Entity {entity_id} not found or has no embedding")
                return KnowledgeGraphQueryResult(
                    query=query,
                    entities=[],
                    relationships=[],
                    metadata={"error": f"Entity {entity_id} not found or has no embedding"}
                )
            reference_embedding = entity.embedding
        else:
            reference_embedding = embedding

        # Find similar entities
        entities_with_scores = []

        for node_id, node_data in self.graph.nodes(data=True):
            node_embedding = node_data.get("embedding")
            if not node_embedding:
                continue

            # Skip the reference entity
            if node_id == entity_id:
                continue

            # Calculate cosine similarity
            similarity = self._cosine_similarity(reference_embedding, node_embedding)
            entities_with_scores.append((similarity, Entity(**node_data)))

        # Sort by similarity (descending)
        entities_with_scores.sort(reverse=True)

        # Get top-k entities
        top_entities = [entity for _, entity in entities_with_scores[:top_k]]

        return KnowledgeGraphQueryResult(
            query=query,
            entities=top_entities,
            relationships=[],
            metadata={"count": len(top_entities)}
        )

    def _cosine_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between two embeddings.

        Args:
            embedding1: First embedding.
            embedding2: Second embedding.

        Returns:
            Cosine similarity.
        """
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)

        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        if norm1 == 0 or norm2 == 0:
            return 0

        return dot_product / (norm1 * norm2)

    def extract_subgraph(self, entity_ids: List[str], include_neighbors: bool = False) -> KnowledgeGraph:
        """
        Extract a subgraph containing the specified entities.

        Args:
            entity_ids: IDs of entities to include in the subgraph.
            include_neighbors: Whether to include neighbors of the specified entities.

        Returns:
            Extracted subgraph.
        """
        # Get the set of entities to include
        entity_set = set(entity_ids)

        # Add neighbors if requested
        if include_neighbors:
            for entity_id in list(entity_set):
                # Add outgoing neighbors
                for _, target in self.graph.out_edges(entity_id):
                    entity_set.add(target)

                # Add incoming neighbors
                for source, _ in self.graph.in_edges(entity_id):
                    entity_set.add(source)

        # Create the subgraph
        subgraph = self.graph.subgraph(entity_set)

        # Convert to KnowledgeGraph model
        entities = {}
        relationships = {}

        for node_id, node_data in subgraph.nodes(data=True):
            entities[node_id] = Entity(**node_data)

        for source, target, edge_data in subgraph.edges(data=True):
            relationship_id = edge_data.get("id")
            if relationship_id:
                relationships[relationship_id] = Relationship(**edge_data)

        return KnowledgeGraph(
            entities=entities,
            relationships=relationships,
            metadata={"source_graph_id": self.current_graph_id}
        )
