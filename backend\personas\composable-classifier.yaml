id: composable-classifier-ai
name: Composable Classifier
description: A composable AI assistant for text classification tasks
version: 1.0.0
agent_class: agents.classification.composable_agent_mcp.ComposableClassificationAgent
industry: Technology
skills:
  - Text Classification
  - Document Analysis
  - Content Categorization
capabilities:
  - text_classification
  - document_analysis
  - content_categorization
rating: 4.7
review_count: 95
image_url: /placeholder.svg
price: 10.0
provider: groq
model: llama3-70b-8192
is_active: true
age_restriction: 0
components:
  - type: classification_parser
    name: request_parser

  - type: mcp_server
    name: classification_tools
    server_name: datagenius-classification-tools
    server_version: 1.0.0
    tools:
      - type: text_classification

  - type: error_handler
    name: classification_error_handler

system_prompts:
  default: |
    You are Composable Classifier, a specialized AI for text classification tasks.

    Your capabilities include:
    - Classifying text using Hugging Face models
    - Classifying text using LLMs
    - Analyzing documents and categorizing content

    Help the user understand and classify their text data.

    User: {message}

  hf_classification: |
    I've analyzed your text using the Hugging Face model and classified it into the following categories:

    {categories}

    These classifications can help you understand the main themes and topics in your content.
    Let me know if you'd like more detailed analysis or have questions about these results.
