import { useState } from 'react';
import { useQ<PERSON>y } from '@tanstack/react-query';
import { adminApi, AdminAnalyticsRequest, AdminAnalyticsResponse } from '@/lib/adminApi';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Loader2,
  AlertCircle,
  Users,
  Bot,
  ShoppingCart,
  DollarSign,
} from 'lucide-react';
import { format, subDays, subMonths, subYears } from 'date-fns';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'recharts';

const AdminAnalytics = () => {
  const { toast } = useToast();
  const [timeRange, setTimeRange] = useState<string>('30d');
  const [groupBy, setGroupBy] = useState<string>('day');
  const [activeTab, setActiveTab] = useState<string>('purchases');

  // Calculate date range based on selected time range
  const getDateRange = (): { start_date: string; end_date: string } => {
    const end_date = new Date();
    let start_date: Date;

    switch (timeRange) {
      case '7d':
        start_date = subDays(end_date, 7);
        break;
      case '30d':
        start_date = subDays(end_date, 30);
        break;
      case '90d':
        start_date = subDays(end_date, 90);
        break;
      case '6m':
        start_date = subMonths(end_date, 6);
        break;
      case '1y':
        start_date = subYears(end_date, 1);
        break;
      default:
        start_date = subDays(end_date, 30);
    }

    return {
      start_date: start_date.toISOString(),
      end_date: end_date.toISOString(),
    };
  };

  // Create analytics request
  const createAnalyticsRequest = (): AdminAnalyticsRequest => {
    const { start_date, end_date } = getDateRange();
    return {
      timeframe: {
        start_date,
        end_date,
      },
      group_by: groupBy,
    };
  };

  // Fetch purchase analytics
  const {
    data: purchaseData,
    isLoading: isPurchaseLoading,
    error: purchaseError,
    refetch: refetchPurchases,
  } = useQuery({
    queryKey: ['adminPurchaseAnalytics', timeRange, groupBy],
    queryFn: () => adminApi.getPurchaseAnalytics(createAnalyticsRequest()),
    enabled: activeTab === 'purchases',
  });

  // Fetch user analytics
  const {
    data: userData,
    isLoading: isUserLoading,
    error: userError,
    refetch: refetchUsers,
  } = useQuery({
    queryKey: ['adminUserAnalytics', timeRange, groupBy],
    queryFn: () => adminApi.getUserAnalytics(createAnalyticsRequest()),
    enabled: activeTab === 'users',
  });

  // Fetch persona analytics
  const {
    data: personaData,
    isLoading: isPersonaLoading,
    error: personaError,
    refetch: refetchPersonas,
  } = useQuery({
    queryKey: ['adminPersonaAnalytics', timeRange, groupBy],
    queryFn: () => adminApi.getPersonaAnalytics(createAnalyticsRequest()),
    enabled: activeTab === 'personas',
  });

  // Format data for charts
  const formatChartData = (data: AdminAnalyticsResponse | undefined) => {
    if (!data) return [];
    return data.data.map((item) => ({
      date: item.date,
      value: item.value,
    }));
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (value === 'purchases' && !purchaseData) {
      refetchPurchases();
    } else if (value === 'users' && !userData) {
      refetchUsers();
    } else if (value === 'personas' && !personaData) {
      refetchPersonas();
    }
  };

  // Get current data based on active tab
  const getCurrentData = () => {
    switch (activeTab) {
      case 'purchases':
        return purchaseData;
      case 'users':
        return userData;
      case 'personas':
        return personaData;
      default:
        return undefined;
    }
  };

  // Get current loading state based on active tab
  const isLoading = () => {
    switch (activeTab) {
      case 'purchases':
        return isPurchaseLoading;
      case 'users':
        return isUserLoading;
      case 'personas':
        return isPersonaLoading;
      default:
        return false;
    }
  };

  // Get current error based on active tab
  const getError = () => {
    switch (activeTab) {
      case 'purchases':
        return purchaseError;
      case 'users':
        return userError;
      case 'personas':
        return personaError;
      default:
        return undefined;
    }
  };

  // Get current refetch function based on active tab
  const getRefetch = () => {
    switch (activeTab) {
      case 'purchases':
        return refetchPurchases;
      case 'users':
        return refetchUsers;
      case 'personas':
        return refetchPersonas;
      default:
        return () => {};
    }
  };

  // Get chart color based on active tab
  const getChartColor = () => {
    switch (activeTab) {
      case 'purchases':
        return '#f59e0b'; // amber-500
      case 'users':
        return '#3b82f6'; // blue-500
      case 'personas':
        return '#8b5cf6'; // violet-500
      default:
        return '#10b981'; // emerald-500
    }
  };

  // Get chart icon based on active tab
  const getChartIcon = () => {
    switch (activeTab) {
      case 'purchases':
        return ShoppingCart;
      case 'users':
        return Users;
      case 'personas':
        return Bot;
      default:
        return DollarSign;
    }
  };

  // Get chart title based on active tab
  const getChartTitle = () => {
    switch (activeTab) {
      case 'purchases':
        return 'Purchase Analytics';
      case 'users':
        return 'User Analytics';
      case 'personas':
        return 'Persona Analytics';
      default:
        return 'Analytics';
    }
  };

  // Get chart description based on active tab
  const getChartDescription = () => {
    switch (activeTab) {
      case 'purchases':
        return 'Track purchase trends and revenue over time.';
      case 'users':
        return 'Monitor user growth and engagement.';
      case 'personas':
        return 'Analyze persona usage and popularity.';
      default:
        return 'View analytics data.';
    }
  };

  // Get value label based on active tab
  const getValueLabel = () => {
    switch (activeTab) {
      case 'purchases':
        return 'Revenue ($)';
      case 'users':
        return 'Users';
      case 'personas':
        return 'Personas';
      default:
        return 'Value';
    }
  };

  // Format tooltip value based on active tab
  const formatTooltipValue = (value: number) => {
    switch (activeTab) {
      case 'purchases':
        return `$${value.toFixed(2)}`;
      default:
        return value.toFixed(0);
    }
  };

  if (isLoading()) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AdminLayout>
    );
  }

  const error = getError();
  if (error) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <AlertCircle className="h-12 w-12 text-destructive mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Analytics</h2>
          <p className="text-muted-foreground mb-4">
            There was a problem loading the analytics data.
          </p>
          <Button onClick={() => getRefetch()()}>Retry</Button>
        </div>
      </AdminLayout>
    );
  }

  const currentData = getCurrentData();
  const chartData = formatChartData(currentData);
  const ChartIcon = getChartIcon();

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Analytics</h1>
        </div>

        <Tabs defaultValue="purchases" value={activeTab} onValueChange={handleTabChange}>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
            <TabsList>
              <TabsTrigger value="purchases">Purchases</TabsTrigger>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="personas">Personas</TabsTrigger>
            </TabsList>
            <div className="flex items-center gap-2">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Time Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="6m">Last 6 months</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Select value={groupBy} onValueChange={setGroupBy}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Group By" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="day">Day</SelectItem>
                  <SelectItem value="week">Week</SelectItem>
                  <SelectItem value="month">Month</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <TabsContent value="purchases" className="space-y-6">
            <AnalyticsContent
              title="Purchase Analytics"
              description="Track purchase trends and revenue over time."
              icon={ShoppingCart}
              data={currentData}
              chartData={chartData}
              chartColor="#f59e0b"
              valueLabel="Revenue ($)"
              formatValue={(value) => `$${value.toFixed(2)}`}
            />
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <AnalyticsContent
              title="User Analytics"
              description="Monitor user growth and engagement."
              icon={Users}
              data={currentData}
              chartData={chartData}
              chartColor="#3b82f6"
              valueLabel="Users"
              formatValue={(value) => value.toFixed(0)}
            />
          </TabsContent>

          <TabsContent value="personas" className="space-y-6">
            <AnalyticsContent
              title="Persona Analytics"
              description="Analyze persona usage and popularity."
              icon={Bot}
              data={currentData}
              chartData={chartData}
              chartColor="#8b5cf6"
              valueLabel="Personas"
              formatValue={(value) => value.toFixed(0)}
            />
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

interface AnalyticsContentProps {
  title: string;
  description: string;
  icon: React.FC<{ className?: string }>;
  data: AdminAnalyticsResponse | undefined;
  chartData: any[];
  chartColor: string;
  valueLabel: string;
  formatValue: (value: number) => string;
}

const AnalyticsContent = ({
  title,
  description,
  icon: Icon,
  data,
  chartData,
  chartColor,
  valueLabel,
  formatValue,
}: AnalyticsContentProps) => {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <Icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatValue(data?.total || 0)}</div>
            <p className="text-xs text-muted-foreground">All time</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average</CardTitle>
            <Icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatValue(data?.average || 0)}</div>
            <p className="text-xs text-muted-foreground">Per period</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Minimum</CardTitle>
            <Icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatValue(data?.min || 0)}</div>
            <p className="text-xs text-muted-foreground">Lowest value</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maximum</CardTitle>
            <Icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatValue(data?.max || 0)}</div>
            <p className="text-xs text-muted-foreground">Highest value</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={chartData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip
                  formatter={(value: number) => [formatValue(value), valueLabel]}
                  labelFormatter={(label) => `Date: ${label}`}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="value"
                  name={valueLabel}
                  stroke={chartColor}
                  activeDot={{ r: 8 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{title} - Bar Chart</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip
                  formatter={(value: number) => [formatValue(value), valueLabel]}
                  labelFormatter={(label) => `Date: ${label}`}
                />
                <Legend />
                <Bar dataKey="value" name={valueLabel} fill={chartColor} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default AdminAnalytics;
