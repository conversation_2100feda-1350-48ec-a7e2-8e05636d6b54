"""
Document query API for the Datagenius backend.

This module provides API endpoints for querying document data stored in vector databases.
"""

import logging
import os
import sys # Added for path manipulation
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..database import get_db, get_file, User
from ..auth import get_current_active_user
from ..config import UPLOAD_DIR

# Import yaml_utils using centralized import utility
from ..utils.import_utils import import_yaml_utils

load_yaml, _ = import_yaml_utils()


from langchain_groq import ChatGroq
from langchain_core.prompts import ChatPromptTemplate

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/document-query",
    tags=["document-query"],
    responses={404: {"description": "Not found"}},
)


class DocumentQueryRequest(BaseModel):
    """Model for document query request."""
    file_id: str
    query_type: str = "marketing_fields"
    specific_fields: Optional[List[str]] = None


class DocumentQueryResponse(BaseModel):
    """Model for document query response."""
    results: Dict[str, Any]


@router.post("", response_model=DocumentQueryResponse)
async def query_document(
    request: DocumentQueryRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Query a document.

    Args:
        request: Query request
        db: Database session
        current_user: Current authenticated user

    Returns:
        Query results
    """
    logger.info(f"User {current_user.id} querying document {request.file_id}")

    # Get file from database
    file = get_file(db, request.file_id)
    if not file:
        logger.warning(f"File not found in database: {request.file_id}")
        raise HTTPException(status_code=404, detail="File not found in database. It may have been deleted.")

    # Check if file exists on disk
    file_path = file.file_path
    # Normalize path separators for consistency
    file_path = file_path.replace("\\", "/")
    if not os.path.exists(file_path):
        logger.warning(f"File not found on disk: {file_path}")
        raise HTTPException(status_code=404, detail="File exists in database but not on disk. Please contact support.")

    # Check if vector store exists for this file
    vector_db_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "vector_db")
    vector_store_id = None
    vector_store_path = None
    found_metadata = False

    # Look for vector store info files
    if os.path.exists(vector_db_dir):
        potential_matches = {} # Store potential matches by base name (UUID)
        for filename in os.listdir(vector_db_dir):
            base_name, ext = os.path.splitext(filename)
            if not base_name.endswith("_info") or not ext.lower() in ['.yaml', '.yml']:
                continue

            info_path = os.path.join(vector_db_dir, filename)
            file_info = None
            store_base_name = base_name[:-5] # e.g., the UUID part

            try:
                logger.debug(f"Loading YAML metadata: {info_path}")
                file_info = load_yaml(info_path)

                if file_info and file_info.get("file_path") == file_path:
                    # Store the match
                    potential_matches[store_base_name] = {
                        "info": file_info,
                        "path": info_path
                    }
                    logger.debug(f"Found potential match: {info_path}")

            except Exception as e:
                logger.warning(f"Could not load or parse metadata file {info_path}: {e}")
                continue

        # Process potential matches
        if potential_matches:
             # Assuming only one match per file_path is expected, take the first one found
            match_key = next(iter(potential_matches)) # Get the first key (UUID)
            match_data = potential_matches[match_key]
            file_info = match_data["info"]
            logger.info(f"Using metadata from: {match_data['path']}")
            vector_store_id = file_info.get("vector_store_id")
            vector_store_path = file_info.get("vector_store_path")
            # Fallback: derive path from ID if not present in metadata
            if not vector_store_path and vector_store_id:
                 vector_store_path = os.path.join(vector_db_dir, vector_store_id)
            found_metadata = True

    else:
        # Create vector_db directory if it doesn't exist
        logger.info(f"Vector DB directory not found, creating: {vector_db_dir}")
        os.makedirs(vector_db_dir, exist_ok=True)

    # If vector store metadata wasn't found or path is missing/invalid, create it
    if not found_metadata or not vector_store_path or not os.path.exists(vector_store_path):
        if found_metadata:
             logger.warning(f"Metadata found for {file_path}, but vector store path '{vector_store_path}' is invalid or missing. Re-embedding.")
        else:
             logger.info(f"No existing vector store metadata found for {file_path}. Embedding document.")

        from agents.tools.mcp.mem0_document_embedding import Mem0DocumentEmbeddingTool

        # Initialize the tool with the correct vector_db_dir
        tool = Mem0DocumentEmbeddingTool()
        await tool.initialize({
            "vector_db_dir": vector_db_dir,
            "data_dir": os.path.dirname(os.path.dirname(os.path.dirname(__file__)))  # Use the backend directory as data_dir
        })

        # Get the absolute path to the file
        abs_file_path = os.path.abspath(file_path)
        logger.info(f"Embedding document with absolute path: {abs_file_path}")

        # Execute the tool to embed the document
        result = await tool.execute({
            "file_path": abs_file_path,
            "operation": "embed"
        })

        if result.get("isError", False):
            logger.error(f"Error embedding document: {result}")
            raise HTTPException(status_code=500, detail=f"Error embedding document: {result.get('content', [{}])[0].get('text', 'Unknown error')}")

        # Get vector store info from result
        metadata = result.get("metadata", {})
        vector_store_id = metadata.get("vector_store_id")
        if not vector_store_id:
            logger.error(f"No vector_store_id in result metadata: {metadata}")
            raise HTTPException(status_code=500, detail="Error creating vector store: No vector_store_id returned")

        vector_store_path = os.path.join(vector_db_dir, vector_store_id)

    # Use mem0ai for vector operations instead of FAISS
    from agents.utils.vector_service import VectorService

    # Initialize vector service
    vector_service = VectorService()

    # Process query based on query type
    if request.query_type == "marketing_fields":
        # Define marketing field queries
        marketing_field_queries = {
            "brand_description": "Based on the provided context, write a concise brand description. Extract information about the company's mission, values, and unique selling points.",
            "target_audience": "Based on the provided context, identify and describe the target audience or customer segments for this business. Include demographics, psychographics, and key characteristics.",
            "products_services": "Based on the provided context, list and briefly describe the main products and/or services offered by the business.",
            "marketing_goals": "Based on the provided context, identify the key marketing goals or objectives for this business. If not explicitly stated, suggest reasonable goals based on the business type and information provided.",
            "existing_content": "Based on the provided context, summarize any existing marketing content, campaigns, or channels mentioned in the document.",
            "keywords": "Based on the provided context, generate a list of 10-15 relevant keywords for this business that could be used for marketing purposes. Format as a comma-separated list.",
            "suggested_topics": "Based on the provided context, suggest 5-7 content topics that would be relevant for this business's marketing strategy. Present as a numbered list."
        }

        # Filter fields if specific fields are requested
        if request.specific_fields:
            marketing_field_queries = {k: v for k, v in marketing_field_queries.items() if k in request.specific_fields}

        # Query the vector store for each marketing field
        results = {}

        # Initialize the LLM
        llm = ChatGroq(
            temperature=0.3,
            model_name="llama3-70b-8192",
            groq_api_key=os.getenv("GROQ_API_KEY", "")
        )

        # Create the prompt template
        prompt = ChatPromptTemplate.from_template(
            """You are an AI assistant that helps extract information from documents.

            Context from document:
            {context}

            Task: {query}

            Provide a concise, well-formatted response based only on the information in the context.
            If the context doesn't contain relevant information, provide a reasonable response based on the type of business or organization mentioned.
            """
        )

        # Create the chain
        chain = prompt | llm

        for field, query in marketing_field_queries.items():
            try:
                # Query the vector store using mem0ai
                results = vector_service.search_document(vector_store_id, query, limit=3)

                if results:
                    # Combine the results
                    context = "\n\n".join([result.get("content", "") for result in results])

                    # Execute the chain
                    response = await chain.ainvoke({"context": context, "query": query})

                    # Store the response
                    results[field] = response.content
                else:
                    results[field] = ""
            except Exception as e:
                logger.error(f"Error querying field {field}: {str(e)}", exc_info=True)
                results[field] = ""

        return DocumentQueryResponse(results=results)
    else:
        raise HTTPException(status_code=400, detail=f"Unsupported query type: {request.query_type}")
