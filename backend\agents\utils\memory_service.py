"""
Memory service for Datagenius using mem0ai.

This module provides a centralized memory service for AI personas using mem0ai,
enabling personalized user experiences, improved context retention, and more
efficient token usage.
"""

import os
import logging
import subprocess
from typing import Dict, Any, List, Optional, Union
from mem0 import Memory

from app.config import (
    MEM0_API_KEY,
    MEM0_ENDPOINT,
    MEM0_SELF_HOSTED,
    MEM0_DEFAULT_TTL,
    MEM0_MAX_MEMORIES,
    MEM0_MEMORY_THRESHOLD
)
from .qdrant_manager import QdrantManager

logger = logging.getLogger(__name__)

class MemoryService:
    """
    Centralized memory service using mem0ai.

    This service provides a unified interface for memory operations across
    all AI personas in the Datagenius application. It implements the Singleton
    pattern to ensure a single instance is shared across the application.
    """

    _instance = None

    def __new__(cls):
        """Implement singleton pattern for memory service."""
        if cls._instance is None:
            cls._instance = super(MemoryService, cls).__new__(cls)
            cls._instance._initialize()
            logger.info("Initialized mem0ai Memory service")
        return cls._instance

    def _initialize(self):
        """Initialize the memory service with configuration."""
        try:
            # Initialize mem0ai Memory with appropriate configuration
            if MEM0_SELF_HOSTED:
                # Initialize for local/self-hosted usage
                logger.info("Initializing mem0ai in self-hosted mode")

                # Get Qdrant connection parameters based on environment
                qdrant_params = QdrantManager.get_qdrant_connection_params()

                # Ensure Qdrant is running
                if not QdrantManager.is_qdrant_running(host=qdrant_params['host'], port=qdrant_params['port']):
                    logger.info(f"Qdrant is not running at {qdrant_params['host']}:{qdrant_params['port']}. Attempting to start it...")

                    # Try to start Qdrant
                    success = QdrantManager.ensure_qdrant_running()

                    # Check again after attempting to start
                    if success and QdrantManager.is_qdrant_running(host=qdrant_params['host'], port=qdrant_params['port']):
                        logger.info(f"Successfully started Qdrant at {qdrant_params['host']}:{qdrant_params['port']}")
                    else:
                        logger.warning(f"Failed to start Qdrant at {qdrant_params['host']}:{qdrant_params['port']}. Memory service will use in-memory storage.")
                        # Fall back to local file-based Qdrant storage
                        logger.info("Falling back to local file-based Qdrant storage")
                        config = {
                            "vector_store": {
                                "provider": "qdrant",  # Use Qdrant with file-based storage
                                "config": {
                                    "collection_name": "mem0",
                                    "path": os.path.join(os.getcwd(), "qdrant_storage"),
                                    "on_disk": True  # Ensure persistence
                                }
                            },
                            "llm": {
                                "provider": "ollama",  # Use Ollama for local LLM
                                "config": {
                                    "model": "llama3",  # Default model, can be changed
                                    "ollama_base_url": "http://localhost:11434"
                                }
                            }
                        }

                        # Initialize Memory with fallback configuration
                        self.memory = Memory.from_config(config)
                        logger.info("Initialized mem0ai with local file-based Qdrant storage and Ollama")
                        return

                logger.info(f"Using Qdrant at {qdrant_params['host']}:{qdrant_params['port']}")

                # Configure for local usage with Qdrant as vector store
                config = {
                    "vector_store": {
                        "provider": "qdrant",
                        "config": qdrant_params
                    },
                    "llm": {
                        "provider": "ollama",  # Use Ollama for local LLM
                        "config": {
                            "model": "llama3",  # Default model, can be changed
                            "ollama_base_url": "http://localhost:11434"
                        }
                    }
                }

                # Initialize Memory with configuration
                self.memory = Memory.from_config(config)
                logger.info("Initialized mem0ai with local Qdrant and Ollama configuration")
            else:
                # Initialize for hosted service with API key
                logger.info("Initializing mem0ai with hosted service")
                api_key = MEM0_API_KEY
                endpoint = MEM0_ENDPOINT if MEM0_ENDPOINT else None

                if not api_key:
                    logger.warning("No mem0ai API key provided for hosted service")

                # Create configuration for hosted service
                if api_key:
                    # Initialize with API key for hosted service
                    self.memory = Memory(api_key=api_key)
                    if endpoint:
                        # Set custom endpoint if provided
                        self.memory.set_endpoint(endpoint)
                else:
                    # Fall back to local mode with minimal configuration
                    logger.warning("No API key provided, falling back to basic local mode")
                    self.memory = Memory()

            # Set configuration from environment or use defaults
            self.default_ttl = MEM0_DEFAULT_TTL
            self.default_limit = 5
            self.max_memories = MEM0_MAX_MEMORIES
            self.memory_threshold = MEM0_MEMORY_THRESHOLD

            # Track initialization status
            self.initialized = True
            logger.info(f"Memory service initialized successfully (self-hosted: {MEM0_SELF_HOSTED})")
        except Exception as e:
            self.initialized = False
            logger.error(f"Failed to initialize memory service: {e}")

    def add_memory(self, content: str, user_id: str, metadata: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Add a memory for a specific user.

        Args:
            content: The content to store in memory
            user_id: The ID of the user this memory belongs to
            metadata: Optional metadata to store with the memory

        Returns:
            Memory object or None if operation failed
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot add memory")
            return None

        try:
            # Ensure metadata is a dictionary
            metadata = metadata or {}

            # Add memory using mem0ai
            result = self.memory.add(content, user_id=user_id, metadata=metadata)

            logger.debug(f"Added memory for user {user_id}: {content[:50]}...")
            return result
        except Exception as e:
            logger.error(f"Error adding memory: {e}")
            return None

    def search_memories(self, query: str, user_id: str, limit: int = None,
                       metadata_filter: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Search for relevant memories for a user.

        Args:
            query: The search query
            user_id: The ID of the user whose memories to search
            limit: Maximum number of results to return (default: self.default_limit)
            metadata_filter: Optional filter for metadata fields

        Returns:
            Dictionary containing search results
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot search memories")
            return {"results": []}

        try:
            # Use default limit if not specified
            limit = limit or self.default_limit

            # Search memories using mem0ai
            results = self.memory.search(
                query,
                user_id=user_id,
                limit=limit,
                metadata_filter=metadata_filter
            )

            logger.debug(f"Found {len(results.get('results', []))} memories for query: {query[:50]}...")
            return results
        except Exception as e:
            logger.error(f"Error searching memories: {e}")
            return {"results": []}

    def add_conversation(self, messages: List[Dict[str, Any]], user_id: str,
                        metadata: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Add a conversation to memory.

        Args:
            messages: List of message dictionaries
            user_id: The ID of the user this conversation belongs to
            metadata: Optional metadata to store with the conversation

        Returns:
            Memory object or None if operation failed
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot add conversation")
            return None

        try:
            # Ensure metadata is a dictionary
            metadata = metadata or {}

            # Format conversation as a string
            if isinstance(messages, list) and messages:
                # Convert messages to a string representation
                conversation_text = self._format_conversation(messages)
            else:
                # Handle empty list or non-list input
                conversation_text = str(messages) if messages else "Empty conversation"

            # Add conversation to memory
            result = self.memory.add(conversation_text, user_id=user_id, metadata=metadata)

            logger.debug(f"Added conversation with {len(messages) if isinstance(messages, list) else 'unknown'} messages for user {user_id}")
            return result
        except Exception as e:
            logger.error(f"Error adding conversation: {e}")
            return None

    def _format_conversation(self, messages: List[Dict[str, Any]]) -> str:
        """
        Format a list of messages into a string for storage.

        Args:
            messages: List of message dictionaries

        Returns:
            Formatted conversation string
        """
        conversation_parts = []

        for msg in messages:
            role = msg.get("role", "unknown")
            content = msg.get("content", "")
            conversation_parts.append(f"{role}: {content}")

        return "\n".join(conversation_parts)

    def delete_memory(self, memory_id: str) -> bool:
        """
        Delete a specific memory.

        Args:
            memory_id: The ID of the memory to delete

        Returns:
            True if successful, False otherwise
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot delete memory")
            return False

        try:
            self.memory.delete(memory_id)
            logger.debug(f"Deleted memory with ID: {memory_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting memory: {e}")
            return False

    def clear_user_memories(self, user_id: str) -> bool:
        """
        Clear all memories for a specific user.

        Args:
            user_id: The ID of the user whose memories to clear

        Returns:
            True if successful, False otherwise
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot clear user memories")
            return False

        try:
            self.memory.clear(user_id=user_id)
            logger.info(f"Cleared all memories for user: {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error clearing memories for user {user_id}: {e}")
            return False
