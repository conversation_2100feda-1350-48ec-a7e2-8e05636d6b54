import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { adminApi } from '@/lib/adminApi';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Loader2,
  AlertCircle,
  Save,
  RefreshCw,
} from 'lucide-react';

// Import settings modules
import GeneralSettings from '@/components/admin/settings/GeneralSettings';
import ApiSettings from '@/components/admin/settings/ApiSettings';
import SecuritySettings from '@/components/admin/settings/SecuritySettings';
import NotificationSettings from '@/components/admin/settings/NotificationSettings';
import IntegrationSettings from '@/components/admin/settings/IntegrationSettings';

const AdminSettings = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);

  // Fetch settings
  const {
    data: settings,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['adminSettings'],
    queryFn: () => adminApi.getSettings(),
  });

  // Handle settings update
  const handleUpdateSettings = async (sectionKey: string, sectionData: any) => {
    setIsSaving(true);
    try {
      await adminApi.updateSettings(sectionKey, sectionData);
      toast({
        title: 'Settings Updated',
        description: 'The settings have been updated successfully.',
      });
      refetch();
    } catch (error) {
      console.error('Error updating settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <AlertCircle className="h-12 w-12 text-destructive mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Settings</h2>
          <p className="text-muted-foreground mb-4">
            There was a problem loading the settings data.
          </p>
          <Button onClick={() => refetch()}>Retry</Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Settings</h1>
          <Button onClick={() => refetch()} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 w-full">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="api">API</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="integrations">Integrations</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Configure general application settings.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <GeneralSettings 
                  settings={settings?.general} 
                  onUpdate={(data) => handleUpdateSettings('general', data)} 
                />
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button 
                  onClick={() => handleUpdateSettings('general', settings?.general)}
                  disabled={isSaving}
                >
                  {isSaving && activeTab === 'general' ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="api" className="space-y-4 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>API Settings</CardTitle>
                <CardDescription>
                  Configure API providers and models.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ApiSettings 
                  settings={settings?.api} 
                  onUpdate={(data) => handleUpdateSettings('api', data)} 
                />
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button 
                  onClick={() => handleUpdateSettings('api', settings?.api)}
                  disabled={isSaving}
                >
                  {isSaving && activeTab === 'api' ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-4 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>
                  Configure security and authentication settings.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SecuritySettings 
                  settings={settings?.security} 
                  onUpdate={(data) => handleUpdateSettings('security', data)} 
                />
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button 
                  onClick={() => handleUpdateSettings('security', settings?.security)}
                  disabled={isSaving}
                >
                  {isSaving && activeTab === 'security' ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-4 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Configure email and notification settings.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <NotificationSettings 
                  settings={settings?.notifications} 
                  onUpdate={(data) => handleUpdateSettings('notifications', data)} 
                />
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button 
                  onClick={() => handleUpdateSettings('notifications', settings?.notifications)}
                  disabled={isSaving}
                >
                  {isSaving && activeTab === 'notifications' ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="integrations" className="space-y-4 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Integration Settings</CardTitle>
                <CardDescription>
                  Configure third-party integrations.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <IntegrationSettings 
                  settings={settings?.integrations} 
                  onUpdate={(data) => handleUpdateSettings('integrations', data)} 
                />
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button 
                  onClick={() => handleUpdateSettings('integrations', settings?.integrations)}
                  disabled={isSaving}
                >
                  {isSaving && activeTab === 'integrations' ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminSettings;
