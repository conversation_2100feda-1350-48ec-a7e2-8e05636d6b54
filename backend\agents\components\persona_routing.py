"""
Component for routing requests to specialized personas based on user needs.
"""

import logging
from typing import Dict, Any, List, Optional

from .base import AgentComponent
from ..registry import AgentRegistry

logger = logging.getLogger(__name__)


class PersonaRoutingComponent(AgentComponent):
    """
    Routes requests to specialized personas based on user needs.
    """

    def __init__(self):
        """Initialize the PersonaRoutingComponent."""
        super().__init__()
        self.agent_registry = AgentRegistry

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"PersonaRoutingComponent '{self.name}' initialized.")
        self.routing_threshold = config.get("routing_threshold", 0.7)
        self.available_personas = {}
        
        # Load available personas
        try:
            self.available_personas = await self.agent_registry.list_personas()
            logger.info(f"Loaded {len(self.available_personas)} personas for routing")
        except Exception as e:
            logger.error(f"Error loading personas for routing: {str(e)}")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the user message to determine if it should be routed to a specialized persona.

        Args:
            context: Context dictionary containing request data.

        Returns:
            Updated context dictionary, potentially with routing information.
        """
        user_message = context.get("message", "")
        logger.debug(f"PersonaRoutingComponent processing message: {user_message}")

        # Check if this is a routing request
        if self._is_routing_request(user_message):
            # Determine the appropriate persona
            persona_id = await self._determine_persona(user_message, context)
            
            if not persona_id:
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["routing_result"] = {
                    "success": False,
                    "message": "I'm not sure which AI persona would be best for your task. Could you provide more details about what you're trying to accomplish?"
                }
                return context
            
            # Add routing information to the context
            context["metadata"] = context.get("metadata", {})
            context["metadata"]["routing_result"] = {
                "success": True,
                "persona_id": persona_id,
                "message": f"I recommend using the {self._get_persona_name(persona_id)} persona for this task."
            }
            
            logger.info(f"Routed request to persona: {persona_id}")
        else:
            logger.debug("Message does not appear to be a routing request.")

        return context

    def _is_routing_request(self, message: str) -> bool:
        """
        Determine if the message is a request to find an appropriate persona.

        Args:
            message: The user message.

        Returns:
            True if this is a routing request, False otherwise.
        """
        # Simple keyword-based detection - could be enhanced with more sophisticated NLP
        routing_keywords = [
            "which persona", "which agent", "recommend a persona", 
            "help me choose", "which ai", "best persona", 
            "who can help", "need help with", "assist me with",
            "analyze", "marketing", "classify"
        ]
        
        return any(keyword in message.lower() for keyword in routing_keywords)

    async def _determine_persona(self, message: str, context: Dict[str, Any]) -> Optional[str]:
        """
        Determine the most appropriate persona for the user's request.

        Args:
            message: The user message.
            context: The current context.

        Returns:
            The ID of the most appropriate persona, or None if no suitable persona is found.
        """
        # Simple rule-based matching - could be enhanced with embeddings and similarity
        message_lower = message.lower()
        
        # Check for data analysis related keywords
        analysis_keywords = ["analyze", "analysis", "data", "chart", "graph", "visualization", 
                           "statistics", "insights", "trends", "dataset", "csv", "excel"]
        if any(keyword in message_lower for keyword in analysis_keywords):
            return self._find_persona_by_capability("data_analysis")
        
        # Check for marketing related keywords
        marketing_keywords = ["marketing", "campaign", "content", "social media", "advertisement", 
                            "copy", "seo", "brand", "promotion", "strategy"]
        if any(keyword in message_lower for keyword in marketing_keywords):
            return self._find_persona_by_capability("marketing")
        
        # Check for classification related keywords
        classification_keywords = ["classify", "classification", "categorize", "sort", "group", 
                                 "label", "tag", "organize"]
        if any(keyword in message_lower for keyword in classification_keywords):
            return self._find_persona_by_capability("classification")
        
        # If no specific keywords match, return None
        return None

    def _find_persona_by_capability(self, capability: str) -> Optional[str]:
        """
        Find a persona that has the specified capability.

        Args:
            capability: The capability to look for.

        Returns:
            The ID of a persona with the specified capability, or None if no such persona is found.
        """
        for persona_id, persona_info in self.available_personas.items():
            if "capabilities" in persona_info and capability in persona_info["capabilities"]:
                return persona_id
        
        # If no persona with the capability is found, return None
        return None

    def _get_persona_name(self, persona_id: str) -> str:
        """
        Get the display name of a persona.

        Args:
            persona_id: The ID of the persona.

        Returns:
            The display name of the persona, or the ID if the persona is not found.
        """
        if persona_id in self.available_personas:
            return self.available_personas[persona_id].get("name", persona_id)
        return persona_id

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["persona_routing"])
