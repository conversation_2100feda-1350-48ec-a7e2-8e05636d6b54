import { useEffect, useRef, useState } from 'react';
import { Network } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { VisualizationData } from '@/utils/visualization';
import Tree from 'react-d3-tree';

interface TreeVisualizationProps {
  visualization: VisualizationData;
  className?: string;
}

export const TreeVisualization = ({ visualization, className = '' }: TreeVisualizationProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const title = visualization.title || 'Tree Visualization';
  const description = visualization.description || 'Hierarchical data visualization';

  // Update dimensions when the component mounts or the window resizes
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height: Math.max(height, 400) });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);

    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, []);

  // Prepare the tree data
  const treeData = visualization.data.tree || {
    name: 'Root',
    children: []
  };

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Network className="h-5 w-5 text-brand-500" />
          <CardTitle className="text-lg">{title}</CardTitle>
        </div>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div 
          ref={containerRef} 
          className="h-[400px] w-full"
        >
          {dimensions.width > 0 && (
            <Tree
              data={treeData}
              orientation="vertical"
              translate={{ x: dimensions.width / 2, y: 50 }}
              collapsible={true}
              zoomable={true}
              nodeSize={{ x: 200, y: 100 }}
              separation={{ siblings: 1, nonSiblings: 2 }}
              pathFunc="step"
              rootNodeClassName="node__root"
              branchNodeClassName="node__branch"
              leafNodeClassName="node__leaf"
            />
          )}
        </div>
      </CardContent>
    </Card>
  );
};
