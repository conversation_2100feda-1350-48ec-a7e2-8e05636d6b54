
import { <PERSON>, <PERSON>H<PERSON>er, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  MoreHorizontal, 
  X, 
  Users, 
  ShoppingCart, 
  CreditCard 
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface MetricsWidgetProps {
  onRemove?: () => void;
}

export function MetricsWidget({ onRemove }: MetricsWidgetProps) {
  const metrics = [
    { name: "Active Customers", value: "1,245", icon: Users, color: "text-blue-500" },
    { name: "New Orders", value: "34", icon: ShoppingCart, color: "text-green-500" },
    { name: "Pending Payments", value: "12", icon: CreditCard, color: "text-yellow-500" },
  ];

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Key Metrics</CardTitle>
        <div className="flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Refresh</DropdownMenuItem>
              <DropdownMenuItem>View details</DropdownMenuItem>
              {onRemove && (
                <DropdownMenuItem onClick={onRemove}>
                  Remove widget
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
          {onRemove && (
            <Button variant="ghost" size="icon" onClick={onRemove} className="h-8 w-8">
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {metrics.map((metric) => (
            <div key={metric.name} className="flex items-center">
              <div className={`mr-2 ${metric.color}`}>
                <metric.icon className="h-4 w-4" />
              </div>
              <div className="flex-1">
                <div className="text-sm">{metric.name}</div>
              </div>
              <div className="font-medium">{metric.value}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
