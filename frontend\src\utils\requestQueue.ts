/**
 * Request queue utility for managing API requests.
 */

interface QueuedRequest {
  id: string;
  execute: () => Promise<any>;
  priority: number;
  timestamp: number;
  resolve: (value: any) => void;
  reject: (reason: any) => void;
}

interface RequestQueueOptions {
  /** Maximum number of concurrent requests */
  concurrency?: number;
  /** Maximum number of requests in the queue */
  maxQueueSize?: number;
  /** Whether to process requests in FIFO order (true) or by priority (false) */
  fifo?: boolean;
}

/**
 * A request queue for managing API requests with concurrency control.
 */
export class RequestQueue {
  private static instance: RequestQueue;
  private queue: QueuedRequest[] = [];
  private activeRequests: number = 0;
  private concurrency: number = 4;
  private maxQueueSize: number = 100;
  private fifo: boolean = true;
  private requestCounter: number = 0;

  private constructor(options: RequestQueueOptions = {}) {
    this.concurrency = options.concurrency || this.concurrency;
    this.maxQueueSize = options.maxQueueSize || this.maxQueueSize;
    this.fifo = options.fifo !== undefined ? options.fifo : this.fifo;
  }

  /**
   * Get the singleton instance of the request queue.
   */
  public static getInstance(options: RequestQueueOptions = {}): RequestQueue {
    if (!RequestQueue.instance) {
      RequestQueue.instance = new RequestQueue(options);
    }
    return RequestQueue.instance;
  }

  /**
   * Enqueue a request to be executed.
   * @param request The request function to execute
   * @param priority The priority of the request (higher is more important)
   * @returns A promise that resolves with the request result
   */
  public enqueue<T>(request: () => Promise<T>, priority: number = 0): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      // Check if the queue is full
      if (this.queue.length >= this.maxQueueSize) {
        reject(new Error('Request queue is full'));
        return;
      }

      // Create a unique ID for the request
      const id = `request-${++this.requestCounter}`;

      // Add the request to the queue
      this.queue.push({
        id,
        execute: request,
        priority,
        timestamp: Date.now(),
        resolve,
        reject,
      });

      // Sort the queue if not using FIFO
      if (!this.fifo) {
        this.sortQueue();
      }

      // Process the queue
      this.processQueue();
    });
  }

  /**
   * Process the next request in the queue if there are available slots.
   */
  private processQueue(): void {
    // If we're at max concurrency or the queue is empty, do nothing
    if (this.activeRequests >= this.concurrency || this.queue.length === 0) {
      return;
    }

    // Get the next request from the queue
    const request = this.queue.shift();
    if (!request) {
      return;
    }

    // Increment the active requests counter
    this.activeRequests++;

    // Execute the request
    request
      .execute()
      .then((result) => {
        request.resolve(result);
      })
      .catch((error) => {
        request.reject(error);
      })
      .finally(() => {
        // Decrement the active requests counter
        this.activeRequests--;

        // Process the next request in the queue
        this.processQueue();
      });
  }

  /**
   * Sort the queue by priority (higher priority first) and then by timestamp (older first).
   */
  private sortQueue(): void {
    this.queue.sort((a, b) => {
      // Sort by priority (higher first)
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }

      // Then by timestamp (older first)
      return a.timestamp - b.timestamp;
    });
  }

  /**
   * Get the number of requests in the queue.
   */
  public size(): number {
    return this.queue.length;
  }

  /**
   * Get the number of active requests.
   */
  public active(): number {
    return this.activeRequests;
  }

  /**
   * Clear all requests from the queue.
   */
  public clear(): void {
    // Reject all queued requests
    for (const request of this.queue) {
      request.reject(new Error('Request queue cleared'));
    }

    // Clear the queue
    this.queue = [];
  }
}

// Export a singleton instance
export const requestQueue = RequestQueue.getInstance();
