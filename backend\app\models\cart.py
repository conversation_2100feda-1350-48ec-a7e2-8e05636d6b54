"""
Cart models for the Datagenius backend.

This module provides Pydantic models for cart functionality.
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel


class CartItemBase(BaseModel):
    """Base model for cart item data."""
    persona_id: str
    quantity: int = 1


class CartItemCreate(CartItemBase):
    """Model for creating a new cart item."""
    pass


class CartItemResponse(CartItemBase):
    """Model for cart item data returned to the client."""
    id: str
    user_id: int
    created_at: datetime

    class Config:
        from_attributes = True


class CartResponse(BaseModel):
    """Model for cart data returned to the client."""
    items: List[CartItemResponse]
    total_items: int


class AddToCartRequest(BaseModel):
    """Model for adding an item to the cart."""
    persona_id: str
    quantity: int = 1


class RemoveFromCartRequest(BaseModel):
    """Model for removing an item from the cart."""
    cart_item_id: str
