"""
Component for assigning specialized roles to personas.
"""

import logging
import json
import time
import uuid
from typing import Dict, Any, List, Optional, Tuple, Set

from backend.schemas.agent_config_schemas import AgentProcessingContext # Added
from .base import AgentComponent
from ..registry import AgentRegistry

logger = logging.getLogger(__name__)


class RoleType:
    """Defines specialized role types for personas."""

    ANALYST = "analyst"
    STRATEGIST = "strategist"
    EXECUTOR = "executor"
    REVIEWER = "reviewer"
    COORDINATOR = "coordinator"
    SPECIALIST = "specialist"
    GENERALIST = "generalist"


class RoleAssignmentComponent(AgentComponent):
    """
    Assigns specialized roles to personas based on task requirements,
    enabling more effective collaboration and task execution.
    """

    def __init__(self):
        """Initialize the RoleAssignmentComponent."""
        super().__init__()
        self.role_assignments = {}  # Track role assignments by conversation
        self.role_capabilities = {}  # Map roles to capabilities
        self.persona_affinities = {}  # Map personas to role affinities

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"RoleAssignmentComponent '{self.name}' initialized.")
        self.agent_registry = AgentRegistry
        self.assignment_ttl = config.get("assignment_ttl", 3600)  # Default TTL: 1 hour
        self.enable_auto_assignment = config.get("enable_auto_assignment", True)
        self.assignment_threshold = config.get("assignment_threshold", 0.7)

        # Load role capabilities from configuration with dynamic defaults
        default_role_capabilities = {
            RoleType.ANALYST: ["data_analysis", "pattern_recognition", "insight_generation"],
            RoleType.STRATEGIST: ["planning", "goal_setting", "strategic_thinking"],
            RoleType.EXECUTOR: ["implementation", "task_execution", "action_taking"],
            RoleType.REVIEWER: ["quality_control", "verification", "feedback"],
            RoleType.COORDINATOR: ["coordination", "communication", "delegation"],
            RoleType.SPECIALIST: ["domain_expertise", "specialized_knowledge", "deep_focus"],
            RoleType.GENERALIST: ["broad_knowledge", "adaptability", "versatility"]
        }
        self.role_capabilities = config.get("role_capabilities", default_role_capabilities)

        # Load persona affinities from configuration or generate dynamically
        self.persona_affinities = config.get("persona_affinities", {})
        if not self.persona_affinities:
            self.persona_affinities = self._generate_dynamic_persona_affinities()

    def _generate_dynamic_persona_affinities(self) -> Dict[str, List[str]]:
        """Generate dynamic persona affinities based on available personas."""
        affinities = {}
        available_personas = self.agent_registry.list_registered_personas()

        for persona_id in available_personas:
            # Infer role affinities from persona name/type
            persona_roles = []

            if "analyst" in persona_id or "analysis" in persona_id:
                persona_roles.extend([RoleType.ANALYST, RoleType.SPECIALIST])
            elif "market" in persona_id:
                persona_roles.extend([RoleType.STRATEGIST, RoleType.EXECUTOR])
            elif "classif" in persona_id:
                persona_roles.extend([RoleType.SPECIALIST, RoleType.ANALYST])
            elif "concierge" in persona_id:
                persona_roles.extend([RoleType.COORDINATOR, RoleType.GENERALIST])
            else:
                # Default roles for unknown personas
                persona_roles.extend([RoleType.GENERALIST])

            affinities[persona_id] = persona_roles

        logger.info(f"Generated dynamic persona affinities for {len(affinities)} personas")
        return affinities

    async def process(self, context: AgentProcessingContext) -> AgentProcessingContext:
        """
        Process the context to assign specialized roles.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object with role assignments.
        """
        user_message = context.message or ""
        conversation_id = str(context.conversation_id) # Ensure string
        current_persona = context.agent_config.id if context.agent_config else "unknown"

        logger.debug(f"RoleAssignmentComponent processing for conversation {conversation_id}")

        # Initialize role context in component_data if not present
        component_specific_data = context.component_data.setdefault(self.name, {})
        roles_data = component_specific_data.setdefault("roles", {
            "assigned_role": None,
            "role_capabilities": [],
            "team_roles": {} # This might be conversation specific, stored in self.role_assignments
        })

        # Check if this conversation has role assignments (from self.role_assignments)
        if conversation_id in self.role_assignments:
            assigned_role_for_current = self.role_assignments[conversation_id].get(current_persona)
            if assigned_role_for_current:
                roles_data["assigned_role"] = assigned_role_for_current
                roles_data["role_capabilities"] = self.role_capabilities.get(assigned_role_for_current, [])
                roles_data["team_roles"] = self.role_assignments[conversation_id] # Entire team's roles for this convo

                context.metadata["current_persona_role"] = { # More specific key
                    "assigned_role": assigned_role_for_current,
                    "capabilities": self.role_capabilities.get(assigned_role_for_current, [])
                }
                logger.debug(f"Updated context with assigned role {assigned_role_for_current} for {current_persona}")

        # Check for role assignment commands
        if self._is_role_assignment_command(user_message):
            target_persona, role_type = self._extract_role_assignment_info(user_message)
            if target_persona and role_type:
                success = await self._assign_role(conversation_id, target_persona, role_type)
                
                assignment_message = f"I've assigned the {role_type} role to {target_persona}." if success else f"I couldn't assign the {role_type} role to {target_persona}."
                context.metadata["role_assignment_result"] = { # Renamed key
                    "success": success,
                    "target_persona": target_persona,
                    "role": role_type,
                    "message": assignment_message
                }
                context.response = assignment_message

                if success and conversation_id in self.role_assignments:
                    roles_data["team_roles"] = self.role_assignments[conversation_id]
                logger.info(f"Assigned role {role_type} to {target_persona} in conversation {conversation_id}")
            else:
                logger.warning(f"Role assignment command detected but no valid parameters found: {user_message}")

        # Check for role query commands
        if self._is_role_query_command(user_message):
            target_persona_query = self._extract_role_query_info(user_message) # Renamed var
            if target_persona_query:
                queried_assigned_role = None # Renamed var
                if conversation_id in self.role_assignments:
                    queried_assigned_role = self.role_assignments[conversation_id].get(target_persona_query)
                
                query_message = f"{target_persona_query} is assigned the {queried_assigned_role} role." if queried_assigned_role else f"{target_persona_query} has no assigned role."
                context.metadata["role_query_result"] = { # Renamed key
                    "target_persona": target_persona_query,
                    "assigned_role": queried_assigned_role,
                    "capabilities": self.role_capabilities.get(queried_assigned_role, []) if queried_assigned_role else [],
                    "message": query_message
                }
                context.response = query_message
                logger.info(f"Queried role for {target_persona_query} in conversation {conversation_id}")
            else:
                logger.warning(f"Role query command detected but no valid parameters found: {user_message}")

        # Check for automatic role assignment for the current_persona
        # roles_data["assigned_role"] should reflect the current persona's role if already set
        if self.enable_auto_assignment and not roles_data.get("assigned_role"): 
            role_needed, suggested_role, reason = self._detect_role_need(user_message, current_persona, context) # Pass AgentProcessingContext
            if role_needed and suggested_role: # Ensure suggested_role is not None
                suggestion_message = f"Based on the task, you might be most effective in the {suggested_role} role. Would you like me to assign this role to you?"
                context.metadata["auto_role_suggestion"] = {
                    "role": suggested_role,
                    "reason": reason,
                    "message": suggestion_message
                }
                # context.response = suggestion_message # Optionally ask the user
                logger.info(f"Automatic role suggestion: {suggested_role} for {current_persona} in conversation {conversation_id}")

        return context

    def _is_role_assignment_command(self, message: str) -> bool:
        """
        Determine if the message is a command to assign a role.

        Args:
            message: The user message.

        Returns:
            True if this is a role assignment command, False otherwise.
        """
        # Simple keyword-based detection
        assignment_keywords = [
            "assign role", "give role", "set role",
            "make someone", "designate as", "appoint as"
        ]

        return any(keyword in message.lower() for keyword in assignment_keywords)

    def _extract_role_assignment_info(self, message: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract the target persona and role type from a role assignment command.

        Args:
            message: The user message.

        Returns:
            A tuple containing the target persona and the role type.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Map keywords to consistent kebab-case persona IDs
        persona_mapping = {
            "analyst": "composable-analysis-ai",
            "analysis": "composable-analysis-ai",
            "marketer": "composable-marketing-ai",
            "marketing": "composable-marketing-ai",
            "classifier": "composable-classifier-ai",
            "classification": "composable-classifier-ai",
            "concierge": "concierge-agent"
        }
        target_persona = None

        for keyword, persona_id_val in persona_mapping.items(): # Renamed persona_id
            if keyword in message_lower:
                target_persona = persona_id_val
                break

        if not target_persona:
            return None, None

        # Check for role types
        role_types = [
            RoleType.ANALYST, RoleType.STRATEGIST, RoleType.EXECUTOR,
            RoleType.REVIEWER, RoleType.COORDINATOR, RoleType.SPECIALIST,
            RoleType.GENERALIST
        ]
        extracted_role_type = None # Renamed role_type

        for role_val in role_types: # Renamed role
            if role_val in message_lower:
                extracted_role_type = role_val
                break

        if not extracted_role_type:
            # Try to infer role from context
            if "analyze" in message_lower or "analysis" in message_lower:
                extracted_role_type = RoleType.ANALYST
            elif "strategy" in message_lower or "plan" in message_lower:
                extracted_role_type = RoleType.STRATEGIST
            elif "execute" in message_lower or "implement" in message_lower:
                extracted_role_type = RoleType.EXECUTOR
            elif "review" in message_lower or "check" in message_lower:
                extracted_role_type = RoleType.REVIEWER
            elif "coordinate" in message_lower or "manage" in message_lower:
                extracted_role_type = RoleType.COORDINATOR
            elif "specialist" in message_lower or "expert" in message_lower:
                extracted_role_type = RoleType.SPECIALIST
            else:
                extracted_role_type = RoleType.GENERALIST

        return target_persona, extracted_role_type

    async def _assign_role(self, conversation_id: str, persona_id: str, role_type: str) -> bool:
        """
        Assign a role to a persona.

        Args:
            conversation_id: The ID of the conversation.
            persona_id: The ID of the persona.
            role_type: The type of role to assign.

        Returns:
            True if the assignment was successful, False otherwise.
        """
        # Initialize role assignments for this conversation if needed
        if conversation_id not in self.role_assignments:
            self.role_assignments[conversation_id] = {}

        # Assign the role
        self.role_assignments[conversation_id][persona_id] = role_type

        logger.info(f"Assigned role {role_type} to {persona_id} in conversation {conversation_id}")

        return True

    def _is_role_query_command(self, message: str) -> bool:
        """
        Determine if the message is a command to query a role.

        Args:
            message: The user message.

        Returns:
            True if this is a role query command, False otherwise.
        """
        # Simple keyword-based detection
        query_keywords = [
            "what role", "which role", "role of",
            "assigned role", "current role", "role assigned"
        ]

        return any(keyword in message.lower() for keyword in query_keywords)

    def _extract_role_query_info(self, message: str) -> Optional[str]:
        """
        Extract the target persona from a role query command.

        Args:
            message: The user message.

        Returns:
            The target persona if found, None otherwise.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Check for persona names
        persona_keywords = ["analyst", "marketer", "classifier", "concierge", "analysis", "marketing", "classification"] # Renamed personas
        target_persona = None

        for persona_keyword in persona_keywords: # Renamed persona
            if persona_keyword in message_lower:
                target_persona = persona_keyword # Assuming keyword is usable as/maps to persona ID
                break

        return target_persona

    def _detect_role_need(self, message: str, persona_id: str, context: AgentProcessingContext) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Detect if a role assignment would be beneficial based on the message content.

        Args:
            message: The user message.
            persona_id: The ID of the current persona.
            context: The current AgentProcessingContext (currently unused in this method).

        Returns:
            A tuple containing a boolean indicating if a role is needed, the suggested role, and the reason.
        """
        # Simple rule-based detection - could be enhanced with NLP
        message_lower = message.lower()

        # Check for role-specific keywords
        analyst_keywords = ["analyze", "analysis", "data", "insights", "patterns"]
        strategist_keywords = ["strategy", "plan", "goals", "objectives", "vision"]
        executor_keywords = ["execute", "implement", "action", "do", "perform"]
        reviewer_keywords = ["review", "check", "verify", "validate", "assess"]
        coordinator_keywords = ["coordinate", "manage", "organize", "delegate", "oversee"]
        specialist_keywords = ["specific", "specialized", "expert", "detailed", "focused"]
        generalist_keywords = ["general", "broad", "overview", "versatile", "adaptable"]

        # Count keyword matches for each role
        role_scores = {
            RoleType.ANALYST: sum(1 for kw in analyst_keywords if kw in message_lower),
            RoleType.STRATEGIST: sum(1 for kw in strategist_keywords if kw in message_lower),
            RoleType.EXECUTOR: sum(1 for kw in executor_keywords if kw in message_lower),
            RoleType.REVIEWER: sum(1 for kw in reviewer_keywords if kw in message_lower),
            RoleType.COORDINATOR: sum(1 for kw in coordinator_keywords if kw in message_lower),
            RoleType.SPECIALIST: sum(1 for kw in specialist_keywords if kw in message_lower),
            RoleType.GENERALIST: sum(1 for kw in generalist_keywords if kw in message_lower)
        }

        # Get the persona's role affinities
        affinities = self.persona_affinities.get(persona_id, [])

        # Boost scores for roles the persona has affinity for
        for role in affinities:
            if role in role_scores:
                role_scores[role] += 2

        # Find the role with the highest score
        max_score = 0
        suggested_role = None

        for role, score in role_scores.items():
            if score > max_score:
                max_score = score
                suggested_role = role

        # Determine if a role assignment is needed
        if max_score >= self.assignment_threshold:
            return True, suggested_role, f"message contains keywords related to {suggested_role} role"

        # If no clear role is detected, suggest a role based on persona affinities
        if affinities:
            return True, affinities[0], f"based on {persona_id}'s affinity for {affinities[0]} role"

        return False, None, None

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", [
            "role_assignment",
            "specialized_roles",
            "role_based_capabilities",
            "team_role_management"
        ])
