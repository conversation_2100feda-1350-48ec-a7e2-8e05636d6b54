import { apiRequest } from './api';

// Types
export interface DataSource {
  id: string;
  name: string;
  type: 'file' | 'database' | 'api' | 'mcp';
  description?: string;
  is_active: boolean;
  metadata: any; // General metadata
  source_metadata?: any; // Specific metadata (e.g., file_id for files)
  user_id: number;
  created_at: string;
  updated_at: string;
}

export interface FileDataSourceCreate {
  name: string;
  type: 'file';
  description?: string;
  is_active?: boolean;
  file_id: string;
}

export interface DatabaseDataSourceCreate {
  name: string;
  type: 'database';
  description?: string;
  is_active?: boolean;
  db_type: string;
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
}

export interface ApiDataSourceCreate {
  name: string;
  type: 'api';
  description?: string;
  is_active?: boolean;
  api_type: string;
  endpoint: string;
  auth_type?: string;
  auth_credentials?: any;
}

export interface McpDataSourceCreate {
  name: string;
  type: 'mcp';
  description?: string;
  is_active?: boolean;
  endpoint: string;
  api_key?: string;
  namespace: string;
  collection_ids: string[];
}

// Data source API functions
export const dataSourceApi = {
  // Create a file data source
  createFileDataSource: async (dataSource: FileDataSourceCreate): Promise<DataSource> => {
    return apiRequest('/data-sources/file', {
      method: 'POST',
      body: JSON.stringify(dataSource),
    });
  },

  // Create a database data source
  createDatabaseDataSource: async (dataSource: DatabaseDataSourceCreate): Promise<DataSource> => {
    return apiRequest('/data-sources/database', {
      method: 'POST',
      body: JSON.stringify(dataSource),
    });
  },

  // Create an API data source
  createApiDataSource: async (dataSource: ApiDataSourceCreate): Promise<DataSource> => {
    return apiRequest('/data-sources/api', {
      method: 'POST',
      body: JSON.stringify(dataSource),
    });
  },

  // Create an MCP data source
  createMcpDataSource: async (dataSource: McpDataSourceCreate): Promise<DataSource> => {
    return apiRequest('/data-sources/mcp', {
      method: 'POST',
      body: JSON.stringify(dataSource),
    });
  },

  // Get a list of all data sources
  getDataSources: async (type?: string): Promise<{ data_sources: DataSource[] }> => {
    const endpoint = type ? `/data-sources?type=${type}` : '/data-sources';
    return apiRequest(endpoint, {
      cache: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutes
    });
  },

  // Get a specific data source
  getDataSource: async (dataSourceId: string): Promise<DataSource> => {
    return apiRequest(`/data-sources/${dataSourceId}`, {
      cache: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutes
    });
  },

  // Delete a data source
  deleteDataSource: async (dataSourceId: string): Promise<{ message: string }> => {
    return apiRequest(`/data-sources/${dataSourceId}`, {
      method: 'DELETE',
    });
  },

  // Query marketing fields for a document
  queryDocumentMarketingFields: async (fileId: string): Promise<{ results: Record<string, string> }> => {
    return apiRequest('/document-query', {
      method: 'POST',
      body: JSON.stringify({
        file_id: fileId,
        query_type: 'marketing_fields',
      }),
    });
  },
};
