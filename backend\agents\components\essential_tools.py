"""
Essential MCP tools for all agents in the Datagenius system.

This module provides functions to ensure that all agents have the essential MCP tools
they need to function properly, regardless of their specific type.
"""

import logging
from typing import Dict, Any, List

from .mcp_server import MCPServerComponent
from ..tools.mcp.registry import MCPToolRegistry

logger = logging.getLogger(__name__)

# List of essential tool types that should be available to all agents
ESSENTIAL_TOOL_TYPES = [
    "content_generation",  # Required for generating greetings and responses
    "text_processing",     # Basic text processing capabilities
    "document_embedding",  # For handling documents
    "data_access"          # For accessing data
]

async def ensure_essential_tools(mcp_server: MCPServerComponent) -> None:
    """
    Ensure that the MCP server component has all essential tools.
    
    Args:
        mcp_server: The MCP server component to check and update
    """
    # Get the list of currently available tools
    available_tools = set(mcp_server.tools.keys())
    logger.info(f"Current tools in MCP server: {available_tools}")
    
    # Check which essential tools are missing
    for tool_type in ESSENTIAL_TOOL_TYPES:
        # Get the tool class from the registry
        tool_class = MCPToolRegistry.get_tool_class(tool_type)
        if not tool_class:
            logger.error(f"Essential tool type '{tool_type}' not found in registry")
            continue
            
        # Create a temporary instance to get the tool name
        temp_tool = tool_class()
        tool_name = temp_tool.name
        
        # Check if the tool is already available
        if tool_name in available_tools:
            logger.info(f"Essential tool '{tool_name}' already available")
            continue
            
        # Add the missing tool
        try:
            logger.info(f"Adding essential tool '{tool_name}' to MCP server")
            tool = tool_class()
            await tool.initialize({})
            mcp_server.tools[tool_name] = tool
            logger.info(f"Added essential tool: {tool_name}")
        except Exception as e:
            logger.error(f"Error initializing essential tool '{tool_type}': {e}")
    
    # Log the updated list of tools
    logger.info(f"Updated tools in MCP server: {set(mcp_server.tools.keys())}")

async def create_mcp_server_with_essential_tools(config: Dict[str, Any] = None) -> MCPServerComponent:
    """
    Create an MCP server component with all essential tools.
    
    Args:
        config: Configuration dictionary for the MCP server component
        
    Returns:
        Initialized MCP server component with essential tools
    """
    if config is None:
        config = {}
        
    # Create and initialize the MCP server component
    mcp_server = MCPServerComponent()
    await mcp_server.initialize(config)
    
    # Ensure essential tools are available
    await ensure_essential_tools(mcp_server)
    
    return mcp_server
