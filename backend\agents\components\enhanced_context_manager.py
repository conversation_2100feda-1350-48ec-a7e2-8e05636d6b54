"""
Enhanced component for managing shared context across agent interactions and personas.
"""

import logging
import json
import time
import uuid
from typing import Dict, Any, List, Optional, Set

from .base import AgentComponent
from .context_manager import ContextManagerComponent

logger = logging.getLogger(__name__)


class EnhancedContextManagerComponent(ContextManagerComponent):
    """
    Enhanced version of the ContextManagerComponent with improved context sharing,
    synchronization, and persistence capabilities.
    """

    def __init__(self):
        """Initialize the EnhancedContextManagerComponent."""
        super().__init__()
        self.context_history = {}  # Store historical context for each conversation
        self.shared_entities = {}  # Store shared entities across personas
        self.context_versions = {}  # Track context versions for synchronization

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        await super()._initialize(config)
        logger.info(f"EnhancedContextManagerComponent '{self.name}' initialized.")
        self.max_history_size = config.get("max_history_size", 10)  # Maximum number of historical contexts to keep
        self.sync_interval = config.get("sync_interval", 300)  # Sync interval in seconds
        self.enable_entity_tracking = config.get("enable_entity_tracking", True)
        self.enable_context_versioning = config.get("enable_context_versioning", True)

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the context with enhanced context management capabilities.

        Args:
            context: Context dictionary containing request data.

        Returns:
            Updated context dictionary with enhanced context management.
        """
        # First, process with the base context manager
        context = await super().process(context)

        user_message = context.get("message", "")
        conversation_id = context.get("conversation_id")
        current_persona = context.get("persona_id", "unknown")

        logger.debug(f"EnhancedContextManagerComponent processing for conversation {conversation_id}")

        # Initialize enhanced context if not present
        if "enhanced_context" not in context:
            context["enhanced_context"] = {
                "version": 1,
                "last_sync": time.time(),
                "tracked_entities": {},
                "context_history": []
            }

        # Track context history
        await self._track_context_history(conversation_id, context)

        # Track entities across personas
        if self.enable_entity_tracking:
            await self._track_entities(conversation_id, context)

        # Version the context for synchronization
        if self.enable_context_versioning:
            await self._version_context(conversation_id, current_persona, context)

        # Check if context synchronization is needed
        if self._needs_synchronization(context):
            await self._synchronize_context(conversation_id, current_persona, context)

        return context

    async def _track_context_history(self, conversation_id: str, context: Dict[str, Any]) -> None:
        """
        Track the history of context for a conversation.

        Args:
            conversation_id: The ID of the conversation.
            context: The current context.
        """
        # Create a simplified version of the context for history
        history_entry = {
            "timestamp": time.time(),
            "persona_id": context.get("persona_id", "unknown"),
            "message": context.get("message", ""),
            "shared_context": context.get("shared_context", {}),
            "enhanced_context": context.get("enhanced_context", {})
        }

        # Add to history
        if conversation_id not in self.context_history:
            self.context_history[conversation_id] = []

        self.context_history[conversation_id].append(history_entry)

        # Limit history size
        if len(self.context_history[conversation_id]) > self.max_history_size:
            self.context_history[conversation_id] = self.context_history[conversation_id][-self.max_history_size:]

        # Add recent history to the context
        context["enhanced_context"]["context_history"] = self.context_history[conversation_id][-3:]  # Last 3 entries
        logger.debug(f"Tracked context history for conversation {conversation_id}")

    async def _track_entities(self, conversation_id: str, context: Dict[str, Any]) -> None:
        """
        Track entities across personas for a conversation.

        Args:
            conversation_id: The ID of the conversation.
            context: The current context.
        """
        # Initialize shared entities for this conversation if needed
        if conversation_id not in self.shared_entities:
            self.shared_entities[conversation_id] = {}

        # Extract entities from the current context
        current_entities = context.get("shared_context", {}).get("entities", {})
        if current_entities:
            # Merge with existing entities
            for entity_type, entities in current_entities.items():
                if entity_type not in self.shared_entities[conversation_id]:
                    self.shared_entities[conversation_id][entity_type] = set()

                if isinstance(entities, list):
                    self.shared_entities[conversation_id][entity_type].update(entities)
                else:
                    self.shared_entities[conversation_id][entity_type].add(entities)

            # Convert sets back to lists for JSON serialization
            serializable_entities = {}
            for entity_type, entities in self.shared_entities[conversation_id].items():
                serializable_entities[entity_type] = list(entities)

            # Update the context with all tracked entities
            context["enhanced_context"]["tracked_entities"] = serializable_entities
            logger.debug(f"Tracked entities for conversation {conversation_id}: {serializable_entities}")

    async def _version_context(self, conversation_id: str, persona_id: str, context: Dict[str, Any]) -> None:
        """
        Version the context for synchronization.

        Args:
            conversation_id: The ID of the conversation.
            persona_id: The ID of the current persona.
            context: The current context.
        """
        # Initialize context versions for this conversation if needed
        if conversation_id not in self.context_versions:
            self.context_versions[conversation_id] = {
                "global_version": 1,
                "persona_versions": {}
            }

        # Get the current version
        current_version = context["enhanced_context"].get("version", 1)

        # Check if this persona has a version
        if persona_id not in self.context_versions[conversation_id]["persona_versions"]:
            self.context_versions[conversation_id]["persona_versions"][persona_id] = current_version

        # Update the version if needed
        if current_version < self.context_versions[conversation_id]["global_version"]:
            # This persona has an outdated version
            context["enhanced_context"]["version"] = self.context_versions[conversation_id]["global_version"]
            context["enhanced_context"]["needs_sync"] = True
            logger.debug(f"Updated context version for {persona_id} in conversation {conversation_id} to {self.context_versions[conversation_id]['global_version']}")
        elif current_version > self.context_versions[conversation_id]["global_version"]:
            # This persona has a newer version, update the global version
            self.context_versions[conversation_id]["global_version"] = current_version
            logger.debug(f"Updated global context version for conversation {conversation_id} to {current_version}")

        # Update the persona version
        self.context_versions[conversation_id]["persona_versions"][persona_id] = context["enhanced_context"]["version"]

    def _needs_synchronization(self, context: Dict[str, Any]) -> bool:
        """
        Determine if context synchronization is needed.

        Args:
            context: The current context.

        Returns:
            True if synchronization is needed, False otherwise.
        """
        enhanced_context = context.get("enhanced_context", {})
        
        # Check if explicit sync is needed
        if enhanced_context.get("needs_sync", False):
            return True
        
        # Check if sync interval has passed
        last_sync = enhanced_context.get("last_sync", 0)
        if time.time() - last_sync > self.sync_interval:
            return True
        
        return False

    async def _synchronize_context(self, conversation_id: str, persona_id: str, context: Dict[str, Any]) -> None:
        """
        Synchronize context across personas.

        Args:
            conversation_id: The ID of the conversation.
            persona_id: The ID of the current persona.
            context: The current context.
        """
        # Update last sync time
        context["enhanced_context"]["last_sync"] = time.time()
        context["enhanced_context"]["needs_sync"] = False
        
        # Merge tracked entities
        if self.enable_entity_tracking and conversation_id in self.shared_entities:
            serializable_entities = {}
            for entity_type, entities in self.shared_entities[conversation_id].items():
                serializable_entities[entity_type] = list(entities)
            
            context["enhanced_context"]["tracked_entities"] = serializable_entities
        
        # Update context history
        if conversation_id in self.context_history:
            context["enhanced_context"]["context_history"] = self.context_history[conversation_id][-3:]  # Last 3 entries
        
        logger.debug(f"Synchronized context for {persona_id} in conversation {conversation_id}")

    async def _save_context(self, conversation_id: str, context: Dict[str, Any], target_persona: str) -> None:
        """
        Save context for transfer to another persona.

        Args:
            conversation_id: The ID of the conversation.
            context: The current context.
            target_persona: The target persona.
        """
        # Create an enhanced version of the context to share
        shared_context = {
            "conversation_id": conversation_id,
            "user_id": context.get("user_id"),
            "source_persona": context.get("persona_id", "concierge"),
            "conversation_history": context.get("conversation_history", []),
            "attached_files": context.get("attached_files", []),
            "shared_context": context.get("shared_context", {}),
            "enhanced_context": context.get("enhanced_context", {}),
            "metadata": {
                "shared_from": context.get("persona_id", "concierge"),
                "timestamp": time.time(),
                "original_request": context.get("message"),
                "transfer_id": str(uuid.uuid4())  # Add a unique ID for this transfer
            }
        }

        # Store the shared context
        key = f"{conversation_id}:{target_persona}"
        self.context_store[key] = shared_context
        logger.debug(f"Saved enhanced context for transfer: {key}")

        # Update context versions
        if self.enable_context_versioning and conversation_id in self.context_versions:
            # Increment the global version
            self.context_versions[conversation_id]["global_version"] += 1
            # Update the source persona version
            source_persona = context.get("persona_id", "concierge")
            self.context_versions[conversation_id]["persona_versions"][source_persona] = self.context_versions[conversation_id]["global_version"]
            logger.debug(f"Updated context version for transfer: {self.context_versions[conversation_id]['global_version']}")

    def _merge_context(self, target_context: Dict[str, Any], shared_context: Dict[str, Any]) -> None:
        """
        Merge shared context into the target context with enhanced capabilities.

        Args:
            target_context: The target context to update.
            shared_context: The shared context to merge.
        """
        # First, use the base merge functionality
        super()._merge_context(target_context, shared_context)

        # Merge enhanced context
        if "enhanced_context" in shared_context:
            if "enhanced_context" not in target_context:
                target_context["enhanced_context"] = {}
            
            # Merge tracked entities
            if "tracked_entities" in shared_context["enhanced_context"]:
                if "tracked_entities" not in target_context["enhanced_context"]:
                    target_context["enhanced_context"]["tracked_entities"] = {}
                
                for entity_type, entities in shared_context["enhanced_context"]["tracked_entities"].items():
                    if entity_type not in target_context["enhanced_context"]["tracked_entities"]:
                        target_context["enhanced_context"]["tracked_entities"][entity_type] = []
                    
                    # Merge entities, avoiding duplicates
                    existing_entities = set(target_context["enhanced_context"]["tracked_entities"][entity_type])
                    existing_entities.update(entities)
                    target_context["enhanced_context"]["tracked_entities"][entity_type] = list(existing_entities)
            
            # Merge context history
            if "context_history" in shared_context["enhanced_context"]:
                if "context_history" not in target_context["enhanced_context"]:
                    target_context["enhanced_context"]["context_history"] = []
                
                # Append history entries, avoiding duplicates by timestamp
                existing_timestamps = {entry["timestamp"] for entry in target_context["enhanced_context"]["context_history"]}
                for entry in shared_context["enhanced_context"]["context_history"]:
                    if entry["timestamp"] not in existing_timestamps:
                        target_context["enhanced_context"]["context_history"].append(entry)
                        existing_timestamps.add(entry["timestamp"])
                
                # Sort by timestamp
                target_context["enhanced_context"]["context_history"].sort(key=lambda x: x["timestamp"])
                
                # Limit history size
                if len(target_context["enhanced_context"]["context_history"]) > self.max_history_size:
                    target_context["enhanced_context"]["context_history"] = target_context["enhanced_context"]["context_history"][-self.max_history_size:]
            
            # Update version information
            target_context["enhanced_context"]["version"] = shared_context["enhanced_context"].get("version", 1)
            target_context["enhanced_context"]["last_sync"] = time.time()
            target_context["enhanced_context"]["needs_sync"] = False
        
        # Add transfer information to metadata
        target_context["metadata"] = target_context.get("metadata", {})
        target_context["metadata"]["context_transfer"] = {
            "source_persona": shared_context.get("source_persona"),
            "timestamp": shared_context.get("metadata", {}).get("timestamp"),
            "transfer_id": shared_context.get("metadata", {}).get("transfer_id")
        }
        
        logger.debug(f"Merged enhanced context from {shared_context.get('source_persona')}")

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["enhanced_context_management", "context_synchronization", "entity_tracking"])
