#!/usr/bin/env python
"""
Script to mark migrations as completed without running them.

This script is useful when migrations have already been applied to the database
but are not tracked in Alembic's history.
"""

import os
import sys
import logging
import argparse
import subprocess

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Mark migrations as completed")
    parser.add_argument("revision", help="Revision to mark as completed")
    return parser.parse_args()

def mark_migration(revision):
    """Mark a migration as completed without running it."""
    try:
        # Change to the backend directory
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        # Mark the migration as completed
        logger.info(f"Marking migration {revision} as completed")
        subprocess.run(["alembic", "stamp", revision], check=True)
        
        logger.info("Migration marked as completed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to mark migration: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error marking migration: {e}")
        sys.exit(1)

def main():
    """Main entry point for the script."""
    args = parse_args()
    mark_migration(args.revision)

if __name__ == "__main__":
    main()
