"""
Base agent interface for the Datagenius backend.

This module defines the base agent interface that all agents must implement.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

from .utils.prompt_template import PromptTemplate
from .utils.memory_service import MemoryService

logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """Base class for all agents in the system."""

    def __init__(self):
        """Initialize the base agent."""
        self.config = {}
        self.prompt_templates = {}
        self.capabilities = []
        self.name = "base-agent"  # Default name, will be overridden during initialization
        self.memory_service = MemoryService()  # Initialize memory service

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the agent with configuration.

        Args:
            config: Configuration dictionary for the agent
        """
        self.config = config

        # Set agent name from configuration
        if "id" in config:
            self.name = config["id"]
            logger.debug(f"Set agent name to: {self.name}")
        elif "name" in config:
            self.name = config["name"]
            logger.debug(f"Set agent name to: {self.name}")

        # Load prompt templates from configuration
        if "system_prompts" in config:
            for name, template in config["system_prompts"].items():
                self.prompt_templates[name] = PromptTemplate(template)
                logger.debug(f"Loaded prompt template '{name}'")

        # Load capabilities from configuration
        if "capabilities" in config:
            self.capabilities = config["capabilities"]
            logger.debug(f"Loaded capabilities: {self.capabilities}")

        # Additional initialization should be implemented by subclasses
        await self._initialize(config)

    @abstractmethod
    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Additional initialization for subclasses.

        Args:
            config: Configuration dictionary for the agent
        """
        pass

    @abstractmethod
    async def process_message(self,
                             user_id: int,
                             message: str,
                             conversation_id: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message and return a response.

        Args:
            user_id: The ID of the user sending the message
            message: The user's message text
            conversation_id: The ID of the conversation
            context: Additional context information

        Returns:
            Dict containing response text and any additional data
        """
        pass

    async def get_capabilities(self) -> List[str]:
        """
        Return a list of capabilities this agent supports.

        Returns:
            List of capability strings
        """
        return self.capabilities

    def get_prompt(self, name: str = "default", **kwargs) -> str:
        """
        Get a formatted prompt by name.

        Args:
            name: Name of the prompt template
            **kwargs: Variables to substitute in the template

        Returns:
            Formatted prompt
        """
        if name not in self.prompt_templates:
            logger.warning(f"Prompt template '{name}' not found, using default")
            name = "default"
            if name not in self.prompt_templates:
                logger.error(f"Default prompt template not found")
                return f"Error: No prompt template found for '{name}'"

        return self.prompt_templates[name].format(**kwargs)

    async def process_with_memory(self,
                                user_id: int,
                                message: str,
                                conversation_id: str,
                                context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message with memory enhancement.

        This method enhances the standard process_message method by:
        1. Retrieving relevant memories for the current context
        2. Adding memories to the context
        3. Processing the request with the enhanced context
        4. Storing the conversation in memory

        Args:
            user_id: The ID of the user sending the message
            message: The user's message text
            conversation_id: The ID of the conversation
            context: Additional context information

        Returns:
            Dict containing response text and any additional data
        """
        # Initialize context if not provided
        context = context or {}

        # Convert user_id to string for memory service
        user_id_str = str(user_id)

        try:
            # Search for relevant memories
            memories = self.memory_service.search_memories(
                query=message,
                user_id=user_id_str,
                metadata_filter={"persona_id": self.name}
            )

            # Add memories to context if found
            if memories and memories.get("results"):
                memory_texts = [m.get("memory", "") for m in memories.get("results", [])]

                # Add memory context to the context dictionary
                context["memory_context"] = "\n\n".join([
                    f"Previous memory: {memory}" for memory in memory_texts
                ])

                # Add raw memories for potential component use
                context["memories"] = memories.get("results", [])

                logger.debug(f"Added {len(memory_texts)} memories to context for user {user_id}")
        except Exception as e:
            logger.error(f"Error retrieving memories: {e}")
            # Continue processing even if memory retrieval fails

        # Process the message with the enhanced context
        response = await self.process_message(user_id, message, conversation_id, context)

        try:
            # Store the conversation in memory if there's a conversation history
            if "conversation_history" in context and len(context["conversation_history"]) > 1:
                # Create metadata
                metadata = {
                    "persona_id": self.name,
                    "conversation_id": conversation_id,
                    "timestamp": context.get("timestamp", 0)
                }

                # Add conversation to memory
                self.memory_service.add_conversation(
                    messages=context["conversation_history"],
                    user_id=user_id_str,
                    metadata=metadata
                )

                logger.debug(f"Stored conversation with {len(context['conversation_history'])} messages for user {user_id}")
        except Exception as e:
            logger.error(f"Error storing conversation: {e}")
            # Continue processing even if memory storage fails

        return response
