"""
Custom LLM providers for PandasAI v3.

This module provides custom LLM provider implementations for PandasAI v3,
supporting various AI providers including OpenAI, Groq, Google Gemini, OpenRouter, and Requesty.
"""

import logging
import os
from typing import Dict, Any, Optional, List, Union

from pandasai.llm.base import LLM

logger = logging.getLogger(__name__)

class GroqLLM(LLM):
    """Groq LLM provider for PandasAI."""

    def __init__(self, api_key: str, model: str = "llama3-70b-8192"):
        """Initialize the Groq LLM provider."""
        super().__init__()
        self.api_key = api_key
        self.model = model
        self._client = None

    @property
    def client(self):
        """Get the Groq client."""
        if self._client is None:
            try:
                import groq
                self._client = groq.Client(api_key=self.api_key)
            except ImportError:
                raise ImportError("The 'groq' package is required to use the Groq LLM provider. "
                                 "Please install it with 'pip install groq'.")
        return self._client

    def completion(self, prompt: str) -> str:
        """Generate a completion for the given prompt."""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1024
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error generating completion with Groq: {e}", exc_info=True)
            raise ValueError(f"Error generating completion with Groq: {e}")

    def generate_code(self, prompt: str) -> str:
        """Generate code for the given prompt."""
        # For code generation, we use a more specific prompt
        code_prompt = f"Generate Python code to answer the following question using pandas and matplotlib/seaborn/plotly. Only return the code without any explanation.\n\nQuestion: {prompt}"
        return self.completion(code_prompt)

    def is_configured(self) -> bool:
        """Check if the LLM provider is configured."""
        return bool(self.api_key)


class GeminiLLM(LLM):
    """Google Gemini LLM provider for PandasAI."""

    def __init__(self, api_key: str, model: str = "gemini-pro"):
        """Initialize the Google Gemini LLM provider."""
        super().__init__()
        self.api_key = api_key
        self.model = model
        self._client = None

    @property
    def client(self):
        """Get the Google Gemini client."""
        if self._client is None:
            try:
                import google.generativeai as genai
                genai.configure(api_key=self.api_key)
                self._client = genai
            except ImportError:
                raise ImportError("The 'google-generativeai' package is required to use the Google Gemini LLM provider. "
                                 "Please install it with 'pip install google-generativeai'.")
        return self._client

    def completion(self, prompt: str) -> str:
        """Generate a completion for the given prompt."""
        try:
            model = self.client.GenerativeModel(self.model)
            response = model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Error generating completion with Google Gemini: {e}", exc_info=True)
            raise ValueError(f"Error generating completion with Google Gemini: {e}")

    def generate_code(self, prompt: str) -> str:
        """Generate code for the given prompt."""
        # For code generation, we use a more specific prompt
        code_prompt = f"Generate Python code to answer the following question using pandas and matplotlib/seaborn/plotly. Only return the code without any explanation.\n\nQuestion: {prompt}"
        return self.completion(code_prompt)

    def is_configured(self) -> bool:
        """Check if the LLM provider is configured."""
        return bool(self.api_key)


class OpenAILLM(LLM):
    """OpenAI LLM provider for PandasAI."""

    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        """Initialize the OpenAI LLM provider."""
        super().__init__()
        self.api_key = api_key
        self.model = model
        self._client = None

    @property
    def client(self):
        """Get the OpenAI client."""
        if self._client is None:
            try:
                import openai
                self._client = openai.OpenAI(api_key=self.api_key)
            except ImportError:
                raise ImportError("The 'openai' package is required to use the OpenAI LLM provider. "
                                 "Please install it with 'pip install openai'.")
        return self._client

    def completion(self, prompt: str) -> str:
        """Generate a completion for the given prompt."""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1024
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error generating completion with OpenAI: {e}", exc_info=True)
            raise ValueError(f"Error generating completion with OpenAI: {e}")

    def generate_code(self, prompt: str) -> str:
        """Generate code for the given prompt."""
        # For code generation, we use a more specific prompt
        code_prompt = f"Generate Python code to answer the following question using pandas and matplotlib/seaborn/plotly. Only return the code without any explanation.\n\nQuestion: {prompt}"
        return self.completion(code_prompt)

    def is_configured(self) -> bool:
        """Check if the LLM provider is configured."""
        return bool(self.api_key)


class OpenRouterLLM(LLM):
    """OpenRouter LLM provider for PandasAI."""

    def __init__(self, api_key: str, model: str = "openai/gpt-3.5-turbo"):
        """Initialize the OpenRouter LLM provider."""
        super().__init__()
        self.api_key = api_key
        self.model = model
        self._client = None
        self.base_url = "https://openrouter.ai/api/v1"

    @property
    def client(self):
        """Get the OpenRouter client."""
        if self._client is None:
            try:
                import openai
                self._client = openai.OpenAI(
                    api_key=self.api_key,
                    base_url=self.base_url,
                    default_headers={
                        "HTTP-Referer": "https://datagenius.app",
                        "X-Title": "Datagenius App"
                    }
                )
            except ImportError:
                raise ImportError("The 'openai' package is required to use the OpenRouter LLM provider. "
                                 "Please install it with 'pip install openai'.")
        return self._client

    def completion(self, prompt: str) -> str:
        """Generate a completion for the given prompt."""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1024
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error generating completion with OpenRouter: {e}", exc_info=True)
            raise ValueError(f"Error generating completion with OpenRouter: {e}")

    def generate_code(self, prompt: str) -> str:
        """Generate code for the given prompt."""
        # For code generation, we use a more specific prompt
        code_prompt = f"Generate Python code to answer the following question using pandas and matplotlib/seaborn/plotly. Only return the code without any explanation.\n\nQuestion: {prompt}"
        return self.completion(code_prompt)

    def is_configured(self) -> bool:
        """Check if the LLM provider is configured."""
        return bool(self.api_key)


class RequestyLLM(LLM):
    """Requesty LLM provider for PandasAI."""

    def __init__(self, api_key: str, model: str = "openai/gpt-3.5-turbo"):
        """Initialize the Requesty LLM provider."""
        super().__init__()
        self.api_key = api_key
        self.model = model
        self._client = None
        self.base_url = "https://router.requesty.ai/v1"

    @property
    def client(self):
        """Get the Requesty client."""
        if self._client is None:
            try:
                import openai
                self._client = openai.OpenAI(
                    api_key=self.api_key,
                    base_url=self.base_url,
                    default_headers={
                        "HTTP-Referer": "https://datagenius.app",
                        "X-Title": "Datagenius App"
                    }
                )
            except ImportError:
                raise ImportError("The 'openai' package is required to use the Requesty LLM provider. "
                                 "Please install it with 'pip install openai'.")
        return self._client

    def completion(self, prompt: str) -> str:
        """Generate a completion for the given prompt."""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1024
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error generating completion with Requesty: {e}", exc_info=True)
            raise ValueError(f"Error generating completion with Requesty: {e}")

    def generate_code(self, prompt: str) -> str:
        """Generate code for the given prompt."""
        # For code generation, we use a more specific prompt
        code_prompt = f"Generate Python code to answer the following question using pandas and matplotlib/seaborn/plotly. Only return the code without any explanation.\n\nQuestion: {prompt}"
        return self.completion(code_prompt)

    def is_configured(self) -> bool:
        """Check if the LLM provider is configured."""
        return bool(self.api_key)
