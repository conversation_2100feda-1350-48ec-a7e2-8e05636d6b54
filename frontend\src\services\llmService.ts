/**
 * Service for interacting with LLM providers.
 */

import { llmProviderManager, LLMMessage, LLMProviderOptions, OpenAIProvider, AnthropicProvider } from '@/utils/llmProviders';

// Initialize providers
const initializeProviders = () => {
  // Check for API keys in environment variables or local storage
  const openaiApiKey = localStorage.getItem('openai_api_key');
  const anthropicApiKey = localStorage.getItem('anthropic_api_key');
  
  // Register OpenAI provider if API key is available
  if (openaiApiKey) {
    const openaiProvider = new OpenAIProvider({
      apiKey: openaiApiKey,
      model: 'gpt-3.5-turbo',
    });
    llmProviderManager.registerProvider(openaiProvider, true);
  }
  
  // Register Anthropic provider if API key is available
  if (anthropicApiKey) {
    const anthropicProvider = new AnthropicProvider({
      apiKey: anthropicApiKey,
      model: 'claude-2',
    });
    llmProviderManager.registerProvider(anthropicProvider, false);
  }
};

// Initialize providers on module load
initializeProviders();

/**
 * Generate a completion using the LLM provider manager.
 * @param messages The messages to generate a completion for
 * @param providerName The name of the provider to use (or null for default)
 * @param options Options for the provider
 * @returns The generated completion
 */
export const generateCompletion = async (
  messages: LLMMessage[],
  providerName: string | null = null,
  options: LLMProviderOptions = {}
) => {
  return llmProviderManager.generateCompletion(providerName, messages, options);
};

/**
 * Stream a completion using the LLM provider manager.
 * @param messages The messages to generate a completion for
 * @param providerName The name of the provider to use (or null for default)
 * @param options Options for the provider
 * @returns An async generator that yields chunks of the completion
 */
export const streamCompletion = async function* (
  messages: LLMMessage[],
  providerName: string | null = null,
  options: LLMProviderOptions = {}
) {
  for await (const chunk of llmProviderManager.streamCompletion(providerName, messages, options)) {
    yield chunk;
  }
};

/**
 * Check which providers are available.
 * @returns An object mapping provider names to availability
 */
export const checkProviderAvailability = async () => {
  return llmProviderManager.checkAvailability();
};

/**
 * Set an API key for a provider.
 * @param providerName The name of the provider
 * @param apiKey The API key
 */
export const setProviderApiKey = (providerName: string, apiKey: string) => {
  // Save the API key to local storage
  localStorage.setItem(`${providerName}_api_key`, apiKey);
  
  // Re-initialize providers
  initializeProviders();
};

/**
 * Get the available provider names.
 * @returns An array of provider names
 */
export const getAvailableProviders = () => {
  const providers = [];
  
  if (localStorage.getItem('openai_api_key')) {
    providers.push('openai');
  }
  
  if (localStorage.getItem('anthropic_api_key')) {
    providers.push('anthropic');
  }
  
  return providers;
};
