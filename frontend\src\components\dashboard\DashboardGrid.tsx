
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useToast } from "@/hooks/use-toast";
import {
  SalesWidget,
  MetricsWidget,
  CustomersWidget,
  RevenueChartWidget
} from "@/components/dashboard/widgets";
import { DynamicWidget } from "./DynamicWidget";
import { useDashboardStore, DashboardWidget } from "@/stores/dashboard-store";

// Default widget configuration for demo purposes
const defaultWidgets = [
  { id: "sales", component: SalesWidget, x: 0, y: 0, w: 1, h: 1 },
  { id: "metrics", component: MetricsWidget, x: 1, y: 0, w: 1, h: 1 },
  { id: "customers", component: CustomersWidget, x: 0, y: 1, w: 1, h: 1 },
  { id: "revenue", component: RevenueChartWidget, x: 1, y: 1, w: 2, h: 1 },
];

export function DashboardGrid() {
  const { toast } = useToast();
  const [staticWidgets, setStaticWidgets] = useState(defaultWidgets);
  const { widgets: dynamicWidgets, removeWidget: removeDynamicWidget } = useDashboardStore();

  const removeStaticWidget = (id: string) => {
    setStaticWidgets(staticWidgets.filter(widget => widget.id !== id));
    toast({
      title: "Widget removed",
      description: "The widget has been removed from your dashboard",
    });
  };

  const handleRemoveDynamicWidget = (id: string) => {
    removeDynamicWidget(id);
    toast({
      title: "Widget removed",
      description: "The widget has been removed from your dashboard",
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {/* Render static widgets */}
      {staticWidgets.map((widget) => (
        <motion.div
          key={widget.id}
          layout
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3 }}
          className="col-span-1"
          style={{
            gridColumn: `span ${widget.w}`,
            gridRow: `span ${widget.h}`,
          }}
        >
          <widget.component onRemove={() => removeStaticWidget(widget.id)} />
        </motion.div>
      ))}

      {/* Render dynamic widgets from visualizations */}
      {dynamicWidgets.map((widget) => (
        <motion.div
          key={widget.id}
          layout
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3 }}
          className="col-span-1"
          style={{
            gridColumn: `span ${widget.position.w}`,
            gridRow: `span ${widget.position.h}`,
          }}
        >
          <DynamicWidget
            widget={widget}
            onRemove={() => handleRemoveDynamicWidget(widget.id)}
          />
        </motion.div>
      ))}
    </div>
  );
}
