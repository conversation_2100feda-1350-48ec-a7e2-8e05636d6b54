import React, { useState, useEffect, useMemo } from 'react'; // Import useEffect and useMemo
import { useForm, useWatch } from 'react-hook-form'; // Import useWatch
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import axios from 'axios';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Info, Plus, X, Loader2, RefreshCw } from 'lucide-react'; // Import Loader2 and RefreshCw
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { providerApi, Provider, ProviderModel } from '@/lib/api'; // Import Provider and ProviderModel types
import { useToast } from '@/hooks/use-toast';
import { DataSource } from '@/lib/dataSourceApi'; // Import DataSource type
import { loadYamlSchemaAsZod } from '@/utils/schema-utils';

// Define a placeholder schema to use until the YAML schema is loaded
const placeholderSchema = z.object({
  content_type: z.enum([
    'marketing_strategy',
    'campaign_strategy',
    'social_media_content',
    'seo_optimization',
    'post_composer'
  ]),
  brand_description: z.string().min(1, "Brand description is required"),
  target_audience: z.string().min(1, "Target audience is required"),
  products_services: z.string(),
  marketing_goals: z.string(),
  existing_content: z.string().optional(),
  keywords: z.string().optional(),
  suggested_topics: z.string().optional(),
  tone: z.string().default("Professional"),
  provider: z.string().optional(),
  model: z.string().optional(),
  temperature: z.number().min(0).max(1).default(0.7),
});

// We'll use this as our form schema
let formSchema = placeholderSchema;

// Load the YAML schema
(async () => {
  try {
    // In a production environment, this would be a proper URL
    // For development, we're using a relative path
    formSchema = await loadYamlSchemaAsZod('/src/schemas/marketing-content-form.yaml');
    console.log('Marketing content form schema loaded from YAML');
  } catch (error) {
    console.error('Error loading YAML schema:', error);
    console.warn('Using placeholder schema instead');
    // Continue using the placeholder schema
  }
})();

export type MarketingContentFormData = z.infer<typeof formSchema>;

interface MarketingContentFormProps {
  onSubmit: (data: MarketingContentFormData) => void;
  defaultValues?: Partial<MarketingContentFormData>;
  isLoading?: boolean;
  dataSourceId?: string; // Changed back to dataSourceId
  onClose?: () => void; // Add onClose prop
}

// Removed local type definitions, will use imported Provider and ProviderModel


export function MarketingContentForm({
  onSubmit,
  defaultValues,
  isLoading = false,
  dataSourceId, // Use dataSourceId again
  onClose,
}: MarketingContentFormProps) {
  const { toast } = useToast();
  const [contentType, setContentType] = useState<string>(defaultValues?.content_type || 'marketing_strategy');
  // Use imported Provider type for state
  const [availableProviders, setAvailableProviders] = useState<Provider[]>([]);
  // Use imported ProviderModel type for state
  const [currentProviderModels, setCurrentProviderModels] = useState<ProviderModel[]>([]);
  const [isLoadingProviders, setIsLoadingProviders] = useState(true);
  const [isLoadingModels, setIsLoadingModels] = useState(false); // Models load based on provider selection
  const [isLoadingDocumentData, setIsLoadingDocumentData] = useState(false); // State for autofill loading

  // Initialize form with default values
  const form = useForm<MarketingContentFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content_type: defaultValues?.content_type || 'marketing_strategy',
      brand_description: defaultValues?.brand_description || '',
      target_audience: defaultValues?.target_audience || '',
      products_services: defaultValues?.products_services || '',
      marketing_goals: defaultValues?.marketing_goals || '',
      existing_content: defaultValues?.existing_content || '',
      keywords: defaultValues?.keywords || '',
      suggested_topics: defaultValues?.suggested_topics || '',
      tone: defaultValues?.tone || 'Professional',
      provider: defaultValues?.provider || '', // Default provider will be set after fetch
      model: defaultValues?.model || '',
      temperature: defaultValues?.temperature || 0.7,
    },
  });

  // Watch the selected provider to update the models dropdown
  const selectedProvider = useWatch({ control: form.control, name: 'provider' });

  // Fetch providers on mount
  useEffect(() => {
    const fetchProviders = async () => {
      setIsLoadingProviders(true);
      try {
        const providersRes = await providerApi.getProviders();
        // Use is_available field from Provider type
        const fetchedProviders = providersRes.providers.filter(p => p.is_available);
        setAvailableProviders(fetchedProviders);

        // Set default provider if none is set and providers are available
        if (!form.getValues('provider') && fetchedProviders.length > 0) {
          form.setValue('provider', fetchedProviders[0].id);
        }
      } catch (error) {
        console.error("Failed to fetch providers:", error);
      } finally {
        setIsLoadingProviders(false);
      }
    };
    fetchProviders();
  }, [form]); // Run once on mount

  // Function to handle autofill action
  const handleAutofill = async () => {
    // Use the dataSourceId prop directly
    if (!dataSourceId) {
      toast({
        title: "No Data Source or Invalid File",
        description: "Please attach a valid file data source first.",
        variant: "default",
      });
      return;
    }

    setIsLoadingDocumentData(true);
    try {
      // Get the API base URL from environment or default to localhost
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      const token = localStorage.getItem('token');

      if (!token) {
        toast({
          title: "Authentication Error",
          description: "You need to be logged in to fetch document data.",
          variant: "destructive",
        });
        return;
      }

      console.log(`Querying document with file_id: ${dataSourceId}`); // Use dataSourceId

      const response = await axios.post(
        `${API_BASE_URL}/document-query`,
        { file_id: dataSourceId, query_type: "marketing_fields" }, // Use dataSourceId
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.data && response.data.results) {
        const fields = response.data.results;

        // Update form values with document data
        if (fields.brand_description) {
          form.setValue('brand_description', fields.brand_description);
        }

        if (fields.target_audience) {
          form.setValue('target_audience', fields.target_audience);
        }

        if (fields.products_services) {
          form.setValue('products_services', fields.products_services);
        }

        if (fields.marketing_goals) {
          form.setValue('marketing_goals', fields.marketing_goals);
        }

        if (fields.existing_content) {
          form.setValue('existing_content', fields.existing_content);
        }

        if (fields.keywords) {
          form.setValue('keywords', fields.keywords);
        }

        if (fields.suggested_topics) {
          form.setValue('suggested_topics', fields.suggested_topics);
        }

        toast({
          title: "Form Autofilled",
          description: "Marketing form fields have been filled based on your document.",
        });
      } else {
         toast({
            title: "No Data Found",
            description: "Could not extract relevant information from the document.",
            variant: "default", // Changed from warning
          });
      }
    } catch (error: any) {
      console.error("Failed to fetch document data:", error);

      // Check if it's a 404 error (file not found)
      if (error.response && error.response.status === 404) {
        const errorDetail = error.response.data?.detail || "The attached file could not be found.";
        toast({
          title: "File Not Found",
          description: errorDetail,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to extract data from your document. Please fill the form manually.",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoadingDocumentData(false);
    }
  };

  // Fetch models when the selected provider changes
  useEffect(() => {
    if (!selectedProvider) {
      setCurrentProviderModels([]);
      return;
    }

    const fetchModels = async () => {
      setIsLoadingModels(true);
      setCurrentProviderModels([]); // Clear previous models
      try {
        // Use the correct API method: getProviderModels
        const modelsRes = await providerApi.getProviderModels(selectedProvider);
        // Assuming the response structure matches { models: ModelInfo[] }
        setCurrentProviderModels(modelsRes.models || []); // Handle potential undefined models array

        // Reset model selection if current model is not in the new list or if list is empty
        const currentModelValue = form.getValues('model');
        const newModels = modelsRes.models || [];
        const isCurrentModelValid = newModels.some(m => m.id === currentModelValue);

        if (!isCurrentModelValid && newModels.length > 0) {
          form.setValue('model', newModels[0].id); // Set to first model of new provider
        } else if (newModels.length === 0) {
          form.setValue('model', ''); // Clear model if none available
        }

      } catch (error) {
        console.error(`Failed to fetch models for provider ${selectedProvider}:`, error);
        setCurrentProviderModels([]); // Clear models on error
        form.setValue('model', ''); // Clear model selection on error
      } finally {
        setIsLoadingModels(false);
      }
    };

    fetchModels();
  }, [selectedProvider, form]); // Run when selectedProvider changes

  // Handle content type change
  const handleContentTypeChange = (value: string) => {
    setContentType(value);
    form.setValue('content_type', value as any);
  };

  // Get form field descriptions based on content type
  const getFieldDescriptions = () => {
    switch (contentType) {
      case 'marketing_strategy':
        return {
          brand_description: "Describe your brand's identity, values, and positioning",
          target_audience: "Describe your target audience demographics, behaviors, and needs",
          products_services: "List and describe your main products or services",
          marketing_goals: "Outline your marketing objectives (e.g., increase brand awareness, generate leads)",
          existing_content: "Describe any existing marketing materials or campaigns",
          keywords: "List important keywords for your brand and industry",
          suggested_topics: "Suggest topics you'd like included in the strategy",
        };
      case 'campaign_strategy':
        return {
          brand_description: "Describe your brand's identity and values",
          target_audience: "Describe the specific audience for this campaign",
          products_services: "Describe the specific products/services featured in this campaign",
          marketing_goals: "Outline the specific goals for this campaign",
          existing_content: "Describe any existing campaign materials or previous campaigns",
          keywords: "List important keywords for this campaign",
          suggested_topics: "Suggest campaign themes or concepts",
        };
      case 'social_media_content':
        return {
          brand_description: "Describe your brand's identity and social media presence",
          target_audience: "Describe your social media audience",
          products_services: "Describe products/services to feature in social content",
          marketing_goals: "Outline your social media objectives",
          existing_content: "Describe your current social media content",
          keywords: "List hashtags and keywords for social media",
          suggested_topics: "Suggest topics for social media posts",
        };
      case 'seo_optimization':
        return {
          brand_description: "Describe your brand and website",
          target_audience: "Describe your target audience for SEO",
          products_services: "Describe products/services to optimize for search",
          marketing_goals: "Outline your SEO objectives",
          existing_content: "Describe your current website content and SEO status",
          keywords: "List target keywords for SEO (very important for this content type)",
          suggested_topics: "Suggest topics for SEO content",
        };
      case 'post_composer':
        return {
          brand_description: "Describe your brand's voice and style",
          target_audience: "Describe the audience for this post",
          products_services: "Describe products/services to feature in this post",
          marketing_goals: "What do you want to achieve with this post?",
          existing_content: "Describe any existing posts or content to reference",
          keywords: "List keywords or hashtags to include",
          suggested_topics: "Suggest specific topics for this post",
        };
      default:
        return {
          brand_description: "Describe your brand",
          target_audience: "Describe your target audience",
          products_services: "Describe your products or services",
          marketing_goals: "Outline your marketing goals",
          existing_content: "Describe any existing content",
          keywords: "List important keywords",
          suggested_topics: "Suggest topics to include",
        };
    }
  };

  const fieldDescriptions = getFieldDescriptions();

  // Get content type description
  const getContentTypeDescription = (type: string) => {
    switch (type) {
      case 'marketing_strategy':
        return 'A comprehensive marketing strategy including market analysis, target audience, competitive positioning, and tactical recommendations.';
      case 'campaign_strategy':
        return 'A focused campaign strategy for a specific marketing initiative, including concept, messaging, channels, and timeline.';
      case 'social_media_content':
        return 'Social media content plan with post ideas, content calendar, and platform-specific recommendations.';
      case 'seo_optimization':
        return 'SEO strategy including keyword analysis, on-page optimization recommendations, and content suggestions.';
      case 'post_composer':
        return 'Compose social media posts for specific platforms with appropriate messaging and hashtags.';
      default:
        return '';
    }
  };

  // Removed console.log

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Create Marketing Content</CardTitle>
        <CardDescription>
          Generate professional marketing content tailored to your needs
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="content_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Content Type</FormLabel>
                  <FormControl>
                    <Tabs
                      defaultValue={field.value}
                      onValueChange={handleContentTypeChange}
                      className="w-full"
                    >
                      <TabsList className="grid grid-cols-2 md:grid-cols-5 w-full">
                        <TabsTrigger value="marketing_strategy">Strategy</TabsTrigger>
                        <TabsTrigger value="campaign_strategy">Campaign</TabsTrigger>
                        <TabsTrigger value="social_media_content">Social Media</TabsTrigger>
                        <TabsTrigger value="seo_optimization">SEO</TabsTrigger>
                        <TabsTrigger value="post_composer">Posts</TabsTrigger>
                      </TabsList>
                      <div className="mt-2 text-sm text-muted-foreground">
                        {getContentTypeDescription(contentType)}
                      </div>
                    </Tabs>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="brand_description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your brand..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.brand_description}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="target_audience"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Audience</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your target audience..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.target_audience}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="products_services"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Products/Services</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your products or services..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.products_services}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="marketing_goals"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Marketing Goals</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your marketing goals..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.marketing_goals}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {contentType === 'social_media_content' && (
              <FormField
                control={form.control}
                name="existing_content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Existing Social Media Content</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your existing social media content..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.existing_content}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {contentType === 'seo_optimization' && (
              <FormField
                control={form.control}
                name="keywords"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SEO Keywords</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter your target keywords, one per line..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.keywords}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {contentType === 'post_composer' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="suggested_topics"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Post Topic</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="What should this post be about?"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {fieldDescriptions.suggested_topics}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="keywords"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hashtags & Keywords</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter hashtags and keywords to include..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {fieldDescriptions.keywords}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormField
                control={form.control}
                name="tone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tone</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select tone" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Professional">Professional</SelectItem>
                        <SelectItem value="Casual">Casual</SelectItem>
                        <SelectItem value="Friendly">Friendly</SelectItem>
                        <SelectItem value="Authoritative">Authoritative</SelectItem>
                        <SelectItem value="Enthusiastic">Enthusiastic</SelectItem>
                        <SelectItem value="Humorous">Humorous</SelectItem>
                        <SelectItem value="Formal">Formal</SelectItem>
                        <SelectItem value="Inspirational">Inspirational</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the tone for your content
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* AI Provider Dropdown */}
              <FormField
                control={form.control}
                name="provider"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>AI Provider</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        // Model reset is handled by useEffect watching selectedProvider
                      }}
                      value={field.value} // Use value for controlled component
                      disabled={isLoadingProviders || availableProviders.length === 0}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {isLoadingProviders ? (
                            <span className="flex items-center text-muted-foreground">
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Loading...
                            </span>
                          ) : (
                            <SelectValue placeholder="Select provider" />
                          )}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {/* Use availableProviders state */}
                        {availableProviders.map((provider) => (
                          <SelectItem key={provider.id} value={provider.id}>
                            {provider.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the AI provider
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* AI Model Dropdown */}
              <FormField
                control={form.control}
                name="model"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>AI Model</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value} // Use value for controlled component
                      disabled={isLoadingModels || !selectedProvider || currentProviderModels.length === 0}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {isLoadingModels ? (
                             <span className="flex items-center text-muted-foreground">
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Loading...
                            </span>
                          ) : (
                            <SelectValue placeholder={!selectedProvider ? "Select provider first" : "Select model"} />
                          )}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {/* Use currentProviderModels state */}
                        {currentProviderModels.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            {model.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the AI model (options depend on provider)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="temperature"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Creativity: {field.value.toFixed(1)}</FormLabel>
                  <FormControl>
                    <Slider
                      min={0}
                      max={1}
                      step={0.1}
                      defaultValue={[field.value]}
                      onValueChange={(values) => field.onChange(values[0])}
                    />
                  </FormControl>
                  <FormDescription>
                    Lower values produce more predictable content, higher values more creative
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-between items-center"> {/* Added items-center */}
        {/* Left side buttons */}
        <div className="flex gap-2">
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          )}
          <Button variant="outline" onClick={() => form.reset()}>
            Reset
          </Button>
          {/* Autofill Button */}
          <Button
            variant="outline"
            onClick={handleAutofill}
            disabled={!dataSourceId || isLoadingDocumentData}
            title={!dataSourceId ? "Attach a data source to enable autofill" : ""}
          >
            {isLoadingDocumentData ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Autofill from Source
          </Button>
        </div>

        {/* Right side button */}
        <div className="flex gap-2">
          {/* Removed loading indicator from here */}
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={isLoading || isLoadingDocumentData} // Also disable if autofilling
          >
            {isLoading ? <><Loader2 className="h-4 w-4 animate-spin mr-2" /> Generating...</> : 'Generate Content'}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
