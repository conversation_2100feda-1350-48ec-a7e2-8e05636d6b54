"""
Fix pending purchases by updating their status to completed.

This script updates all pending purchases to completed status.
"""

import os
import sys
import logging

# Add the parent directory to sys.path to allow importing from app
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from sqlalchemy.orm import Session
from app.database import get_db, update_purchase_status

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def fix_pending_purchases():
    """
    Update all pending purchases to completed status.
    """
    logger.info("Fixing pending purchases...")

    # Get database session
    db = next(get_db())

    try:
        # Get all pending purchases
        from sqlalchemy import text
        result = db.execute(text("SELECT id FROM purchases WHERE payment_status = 'pending'"))
        pending_purchase_ids = [row[0] for row in result]
        
        logger.info(f"Found {len(pending_purchase_ids)} pending purchases")
        
        # Update each purchase to completed
        for purchase_id in pending_purchase_ids:
            updated_purchase = update_purchase_status(db, purchase_id, "completed")
            logger.info(f"Updated purchase {purchase_id} status to completed")
        
        logger.info("Successfully fixed all pending purchases")
    except Exception as e:
        logger.error(f"Error fixing pending purchases: {e}", exc_info=True)


if __name__ == "__main__":
    fix_pending_purchases()
