"""
Enhanced LLM processor component for the Datagenius agent system.

This module provides an enhanced implementation of the LLM processor component,
with improved error handling, model initialization, and prompt management.
"""

import logging
import os
from typing import Dict, Any, List, Optional

from backend.schemas.agent_config_schemas import AgentProcessingContext # Added
from .base import AgentComponent
from ..utils.model_init import initialize_agent_model
from ..utils.prompt_template import PromptTemplate

logger = logging.getLogger(__name__)


class EnhancedLLMProcessorComponent(AgentComponent):
    """Enhanced component for processing messages using an LLM."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the LLM processor component.

        Args:
            config: Configuration dictionary for the component
        """
        # Initialize prompt templates
        self.prompt_templates = {}
        if "prompt_templates" in config:
            for name, template in config["prompt_templates"].items():
                self.prompt_templates[name] = PromptTemplate(template)
                logger.debug(f"Loaded prompt template '{name}'")

        # Set default prompt template name
        self.default_prompt_template = config.get("default_prompt_template", "default")

        # Initialize LLM
        try:
            # Use the centralized model provider system
            self.llm = await initialize_agent_model(
                config=config,
                default_provider=config.get("provider", "groq"),
                default_model=config.get("model", "llama3-70b-8192")
            )
            logger.info(f"Successfully initialized model for LLM processor component: {self.name}")
        except Exception as e:
            logger.error(f"Error initializing model for LLM processor component {self.name}: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to initialize model: {str(e)}")

        # Store additional configuration
        self.temperature = config.get("temperature", 0.7)
        self.max_tokens = config.get("max_tokens", 1000)
        self.system_message = config.get("system_message", "You are a helpful AI assistant.")

    async def process(self, context: AgentProcessingContext) -> AgentProcessingContext:
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object.
        """
        # Check if this component should be skipped
        if context.metadata.get("skip_llm_processor", False):
            logger.debug(f"Skipping LLM processor component: {self.name}")
            return context

        # Get message from context
        message = context.message or ""
        if not message:
            logger.warning("No message found in context")
            # Only set response if no other component has set it.
            if not context.response:
                 context.response = "I didn't receive a message to process."
            return context

        # Get prompt template name from context metadata or use default
        prompt_template_name = context.metadata.get("prompt_template_name", self.default_prompt_template)

        try:
            # Format the prompt using the template
            if prompt_template_name in self.prompt_templates:
                # Construct format_context from AgentProcessingContext
                format_context_payload = {
                    "message": message,
                    "user_id": str(context.user_id),
                    "conversation_id": str(context.conversation_id),
                }
                # Merge metadata, initial_context, and component_data
                # Ensure that complex objects are handled or excluded if not suitable for templating
                format_context_payload.update(context.initial_context) # initial_context is already a dict
                format_context_payload.update(context.metadata) # metadata is already a dict
                
                # Merge component_data, prefixing keys to avoid clashes
                for comp_name, comp_data_dict in context.component_data.items():
                    if isinstance(comp_data_dict, dict):
                        for k, v in comp_data_dict.items():
                            # Ensure value is serializable or appropriate for template
                            if isinstance(v, (str, int, float, bool, list, dict)):
                                format_context_payload[f"{comp_name}_{k}"] = v
                    elif isinstance(comp_data_dict, (str, int, float, bool, list)):
                         format_context_payload[comp_name] = comp_data_dict


                # Add conversation history if available from initial_context
                conversation_history = context.initial_context.get("conversation_history")
                if conversation_history:
                    format_context_payload["conversation_history"] = self._format_conversation_history(conversation_history)
                else:
                    format_context_payload["conversation_history"] = ""
                
                prompt = self.prompt_templates[prompt_template_name].format(**format_context_payload)
                logger.debug(f"Using prompt template: {prompt_template_name}")
            else:
                # Use the message directly if no template is found
                logger.warning(f"Prompt template '{prompt_template_name}' not found, using message directly")
                prompt = message

            # Call the LLM
            logger.debug(f"Calling LLM with prompt: {prompt[:200]}{'...' if len(prompt) > 200 else ''}")

            # Different LLM implementations have different APIs, so we need to handle them differently
            llm_response_obj = None # To store the raw response object
            if hasattr(self.llm, "ainvoke"): # LangChain LCEL style (preferred)
                llm_response_obj = await self.llm.ainvoke(prompt)
                if hasattr(llm_response_obj, "content"): # AIMessage, HumanMessage, etc.
                    response_text = llm_response_obj.content
                else: # If it's a plain string (less common for ainvoke)
                    response_text = str(llm_response_obj)
            elif hasattr(self.llm, "agenerate"): # Older LangChain style
                llm_response_obj = await self.llm.agenerate([prompt])
                response_text = llm_response_obj.generations[0][0].text
            # Add other LLM client specific calls if necessary, e.g. direct OpenAI client
            # For example, if self.llm was an OpenAI client instance:
            # elif isinstance(self.llm, openai.AsyncOpenAI):
            #     completion = await self.llm.chat.completions.create(
            #         model=self.config.get("model", "gpt-3.5-turbo"), # Ensure model is configured
            #         messages=[{"role": "system", "content": self.system_message}, {"role": "user", "content": prompt}],
            #         temperature=self.temperature,
            #         max_tokens=self.max_tokens
            #     )
            #     response_text = completion.choices[0].message.content
            #     llm_response_obj = completion
            else:
                # Generic fallback or raise error
                logger.error(f"Unknown LLM interface for {self.llm.__class__.__name__}. Cannot invoke.")
                context.add_error(self.name, "unknown_llm_interface", {"llm_class": self.llm.__class__.__name__})
                context.response = "There was an issue communicating with the language model."
                return context

            # Update context with response
            context.response = response_text
            context.metadata["llm_processor_used"] = self.name # Renamed key
            context.metadata["llm_provider"] = self.config.get("provider")
            context.metadata["llm_model_used"] = self.config.get("model")


            logger.debug(f"LLM response: {response_text[:200]}{'...' if len(response_text) > 200 else ''}")
            return context

        except Exception as e:
            logger.error(f"Error processing with LLM: {str(e)}", exc_info=True)
            context.add_error(self.name, f"llm_processing_error: {str(e)}")
            context.response = f"I encountered an error while processing your request with the language model."
            return context

    def _format_conversation_history(self, conversation_history: List[Dict[str, Any]]) -> str:
        """
        Format conversation history as a string for inclusion in prompts.

        Args:
            conversation_history: List of message dictionaries with sender, content, and metadata

        Returns:
            Formatted conversation history string
        """
        if not conversation_history:
            return ""

        history_lines = ["### Conversation History:"]

        # Limit to the last 10 messages to avoid context overflow
        recent_messages = conversation_history[-10:] if len(conversation_history) > 10 else conversation_history

        for msg in recent_messages:
            sender = msg.get("sender", "unknown")
            content = msg.get("content", "")

            # Skip processing messages
            if content == "Processing your request...":
                continue

            # Format based on sender
            if sender == "user":
                history_lines.append(f"User: {content}")
            elif sender == "ai":
                # Check if this was generated content
                if msg.get("metadata", {}).get("generated_content", False):
                    # For generated content, include a summary instead of the full content
                    history_lines.append(f"AI: [Generated marketing content based on user request]")
                else:
                    history_lines.append(f"AI: {content}")

        history_lines.append("### End of Conversation History\n")

        return "\n".join(history_lines)

    def get_prompt(self, name: str, **kwargs) -> str:
        """
        Get a formatted prompt by name.

        Args:
            name: Name of the prompt template
            **kwargs: Variables to substitute in the template

        Returns:
            Formatted prompt
        """
        if name not in self.prompt_templates:
            logger.warning(f"Prompt template '{name}' not found")
            return f"Prompt template '{name}' not found"

        try:
            return self.prompt_templates[name].format(**kwargs)
        except KeyError as e:
            logger.error(f"Missing key in prompt template '{name}': {e}")
            return f"Error formatting prompt template '{name}': missing key {e}"
        except Exception as e:
            logger.error(f"Error formatting prompt template '{name}': {e}")
            return f"Error formatting prompt template '{name}': {e}"
