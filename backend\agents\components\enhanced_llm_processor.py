"""
Enhanced LLM processor component for the Datagenius agent system.

This module provides an enhanced implementation of the LLM processor component,
with improved error handling, model initialization, and prompt management.
"""

import logging
import os
from typing import Dict, Any, List, Optional

from .base import AgentComponent
from ..utils.model_init import initialize_agent_model
from ..utils.prompt_template import PromptTemplate

logger = logging.getLogger(__name__)


class EnhancedLLMProcessorComponent(AgentComponent):
    """Enhanced component for processing messages using an LLM."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the LLM processor component.

        Args:
            config: Configuration dictionary for the component
        """
        # Initialize prompt templates
        self.prompt_templates = {}
        if "prompt_templates" in config:
            for name, template in config["prompt_templates"].items():
                self.prompt_templates[name] = PromptTemplate(template)
                logger.debug(f"Loaded prompt template '{name}'")

        # Set default prompt template name
        self.default_prompt_template = config.get("default_prompt_template", "default")

        # Initialize LLM
        try:
            # Use the centralized model provider system
            self.llm = await initialize_agent_model(
                config=config,
                default_provider=config.get("provider", "groq"),
                default_model=config.get("model", "llama3-70b-8192")
            )
            logger.info(f"Successfully initialized model for LLM processor component: {self.name}")
        except Exception as e:
            logger.error(f"Error initializing model for LLM processor component {self.name}: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to initialize model: {str(e)}")

        # Store additional configuration
        self.temperature = config.get("temperature", 0.7)
        self.max_tokens = config.get("max_tokens", 1000)
        self.system_message = config.get("system_message", "You are a helpful AI assistant.")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        # Check if this component should be skipped
        if context.get("skip_llm_processor", False):
            logger.debug(f"Skipping LLM processor component: {self.name}")
            return context

        # Get message from context
        message = context.get("message", "")
        if not message:
            logger.warning("No message found in context")
            context["response"] = "I didn't receive a message to process."
            return context

        # Get prompt template name from context or use default
        prompt_template_name = context.get("prompt_template_name", self.default_prompt_template)

        try:
            # Format the prompt using the template
            if prompt_template_name in self.prompt_templates:
                # Create a copy of the context for prompt formatting
                format_context = {**context}

                # Remove large objects that shouldn't be passed to the template
                format_context.pop("agent_components", None)

                # Add conversation history if available
                if "conversation_history" in context:
                    # Format conversation history as a string
                    conversation_history_str = self._format_conversation_history(context["conversation_history"])
                    format_context["conversation_history"] = conversation_history_str
                elif "context" in context and "conversation_history" in context["context"]:
                    # Format conversation history as a string
                    conversation_history_str = self._format_conversation_history(context["context"]["conversation_history"])
                    format_context["conversation_history"] = conversation_history_str
                else:
                    format_context["conversation_history"] = ""

                prompt = self.prompt_templates[prompt_template_name].format(**format_context)
                logger.debug(f"Using prompt template: {prompt_template_name}")
            else:
                # Use the message directly if no template is found
                logger.warning(f"Prompt template '{prompt_template_name}' not found, using message directly")
                prompt = message

            # Call the LLM
            logger.debug(f"Calling LLM with prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")

            # Different LLM implementations have different APIs, so we need to handle them differently
            if hasattr(self.llm, "invoke"):
                # LangChain style
                response = await self.llm.ainvoke(prompt)
                if hasattr(response, "content"):
                    # ChatMessage style response
                    response_text = response.content
                else:
                    # String response
                    response_text = str(response)
            elif hasattr(self.llm, "agenerate"):
                # Legacy LangChain style
                response = await self.llm.agenerate([prompt])
                response_text = response.generations[0][0].text
            elif hasattr(self.llm, "acompletion"):
                # OpenAI style
                response = await self.llm.acompletion(
                    prompt=prompt,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )
                response_text = response.choices[0].text
            else:
                # Generic fallback
                logger.warning(f"Unknown LLM interface for {self.llm.__class__.__name__}, attempting direct call")
                response = await self.llm(prompt)
                response_text = str(response)

            # Update context with response
            context["response"] = response_text
            context["metadata"]["llm_processor"] = self.name

            logger.debug(f"LLM response: {response_text[:100]}{'...' if len(response_text) > 100 else ''}")
            return context

        except Exception as e:
            logger.error(f"Error processing with LLM: {str(e)}", exc_info=True)
            context["response"] = f"I encountered an error while processing your request: {str(e)}"
            context["metadata"]["error"] = str(e)
            return context

    def _format_conversation_history(self, conversation_history: List[Dict[str, Any]]) -> str:
        """
        Format conversation history as a string for inclusion in prompts.

        Args:
            conversation_history: List of message dictionaries with sender, content, and metadata

        Returns:
            Formatted conversation history string
        """
        if not conversation_history:
            return ""

        history_lines = ["### Conversation History:"]

        # Limit to the last 10 messages to avoid context overflow
        recent_messages = conversation_history[-10:] if len(conversation_history) > 10 else conversation_history

        for msg in recent_messages:
            sender = msg.get("sender", "unknown")
            content = msg.get("content", "")

            # Skip processing messages
            if content == "Processing your request...":
                continue

            # Format based on sender
            if sender == "user":
                history_lines.append(f"User: {content}")
            elif sender == "ai":
                # Check if this was generated content
                if msg.get("metadata", {}).get("generated_content", False):
                    # For generated content, include a summary instead of the full content
                    history_lines.append(f"AI: [Generated marketing content based on user request]")
                else:
                    history_lines.append(f"AI: {content}")

        history_lines.append("### End of Conversation History\n")

        return "\n".join(history_lines)

    def get_prompt(self, name: str, **kwargs) -> str:
        """
        Get a formatted prompt by name.

        Args:
            name: Name of the prompt template
            **kwargs: Variables to substitute in the template

        Returns:
            Formatted prompt
        """
        if name not in self.prompt_templates:
            logger.warning(f"Prompt template '{name}' not found")
            return f"Prompt template '{name}' not found"

        try:
            return self.prompt_templates[name].format(**kwargs)
        except KeyError as e:
            logger.error(f"Missing key in prompt template '{name}': {e}")
            return f"Error formatting prompt template '{name}': missing key {e}"
        except Exception as e:
            logger.error(f"Error formatting prompt template '{name}': {e}")
            return f"Error formatting prompt template '{name}': {e}"
