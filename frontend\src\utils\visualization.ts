/**
 * Visualization utilities for rendering data visualizations.
 */

export type VisualizationType = 'chart' | 'table' | 'tree' | 'network' | 'heatmap' | 'custom' | 'classification' | 'marketing' | 'data_preview';

export interface VisualizationData {
  type: VisualizationType;
  title?: string;
  description?: string;
  data: any;
  config?: any;
}

/**
 * Validates visualization data to ensure it has the required properties.
 */
export function validateVisualization(visualization: any): VisualizationData | null {
  if (!visualization || typeof visualization !== 'object') {
    console.error('Invalid visualization data: not an object');
    return null;
  }

  if (!visualization.type || !visualization.data) {
    console.error('Invalid visualization data: missing required properties');
    return null;
  }

  // Validate based on type
  switch (visualization.type) {
    case 'chart':
      if (!validateChartData(visualization.data)) {
        return null;
      }
      break;
    case 'table':
      if (!validateTableData(visualization.data)) {
        return null;
      }
      break;
    case 'classification':
      // Basic validation for classification data
      if (!visualization.data || !visualization.data.results) {
        console.error('Invalid classification data: missing results');
        return null;
      }
      break;
    case 'marketing':
      // Basic validation for marketing content data
      if (!visualization.data) {
        console.error('Invalid marketing content data: missing data');
        return null;
      }
      break;
    case 'data_preview':
      // Basic validation for data preview
      if (!visualization.data || (!visualization.data.preview_data && !visualization.data.description)) {
        console.error('Invalid data preview: missing preview data or description');
        return null;
      }
      break;
    case 'tree':
    case 'network':
    case 'heatmap':
    case 'custom':
      // Basic validation for other types
      if (!visualization.data) {
        console.error(`Invalid ${visualization.type} data: missing data`);
        return null;
      }
      break;
    default:
      console.error(`Unknown visualization type: ${visualization.type}`);
      return null;
  }

  return visualization as VisualizationData;
}

/**
 * Validates chart data.
 */
function validateChartData(data: any): boolean {
  if (!data) {
    console.error('Invalid chart data: missing data');
    return false;
  }

  // If it's an image-based visualization, it should have an image property
  if (data.image) {
    return true;
  }

  // For standard chart data
  if (!data.labels || !Array.isArray(data.labels)) {
    console.error('Invalid chart data: missing or invalid labels');
    return false;
  }

  if (!data.datasets || !Array.isArray(data.datasets)) {
    console.error('Invalid chart data: missing or invalid datasets');
    return false;
  }

  return true;
}

/**
 * Validates table data.
 */
function validateTableData(data: any): boolean {
  if (!data) {
    console.error('Invalid table data: missing data');
    return false;
  }

  if (!data.headers || !Array.isArray(data.headers)) {
    console.error('Invalid table data: missing or invalid headers');
    return false;
  }

  if (!data.rows || !Array.isArray(data.rows)) {
    console.error('Invalid table data: missing or invalid rows');
    return false;
  }

  return true;
}

/**
 * Processes raw visualization data from the API.
 */
export function processVisualizationData(metadata: any): VisualizationData | null {
  if (!metadata) {
    return null;
  }

  // Handle direct visualization object
  if (metadata.visualization && typeof metadata.visualization === 'object') {
    console.log('Found visualization object in metadata:', metadata.visualization);
    return validateVisualization(metadata.visualization);
  }

  // Handle image-based visualization from content
  if (metadata.content && Array.isArray(metadata.content)) {
    const imageContent = metadata.content.find(item => item.type === 'image' && item.src);
    if (imageContent) {
      console.log('Found image content in metadata:', imageContent);
      return validateVisualization({
        type: 'chart',
        title: metadata.prompt || 'Visualization',
        description: 'Generated visualization',
        data: {
          image: imageContent.src
        }
      });
    }
  }

  // Handle classification results
  if (metadata.sample_results && metadata.classification_type) {
    return validateVisualization({
      type: 'classification',
      title: 'Classification Results',
      description: `${metadata.sample_results.length} texts classified using ${metadata.classification_type === 'llm' ? 'LLM' : 'Hugging Face'} classification`,
      data: {
        results: metadata.sample_results,
        classification_type: metadata.classification_type
      }
    });
  }

  // Handle marketing content
  if (metadata.task_type && metadata.generated_content) {
    // Use the response field which contains the actual content
    return validateVisualization({
      type: 'marketing',
      title: `${metadata.task_type.replace(/_/g, ' ')} Content`,
      description: 'Generated marketing content',
      data: {
        content: metadata.response || metadata.content || '',
        task_type: metadata.task_type,
        canExport: true
      }
    });
  }

  // Handle data preview
  if (metadata.data_preview || metadata.data_profile) {
    const previewType = metadata.data_preview ? 'preview' : 'profile';
    const previewData = metadata.data_preview || metadata.data_profile;

    // Extract preview data from the metadata
    let dataToDisplay = null;
    let columns = [];

    if (previewData.metadata && previewData.metadata.preview_data) {
      dataToDisplay = previewData.metadata.preview_data;
      columns = Object.keys(dataToDisplay[0] || {});
    } else if (previewData.metadata && previewData.metadata.filtered_data) {
      dataToDisplay = previewData.metadata.filtered_data;
      columns = Object.keys(dataToDisplay[0] || {});
    } else if (previewData.metadata && previewData.metadata.columns) {
      columns = previewData.metadata.columns;
    }

    // Get the description text
    let descriptionText = '';
    if (previewData.content && previewData.content.length > 0) {
      descriptionText = previewData.content.map((item: any) =>
        item.type === 'text' ? item.text : ''
      ).join('\n');
    }

    return validateVisualization({
      type: 'data_preview',
      title: `Data ${previewType === 'preview' ? 'Preview' : 'Profile'}`,
      description: `${previewType === 'preview' ? 'First few rows' : 'Statistical summary'} of the data`,
      data: {
        preview_data: dataToDisplay,
        columns: columns,
        description: descriptionText,
        metadata: previewData.metadata || {}
      }
    });
  }

  // Handle string-based visualization types (legacy format)
  if (metadata.visualization && typeof metadata.visualization === 'string') {
    // Convert legacy format to new format
    const type = metadata.visualization as VisualizationType;
    let data: any;

    switch (type) {
      case 'chart':
        data = {
          labels: metadata.chart_labels || [],
          datasets: metadata.chart_datasets || [],
        };
        break;
      case 'table':
        data = {
          headers: metadata.table_headers || [],
          rows: metadata.table_rows || [],
        };
        break;
      default:
        data = metadata.visualization_data || {};
    }

    return validateVisualization({
      type,
      title: metadata.visualization_title,
      description: metadata.visualization_description,
      data,
      config: metadata.visualization_config,
    });
  }

  return null;
}

/**
 * Creates a sample chart visualization for testing.
 */
export function createSampleChartVisualization(): VisualizationData {
  return {
    type: 'chart',
    title: 'Sample Chart',
    description: 'A sample chart visualization',
    data: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
      datasets: [
        {
          label: 'Sales',
          data: [65, 59, 80, 81, 56],
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderColor: 'rgba(75, 192, 192, 1)',
        },
        {
          label: 'Revenue',
          data: [28, 48, 40, 19, 86],
          backgroundColor: 'rgba(153, 102, 255, 0.2)',
          borderColor: 'rgba(153, 102, 255, 1)',
        },
      ],
    },
    config: {
      type: 'bar',
    },
  };
}

/**
 * Creates a sample table visualization for testing.
 */
export function createSampleTableVisualization(): VisualizationData {
  return {
    type: 'table',
    title: 'Sample Table',
    description: 'A sample table visualization',
    data: {
      headers: ['Name', 'Age', 'City', 'Occupation'],
      rows: [
        ['John Doe', 32, 'New York', 'Developer'],
        ['Jane Smith', 28, 'San Francisco', 'Designer'],
        ['Bob Johnson', 45, 'Chicago', 'Manager'],
        ['Alice Brown', 24, 'Boston', 'Student'],
      ],
    },
  };
}
