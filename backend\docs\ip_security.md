# IP Address Security in Datagenius

This document explains how IP addresses are handled securely in the Datagenius application.

## Overview

IP addresses are considered personally identifiable information (PII) in many jurisdictions. To enhance security and privacy, Datagenius implements the following measures:

1. **IP Address Hashing**: All IP addresses are hashed using SHA-256 before storage
2. **IP Validation**: Optional validation of IP addresses during token refresh
3. **IP Change Lockout**: Optional lockout of users when their IP address changes
4. **Anonymized Logging**: IP addresses in logs are anonymized

## Implementation Details

### IP Address Hashing

When a user logs in or refreshes their token, their IP address is hashed using SHA-256 with a salt (the JWT secret key). This ensures that:

- Raw IP addresses are never stored in the database
- The hash is unique to the application instance (due to the salt)
- The hash cannot be reversed to obtain the original IP address

### IP Validation

The application can be configured to validate IP addresses during token refresh. When enabled:

1. The current IP address is hashed
2. The hash is compared with the stored hash from the previous login/refresh
3. If they don't match, the token refresh is rejected

This helps prevent token theft and session hijacking.

### IP Change Lockout

When IP change lockout is enabled, users are locked out of their account when their IP address changes. This requires them to log in again, providing an additional layer of security.

### Anonymized Logging

For debugging and audit purposes, IP addresses may be logged. However, they are anonymized before logging:

- IPv4 addresses have the last octet masked (e.g., 192.168.1.xxx)
- IPv6 addresses have the last 80 bits masked

## Configuration

The following environment variables control IP security features:

- `ENFORCE_IP_VALIDATION`: When set to "true", enforces IP validation during token refresh
- `IP_CHANGE_LOCKOUT`: When set to "true", invalidates tokens when IP address changes

## Security Considerations

- IP hashing provides privacy benefits but is not a substitute for proper access controls
- Users on mobile networks may experience IP changes during normal usage
- Consider the user experience impact before enabling strict IP validation

## Recommendations

For most deployments, we recommend:

- Always keep IP hashing enabled (default)
- Enable IP validation for applications with sensitive data
- Use IP change lockout only for high-security applications
- Consider implementing a gradual approach, where suspicious IP changes trigger additional verification rather than immediate lockout
