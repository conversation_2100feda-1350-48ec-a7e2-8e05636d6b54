"""
API endpoints for model providers.

This module provides API endpoints for managing model providers.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

from agents.utils.model_providers.utils import (
    list_available_providers,
    list_available_models,
    get_api_key
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/providers",
    tags=["providers"],
    responses={404: {"description": "Not found"}}
)


class ProviderInfo(BaseModel):
    """Provider information model."""
    id: str
    name: str
    available: bool
    error: Optional[str] = None


class ModelInfo(BaseModel):
    """Model information model."""
    id: str
    name: str
    description: Optional[str] = None
    context_length: Optional[int] = None
    provider: str


class ProvidersResponse(BaseModel):
    """Response model for providers endpoint."""
    providers: List[ProviderInfo]


class ModelsResponse(BaseModel):
    """Response model for models endpoint."""
    models: Dict[str, List[ModelInfo]]


@router.get("/", response_model=ProvidersResponse)
async def get_providers():
    """
    Get all available providers.
    """
    try:
        providers = await list_available_providers()
        return {"providers": providers}
    except Exception as e:
        logger.error(f"Error getting providers: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting providers: {str(e)}"
        )


@router.get("/models", response_model=ModelsResponse)
async def get_models():
    """
    Get all available models.
    """
    try:
        models_by_provider = await list_available_models()
        return {"models": models_by_provider}
    except Exception as e:
        logger.error(f"Error getting models: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting models: {str(e)}"
        )


@router.get("/global-availability")
async def get_global_availability():
    """
    Get global availability of providers based on API keys.
    """
    try:
        providers = ["groq", "openai", "anthropic", "gemini", "openrouter", "cohere", "mistral", "azure"]
        availability = {}
        
        for provider in providers:
            api_key = get_api_key(provider)
            availability[provider] = api_key is not None
            
        return availability
    except Exception as e:
        logger.error(f"Error getting global availability: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting global availability: {str(e)}"
        )
