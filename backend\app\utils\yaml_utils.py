from typing import Dict, Any
import yaml
import json
import os
import logging
import sys

# Set up logging
logger = logging.getLogger(__name__)

# Define a local deprecation warning function
def warn_json_usage(file_path: str, alternative: str = "YAML") -> None:
    """
    Log a deprecation warning for JSON file usage.

    Args:
        file_path: Path to the JSON file being used
        alternative: Alternative format to suggest (default: YAML)
    """
    logger.warning(
        f"DEPRECATION WARNING: Using JSON file format at {file_path}. "
        f"This format is deprecated and will be removed in a future version. "
        f"Please migrate to {alternative} format."
    )

def load_yaml(file_path: str, default_encoding: str = "utf-8") -> Dict[str, Any]:
    """
    Load YAML file with robust encoding handling.

    Args:
        file_path: Path to the YAML file
        default_encoding: Default encoding to try first

    Returns:
        Parsed YAML content as a dictionary

    Raises:
        ValueError: If the file is not a YAML file
        Exception: If there is an error loading the YAML file
    """
    # Check if this is a YAML file
    if not file_path.endswith(('.yaml', '.yml')):
        raise ValueError(f"File {file_path} is not a YAML file. Only YAML files are supported.")

    try:
        with open(file_path, 'r', encoding=default_encoding) as f:
            return yaml.safe_load(f)
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            raise Exception(f"Error loading YAML file: {e}")
    except Exception as e:
        raise Exception(f"Error loading YAML file: {e}")

def save_yaml(data: Dict[str, Any], file_path: str, encoding: str = "utf-8") -> bool:
    """
    Save data to a YAML file.

    Args:
        data: Data to save
        file_path: Path to save the file
        encoding: File encoding

    Returns:
        True if successful, False otherwise

    Raises:
        ValueError: If the file is not a YAML file
    """
    # Check if this is a YAML file
    if not file_path.endswith(('.yaml', '.yml')):
        raise ValueError(f"File {file_path} is not a YAML file. Only YAML files are supported.")

    try:
        with open(file_path, 'w', encoding=encoding) as f:
            yaml.dump(data, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error saving YAML file: {e}")
        return False

def json_to_yaml(json_data: Dict[str, Any]) -> str:
    """
    Convert JSON data to YAML string.

    Args:
        json_data: JSON data as dictionary

    Returns:
        YAML string representation
    """
    return yaml.dump(json_data, indent=2)

def yaml_to_json(yaml_data: str) -> Dict[str, Any]:
    """
    Convert YAML string to JSON data.

    Args:
        yaml_data: YAML string

    Returns:
        JSON data as dictionary
    """
    # Directly loading YAML often results in Python types compatible with JSON
    return yaml.safe_load(yaml_data)

def convert_file(input_path: str, output_path: str) -> bool:
    """
    Convert a file from JSON to YAML format.

    Args:
        input_path: Path to input JSON file
        output_path: Path to output YAML file

    Returns:
        True if successful, False otherwise

    Raises:
        ValueError: If the input file is not a JSON file or the output file is not a YAML file
    """
    # Validate file extensions
    if not input_path.endswith('.json'):
        raise ValueError(f"Input file {input_path} is not a JSON file. Only JSON files are supported as input.")

    if not output_path.endswith(('.yaml', '.yml')):
        raise ValueError(f"Output file {output_path} is not a YAML file. Only YAML files are supported as output.")

    try:
        # Converting from JSON to YAML
        with open(input_path, 'r') as f:
            json_data = json.load(f)
        yaml_string = json_to_yaml(json_data)
        with open(output_path, 'w') as f:
            f.write(yaml_string)
        return True
    except Exception as e:
        logger.error(f"Error converting file: {e}")
        return False

def schema_to_yaml(schema: Dict[str, Any], include_comments: bool = True) -> str:
    """
    Convert JSON Schema to YAML with optional explanatory comments.

    Args:
        schema: JSON Schema as dictionary
        include_comments: Whether to include explanatory comments

    Returns:
        YAML string representation of the schema
    """
    # TODO: Implement special handling for schema elements and comment generation
    # For now, just dump the schema as YAML
    yaml_string = yaml.dump(schema, indent=2, sort_keys=False, default_flow_style=False)
    return yaml_string

def yaml_to_schema(yaml_data: str) -> Dict[str, Any]:
    """
    Convert YAML to JSON Schema.

    Args:
        yaml_data: YAML string

    Returns:
        JSON Schema as dictionary
    """
    # TODO: Add validation to ensure the loaded YAML conforms to JSON Schema structure
    schema = yaml.safe_load(yaml_data)
    if not isinstance(schema, dict):
        raise ValueError("YAML data does not represent a valid schema (must be a dictionary)")
    # Add more specific JSON Schema validation here if needed
    return schema
