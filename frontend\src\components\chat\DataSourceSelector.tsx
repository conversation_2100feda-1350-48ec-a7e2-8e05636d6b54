import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Loader2, Database, FileText, Globe, Server, Search, X } from "lucide-react";
import { dataSourceApi, DataSource } from "@/lib/dataSourceApi";
import { useToast } from "@/hooks/use-toast";

interface DataSourceSelectorProps {
  onSelectDataSource: (dataSource: DataSource) => void;
  currentDataSourceId?: string;
}

export const DataSourceSelector = ({ onSelectDataSource, currentDataSourceId }: DataSourceSelectorProps) => {
  const { toast } = useToast();
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedType, setSelectedType] = useState<string | null>(null);

  // Load data sources
  useEffect(() => {
    const loadDataSources = async () => {
      setIsLoading(true);
      try {
        const response = await dataSourceApi.getDataSources();
        setDataSources(response.data_sources);
      } catch (error) {
        console.error("Failed to load data sources:", error);
        toast({
          title: "Error",
          description: "Failed to load data sources.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadDataSources();
  }, [toast]);

  // Filter data sources based on search query and selected type
  const filteredDataSources = dataSources.filter((dataSource) => {
    const matchesSearch = dataSource.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (dataSource.description && dataSource.description.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesType = selectedType ? dataSource.type === selectedType : true;
    
    return matchesSearch && matchesType;
  });

  // Get icon based on data source type
  const getDataSourceIcon = (type: string) => {
    switch (type) {
      case "file":
        return <FileText className="h-5 w-5 text-blue-500" />;
      case "database":
        return <Database className="h-5 w-5 text-green-500" />;
      case "api":
        return <Globe className="h-5 w-5 text-purple-500" />;
      case "mcp":
        return <Server className="h-5 w-5 text-orange-500" />;
      default:
        return <Database className="h-5 w-5 text-gray-500" />;
    }
  };

  // Handle selecting a data source
  const handleSelectDataSource = (dataSource: DataSource) => {
    onSelectDataSource(dataSource);
  };

  // Clear search query
  const clearSearch = () => {
    setSearchQuery("");
  };

  // Clear type filter
  const clearTypeFilter = () => {
    setSelectedType(null);
  };

  return (
    <div className="space-y-4">
      {/* Search and filter */}
      <div className="flex flex-col gap-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search data sources..."
            className="pl-10 pr-10"
          />
          {searchQuery && (
            <button
              onClick={clearSearch}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        
        <div className="flex gap-2 flex-wrap">
          <Button
            size="sm"
            variant={selectedType === "file" ? "default" : "outline"}
            onClick={() => setSelectedType(selectedType === "file" ? null : "file")}
            className="flex items-center gap-1"
          >
            <FileText className="h-4 w-4" />
            Files
          </Button>
          <Button
            size="sm"
            variant={selectedType === "database" ? "default" : "outline"}
            onClick={() => setSelectedType(selectedType === "database" ? null : "database")}
            className="flex items-center gap-1"
          >
            <Database className="h-4 w-4" />
            Databases
          </Button>
          <Button
            size="sm"
            variant={selectedType === "api" ? "default" : "outline"}
            onClick={() => setSelectedType(selectedType === "api" ? null : "api")}
            className="flex items-center gap-1"
          >
            <Globe className="h-4 w-4" />
            APIs
          </Button>
          <Button
            size="sm"
            variant={selectedType === "mcp" ? "default" : "outline"}
            onClick={() => setSelectedType(selectedType === "mcp" ? null : "mcp")}
            className="flex items-center gap-1"
          >
            <Server className="h-4 w-4" />
            MCP
          </Button>
          {selectedType && (
            <Button
              size="sm"
              variant="ghost"
              onClick={clearTypeFilter}
              className="flex items-center gap-1 text-gray-500"
            >
              <X className="h-4 w-4" />
              Clear filter
            </Button>
          )}
        </div>
      </div>

      {/* Data sources list */}
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      ) : filteredDataSources.length === 0 ? (
        <Card className="p-6 text-center">
          <div className="flex flex-col items-center gap-3">
            <Database className="h-12 w-12 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-700">No data sources found</h3>
            <p className="text-gray-500 max-w-xs">
              {searchQuery || selectedType
                ? "Try adjusting your search or filters"
                : "Add data sources in the Data Integration section to use them in chat."}
            </p>
            {(searchQuery || selectedType) && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setSearchQuery("");
                  setSelectedType(null);
                }}
                className="mt-2"
              >
                Clear all filters
              </Button>
            )}
          </div>
        </Card>
      ) : (
        <div className="space-y-3 max-h-[60vh] overflow-y-auto pr-1">
          {filteredDataSources.map((dataSource) => (
            <Card
              key={dataSource.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 transition-all duration-200 hover:shadow-md group ${
                currentDataSourceId === dataSource.id ? "ring-2 ring-brand-500 bg-brand-50" : ""
              }`}
              onClick={() => handleSelectDataSource(dataSource)}
            >
              <div className="flex items-start gap-3">
                <div className="p-2 bg-gray-100 rounded-md group-hover:bg-white">
                  {getDataSourceIcon(dataSource.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-900 truncate">{dataSource.name}</h3>
                  {dataSource.description && (
                    <p className="text-sm text-gray-500 truncate">{dataSource.description}</p>
                  )}
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs px-2 py-0.5 bg-gray-200 text-gray-700 rounded-full">
                      {dataSource.type}
                    </span>
                    <span className="text-xs text-gray-500">
                      Updated {new Date(dataSource.updated_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
