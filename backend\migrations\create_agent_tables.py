"""
Database migration script to create agent-related tables.

This script creates the conversations and messages tables for the agent-based architecture.
"""

import logging
import sys
import os
from pathlib import Path

# Add the parent directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from app.database import engine, Base, Conversation, Message

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_agent_tables():
    """Create the agent-related tables in the database."""
    logger.info("Creating agent-related tables...")
    
    # Create tables
    Base.metadata.create_all(bind=engine, tables=[
        Conversation.__table__,
        Message.__table__
    ])
    
    logger.info("Agent-related tables created successfully.")


if __name__ == "__main__":
    create_agent_tables()
