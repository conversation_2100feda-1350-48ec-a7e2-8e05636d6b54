
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>,
  Table2,
  MessageSquare,
  Bell
} from "lucide-react";

interface WidgetOption {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
}

interface AddWidgetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onWidgetAdded: () => void;
}

export function AddWidgetDialog({
  open,
  onOpenChange,
  onWidgetAdded,
}: AddWidgetDialogProps) {
  const [selectedWidget, setSelectedWidget] = useState<string | null>(null);

  const widgetOptions: WidgetOption[] = [
    {
      id: "line-chart",
      name: "Line Chart",
      description: "Visualize trends over time",
      icon: <PERSON><PERSON><PERSON>,
    },
    {
      id: "bar-chart",
      name: "Bar Chart",
      description: "Compare categories side by side",
      icon: <PERSON><PERSON><PERSON>,
    },
    {
      id: "area-chart",
      name: "Area Chart",
      description: "Show cumulative totals over time",
      icon: AreaChart,
    },
    {
      id: "pie-chart",
      name: "Pie Chart",
      description: "Display proportion between categories",
      icon: PieChart,
    },
    {
      id: "data-table",
      name: "Data Table",
      description: "Show detailed records in tabular format",
      icon: Table2,
    },
    {
      id: "activity-feed",
      name: "Activity Feed",
      description: "Show recent events and activities",
      icon: MessageSquare,
    },
    {
      id: "alerts",
      name: "Alerts Widget",
      description: "Display important notifications",
      icon: Bell,
    },
  ];

  const handleAddWidget = () => {
    // In a real application, we would add the widget to the dashboard
    onWidgetAdded();
    onOpenChange(false);
    setSelectedWidget(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[625px]">
        <DialogHeader>
          <DialogTitle>Add Widget</DialogTitle>
          <DialogDescription>
            Choose a widget to add to your dashboard.
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 py-4">
          {widgetOptions.map((widget) => (
            <div
              key={widget.id}
              className={`
                p-4 rounded-lg border cursor-pointer transition-all
                ${selectedWidget === widget.id ? "border-primary bg-primary/5" : "border-border hover:border-primary/50"}
              `}
              onClick={() => setSelectedWidget(widget.id)}
            >
              <div className="flex flex-col items-center text-center space-y-2">
                <widget.icon className="h-8 w-8 text-primary" />
                <Label>{widget.name}</Label>
                <p className="text-xs text-muted-foreground">{widget.description}</p>
              </div>
            </div>
          ))}
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => {
              setSelectedWidget(null);
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleAddWidget}
            disabled={!selectedWidget}
          >
            Add Widget
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
