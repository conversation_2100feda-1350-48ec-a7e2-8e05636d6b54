"""
Simple script to test component registration.
"""

import logging
import sys
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the component registry
from backend.agents.components.registry import ComponentRegistry


def test_component_registration():
    """Test that components are properly registered."""
    try:
        # List all registered components
        components = ComponentRegistry.list_registered_components()
        logger.info(f"Registered components: {components}")
        
        # Check if the concierge welcome component is registered
        if "concierge_welcome" in components:
            logger.info("ConciergeWelcomeComponent is registered")
            return True
        else:
            logger.error("ConciergeWelcomeComponent is not registered")
            return False
    except Exception as e:
        logger.error(f"Error testing component registration: {e}")
        return False


if __name__ == "__main__":
    # Run the test
    result = test_component_registration()
    
    # Exit with appropriate status code
    sys.exit(0 if result else 1)
