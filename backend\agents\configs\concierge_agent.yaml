# Configuration for the Concierge Agent

name: "concierge-agent"
description: "Your helpful guide to Datagenius. I can help you find the right AI persona for your task and assist with data."
class_path: "backend.agents.concierge_agent.concierge.ConciergeAgent"
system_prompt_template: "concierge_base.prompt"

# Concierge-specific configuration
recommendation_threshold: 0.7
max_recommendations: 3
consider_user_history: true

# Intent recognition patterns (configurable)
intent_patterns:
  persona_request:
    - "recommend"
    - "suggest"
    - "which agent"
    - "which persona"
    - "help me choose"
    - "what should I use"
    - "best for"
    - "need help with"
  data_help:
    - "upload"
    - "attach"
    - "data"
    - "file"
    - "csv"
    - "excel"
    - "pdf"
    - "analyze my data"
    - "process my file"
    - "import data"
  analysis_request:
    - "analyze"
    - "analysis"
    - "insights"
    - "patterns"
    - "trends"
    - "statistics"
    - "visualize"
    - "chart"
    - "graph"
    - "report"
  marketing_request:
    - "marketing"
    - "campaign"
    - "content"
    - "social media"
    - "seo"
    - "advertising"
    - "promotion"
    - "brand"
    - "copy"
    - "strategy"
  classification_request:
    - "classify"
    - "categorize"
    - "label"
    - "tag"
    - "organize"
    - "sort"
    - "group"
    - "cluster"
    - "identify"

# Intent to persona mapping (configurable)
intent_persona_mapping:
  analysis_request:
    - "composable-analyst"
    - "data-assistant"
  marketing_request:
    - "composable-marketer"
  classification_request:
    - "composable-classifier"
  data_help:
    - "composable-analyst"
    - "data-assistant"
  persona_request: []  # Handled by recommendation logic

# Components for the concierge agent
components:
  - type: "concierge_welcome"
    name: "ConciergeWelcome"
    config:
      greeting_message: "Hello! I'm the Datagenius Concierge. How can I help you today? You can ask me to find the right AI persona for your task or help you with your data."

  - type: "enhanced_context_manager"
    name: "EnhancedContextManager"
    config:
      context_ttl: 3600  # 1 hour TTL for context
      max_history_size: 10
      sync_interval: 300  # 5 minutes
      enable_entity_tracking: true
      enable_context_versioning: true

  - type: "persona_recommender"
    name: "PersonaRecommender"
    config:
      recommendation_threshold: 0.7
      max_recommendations: 3
      consider_user_history: true

  - type: "data_attachment_assistant"
    name: "DataAttachmentAssistant"
    config:
      supported_file_types:
        - "csv"
        - "xlsx"
        - "pdf"
        - "docx"
        - "txt"

  - type: "persona_routing"
    name: "PersonaRouter"
    config:
      routing_threshold: 0.7

  - type: "concierge_state_tracker"
    name: "StateTracker"
    config:
      state_ttl: 3600  # 1 hour TTL for states
      max_states: 1000
      cleanup_interval: 300  # 5 minutes

  - type: "persona_coordinator"
    name: "PersonaCoordinator"
    config:
      enable_auto_coordination: true
      coordination_threshold: 0.7
      max_coordination_depth: 3
      coordination_ttl: 3600  # 1 hour TTL for coordination data

  - type: "bidirectional_communication"
    name: "BidirectionalCommunication"
    config:
      enable_auto_callbacks: true
      callback_threshold: 0.7
      message_ttl: 3600  # 1 hour TTL for messages

  - type: "team_manager"
    name: "TeamManager"
    config:
      team_ttl: 3600  # 1 hour TTL for teams
      max_team_size: 5
      enable_auto_team_formation: true
      team_formation_threshold: 0.7
      team_templates:
        analysis_team:
          roles:
            manager: 1
            specialist: 2
            assistant: 1
          hierarchy:
            manager: [specialist, assistant]
            specialist: []
            assistant: []
        marketing_team:
          roles:
            manager: 1
            strategist: 1
            executor: 2
          hierarchy:
            manager: [strategist, executor]
            strategist: [executor]
            executor: []

  - type: "advanced_router"
    name: "AdvancedRouter"
    config:
      routing_ttl: 3600  # 1 hour TTL for routing
      max_routing_history: 100
      load_threshold: 10
      error_threshold: 3
      enable_auto_fallback: true
      fallback_chains:
        composable-analysis-ai: [composable-marketing-ai, composable-classifier-ai, concierge-agent]
        composable-marketing-ai: [composable-analysis-ai, composable-classifier-ai, concierge-agent]
        composable-classifier-ai: [composable-analysis-ai, composable-marketing-ai, concierge-agent]
        concierge-agent: [composable-analysis-ai, composable-marketing-ai, composable-classifier-ai]

  - type: "role_assignment"
    name: "RoleAssignment"
    config:
      assignment_ttl: 3600  # 1 hour TTL for role assignments
      enable_auto_assignment: true
      assignment_threshold: 0.7
      role_capabilities:
        analyst: [data_analysis, pattern_recognition, insight_generation]
        strategist: [planning, goal_setting, strategic_thinking]
        executor: [implementation, task_execution, action_taking]
        reviewer: [quality_control, verification, feedback]
        coordinator: [coordination, communication, delegation]
        specialist: [domain_expertise, specialized_knowledge, deep_focus]
        generalist: [broad_knowledge, adaptability, versatility]
      persona_affinities:
        composable-analysis-ai: [analyst, specialist]
        composable-marketing-ai: [strategist, executor]
        composable-classifier-ai: [specialist, analyst]
        concierge-agent: [coordinator, generalist]

  - type: "mcp_server"
    name: "MCPServer"
    config:
      enable_auto_tools: true
      tool_selection_threshold: 0.7
      max_tool_calls: 10
      tool_timeout: 30  # 30 seconds timeout for tool calls

# Capabilities provided by this agent
capabilities:
  - "guidance"
  - "persona_recommendation"
  - "data_assistance"
  - "persona_routing"
  - "enhanced_context_management"
  - "persona_coordination"
  - "bidirectional_communication"
  - "handoff_management"
  - "collaboration_management"
  - "team_management"
  - "hierarchical_teams"
  - "role_assignment"
  - "advanced_routing"
  - "fallback_mechanisms"
  - "specialized_roles"
  - "mcp_tools"

# Default model configuration (can be overridden by components)
model_config:
  provider: "openai" # Example provider
  model: "gpt-4o" # Example model
  temperature: 0.7
  max_tokens: 1500

# Agent-specific settings (if any)
settings:
  greeting_message: "Hello! I'm the Datagenius Concierge. How can I help you today? You can ask me to find the right AI persona for your task or help you with your data."
