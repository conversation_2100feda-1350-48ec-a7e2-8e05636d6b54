"""
Deployment workflow MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for managing the deployment workflow
of persona versions.
"""

import logging
import os
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional

from .base import BaseMCPTool

logger = logging.getLogger(__name__)


class DeploymentTool(BaseMCPTool):
    """Tool for managing persona deployment workflows."""

    def __init__(self):
        """Initialize the deployment tool."""
        super().__init__(
            name="deployment",
            description="Manage deployment workflows for persona versioning",
            input_schema={
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["create_version", "activate_version", "rollback_version", "list_versions", "delete_version", "compare_versions"],
                        "description": "The deployment operation to perform"
                    },
                    "persona_id": {
                        "type": "string",
                        "description": "ID of the persona"
                    },
                    "version": {
                        "type": "string",
                        "description": "Version string"
                    },
                    "config": {
                        "type": "object",
                        "description": "Configuration dictionary for create_version operation"
                    },
                    "version2": {
                        "type": "string",
                        "description": "Second version string for compare_versions operation"
                    },
                    "personas_dir": {
                        "type": "string",
                        "description": "Directory containing persona configurations",
                        "default": "personas"
                    }
                },
                "required": ["operation", "persona_id"]
            }
        )

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # No additional initialization needed
        pass

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the deployment tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution (following the inputSchema)

        Returns:
            Tool execution results in MCP format
        """
        operation = arguments["operation"]
        persona_id = arguments["persona_id"]
        version = arguments.get("version")
        config = arguments.get("config", {})
        version2 = arguments.get("version2")
        personas_dir = arguments.get("personas_dir", "personas")

        logger.info(f"Executing deployment operation: {operation} for persona: {persona_id}")

        try:
            if operation == "create_version":
                if not version:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Version is required for create_version operation"
                            }
                        ]
                    }
                if not config:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Config is required for create_version operation"
                            }
                        ]
                    }
                
                result = self._create_version(persona_id, version, config, personas_dir)
                if result:
                    return {
                        "content": [
                            {
                                "type": "text",
                                "text": f"Successfully created version {version} for persona {persona_id}"
                            }
                        ],
                        "metadata": {
                            "persona_id": persona_id,
                            "version": version,
                            "operation": "create_version",
                            "success": True
                        }
                    }
                else:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Failed to create version {version} for persona {persona_id}"
                            }
                        ],
                        "metadata": {
                            "persona_id": persona_id,
                            "version": version,
                            "operation": "create_version",
                            "success": False
                        }
                    }
            
            elif operation == "activate_version":
                if not version:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Version is required for activate_version operation"
                            }
                        ]
                    }
                
                result = self._activate_version(persona_id, version, personas_dir)
                if result:
                    return {
                        "content": [
                            {
                                "type": "text",
                                "text": f"Successfully activated version {version} for persona {persona_id}"
                            }
                        ],
                        "metadata": {
                            "persona_id": persona_id,
                            "version": version,
                            "operation": "activate_version",
                            "success": True
                        }
                    }
                else:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Failed to activate version {version} for persona {persona_id}"
                            }
                        ],
                        "metadata": {
                            "persona_id": persona_id,
                            "version": version,
                            "operation": "activate_version",
                            "success": False
                        }
                    }
            
            elif operation == "rollback_version":
                if not version:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Version is required for rollback_version operation"
                            }
                        ]
                    }
                
                result = self._activate_version(persona_id, version, personas_dir)
                if result:
                    return {
                        "content": [
                            {
                                "type": "text",
                                "text": f"Successfully rolled back to version {version} for persona {persona_id}"
                            }
                        ],
                        "metadata": {
                            "persona_id": persona_id,
                            "version": version,
                            "operation": "rollback_version",
                            "success": True
                        }
                    }
                else:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Failed to roll back to version {version} for persona {persona_id}"
                            }
                        ],
                        "metadata": {
                            "persona_id": persona_id,
                            "version": version,
                            "operation": "rollback_version",
                            "success": False
                        }
                    }
            
            elif operation == "list_versions":
                versions = self._list_versions(persona_id, personas_dir)
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Available versions for persona {persona_id}: {', '.join(versions) if versions else 'None'}"
                        }
                    ],
                    "metadata": {
                        "persona_id": persona_id,
                        "versions": versions,
                        "operation": "list_versions",
                        "success": True
                    }
                }
            
            elif operation == "delete_version":
                if not version:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Version is required for delete_version operation"
                            }
                        ]
                    }
                
                result = self._delete_version(persona_id, version, personas_dir)
                if result:
                    return {
                        "content": [
                            {
                                "type": "text",
                                "text": f"Successfully deleted version {version} for persona {persona_id}"
                            }
                        ],
                        "metadata": {
                            "persona_id": persona_id,
                            "version": version,
                            "operation": "delete_version",
                            "success": True
                        }
                    }
                else:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Failed to delete version {version} for persona {persona_id}"
                            }
                        ],
                        "metadata": {
                            "persona_id": persona_id,
                            "version": version,
                            "operation": "delete_version",
                            "success": False
                        }
                    }
            
            elif operation == "compare_versions":
                if not version or not version2:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Both version and version2 are required for compare_versions operation"
                            }
                        ]
                    }
                
                differences = self._compare_versions(persona_id, version, version2, personas_dir)
                if differences:
                    diff_text = "Differences between versions:\n\n"
                    for key, value in differences.items():
                        diff_text += f"- {key}:\n"
                        diff_text += f"  - Version {version}: {value['version1']}\n"
                        diff_text += f"  - Version {version2}: {value['version2']}\n\n"
                    
                    return {
                        "content": [
                            {
                                "type": "text",
                                "text": diff_text
                            }
                        ],
                        "metadata": {
                            "persona_id": persona_id,
                            "version1": version,
                            "version2": version2,
                            "differences": differences,
                            "operation": "compare_versions",
                            "success": True
                        }
                    }
                else:
                    return {
                        "content": [
                            {
                                "type": "text",
                                "text": f"No differences found between versions {version} and {version2} for persona {persona_id}"
                            }
                        ],
                        "metadata": {
                            "persona_id": persona_id,
                            "version1": version,
                            "version2": version2,
                            "differences": {},
                            "operation": "compare_versions",
                            "success": True
                        }
                    }
            
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unknown operation: {operation}"
                        }
                    ]
                }
        
        except Exception as e:
            logger.error(f"Error executing deployment operation: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error executing deployment operation: {str(e)}"
                    }
                ],
                "metadata": {
                    "persona_id": persona_id,
                    "operation": operation,
                    "error": str(e),
                    "success": False
                }
            }

    def _create_version(self, persona_id: str, version: str, config: Dict[str, Any], personas_dir: str = "personas") -> bool:
        """
        Create a new version file for a persona.

        Args:
            persona_id: ID of the persona
            version: Version string
            config: Configuration dictionary
            personas_dir: Directory containing persona configurations

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure the personas directory exists
            personas_path = Path(personas_dir)
            if not personas_path.exists():
                logger.error(f"Personas directory {personas_dir} does not exist")
                return False
            
            # Create the version file path
            version_file = personas_path / f"{persona_id}-{version}.yaml"
            
            # Check if the version file already exists
            if version_file.exists():
                logger.warning(f"Version file {version_file} already exists")
                return False
            
            # Ensure the version is set in the config
            config["version"] = version
            
            # Write the configuration to the version file
            with open(version_file, "w") as f:
                yaml.dump(config, f, default_flow_style=False)
            
            logger.info(f"Created version file {version_file}")
            return True
        except Exception as e:
            logger.error(f"Error creating version file: {e}")
            return False

    def _activate_version(self, persona_id: str, version: str, personas_dir: str = "personas") -> bool:
        """
        Activate a version by copying its configuration to the main persona file.

        Args:
            persona_id: ID of the persona
            version: Version string
            personas_dir: Directory containing persona configurations

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure the personas directory exists
            personas_path = Path(personas_dir)
            if not personas_path.exists():
                logger.error(f"Personas directory {personas_dir} does not exist")
                return False
            
            # Get the version file path
            version_file = personas_path / f"{persona_id}-{version}.yaml"
            
            # Check if the version file exists
            if not version_file.exists():
                logger.error(f"Version file {version_file} does not exist")
                return False
            
            # Get the main persona file path
            persona_file = personas_path / f"{persona_id}.yaml"
            
            # Load the version configuration
            with open(version_file, "r") as f:
                config = yaml.safe_load(f)
            
            # Write the configuration to the main persona file
            with open(persona_file, "w") as f:
                yaml.dump(config, f, default_flow_style=False)
            
            logger.info(f"Activated version {version} for persona {persona_id}")
            return True
        except Exception as e:
            logger.error(f"Error activating version: {e}")
            return False

    def _list_versions(self, persona_id: str, personas_dir: str = "personas") -> List[str]:
        """
        List all available versions for a persona.

        Args:
            persona_id: ID of the persona
            personas_dir: Directory containing persona configurations

        Returns:
            List of version strings
        """
        try:
            # Ensure the personas directory exists
            personas_path = Path(personas_dir)
            if not personas_path.exists():
                logger.error(f"Personas directory {personas_dir} does not exist")
                return []
            
            # Find all version files for this persona
            versions = []
            for file_path in personas_path.glob(f"{persona_id}-*.yaml"):
                # Extract the version from the filename
                version = file_path.stem.split("-", 1)[1]
                versions.append(version)
            
            return sorted(versions)
        except Exception as e:
            logger.error(f"Error listing versions: {e}")
            return []

    def _delete_version(self, persona_id: str, version: str, personas_dir: str = "personas") -> bool:
        """
        Delete a version file.

        Args:
            persona_id: ID of the persona
            version: Version string
            personas_dir: Directory containing persona configurations

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure the personas directory exists
            personas_path = Path(personas_dir)
            if not personas_path.exists():
                logger.error(f"Personas directory {personas_dir} does not exist")
                return False
            
            # Get the version file path
            version_file = personas_path / f"{persona_id}-{version}.yaml"
            
            # Check if the version file exists
            if not version_file.exists():
                logger.error(f"Version file {version_file} does not exist")
                return False
            
            # Delete the version file
            os.remove(version_file)
            
            logger.info(f"Deleted version {version} for persona {persona_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting version: {e}")
            return False

    def _compare_versions(self, persona_id: str, version1: str, version2: str, personas_dir: str = "personas") -> Dict[str, Any]:
        """
        Compare two versions of a persona.

        Args:
            persona_id: ID of the persona
            version1: First version string
            version2: Second version string
            personas_dir: Directory containing persona configurations

        Returns:
            Dictionary with differences between the versions
        """
        try:
            # Ensure the personas directory exists
            personas_path = Path(personas_dir)
            if not personas_path.exists():
                logger.error(f"Personas directory {personas_dir} does not exist")
                return {}
            
            # Get the version file paths
            version1_file = personas_path / f"{persona_id}-{version1}.yaml"
            version2_file = personas_path / f"{persona_id}-{version2}.yaml"
            
            # Check if the version files exist
            if not version1_file.exists():
                logger.error(f"Version file {version1_file} does not exist")
                return {}
            
            if not version2_file.exists():
                logger.error(f"Version file {version2_file} does not exist")
                return {}
            
            # Load the version configurations
            with open(version1_file, "r") as f:
                config1 = yaml.safe_load(f)
            
            with open(version2_file, "r") as f:
                config2 = yaml.safe_load(f)
            
            # Compare the configurations
            differences = {}
            
            # Find keys that are in config1 but not in config2
            for key in config1:
                if key not in config2:
                    differences[key] = {
                        "version1": config1[key],
                        "version2": None
                    }
                elif config1[key] != config2[key]:
                    differences[key] = {
                        "version1": config1[key],
                        "version2": config2[key]
                    }
            
            # Find keys that are in config2 but not in config1
            for key in config2:
                if key not in config1:
                    differences[key] = {
                        "version1": None,
                        "version2": config2[key]
                    }
            
            return differences
        except Exception as e:
            logger.error(f"Error comparing versions: {e}")
            return {}
