"""
Provider models utility functions.

This module provides utility functions for fetching available models from AI providers.
"""

import os
import logging
import json
import requests
from typing import List, Dict, Any, Optional, Tuple

# Configure logging
logger = logging.getLogger(__name__)

def fetch_openai_models(api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models from OpenAI API.
    
    Args:
        api_key: OpenAI API key
        endpoint: OpenAI API endpoint
        
    Returns:
        List of available models
    """
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Normalize endpoint URL
        if endpoint.endswith('/'):
            endpoint = endpoint[:-1]
            
        # Make request to OpenAI API
        response = requests.get(f"{endpoint}/models", headers=headers, timeout=10)
        response.raise_for_status()
        
        # Parse response
        data = response.json()
        models = data.get("data", [])
        
        # Filter and format models
        chat_models = []
        for model in models:
            model_id = model.get("id", "")
            # Only include chat models
            if any(prefix in model_id for prefix in ["gpt-", "ft:gpt-"]):
                chat_models.append({
                    "id": model_id,
                    "name": model_id.replace("gpt-", "GPT ").replace("-turbo", " Turbo"),
                    "description": model.get("description", ""),
                    "created": model.get("created", 0),
                    "context_length": model.get("context_length", 0),
                })
                
        # Sort models by creation date (newest first)
        chat_models.sort(key=lambda x: x.get("created", 0), reverse=True)
        return chat_models
    except Exception as e:
        logger.error(f"Error fetching OpenAI models: {str(e)}", exc_info=True)
        return []

def fetch_groq_models(api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models from Groq API.
    
    Args:
        api_key: Groq API key
        endpoint: Groq API endpoint
        
    Returns:
        List of available models
    """
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Normalize endpoint URL
        if endpoint.endswith('/'):
            endpoint = endpoint[:-1]
            
        # Make request to Groq API
        response = requests.get(f"{endpoint}/models", headers=headers, timeout=10)
        response.raise_for_status()
        
        # Parse response
        data = response.json()
        models = data.get("data", [])
        
        # Format models
        formatted_models = []
        for model in models:
            model_id = model.get("id", "")
            formatted_models.append({
                "id": model_id,
                "name": model_id.replace("-", " ").title(),
                "description": model.get("description", ""),
                "created": model.get("created", 0),
                "context_length": model.get("context_window", 0),
            })
                
        # Sort models by creation date (newest first)
        formatted_models.sort(key=lambda x: x.get("created", 0), reverse=True)
        return formatted_models
    except Exception as e:
        logger.error(f"Error fetching Groq models: {str(e)}", exc_info=True)
        return []

def fetch_gemini_models(api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models from Google Gemini API.
    
    Args:
        api_key: Google API key
        endpoint: Gemini API endpoint
        
    Returns:
        List of available models
    """
    # Gemini doesn't have a models endpoint, so we return a static list
    # In a production environment, you might want to check model availability
    # by making a test request to each model
    return [
        {
            "id": "gemini-1.5-pro",
            "name": "Gemini 1.5 Pro",
            "description": "Google's most capable multimodal model",
            "context_length": 1000000,
        },
        {
            "id": "gemini-1.5-flash",
            "name": "Gemini 1.5 Flash",
            "description": "Fast and efficient multimodal model",
            "context_length": 1000000,
        },
        {
            "id": "gemini-1.0-pro",
            "name": "Gemini 1.0 Pro",
            "description": "Google's previous generation multimodal model",
            "context_length": 32768,
        }
    ]

def fetch_anthropic_models(api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models from Anthropic API.
    
    Args:
        api_key: Anthropic API key
        endpoint: Anthropic API endpoint
        
    Returns:
        List of available models
    """
    try:
        headers = {
            "x-api-key": api_key,
            "Content-Type": "application/json"
        }
        
        # Anthropic doesn't have a models endpoint in their v1 API
        # In a production environment, you might want to check model availability
        # by making a test request to each model
        return [
            {
                "id": "claude-3-opus-20240229",
                "name": "Claude 3 Opus",
                "description": "Most powerful Claude model",
                "context_length": 200000,
            },
            {
                "id": "claude-3-sonnet-20240229",
                "name": "Claude 3 Sonnet",
                "description": "Balanced performance and speed",
                "context_length": 200000,
            },
            {
                "id": "claude-3-haiku-20240307",
                "name": "Claude 3 Haiku",
                "description": "Fast and efficient model",
                "context_length": 200000,
            }
        ]
    except Exception as e:
        logger.error(f"Error fetching Anthropic models: {str(e)}", exc_info=True)
        return []

def fetch_openrouter_models(api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models from OpenRouter API.
    
    Args:
        api_key: OpenRouter API key
        endpoint: OpenRouter API endpoint
        
    Returns:
        List of available models
    """
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Normalize endpoint URL
        if endpoint.endswith('/'):
            endpoint = endpoint[:-1]
            
        # Make request to OpenRouter API
        response = requests.get(f"{endpoint}/models", headers=headers, timeout=10)
        response.raise_for_status()
        
        # Parse response
        models = response.json()
        
        # Format models
        formatted_models = []
        for model in models:
            model_id = model.get("id", "")
            context_window = model.get("context_length", 0)
            
            # Skip non-chat models
            if not model.get("chat", False):
                continue
                
            formatted_models.append({
                "id": model_id,
                "name": model.get("name", model_id),
                "description": f"{model.get('description', '')} (via {model.get('provider', 'Unknown')})",
                "context_length": context_window,
                "provider": model.get("provider", "Unknown"),
            })
                
        # Sort models by provider and name
        formatted_models.sort(key=lambda x: (x.get("provider", ""), x.get("name", "")))
        return formatted_models
    except Exception as e:
        logger.error(f"Error fetching OpenRouter models: {str(e)}", exc_info=True)
        return []

def fetch_provider_models(provider_id: str, api_key: str, endpoint: str) -> List[Dict[str, Any]]:
    """
    Fetch available models for a provider.
    
    Args:
        provider_id: Provider ID
        api_key: API key for the provider
        endpoint: API endpoint for the provider
        
    Returns:
        List of available models
    """
    if not api_key or not endpoint:
        logger.warning(f"Missing API key or endpoint for provider {provider_id}")
        return []
        
    # Fetch models based on provider
    if provider_id == "openai":
        return fetch_openai_models(api_key, endpoint)
    elif provider_id == "groq":
        return fetch_groq_models(api_key, endpoint)
    elif provider_id == "gemini":
        return fetch_gemini_models(api_key, endpoint)
    elif provider_id == "anthropic":
        return fetch_anthropic_models(api_key, endpoint)
    elif provider_id == "openrouter":
        return fetch_openrouter_models(api_key, endpoint)
    else:
        logger.warning(f"Unsupported provider: {provider_id}")
        return []
