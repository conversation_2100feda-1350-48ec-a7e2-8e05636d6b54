#!/usr/bin/env python
"""
Scrip<PERSON> to test Qdrant connection with detailed logging.

This script attempts to connect to Qdrant using different host configurations
and provides detailed logging about the connection attempts.
"""

import os
import sys
import time
import logging
import requests
import subprocess
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Constants
CONTAINER_NAME = "datagenius-qdrant"
DEFAULT_HOST = os.getenv("QDRANT_HOST", "localhost")
DEFAULT_PORT = int(os.getenv("QDRANT_PORT", "6333"))

def is_qdrant_running(host, port):
    """Check if Qdrant is running at the specified host and port."""
    try:
        logger.info(f"Checking if Qdrant is running at http://{host}:{port}/health")
        response = requests.get(f"http://{host}:{port}/health", timeout=5)
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response content: {response.text}")
        return response.status_code == 200
    except requests.RequestException as e:
        logger.error(f"Failed to connect to Qdrant at {host}:{port}: {str(e)}")
        return False

def get_container_ip():
    """Get the IP address of the Qdrant container."""
    try:
        result = subprocess.run(
            ["docker", "inspect", "-f", "{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}", CONTAINER_NAME],
            capture_output=True,
            text=True,
            check=True
        )
        ip = result.stdout.strip()
        logger.info(f"Qdrant container IP: {ip}")
        return ip
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to get container IP: {e}")
        return None

def get_container_status():
    """Get the status of the Qdrant container."""
    try:
        result = subprocess.run(
            ["docker", "inspect", "--format", "{{.State.Status}}", CONTAINER_NAME],
            capture_output=True,
            text=True,
            check=True
        )
        status = result.stdout.strip()
        logger.info(f"Qdrant container status: {status}")
        return status
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to get container status: {e}")
        return None

def get_container_health():
    """Get the health status of the Qdrant container."""
    try:
        result = subprocess.run(
            ["docker", "inspect", "--format", "{{.State.Health.Status}}", CONTAINER_NAME],
            capture_output=True,
            text=True,
            check=True
        )
        health = result.stdout.strip()
        logger.info(f"Qdrant container health: {health}")
        return health
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to get container health: {e}")
        return None

def get_container_logs():
    """Get the logs of the Qdrant container."""
    try:
        result = subprocess.run(
            ["docker", "logs", "--tail", "20", CONTAINER_NAME],
            capture_output=True,
            text=True,
            check=True
        )
        logs = result.stdout
        logger.info(f"Qdrant container logs: {logs}")
        return logs
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to get container logs: {e}")
        return None

def main():
    """Main function to test Qdrant connection."""
    logger.info("Testing Qdrant connection...")
    
    # Check environment variables
    logger.info(f"QDRANT_HOST from environment: {os.getenv('QDRANT_HOST', 'not set')}")
    logger.info(f"QDRANT_PORT from environment: {os.getenv('QDRANT_PORT', 'not set')}")
    
    # Check container status
    logger.info("Checking container status...")
    status = get_container_status()
    health = get_container_health()
    
    # Get container IP
    logger.info("Getting container IP...")
    container_ip = get_container_ip()
    
    # Try connecting to different hosts
    hosts_to_try = ["localhost", "127.0.0.1", "qdrant"]
    if container_ip:
        hosts_to_try.append(container_ip)
    
    logger.info(f"Trying to connect to Qdrant on hosts: {hosts_to_try}")
    
    for host in hosts_to_try:
        logger.info(f"Trying host: {host}")
        if is_qdrant_running(host, DEFAULT_PORT):
            logger.info(f"Successfully connected to Qdrant at {host}:{DEFAULT_PORT}")
        else:
            logger.info(f"Failed to connect to Qdrant at {host}:{DEFAULT_PORT}")
    
    # Get container logs
    logger.info("Getting container logs...")
    logs = get_container_logs()
    
    logger.info("Test completed.")

if __name__ == "__main__":
    main()
