"""
Component for coordinating interactions between personas.
"""

import logging
import json
import time
from typing import Dict, Any, List, Optional, Tuple

from .base import AgentComponent
from ..registry import AgentRegistry

logger = logging.getLogger(__name__)


class PersonaCoordinatorComponent(AgentComponent):
    """
    Coordinates interactions between personas, enabling seamless handoffs,
    collaborative problem-solving, and shared context management.
    """

    def __init__(self):
        """Initialize the PersonaCoordinatorComponent."""
        super().__init__()
        self.coordination_store = {}  # In-memory store for coordination data
        self.handoff_registry = {}  # Track handoffs between personas

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"PersonaCoordinatorComponent '{self.name}' initialized.")
        self.agent_registry = AgentRegistry
        self.enable_auto_coordination = config.get("enable_auto_coordination", True)
        self.coordination_threshold = config.get("coordination_threshold", 0.7)
        self.max_coordination_depth = config.get("max_coordination_depth", 3)
        self.coordination_ttl = config.get("coordination_ttl", 3600)  # Default TTL: 1 hour

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the context to coordinate interactions between personas.

        Args:
            context: Context dictionary containing request data.

        Returns:
            Updated context dictionary with coordination capabilities.
        """
        user_message = context.get("message", "")
        conversation_id = context.get("conversation_id")
        current_persona = context.get("persona_id", "unknown")

        logger.debug(f"PersonaCoordinatorComponent processing for conversation {conversation_id}")

        # Initialize coordination context if not present
        if "coordination" not in context:
            context["coordination"] = {
                "handoffs": [],
                "collaborations": [],
                "coordination_chain": [],
                "coordination_depth": 0
            }

        # Check for handoff commands
        if self._is_handoff_command(user_message):
            # Extract handoff target from command
            handoff_target, handoff_reason = self._extract_handoff_info(user_message)
            if handoff_target:
                # Register handoff
                await self._register_handoff(conversation_id, current_persona, handoff_target, handoff_reason)

                # Add handoff information to the context
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["handoff_request"] = {
                    "target_persona": handoff_target,
                    "reason": handoff_reason,
                    "message": f"I'll hand you off to the {handoff_target} persona for {handoff_reason}."
                }

                logger.info(f"Handoff initiated to {handoff_target} for conversation {conversation_id}")
            else:
                logger.warning(f"Handoff command detected but no target persona found: {user_message}")

        # Check for collaboration commands
        if self._is_collaboration_command(user_message):
            # Extract collaboration information
            collaboration_targets, collaboration_task = self._extract_collaboration_info(user_message)
            if collaboration_targets:
                # Register collaboration
                await self._register_collaboration(conversation_id, current_persona, collaboration_targets, collaboration_task)

                # Add collaboration information to the context
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["collaboration_request"] = {
                    "target_personas": collaboration_targets,
                    "task": collaboration_task,
                    "message": f"I'll coordinate with {', '.join(collaboration_targets)} to help with {collaboration_task}."
                }

                logger.info(f"Collaboration initiated with {collaboration_targets} for conversation {conversation_id}")
            else:
                logger.warning(f"Collaboration command detected but no target personas found: {user_message}")

        # Check if this is a handoff target
        is_handoff_target, handoff_source, handoff_reason = await self._check_handoff_target(conversation_id, current_persona)
        if is_handoff_target:
            # Add handoff information to the context
            context["coordination"]["handoffs"].append({
                "source_persona": handoff_source,
                "reason": handoff_reason,
                "timestamp": time.time()
            })

            # Add handoff information to the metadata
            context["metadata"] = context.get("metadata", {})
            context["metadata"]["handoff_received"] = {
                "source_persona": handoff_source,
                "reason": handoff_reason,
                "message": f"I'm taking over from the {handoff_source} persona to help with {handoff_reason}."
            }

            logger.info(f"Handoff received from {handoff_source} for conversation {conversation_id}")

        # Check if this is a collaboration participant
        is_collaboration_participant, collaboration_source, collaboration_task = await self._check_collaboration_participant(conversation_id, current_persona)
        if is_collaboration_participant:
            # Add collaboration information to the context
            context["coordination"]["collaborations"].append({
                "source_persona": collaboration_source,
                "task": collaboration_task,
                "timestamp": time.time()
            })

            # Add collaboration information to the metadata
            context["metadata"] = context.get("metadata", {})
            context["metadata"]["collaboration_received"] = {
                "source_persona": collaboration_source,
                "task": collaboration_task,
                "message": f"I'm collaborating with the {collaboration_source} persona on {collaboration_task}."
            }

            logger.info(f"Collaboration received from {collaboration_source} for conversation {conversation_id}")

        # Update coordination chain
        if "coordination_chain" not in context["coordination"]:
            context["coordination"]["coordination_chain"] = []

        # Add current persona to the chain if not already present
        if not context["coordination"]["coordination_chain"] or context["coordination"]["coordination_chain"][-1] != current_persona:
            context["coordination"]["coordination_chain"].append(current_persona)

        # Update coordination depth
        context["coordination"]["coordination_depth"] = len(context["coordination"]["coordination_chain"])

        # Check if coordination depth exceeds maximum
        if context["coordination"]["coordination_depth"] > self.max_coordination_depth:
            # Add warning to metadata
            context["metadata"] = context.get("metadata", {})
            context["metadata"]["coordination_warning"] = {
                "message": f"Coordination depth exceeds maximum ({self.max_coordination_depth}). Consider simplifying the workflow."
            }

            logger.warning(f"Coordination depth exceeds maximum for conversation {conversation_id}: {context['coordination']['coordination_depth']}")

        # Check for automatic coordination detection
        if self.enable_auto_coordination and not self._is_handoff_command(user_message) and not self._is_collaboration_command(user_message):
            coordination_needed, coordination_type, target_personas, reason = self._detect_coordination_need(user_message, context)
            if coordination_needed:
                # Add automatic coordination suggestion to the context
                context["metadata"] = context.get("metadata", {})
                context["metadata"]["auto_coordination_suggestion"] = {
                    "type": coordination_type,
                    "target_personas": target_personas,
                    "reason": reason,
                    "message": f"It seems you might benefit from {coordination_type} with the {', '.join(target_personas)} persona(s). Would you like me to arrange that?"
                }

                logger.info(f"Automatic {coordination_type} suggestion for {target_personas} in conversation {conversation_id}")

        return context

    def _is_handoff_command(self, message: str) -> bool:
        """
        Determine if the message is a command to hand off to another persona.

        Args:
            message: The user message.

        Returns:
            True if this is a handoff command, False otherwise.
        """
        # Simple keyword-based detection
        handoff_keywords = [
            "hand off to", "transfer to", "switch to", "change to",
            "let me talk to", "connect me with", "pass me to"
        ]

        return any(keyword in message.lower() for keyword in handoff_keywords)

    def _extract_handoff_info(self, message: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract the handoff target and reason from a handoff command.

        Args:
            message: The user message.

        Returns:
            A tuple containing the target persona and the reason for the handoff.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Map keywords to consistent kebab-case persona IDs
        persona_mapping = {
            "analyst": "composable-analysis-ai",
            "analysis": "composable-analysis-ai",
            "marketer": "composable-marketing-ai",
            "marketing": "composable-marketing-ai",
            "classifier": "composable-classifier-ai",
            "classification": "composable-classifier-ai",
            "concierge": "concierge-agent"
        }
        target_persona = None

        for keyword, persona_id in persona_mapping.items():
            if keyword in message_lower:
                target_persona = persona_id
                break

        if not target_persona:
            return None, None

        # Extract reason (simple heuristic - everything after "because" or "for")
        reason = None
        if "because" in message_lower:
            reason = message_lower.split("because", 1)[1].strip()
        elif "for" in message_lower:
            reason = message_lower.split("for", 1)[1].strip()
        else:
            # Default reason
            reason = f"assistance with {message_lower}"

        return target_persona, reason

    async def _register_handoff(self, conversation_id: str, source_persona: str, target_persona: str, reason: str) -> None:
        """
        Register a handoff between personas.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The persona initiating the handoff.
            target_persona: The persona to hand off to.
            reason: The reason for the handoff.
        """
        handoff_info = {
            "conversation_id": conversation_id,
            "source_persona": source_persona,
            "target_persona": target_persona,
            "reason": reason,
            "timestamp": time.time(),
            "status": "pending"
        }

        # Store the handoff
        key = f"{conversation_id}:{target_persona}:handoffs"
        if key not in self.handoff_registry:
            self.handoff_registry[key] = []

        self.handoff_registry[key].append(handoff_info)
        logger.debug(f"Registered handoff: {key}")

    async def _check_handoff_target(self, conversation_id: str, current_persona: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Check if the current persona is the target of a handoff.

        Args:
            conversation_id: The ID of the conversation.
            current_persona: The current persona.

        Returns:
            A tuple containing a boolean indicating if this is a handoff target, the source persona, and the reason.
        """
        key = f"{conversation_id}:{current_persona}:handoffs"
        handoffs = self.handoff_registry.get(key, [])

        # Filter for pending handoffs
        pending_handoffs = [h for h in handoffs if h["status"] == "pending"]

        if pending_handoffs:
            # Get the most recent handoff
            handoff = sorted(pending_handoffs, key=lambda x: x["timestamp"], reverse=True)[0]

            # Update status to "completed"
            handoff["status"] = "completed"

            # Update the registry
            for i, h in enumerate(handoffs):
                if h["timestamp"] == handoff["timestamp"] and h["source_persona"] == handoff["source_persona"]:
                    handoffs[i] = handoff
                    break

            self.handoff_registry[key] = handoffs

            return True, handoff["source_persona"], handoff["reason"]

        return False, None, None

    def _is_collaboration_command(self, message: str) -> bool:
        """
        Determine if the message is a command to collaborate with other personas.

        Args:
            message: The user message.

        Returns:
            True if this is a collaboration command, False otherwise.
        """
        # Simple keyword-based detection
        collaboration_keywords = [
            "collaborate with", "work with", "team up with", "coordinate with",
            "involve", "include", "bring in", "consult with"
        ]

        return any(keyword in message.lower() for keyword in collaboration_keywords)

    def _extract_collaboration_info(self, message: str) -> Tuple[List[str], Optional[str]]:
        """
        Extract the collaboration targets and task from a collaboration command.

        Args:
            message: The user message.

        Returns:
            A tuple containing the list of target personas and the collaboration task.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Check for common persona names
        personas = ["analyst", "marketer", "classifier", "concierge", "analysis", "marketing", "classification"]
        target_personas = []

        for persona in personas:
            if persona in message_lower:
                target_personas.append(persona)

        if not target_personas:
            return [], None

        # Extract task (simple heuristic - everything after "to" or "on")
        task = None
        if "to" in message_lower:
            task = message_lower.split("to", 1)[1].strip()
        elif "on" in message_lower:
            task = message_lower.split("on", 1)[1].strip()
        else:
            # Default task
            task = f"work on {message_lower}"

        return target_personas, task

    async def _register_collaboration(self, conversation_id: str, source_persona: str, target_personas: List[str], task: str) -> None:
        """
        Register a collaboration between personas.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The persona initiating the collaboration.
            target_personas: The personas to collaborate with.
            task: The collaboration task.
        """
        collaboration_info = {
            "conversation_id": conversation_id,
            "source_persona": source_persona,
            "target_personas": target_personas,
            "task": task,
            "timestamp": time.time(),
            "status": "pending"
        }

        # Store the collaboration for each target persona
        for target_persona in target_personas:
            key = f"{conversation_id}:{target_persona}:collaborations"
            if key not in self.coordination_store:
                self.coordination_store[key] = []

            self.coordination_store[key].append(collaboration_info)
            logger.debug(f"Registered collaboration: {key}")

    async def _check_collaboration_participant(self, conversation_id: str, current_persona: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Check if the current persona is a participant in a collaboration.

        Args:
            conversation_id: The ID of the conversation.
            current_persona: The current persona.

        Returns:
            A tuple containing a boolean indicating if this is a collaboration participant, the source persona, and the task.
        """
        key = f"{conversation_id}:{current_persona}:collaborations"
        collaborations = self.coordination_store.get(key, [])

        # Filter for pending collaborations
        pending_collaborations = [c for c in collaborations if c["status"] == "pending"]

        if pending_collaborations:
            # Get the most recent collaboration
            collaboration = sorted(pending_collaborations, key=lambda x: x["timestamp"], reverse=True)[0]

            # Update status to "active"
            collaboration["status"] = "active"

            # Update the store
            for i, c in enumerate(collaborations):
                if c["timestamp"] == collaboration["timestamp"] and c["source_persona"] == collaboration["source_persona"]:
                    collaborations[i] = collaboration
                    break

            self.coordination_store[key] = collaborations

            return True, collaboration["source_persona"], collaboration["task"]

        return False, None, None

    def _detect_coordination_need(self, message: str, context: Dict[str, Any]) -> Tuple[bool, str, List[str], Optional[str]]:
        """
        Detect if coordination with other personas is needed based on the message content.

        Args:
            message: The user message.
            context: The current context.

        Returns:
            A tuple containing a boolean indicating if coordination is needed, the coordination type,
            the list of target personas, and the reason.
        """
        # Simple rule-based detection - could be enhanced with NLP
        message_lower = message.lower()
        current_persona = context.get("persona_id", "unknown")

        # Check for complex tasks that might require collaboration
        complex_task_keywords = ["complex", "complicated", "multi-step", "multiple", "comprehensive", "detailed"]
        is_complex_task = any(keyword in message_lower for keyword in complex_task_keywords)

        # Check for cross-domain tasks
        cross_domain = False
        domains = []

        # Analysis domain
        analysis_keywords = ["analyze", "analysis", "data", "chart", "graph", "visualization"]
        if any(keyword in message_lower for keyword in analysis_keywords):
            domains.append("analysis")
            if current_persona != "analysis" and current_persona != "analyst":
                cross_domain = True

        # Marketing domain
        marketing_keywords = ["marketing", "campaign", "content", "social media", "advertisement"]
        if any(keyword in message_lower for keyword in marketing_keywords):
            domains.append("marketing")
            if current_persona != "marketing" and current_persona != "marketer":
                cross_domain = True

        # Classification domain
        classification_keywords = ["classify", "classification", "categorize", "sort", "group"]
        if any(keyword in message_lower for keyword in classification_keywords):
            domains.append("classification")
            if current_persona != "classification" and current_persona != "classifier":
                cross_domain = True

        # Determine coordination type and targets
        if cross_domain and is_complex_task:
            # Complex cross-domain task - suggest collaboration
            target_personas = []
            if "analysis" in domains and current_persona != "analysis" and current_persona != "analyst":
                target_personas.append("analyst")
            if "marketing" in domains and current_persona != "marketing" and current_persona != "marketer":
                target_personas.append("marketer")
            if "classification" in domains and current_persona != "classification" and current_persona != "classifier":
                target_personas.append("classifier")

            return True, "collaboration", target_personas, "complex cross-domain task"
        elif cross_domain:
            # Simple cross-domain task - suggest handoff
            target_persona = None
            if "analysis" in domains and current_persona != "analysis" and current_persona != "analyst":
                target_persona = "analyst"
            elif "marketing" in domains and current_persona != "marketing" and current_persona != "marketer":
                target_persona = "marketer"
            elif "classification" in domains and current_persona != "classification" and current_persona != "classifier":
                target_persona = "classifier"

            if target_persona:
                return True, "handoff", [target_persona], f"{domains[0]} task"

        return False, "", [], None

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["persona_coordination", "handoff_management", "collaboration_management"])
