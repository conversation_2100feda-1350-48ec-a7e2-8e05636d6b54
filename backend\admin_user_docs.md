# Creating Admin Users for Datagenius

This document explains how to create admin users for the Datagenius application using the provided command-line tools.

## Prerequisites

- Python 3.8 or higher
- Access to the Datagenius backend directory
- Database connection configured in the application

## Using the Command-Line Tool

### Option 1: Using the Python Script Directly

You can run the Python script directly with command-line arguments:

```bash
# Navigate to the backend directory
cd backend

# Run the script with required arguments
python create_admin.py --email <EMAIL> --password secure_password --username admin --first-name Admin --last-name User
```

Required arguments:
- `--email`: The email address for the admin user
- `--password`: The password for the admin user

Optional arguments:
- `--username`: A username for the admin user
- `--first-name`: The admin user's first name
- `--last-name`: The admin user's last name

### Option 2: Using the Interactive Scripts

#### For Windows Users

1. Navigate to the backend directory
2. Double-click on `create_admin.bat` or run it from the command prompt
3. Follow the interactive prompts to enter the admin user details

```
cd backend
create_admin.bat
```

#### For Linux/Mac Users

1. Navigate to the backend directory
2. Make the script executable: `chmod +x create_admin.sh`
3. Run the script: `./create_admin.sh`
4. Follow the interactive prompts to enter the admin user details

```bash
cd backend
chmod +x create_admin.sh
./create_admin.sh
```

## What Happens When You Run the Script

The script will:

1. Check if a user with the provided email already exists
   - If the user exists and is already an admin, it will notify you
   - If the user exists but is not an admin, it will update the user to be an admin
   - If the user doesn't exist, it will create a new admin user

2. The new or updated user will have:
   - `is_active` set to `true`
   - `is_verified` set to `true`
   - `is_superuser` set to `true`

## Accessing the Admin Panel

After creating an admin user, you can:

1. Log in to the Datagenius application using the admin credentials
2. Access the admin panel by navigating to `/admin` in your browser
3. Use the admin features to manage AI personas, users, and view analytics

## Troubleshooting

If you encounter any issues:

1. Make sure you're running the script from the backend directory
2. Check that the database connection is properly configured in `config.py`
3. Ensure that the database is accessible and running
4. Check the application logs for any error messages

If the script fails with import errors, make sure you have all the required dependencies installed:

```bash
pip install sqlalchemy passlib
```
