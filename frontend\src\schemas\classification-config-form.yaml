title: Classification Configuration Form
description: Form for configuring text classification
properties:
  classification_type:
    type: enum
    enum:
      - llm
      - huggingface
    description: Type of classification to use
    default: llm
  
  model_provider:
    type: string
    description: AI provider to use (for LLM classification)
    default: groq
  
  model_name:
    type: string
    description: AI model to use (for LLM classification)
  
  hf_model_name:
    type: string
    description: Hugging Face model to use (for Hugging Face classification)
    default: facebook/bart-large-mnli
  
  temperature:
    type: number
    description: Temperature for generation (for LLM classification)
    minimum: 0
    maximum: 1
    default: 0.1
  
  threshold:
    type: number
    description: Classification confidence threshold
    minimum: 0
    maximum: 1
    default: 0.5
  
  sample_size:
    type: integer
    description: Number of samples to use for classification
    minimum: 1
    default: 100
  
  categories:
    type: array
    description: Categories for classification
    items:
      type: string
    default: []
  
  hierarchy_levels:
    type: array
    description: Hierarchy levels for classification
    items:
      type: string
    default:
      - theme
      - category

required:
  - classification_type
