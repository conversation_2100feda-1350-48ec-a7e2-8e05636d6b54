"""
DOCX conversion API for the Datagenius backend.

This module provides API endpoints for converting markdown content to DOCX format.
"""

import logging
from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel
from typing import Optional
from io import BytesIO
import base64

from agents.marketing_agent.utils import convert_to_docx

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/docx",
    tags=["DOCX Conversion"],
    responses={404: {"description": "Not found"}},
)


class DocxConversionRequest(BaseModel):
    """Request model for DOCX conversion."""
    content: str
    title: Optional[str] = "Generated Content"


class DocxConversionResponse(BaseModel):
    """Response model for DOCX conversion."""
    base64_docx: str
    filename: str


@router.post("/convert-to-docx", response_model=DocxConversionResponse)
async def convert_markdown_to_docx(request: DocxConversionRequest = Body(...)):
    """
    Convert markdown content to DOCX format.

    Args:
        request: The conversion request containing markdown content

    Returns:
        DocxConversionResponse with base64-encoded DOCX content
    """
    try:
        # Convert the markdown content to DOCX
        docx_bytes = convert_to_docx(request.content, title=request.title)

        # Convert to base64 for transmission
        base64_docx = base64.b64encode(docx_bytes).decode('utf-8')

        # Generate a filename
        filename = f"{request.title.lower().replace(' ', '_')}.docx"

        return DocxConversionResponse(
            base64_docx=base64_docx,
            filename=filename
        )
    except Exception as e:
        logger.error(f"Error converting to DOCX: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error converting to DOCX: {str(e)}")
