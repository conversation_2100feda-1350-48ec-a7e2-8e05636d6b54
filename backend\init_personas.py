"""
Run the persona initialization script directly.

This script is a convenience wrapper around the init_personas.py script in the scripts directory.
"""

import logging
from scripts.init_personas import load_personas_from_yaml

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    logger.info("Starting persona initialization...")
    load_personas_from_yaml()
    logger.info("Persona initialization complete.")
