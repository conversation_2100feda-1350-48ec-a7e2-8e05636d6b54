"""
<PERSON><PERSON><PERSON> to initialize the database in Docker.

This script connects to the PostgreSQL database in Docker and creates the database if it doesn't exist.
"""
import os
import sys
import logging
import time
import psycopg2
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get database configuration from environment variables
DB_USER = os.getenv("POSTGRES_USER", "datagenius")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "datagenius_password")
DB_NAME = os.getenv("POSTGRES_DB", "datagenius")

# Always use 'localhost' when running this script from outside Docker
# The script is meant to be run on the host machine, not inside Docker
DB_HOST = "localhost"  # Use localhost for local development

DB_PORT = "5432"

def wait_for_postgres(max_retries=10, retry_interval=5):
    """Wait for PostgreSQL to be ready."""
    retries = 0
    logger.info(f"Attempting to connect to PostgreSQL at {DB_HOST}:{DB_PORT} as user '{DB_USER}'")
    while retries < max_retries:
        try:
            # Connect to the default 'postgres' database
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                user=DB_USER,
                password=DB_PASSWORD,
                dbname="postgres"
            )
            conn.close()
            logger.info("PostgreSQL is ready")
            return True
        except psycopg2.OperationalError as e:
            logger.warning(f"PostgreSQL not ready yet: {e}")
            logger.info(f"Retrying in {retry_interval} seconds... (Attempt {retries+1}/{max_retries})")
            retries += 1
            time.sleep(retry_interval)

    logger.error("Max retries reached. PostgreSQL is not available.")
    return False

def create_database():
    """Create the database if it doesn't exist."""
    try:
        # Connect to the default 'postgres' database
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            dbname="postgres"
        )
        conn.autocommit = True
        cursor = conn.cursor()

        # Check if database exists
        cursor.execute(f"SELECT 1 FROM pg_database WHERE datname = '{DB_NAME}'")
        exists = cursor.fetchone()

        if not exists:
            logger.info(f"Creating database '{DB_NAME}'...")
            cursor.execute(f"CREATE DATABASE {DB_NAME}")
            logger.info(f"Database '{DB_NAME}' created successfully")
        else:
            logger.info(f"Database '{DB_NAME}' already exists")

        cursor.close()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error creating database: {e}")
        return False

def main():
    """Main entry point for the script."""
    logger.info("Initializing Docker database...")
    logger.info(f"Database configuration: Host={DB_HOST}, Port={DB_PORT}, User={DB_USER}, DB={DB_NAME}")

    # Wait for PostgreSQL to be ready
    if not wait_for_postgres():
        logger.error("Failed to connect to PostgreSQL")
        sys.exit(1)

    # Create the database
    if not create_database():
        logger.error("Failed to create database")
        sys.exit(1)

    logger.info(f"Database '{DB_NAME}' initialization completed successfully")

if __name__ == "__main__":
    main()
