# Google OAuth Implementation Strategy for Datagenius

This document outlines a comprehensive strategy for implementing Google OAuth authentication in the Datagenius application. The implementation will allow users to sign in with their Google accounts, enhancing the user experience and providing a secure alternative to password-based authentication.

## Phase 1: Environment Setup and Configuration

### 1.1 Google Cloud Platform Setup
1. Create or use an existing Google Cloud Platform (GCP) project
2. Configure the OAuth consent screen:
   - Set application name, logo, and developer contact information
   - Add scopes for email and profile information
3. Create OAuth 2.0 credentials:
   - Create a Web application OAuth client ID
   - Add authorized JavaScript origins (e.g., `http://localhost:5173`, production URL)
   - Add authorized redirect URIs (e.g., `http://localhost:5173/auth/google/callback`, production callback URL)
4. Note the Client ID and Client Secret

### 1.2 Environment Configuration
Add the following variables to the `.env` file:

```
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-client-id
GOOGLE_CLIENT_SECRET=your-client-secret
GOOGLE_REDIRECT_URI=http://localhost:5173/auth/google/callback
```

## Phase 2: Backend Implementation

### 2.1 Update Database Schema
Ensure the User model has fields for OAuth provider and OAuth ID:

```python
# Already exists in the database.py User model
oauth_provider = Column(String(50), nullable=True)
oauth_id = Column(String(255), nullable=True)
```

### 2.2 Create OAuth Models
Add OAuth-specific models to `app/models/auth.py`:

```python
class OAuthRequest(BaseModel):
    """Model for OAuth authorization request."""
    provider: str
    redirect_uri: str
    state: Optional[str] = None

class GoogleAuthRequest(BaseModel):
    """Model for Google OAuth authorization request."""
    code: str
    state: Optional[str] = None
```

### 2.3 Implement OAuth Routes
Create new routes in `app/api/auth.py`:

```python
@router.get("/google/login")
async def google_login(request: Request):
    """
    Initiate Google OAuth login flow.
    
    Redirects the user to the Google OAuth consent screen.
    """
    # Generate a random state parameter for CSRF protection
    state = secrets.token_urlsafe(32)
    
    # Store state in session or Redis for validation
    # This is important for security to prevent CSRF attacks
    
    # Build the authorization URL
    params = {
        "client_id": config.GOOGLE_CLIENT_ID,
        "redirect_uri": config.GOOGLE_REDIRECT_URI,
        "response_type": "code",
        "scope": "email profile",
        "state": state,
        "access_type": "offline",  # For refresh token
        "prompt": "consent"  # Force consent screen to always appear
    }
    
    auth_url = f"https://accounts.google.com/o/oauth2/v2/auth?{urlencode(params)}"
    
    # Redirect to Google's OAuth page
    return RedirectResponse(url=auth_url)

@router.post("/google/callback")
async def google_callback(
    request: GoogleAuthRequest,
    db: Session = Depends(get_db)
):
    """
    Handle Google OAuth callback.
    
    Exchanges the authorization code for tokens and creates or updates the user.
    """
    # Validate state parameter (CSRF protection)
    # This should match the state sent in the login request
    
    try:
        # Exchange authorization code for tokens
        token_url = "https://oauth2.googleapis.com/token"
        data = {
            "client_id": config.GOOGLE_CLIENT_ID,
            "client_secret": config.GOOGLE_CLIENT_SECRET,
            "code": request.code,
            "redirect_uri": config.GOOGLE_REDIRECT_URI,
            "grant_type": "authorization_code"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(token_url, data=data)
            token_data = response.json()
            
            if response.status_code != 200:
                logger.error(f"Google OAuth token exchange failed: {token_data}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Failed to authenticate with Google"
                )
            
            # Get user info from Google
            access_token = token_data["access_token"]
            userinfo_url = "https://www.googleapis.com/oauth2/v2/userinfo"
            headers = {"Authorization": f"Bearer {access_token}"}
            
            userinfo_response = await client.get(userinfo_url, headers=headers)
            user_info = userinfo_response.json()
            
            if userinfo_response.status_code != 200:
                logger.error(f"Google OAuth userinfo failed: {user_info}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Failed to get user info from Google"
                )
            
            # Check if user exists
            google_id = user_info["id"]
            email = user_info["email"]
            
            # Try to find user by OAuth ID first
            user = get_user_by_oauth(db, "google", google_id)
            
            if not user:
                # Try to find user by email
                user = get_user_by_email(db, email)
                
                if user:
                    # Update existing user with OAuth info
                    user.oauth_provider = "google"
                    user.oauth_id = google_id
                    db.commit()
                else:
                    # Create new user
                    from ..database import User as DBUser
                    user = DBUser(
                        email=email,
                        username=email.split("@")[0],  # Use part of email as username
                        first_name=user_info.get("given_name"),
                        last_name=user_info.get("family_name"),
                        oauth_provider="google",
                        oauth_id=google_id,
                        is_active=True,
                        is_verified=True  # Auto-verify OAuth users
                    )
                    db.add(user)
                    db.commit()
                    db.refresh(user)
            
            # Create access token
            access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = create_access_token(
                data={"sub": str(user.id)},
                expires_delta=access_token_expires
            )
            
            # Create refresh token
            refresh_token = create_refresh_token(user.id)
            
            # Return tokens
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": config.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            }
    except Exception as e:
        logger.error(f"Google OAuth callback error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Authentication error: {str(e)}"
        )
```

### 2.4 Update Dependencies
Add required packages to `requirements.txt`:

```
httpx==0.24.1  # Async HTTP client for OAuth requests
```

## Phase 3: Frontend Implementation

### 3.1 Create OAuth Button Component
Create a new component for the Google login button:

```tsx
// src/components/auth/GoogleLoginButton.tsx
import { Button } from "@/components/ui/button";
import { FcGoogle } from "react-icons/fc";

interface GoogleLoginButtonProps {
  onClick: () => void;
  isLoading?: boolean;
}

export const GoogleLoginButton = ({ onClick, isLoading = false }: GoogleLoginButtonProps) => {
  return (
    <Button
      variant="outline"
      type="button"
      className="w-full flex items-center justify-center gap-2"
      onClick={onClick}
      disabled={isLoading}
    >
      <FcGoogle className="h-5 w-5" />
      {isLoading ? "Connecting..." : "Continue with Google"}
    </Button>
  );
};
```

### 3.2 Update Auth API Service
Add Google OAuth methods to `src/lib/authApi.ts`:

```typescript
// Add to existing authApi object
googleLogin: async (): Promise<void> => {
  // Redirect to backend endpoint that will redirect to Google
  window.location.href = `${API_BASE_URL}/auth/google/login`;
},

googleCallback: async (code: string, state?: string): Promise<TokenResponse> => {
  const response = await fetch(`${API_BASE_URL}/auth/google/callback`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ code, state }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.detail || 'Google authentication failed');
  }

  return response.json();
},
```

### 3.3 Update Auth Context
Add Google OAuth methods to `src/contexts/AuthContext.tsx`:

```typescript
// Add to AuthContextType interface
googleLogin: () => void;
handleGoogleCallback: (code: string, state?: string) => Promise<void>;

// Add to context default values
googleLogin: () => {},
handleGoogleCallback: async () => {},

// Implement in AuthProvider
const googleLogin = () => {
  authApi.googleLogin();
};

const handleGoogleCallback = async (code: string, state?: string): Promise<void> => {
  setIsLoading(true);

  try {
    const data = await authApi.googleCallback(code, state);

    // Store tokens in localStorage
    localStorage.setItem('token', data.access_token);
    if (data.refresh_token) {
      localStorage.setItem('refresh_token', data.refresh_token);
    }

    // Fetch user data
    const userData = await fetchUserData();
    setUser(userData);

    // Set up the token refresh timer
    setupRefreshTimer();

    // Show success toast
    toast({
      title: 'Login Successful',
      description: `Welcome${userData.first_name ? ', ' + userData.first_name : ''}!`,
    });

    // Navigate to dashboard
    navigate('/dashboard');
  } catch (error) {
    toast({
      title: 'Google Login Failed',
      description: error instanceof Error ? error.message : 'An error occurred during Google login',
      variant: 'destructive',
    });
    throw error;
  } finally {
    setIsLoading(false);
  }
};

// Add to contextValue
const contextValue: AuthContextType = {
  // ...existing values
  googleLogin,
  handleGoogleCallback,
};
```

### 3.4 Create Callback Page
Create a callback page to handle the OAuth redirect:

```tsx
// src/pages/GoogleCallback.tsx
import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2 } from "lucide-react";

const GoogleCallback = () => {
  const { handleGoogleCallback } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const processCallback = async () => {
      // Parse the URL for code and state parameters
      const searchParams = new URLSearchParams(location.search);
      const code = searchParams.get("code");
      const state = searchParams.get("state");
      const error = searchParams.get("error");

      if (error) {
        setError(`Authentication error: ${error}`);
        setTimeout(() => navigate("/login"), 3000);
        return;
      }

      if (!code) {
        setError("No authorization code received");
        setTimeout(() => navigate("/login"), 3000);
        return;
      }

      try {
        await handleGoogleCallback(code, state || undefined);
        // Successful login will redirect in the handler
      } catch (err) {
        setError(err instanceof Error ? err.message : "Authentication failed");
        setTimeout(() => navigate("/login"), 3000);
      }
    };

    processCallback();
  }, [handleGoogleCallback, location.search, navigate]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      {error ? (
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Authentication Error</h1>
          <p className="mb-4">{error}</p>
          <p>Redirecting to login page...</p>
        </div>
      ) : (
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4">Completing Authentication</h1>
          <p>Please wait while we complete the authentication process...</p>
        </div>
      )}
    </div>
  );
};

export default GoogleCallback;
```

### 3.5 Update Routes
Add the callback route to `src/App.tsx` or your router configuration:

```tsx
<Route path="/auth/google/callback" element={<GoogleCallback />} />
```

### 3.6 Update Login Page
Add the Google login button to the login page:

```tsx
// In src/pages/Login.tsx
import { GoogleLoginButton } from "@/components/auth/GoogleLoginButton";

// Inside the Login component
const { login, isLoading, googleLogin } = useAuth();

// Add this to the form, typically before or after the regular login button
<div className="relative my-4">
  <div className="absolute inset-0 flex items-center">
    <span className="w-full border-t" />
  </div>
  <div className="relative flex justify-center text-xs uppercase">
    <span className="bg-background px-2 text-muted-foreground">
      Or continue with
    </span>
  </div>
</div>

<GoogleLoginButton onClick={googleLogin} isLoading={isLoading} />
```

## Phase 4: Security Enhancements

### 4.1 CSRF Protection
Implement state parameter validation to prevent CSRF attacks:

1. Store the state parameter in Redis when initiating the OAuth flow
2. Validate the state parameter in the callback endpoint

```python
# In the google_login endpoint
state = secrets.token_urlsafe(32)
# Store state in Redis with expiration
redis_client.set(f"oauth_state:{state}", "1", ex=600)  # 10 minutes expiration

# In the google_callback endpoint
state = request.state
if not state or not redis_client.get(f"oauth_state:{state}"):
    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail="Invalid state parameter"
    )
# Delete the state from Redis after use
redis_client.delete(f"oauth_state:{state}")
```

### 4.2 Account Linking
Allow users to link their Google account to an existing account:

1. Add endpoints for linking and unlinking OAuth providers
2. Add UI components for managing linked accounts in the user profile

### 4.3 Error Handling
Implement comprehensive error handling for OAuth flows:

1. Handle common OAuth errors (invalid_grant, access_denied, etc.)
2. Provide clear error messages to users
3. Log detailed error information for debugging

## Phase 5: Testing and Deployment

### 5.1 Testing
1. Test the OAuth flow in development environment
2. Test account creation with Google OAuth
3. Test login with existing Google-linked accounts
4. Test error scenarios (invalid code, expired code, etc.)

### 5.2 Deployment Considerations
1. Update OAuth client configuration with production URLs
2. Set up proper environment variables in production
3. Monitor OAuth-related errors in production logs

## Implementation Timeline

1. **Phase 1 (Environment Setup)**: 1 day
2. **Phase 2 (Backend Implementation)**: 2-3 days
3. **Phase 3 (Frontend Implementation)**: 2-3 days
4. **Phase 4 (Security Enhancements)**: 1-2 days
5. **Phase 5 (Testing and Deployment)**: 1-2 days

Total estimated time: 7-11 days
