"""
Migration script for persona versioning.

This migration adds the persona_versions table to support versioning of personas.
"""

import logging
from alembic import op
import sqlalchemy as sa
from sqlalchemy.inspection import inspect

# Configure logging
logger = logging.getLogger(__name__)

# Revision identifiers
revision = 'persona_versioning_001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema."""
    logger.info("Running persona versioning migration")
    
    # Get database inspector
    inspector = inspect(op.get_bind())
    tables = inspector.get_table_names()
    
    # Only create persona_versions table if it doesn't exist
    if 'persona_versions' not in tables:
        logger.info("Creating persona_versions table")
        op.create_table(
            'persona_versions',
            sa.Column('id', sa.String(100), primary_key=True, index=True),
            sa.Column('persona_id', sa.String(50), sa.<PERSON>ey('personas.id'), index=True),
            sa.Column('version', sa.String(20), nullable=False),
            sa.Column('config', sa.<PERSON>, nullable=False),
            sa.Column('is_active', sa.<PERSON>(), default=False),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
            sa.Column('created_by', sa.Integer, sa.ForeignKey('users.id')),
        )
        logger.info("Created persona_versions table")
    else:
        logger.info("persona_versions table already exists")


def downgrade() -> None:
    """Downgrade database schema."""
    logger.info("Downgrading persona versioning migration")
    
    # Get database inspector
    inspector = inspect(op.get_bind())
    tables = inspector.get_table_names()
    
    # Only drop persona_versions table if it exists
    if 'persona_versions' in tables:
        logger.info("Dropping persona_versions table")
        op.drop_table('persona_versions')
        logger.info("Dropped persona_versions table")
    else:
        logger.info("persona_versions table doesn't exist")
