"""
OpenAI model provider for the Datagenius backend.

This module provides a model provider implementation for OpenAI.
"""

import logging
import os
import requests
from typing import Dict, Any, List, Union, Optional
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.language_models.chat_models import BaseChatModel


from .base import BaseModelProvider
from .exceptions import ModelInitializationError, ModelNotFoundError
from .config import get_provider_config

# Configure logging
logger = logging.getLogger(__name__)


class OpenAIProvider(BaseModelProvider):
    """Model provider implementation for OpenAI."""

    @property
    def provider_id(self) -> str:
        """Get the provider ID."""
        return "openai"

    @property
    def provider_name(self) -> str:
        """Get the provider name."""
        return "OpenAI"

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the provider with configuration.

        Args:
            config: Configuration dictionary for the provider
        """
        await super().initialize(config)

        # Get provider configuration
        provider_config = get_provider_config("openai")

        # Set default model if not specified
        if not self._default_model_id:
            self._default_model_id = provider_config.get("default_model", "gpt-3.5-turbo")

        # Set default endpoint if not specified
        if not self._endpoint:
            self._endpoint = provider_config.get("endpoint", "https://api.openai.com/v1")

        # Set API key from configuration if not specified
        if not self._api_key:
            self._api_key = provider_config.get("api_key", "")

        # Check if we have an API key
        if not self._api_key:
            logger.warning("No OpenAI API key provided")

    async def _initialize_model(self, model_id: str, config: Dict[str, Any]) -> Union[BaseLanguageModel, BaseChatModel]:
        """
        Initialize a model instance.

        Args:
            model_id: ID of the model to initialize
            config: Configuration for the model

        Returns:
            Initialized model instance

        Raises:
            ModelInitializationError: If there's an error initializing the model
            ModelNotFoundError: If the model is not found
        """
        try:
            # Check if the model exists
            models = await self.list_models()
            model_exists = any(model["id"] == model_id for model in models)

            if not model_exists:
                # If the model doesn't exist but is a known model, we'll try anyway
                known_models = ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o"]
                if model_id not in known_models:
                    raise ModelNotFoundError(f"Model '{model_id}' not found in OpenAI")

            # Import here to avoid hard dependencies
            try:
                from langchain_openai import ChatOpenAI
            except ImportError:
                logger.warning("langchain-openai not installed, attempting to install...")
                import subprocess
                subprocess.check_call(["pip", "install", "langchain-openai"])
                from langchain_openai import ChatOpenAI

            # Initialize the model
            model = ChatOpenAI(
                temperature=config.get("temperature", 0.7),
                model_name=model_id,
                api_key=self._api_key
            )

            logger.info(f"Initialized OpenAI model '{model_id}'")
            return model

        except ImportError as e:
            raise ModelInitializationError(f"Error importing OpenAI: {str(e)}")
        except Exception as e:
            raise ModelInitializationError(f"Error initializing OpenAI model '{model_id}': {str(e)}")

    async def _fetch_models(self) -> List[Dict[str, Any]]:
        """
        Fetch available models from OpenAI.

        Returns:
            List of model metadata dictionaries
        """
        if not self._api_key:
            # Return a static list of known models if no API key
            return [
                {
                    "id": "gpt-4o",
                    "name": "GPT-4o",
                    "description": "OpenAI's most advanced model",
                    "context_length": 128000,
                    "provider": "openai"
                },
                {
                    "id": "gpt-4-turbo",
                    "name": "GPT-4 Turbo",
                    "description": "OpenAI's most capable model",
                    "context_length": 128000,
                    "provider": "openai"
                },
                {
                    "id": "gpt-4",
                    "name": "GPT-4",
                    "description": "OpenAI's powerful model",
                    "context_length": 8192,
                    "provider": "openai"
                },
                {
                    "id": "gpt-3.5-turbo",
                    "name": "GPT-3.5 Turbo",
                    "description": "OpenAI's efficient model",
                    "context_length": 16385,
                    "provider": "openai"
                }
            ]

        try:
            # Make request to OpenAI API
            headers = {
                "Authorization": f"Bearer {self._api_key}"
            }

            response = requests.get(f"{self._endpoint}/models", headers=headers, timeout=10)
            response.raise_for_status()

            # Parse response
            data = response.json()
            models = data.get("data", [])

            # Format models
            formatted_models = []
            for model in models:
                model_id = model.get("id", "")

                # Skip non-chat models
                if not (model_id.startswith("gpt-") and ("turbo" in model_id or model_id.startswith("gpt-4"))):
                    continue

                # Get display name
                display_name = model.get("name", model_id)
                if display_name == model_id:
                    # Try to make a nicer display name
                    parts = model_id.split("-")
                    if len(parts) > 1:
                        display_name = " ".join(part.upper() if part.upper() == "GPT" else part.capitalize() for part in parts)

                # Get context window
                context_length = 0
                if "gpt-4o" in model_id:
                    context_length = 128000
                elif "gpt-4-turbo" in model_id:
                    context_length = 128000
                elif "gpt-4" in model_id:
                    context_length = 8192
                elif "gpt-3.5-turbo" in model_id:
                    context_length = 16385

                formatted_models.append({
                    "id": model_id,
                    "name": display_name,
                    "description": model.get("description", ""),
                    "created": model.get("created", 0),
                    "context_length": context_length,
                    "provider": "openai"
                })

            # Sort models by creation date (newest first)
            formatted_models.sort(key=lambda x: x.get("created", 0), reverse=True)
            return formatted_models

        except Exception as e:
            logger.error(f"Error fetching OpenAI models: {str(e)}", exc_info=True)
            # Return a static list of known models as a fallback
            return [
                {
                    "id": "gpt-4o",
                    "name": "GPT-4o",
                    "description": "OpenAI's most advanced model",
                    "context_length": 128000,
                    "provider": "openai"
                },
                {
                    "id": "gpt-3.5-turbo",
                    "name": "GPT-3.5 Turbo",
                    "description": "OpenAI's efficient model",
                    "context_length": 16385,
                    "provider": "openai"
                }
            ]
