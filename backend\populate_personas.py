#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to populate the database with default AI personas.
This script reads the default personas from the frontend configuration
and adds them to the database.
"""

import sys
import os
import json
import logging
from datetime import datetime, timezone
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Set up the path to import from the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import database models and config
try:
    from app.database import Persona, Base
    from app import config
except ImportError:
    logger.error("Failed to import required modules. Make sure you're running this script from the backend directory.")
    sys.exit(1)

def get_utc_now():
    """Get current UTC datetime."""
    return datetime.now(timezone.utc)

# Default personas data (copied from frontend/src/data/aiPersonas.ts)
DEFAULT_PERSONAS = [
    {
        "id": "marketing-ai",
        "name": "Marketing AI",
        "description": "Strategic marketing assistant for campaign planning and content creation.",
        "industry": "Marketing",
        "skills": ["Marketing Strategy", "Campaign Planning", "Content Creation"],
        "rating": 4.6,
        "review_count": 92,
        "image_url": "/placeholder.svg",
        "price": 10.0,
        "provider": "groq",
        "model": "llama3-8b-8192",
        "is_active": True,
        "age_restriction": 0,
        "content_filters": None
    },
    {
        "id": "analysis-ai",
        "name": "Analysis AI",
        "description": "Data analysis specialist for exploring and visualizing datasets.",
        "industry": "Data Science",
        "skills": ["Data Cleaning", "Data Visualization", "Statistical Analysis"],
        "rating": 4.7,
        "review_count": 105,
        "image_url": "/placeholder.svg",
        "price": 10.0,
        "provider": "groq",
        "model": "llama3-8b-8192",
        "is_active": True,
        "age_restriction": 0,
        "content_filters": None
    },
    {
        "id": "classifier-ai",
        "name": "Classifier AI",
        "description": "Text classification specialist for organizing and categorizing content.",
        "industry": "Technology",
        "skills": ["Text Classification", "Document Organization", "Content Analysis"],
        "rating": 4.7,
        "review_count": 95,
        "image_url": "/placeholder.svg",
        "price": 10.0,
        "provider": "groq",
        "model": "llama3-8b-8192",
        "is_active": True,
        "age_restriction": 0,
        "content_filters": None
    }
]

def populate_personas():
    """Populate the database with default personas."""
    # Create database engine and session
    engine = create_engine(
        config.DATABASE_URL,
        echo=False,
        connect_args={"check_same_thread": False} if config.DATABASE_URL.startswith("sqlite") else {},
    )
    
    # Create tables if they don't exist
    Base.metadata.create_all(bind=engine)
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Check and add each persona
        for persona_data in DEFAULT_PERSONAS:
            # Check if persona already exists
            existing_persona = db.query(Persona).filter(Persona.id == persona_data["id"]).first()
            
            if existing_persona:
                logger.info(f"Persona {persona_data['id']} already exists. Updating...")
                
                # Update existing persona
                for key, value in persona_data.items():
                    if key == "skills" and isinstance(value, list):
                        setattr(existing_persona, key, value)
                    elif key != "id":  # Don't update the ID
                        setattr(existing_persona, key, value)
                
                existing_persona.updated_at = get_utc_now()
            else:
                logger.info(f"Creating new persona: {persona_data['id']}")
                
                # Create new persona
                new_persona = Persona(
                    id=persona_data["id"],
                    name=persona_data["name"],
                    description=persona_data["description"],
                    industry=persona_data["industry"],
                    skills=persona_data["skills"],
                    rating=persona_data["rating"],
                    review_count=persona_data["review_count"],
                    image_url=persona_data["image_url"],
                    price=persona_data["price"],
                    provider=persona_data["provider"],
                    model=persona_data["model"],
                    is_active=persona_data["is_active"],
                    age_restriction=persona_data["age_restriction"],
                    content_filters=persona_data["content_filters"],
                    created_at=get_utc_now(),
                    updated_at=get_utc_now()
                )
                
                db.add(new_persona)
        
        # Commit changes
        db.commit()
        logger.info("Successfully populated personas.")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error populating personas: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    try:
        populate_personas()
    except Exception as e:
        logger.error(f"Failed to populate personas: {str(e)}")
        sys.exit(1)
