"""
Vector database service for Datagenius using mem0ai.

This module provides a unified vector database service using mem0ai and Qdrant,
replacing the previous FAISS implementation. It handles document embedding,
storage, and retrieval for semantic search and other vector operations.
"""

import os
import logging
import uuid
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path

from mem0 import Memory
from langchain_core.documents import Document

from app.config import (
    MEM0_API_KEY,
    MEM0_ENDPOINT,
    MEM0_SELF_HOSTED,
    MEM0_DEFAULT_TTL,
    MEM0_MAX_MEMORIES,
    MEM0_MEMORY_THRESHOLD
)
from .memory_service import MemoryService
from .qdrant_manager import QdrantManager

logger = logging.getLogger(__name__)

class VectorService:
    """
    Unified vector database service using mem0ai.

    This service provides a unified interface for vector database operations
    across all AI personas in the Datagenius application. It implements the
    Singleton pattern to ensure a single instance is shared across the application.
    """

    _instance = None

    def __new__(cls):
        """Implement singleton pattern for vector service."""
        if cls._instance is None:
            cls._instance = super(VectorService, cls).__new__(cls)
            cls._instance._initialize()
            logger.info("Initialized mem0ai Vector service")
        return cls._instance

    def _initialize(self):
        """Initialize the vector service with configuration."""
        try:
            # Use the existing memory service to leverage its configuration
            self.memory_service = MemoryService()
            self.memory = self.memory_service.memory
            
            # Set configuration
            self.vector_db_dir = os.path.join(os.getcwd(), "vector_db")
            os.makedirs(self.vector_db_dir, exist_ok=True)
            
            # Track initialization status
            self.initialized = self.memory_service.initialized
            logger.info(f"Vector service initialized successfully (self-hosted: {MEM0_SELF_HOSTED})")
        except Exception as e:
            self.initialized = False
            logger.error(f"Failed to initialize vector service: {e}")

    def embed_document(self, file_path: str, chunk_size: int = 1000, 
                      chunk_overlap: int = 200) -> Tuple[str, Dict[str, Any]]:
        """
        Embed a document using mem0ai.

        Args:
            file_path: Path to the document file
            chunk_size: Size of text chunks for splitting
            chunk_overlap: Overlap between chunks

        Returns:
            Tuple of (vector_store_id, file_info)
        """
        if not self.initialized:
            logger.warning("Vector service not initialized, cannot embed document")
            raise RuntimeError("Vector service not initialized")

        try:
            # Generate a unique ID for the vector store
            vector_store_id = str(uuid.uuid4())
            
            # Get file extension
            file_extension = os.path.splitext(file_path)[1].lower()
            
            # Load document content
            document_content = self._load_document(file_path)
            
            # Create metadata for the document
            metadata = {
                "source": file_path,
                "file_type": file_extension.lstrip("."),
                "chunk_size": chunk_size,
                "chunk_overlap": chunk_overlap,
                "vector_store_id": vector_store_id
            }
            
            # Add document to mem0ai as a memory with special metadata
            result = self.memory.add(
                document_content,
                user_id="system",  # Use system user for documents
                metadata={
                    "document_id": vector_store_id,
                    "file_path": file_path,
                    "file_type": file_extension.lstrip("."),
                    "is_document": True,
                    **metadata
                }
            )
            
            # Create file info
            file_info = {
                "file_path": file_path,
                "file_type": file_extension.lstrip("."),
                "vector_store_id": vector_store_id,
                "memory_id": result.get("id") if result else None,
                **metadata
            }
            
            # Save file info to disk for future reference
            self._save_file_info(vector_store_id, file_info)
            
            logger.info(f"Embedded document {file_path} with vector store ID: {vector_store_id}")
            return vector_store_id, file_info
        except Exception as e:
            logger.error(f"Error embedding document: {e}")
            raise

    def search_document(self, vector_store_id: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Search a document using mem0ai.

        Args:
            vector_store_id: ID of the vector store
            query: Search query
            limit: Maximum number of results to return

        Returns:
            List of search results
        """
        if not self.initialized:
            logger.warning("Vector service not initialized, cannot search document")
            return []

        try:
            # Search memories using mem0ai with document_id filter
            results = self.memory.search(
                query,
                user_id="system",  # Use system user for documents
                limit=limit,
                metadata_filter={"document_id": vector_store_id, "is_document": True}
            )
            
            # Format results
            formatted_results = []
            for result in results.get("results", []):
                formatted_results.append({
                    "content": result.get("content", ""),
                    "metadata": result.get("metadata", {})
                })
            
            logger.debug(f"Found {len(formatted_results)} results for query: {query[:50]}...")
            return formatted_results
        except Exception as e:
            logger.error(f"Error searching document: {e}")
            return []

    def _load_document(self, file_path: str) -> str:
        """
        Load document content from file.

        Args:
            file_path: Path to the document file

        Returns:
            Document content as string
        """
        file_extension = os.path.splitext(file_path)[1].lower()
        
        try:
            # Use appropriate loader based on file extension
            if file_extension == '.pdf':
                from langchain_community.document_loaders import PyPDFLoader
                loader = PyPDFLoader(file_path)
                documents = loader.load()
                return "\n\n".join([doc.page_content for doc in documents])
            elif file_extension in ['.docx', '.doc']:
                from langchain_community.document_loaders import Docx2txtLoader
                loader = Docx2txtLoader(file_path)
                documents = loader.load()
                return "\n\n".join([doc.page_content for doc in documents])
            elif file_extension == '.txt':
                from langchain_community.document_loaders import TextLoader
                loader = TextLoader(file_path)
                documents = loader.load()
                return "\n\n".join([doc.page_content for doc in documents])
            else:
                # Try to infer file type and use appropriate loader
                try:
                    from langchain_community.document_loaders import TextLoader
                    loader = TextLoader(file_path)
                    documents = loader.load()
                    return "\n\n".join([doc.page_content for doc in documents])
                except Exception as e:
                    logger.error(f"Error loading document with TextLoader: {e}")
                    raise ValueError(f"Unsupported file type: {file_extension}")
        except Exception as e:
            logger.error(f"Error loading document: {e}")
            raise

    def _save_file_info(self, vector_store_id: str, file_info: Dict[str, Any]) -> bool:
        """
        Save file info to disk.

        Args:
            vector_store_id: ID of the vector store
            file_info: File information dictionary

        Returns:
            True if successful, False otherwise
        """
        try:
            # Save as YAML
            import yaml
            info_path = os.path.join(self.vector_db_dir, f"{vector_store_id}_info.yaml")
            with open(info_path, 'w') as f:
                yaml.dump(file_info, f)
            
            logger.debug(f"Saved file info to {info_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving file info: {e}")
            return False
