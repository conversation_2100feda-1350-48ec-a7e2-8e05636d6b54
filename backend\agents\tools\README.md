# Shared Agent Tools

This directory contains shared tools that can be used by any agent in the Datagenius backend. These tools provide common functionality that might be needed by multiple agents.

## MCP Tools

All tools in the Datagenius backend use the Model Context Protocol (MCP) standard, which provides a standardized way to define, register, and use tools across the application.

### Available MCP Tools

- **Data Analysis Tools**:
  - `DataAnalysisTool`: Analyze data using pandas operations
  - `DataCleaningTool`: Clean data by handling missing values and other issues
  - `DataVisualizationTool`: Generate visualizations of data
  - `DataQueryingTool`: Query data using natural language
  - `AdvancedQueryTool`: Execute advanced SQL-like queries on data
  - `DataFilteringTool`: Filter data based on specified filters

- **Text Processing Tools**:
  - `TextProcessingTool`: Process text using various operations
  - `SentimentAnalysisTool`: Perform sentiment analysis on text data
  - `TextClassificationTool`: Classify text using various models
  - `ContentGenerationTool`: Generate marketing content based on specified parameters
  - `DocumentEmbeddingTool`: Process documents, create embeddings, and store them in a vector database

- **PandasAI Tools**:
  - `PandasAIAnalysisTool`: Analyze data using PandasAI v3
  - `PandasAIVisualizationTool`: Visualize data using PandasAI v3
  - `PandasAIQueryTool`: Query data using natural language with PandasAI v3
  - `StatisticalAnalysisTool`: Perform statistical analysis using PandasAI

### Usage Example

```python
from agents.components.mcp_server import MCPServerComponent

# Create an MCP server component
mcp_server = MCPServerComponent()

# Call an MCP tool
result = await mcp_server.call_tool("clean_data", {
    "file_path": "data/example.csv",
    "drop_columns": ["column_to_drop"],
    "drop_rows": True,
    "fill_method": "mean"
})

# Call a PandasAI tool
visualization_result = await mcp_server.call_tool("pandasai_visualization", {
    "file_path": "data/example.csv",
    "prompt": "Create a histogram of the Sales column",
    "api_key": api_key,
    "provider": "openai"
})
```

## Adding New MCP Tools

To add new MCP tools:

1. Create a new Python file in the `mcp` subdirectory
2. Implement a new tool class that extends `BaseMCPTool`
3. Add the tool to the registry in `mcp/register.py`
4. Import the tool in `__init__.py` for easy access
5. Document the tool in this README file

### MCP Tool Structure

Each MCP tool should follow this structure:

```python
from .base import BaseMCPTool

class MyNewTool(BaseMCPTool):
    """Tool for doing something useful."""

    def __init__(self):
        """Initialize the tool."""
        super().__init__(
            name="my_new_tool",
            description="Does something useful",
            input_schema={
                "type": "object",
                "properties": {
                    "param1": {"type": "string"},
                    "param2": {"type": "integer"}
                },
                "required": ["param1"]
            }
        )

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the tool with configuration."""
        pass

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the tool with the provided arguments."""
        # Implement tool logic here
        return {
            "content": [
                {
                    "type": "text",
                    "text": "Result of the tool execution"
                }
            ],
            "metadata": {
                "some_key": "some_value"
            }
        }
```
