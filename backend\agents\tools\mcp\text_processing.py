"""
Text processing MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for processing text.
"""

import logging
import re
from typing import Dict, Any, List, Optional

from .base import BaseMCPTool

logger = logging.getLogger(__name__)


class TextProcessingTool(BaseMCPTool):
    """Tool for processing text."""

    def __init__(self):
        """Initialize the text processing tool."""
        super().__init__(
            name="process_text",
            description="Process text using various operations",
            input_schema={
                "type": "object",
                "properties": {
                    "text": {"type": "string"},
                    "operation": {
                        "type": "string", 
                        "enum": [
                            "count_words", 
                            "count_chars", 
                            "extract_emails", 
                            "extract_urls",
                            "extract_entities",
                            "summarize",
                            "sentiment"
                        ]
                    },
                    "params": {"type": "object"}
                },
                "required": ["text", "operation"]
            },
            annotations={
                "title": "Process Text",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        logger.info("Initialized text processing tool")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the text processing tool.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            text = arguments["text"]
            operation = arguments["operation"]
            params = arguments.get("params", {})

            if not text:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "Text is empty"
                        }
                    ]
                }

            # Perform the operation
            if operation == "count_words":
                words = text.split()
                result = f"Word count: {len(words)}"
                
            elif operation == "count_chars":
                result = f"Character count: {len(text)}"
                
            elif operation == "extract_emails":
                email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
                emails = re.findall(email_pattern, text)
                if emails:
                    result = "Extracted emails:\n" + "\n".join(emails)
                else:
                    result = "No emails found in the text."
                    
            elif operation == "extract_urls":
                url_pattern = r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+'
                urls = re.findall(url_pattern, text)
                if urls:
                    result = "Extracted URLs:\n" + "\n".join(urls)
                else:
                    result = "No URLs found in the text."
                    
            elif operation == "extract_entities":
                # Simple entity extraction using regex patterns
                # In a real implementation, you would use a proper NLP library
                patterns = {
                    "dates": r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{1,2} (?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{2,4}',
                    "phone_numbers": r'\+?[\d\s()-]{10,}',
                    "money": r'\$\d+(?:\.\d{2})?|\d+(?:\.\d{2})? (?:dollars|USD)',
                }
                
                entities = {}
                for entity_type, pattern in patterns.items():
                    matches = re.findall(pattern, text)
                    if matches:
                        entities[entity_type] = matches
                
                if entities:
                    result = "Extracted entities:\n"
                    for entity_type, matches in entities.items():
                        result += f"\n{entity_type.capitalize()}:\n"
                        for match in matches:
                            result += f"- {match}\n"
                else:
                    result = "No entities found in the text."
                    
            elif operation == "summarize":
                # Simple summarization by extracting the first few sentences
                # In a real implementation, you would use a proper NLP library
                sentences = re.split(r'(?<=[.!?])\s+', text)
                num_sentences = params.get("num_sentences", 3)
                summary = " ".join(sentences[:min(num_sentences, len(sentences))])
                result = f"Summary:\n{summary}"
                
            elif operation == "sentiment":
                # Simple sentiment analysis using keyword counting
                # In a real implementation, you would use a proper NLP library
                positive_words = ["good", "great", "excellent", "positive", "happy", "love", "like", "best"]
                negative_words = ["bad", "terrible", "negative", "sad", "hate", "dislike", "worst"]
                
                words = re.findall(r'\b\w+\b', text.lower())
                positive_count = sum(1 for word in words if word in positive_words)
                negative_count = sum(1 for word in words if word in negative_words)
                
                if positive_count > negative_count:
                    sentiment = "positive"
                elif negative_count > positive_count:
                    sentiment = "negative"
                else:
                    sentiment = "neutral"
                    
                result = f"Sentiment analysis:\n"
                result += f"- Sentiment: {sentiment}\n"
                result += f"- Positive words: {positive_count}\n"
                result += f"- Negative words: {negative_count}"
                
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported operation: {operation}"
                        }
                    ]
                }

            return {
                "content": [
                    {
                        "type": "text",
                        "text": result
                    }
                ]
            }
        except Exception as e:
            logger.error(f"Error processing text: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error processing text: {str(e)}"
                    }
                ]
            }
