import React from 'react';
import { motion } from 'framer-motion';
import {
  MessageSquare,
  UserCircle,
  FileUp,
  Compass,
  ArrowRight,
  CheckCircle2,
  HelpCircle
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Define the possible stages in the concierge workflow
export type WorkflowStage =
  | 'initial'
  | 'greeting'
  | 'needs_assessment'
  | 'persona_recommendation'
  | 'data_guidance'
  | 'data_attached'
  | 'persona_selection'
  | 'handoff'
  | 'followup'
  | 'completed';

interface WorkflowStageIndicatorProps {
  currentStage: WorkflowStage;
  className?: string;
}

// Define the stages and their properties
const stages: Array<{
  id: WorkflowStage;
  label: string;
  description: string;
  icon: React.ReactNode;
}> = [
  {
    id: 'greeting',
    label: 'Welcome',
    description: 'Initial greeting and introduction',
    icon: <MessageSquare className="h-5 w-5" />
  },
  {
    id: 'needs_assessment',
    label: 'Needs',
    description: 'Understanding your requirements',
    icon: <HelpCircle className="h-5 w-5" />
  },
  {
    id: 'persona_recommendation',
    label: 'Recommend',
    description: 'Suggesting appropriate AI personas',
    icon: <Compass className="h-5 w-5" />
  },
  {
    id: 'data_guidance',
    label: 'Data',
    description: 'Guidance on attaching data files',
    icon: <FileUp className="h-5 w-5" />
  },
  {
    id: 'persona_selection',
    label: 'Select',
    description: 'Selecting an AI persona',
    icon: <UserCircle className="h-5 w-5" />
  },
  {
    id: 'handoff',
    label: 'Handoff',
    description: 'Transferring to the selected persona',
    icon: <ArrowRight className="h-5 w-5" />
  },
  {
    id: 'completed',
    label: 'Complete',
    description: 'Workflow completed',
    icon: <CheckCircle2 className="h-5 w-5" />
  }
];

// Helper function to get the index of the current stage
const getCurrentStageIndex = (currentStage: WorkflowStage): number => {
  // Handle special cases
  if (currentStage === 'initial') return -1;
  if (currentStage === 'data_attached') {
    // Data attached comes after data guidance
    return stages.findIndex(stage => stage.id === 'data_guidance');
  }
  if (currentStage === 'followup') {
    // Followup comes after handoff
    return stages.findIndex(stage => stage.id === 'handoff');
  }

  // Normal case
  return stages.findIndex(stage => stage.id === currentStage);
};

export const WorkflowStageIndicator: React.FC<WorkflowStageIndicatorProps> = ({
  currentStage,
  className = ''
}) => {
  const currentIndex = getCurrentStageIndex(currentStage);

  // Don't show the indicator for initial stage or if we can't find the current stage
  if (currentIndex < 0 && currentStage !== 'data_attached' && currentStage !== 'followup') {
    return null;
  }

  return (
    <TooltipProvider>
      <div className={`flex items-center justify-between bg-white rounded-lg p-2 shadow-sm ${className}`}>
        {stages.map((stage, index) => {
          // Determine if this stage is active, completed, or upcoming
          const isActive = index === currentIndex ||
            (currentStage === 'data_attached' && stage.id === 'data_guidance') ||
            (currentStage === 'followup' && stage.id === 'handoff');
          const isCompleted = index < currentIndex;

          return (
            <React.Fragment key={stage.id}>
              {/* Stage indicator */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <motion.div
                    className={`flex flex-col items-center ${
                      isActive
                        ? 'text-brand-600'
                        : isCompleted
                          ? 'text-green-600'
                          : 'text-gray-400'
                    }`}
                    animate={{
                      scale: isActive ? 1.1 : 1,
                      opacity: isActive || isCompleted ? 1 : 0.7
                    }}
                    whileHover={{
                      scale: 1.05,
                      transition: { duration: 0.2 }
                    }}
                    transition={{
                      type: "spring",
                      stiffness: 300,
                      damping: 20
                    }}
                  >
                    <motion.div
                      className={`rounded-full p-1.5 ${
                        isActive
                          ? 'bg-brand-100'
                          : isCompleted
                            ? 'bg-green-100'
                            : 'bg-gray-100'
                      }`}
                      animate={isActive ? {
                        boxShadow: ['0 0 0 0 rgba(79, 70, 229, 0.2)', '0 0 0 8px rgba(79, 70, 229, 0)', '0 0 0 0 rgba(79, 70, 229, 0)'],
                      } : {}}
                      transition={isActive ? {
                        repeat: Infinity,
                        duration: 2
                      } : {}}
                    >
                      {stage.icon}
                    </motion.div>
                    <span className="text-xs mt-1 hidden md:block">{stage.label}</span>
                  </motion.div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{stage.description}</p>
                </TooltipContent>
              </Tooltip>

              {/* Connector line between stages */}
              {index < stages.length - 1 && (
                <motion.div
                  className={`h-0.5 w-4 md:w-8 ${
                    isCompleted ? 'bg-green-500' : 'bg-gray-200'
                  }`}
                  initial={{ scaleX: 0 }}
                  animate={{
                    scaleX: 1,
                    transition: {
                      delay: 0.3 + index * 0.1,
                      duration: 0.5,
                      ease: "easeInOut"
                    }
                  }}
                  style={{ originX: 0 }}
                />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </TooltipProvider>
  );
};
