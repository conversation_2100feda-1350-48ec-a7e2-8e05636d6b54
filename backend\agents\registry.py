"""
Agent registry for the Datagenius backend.

This module provides a registry for mapping persona IDs to agent implementations.
"""

import logging
import os
import importlib
import yaml
from pathlib import Path
from typing import Dict, Type, Optional, List, Any
from .base import BaseAgent
from .persona_manager import persona_manager
from .enhanced_base import EnhancedBaseAgent
from .enhanced_composable import EnhancedComposableAgent

logger = logging.getLogger(__name__)


class AgentRegistry:
    """Registry for mapping persona IDs to agent implementations."""

    _registry: Dict[str, Type[BaseAgent]] = {}
    _configurations: Dict[str, Dict[str, Any]] = {}
    _versions: Dict[str, Dict[str, Dict[str, Any]]] = {}  # persona_id -> version -> config

    @classmethod
    def register(cls, persona_id: str, agent_class: Type[BaseAgent]) -> None:
        """
        Register an agent class for a persona ID.

        Args:
            persona_id: The ID of the persona
            agent_class: The agent class to register
        """
        cls._registry[persona_id] = agent_class
        logger.info(f"Registered agent class {agent_class.__name__} for persona ID '{persona_id}'")

    @classmethod
    def get_agent_class(cls, persona_id: str) -> Optional[Type[BaseAgent]]:
        """
        Get the agent class for a persona ID.

        Args:
            persona_id: The ID of the persona

        Returns:
            The agent class if found, None otherwise
        """
        agent_class = cls._registry.get(persona_id)
        if agent_class is None:
            logger.warning(f"Registry lookup: No agent class found for persona ID '{persona_id}'")
        else:
            # Log the class found
            logger.info(f"Registry lookup: Found agent class '{agent_class.__name__}' for persona ID '{persona_id}'")
        return agent_class

    @classmethod
    def list_registered_personas(cls) -> List[str]:
        """
        List all registered persona IDs.

        Returns:
            List of registered persona IDs
        """
        return list(cls._registry.keys())

    @classmethod
    def load_configurations(cls, config_dir: str) -> None:
        """
        Load agent configurations from a directory.

        Args:
            config_dir: Directory containing agent configuration files
        """
        logger.info(f"Loading agent configurations from {config_dir}")

        # Use the PersonaManager to load configurations
        persona_manager.personas_dir = config_dir
        persona_configs = persona_manager.load_persona_configs()

        # Store configurations in the registry
        cls._configurations = persona_configs

        # Load main agent configurations and register agent classes
        # This part now focuses on loading agent implementations based on 'name' and 'class_path'
        for agent_name, config in persona_configs.items(): # Use agent_name as the key from loaded configs
            try:
                # Use 'name' from config content as the registration key, fallback to the key from load_persona_configs if 'name' is missing
                registration_key = config.get("name", agent_name) 
                logger.info(f"Processing configuration for agent registration key '{registration_key}'")

                # Dynamically import the agent class using 'class_path'
                if "class_path" in config:
                    agent_class_path = config["class_path"]
                    logger.info(f"Attempting to import agent class '{agent_class_path}' for agent '{registration_key}'")
                    try:
                        module_path, class_name = agent_class_path.rsplit(".", 1)
                        module = importlib.import_module(module_path)
                        agent_class = getattr(module, class_name)

                        # Ensure it's a subclass of BaseAgent (optional but good practice)
                        if not issubclass(agent_class, BaseAgent):
                             logger.error(f"Class '{agent_class_path}' is not a subclass of BaseAgent for agent '{registration_key}'")
                             continue # Skip registration

                        # Register the agent class using the registration_key ('name' field from YAML)
                        cls.register(registration_key, agent_class)
                        # Add specific log to confirm registration
                        logger.info(f"Successfully registered {agent_class.__name__} for agent name '{registration_key}'")

                    except (ImportError, AttributeError, ValueError) as e: # Added ValueError for rsplit
                        logger.error(f"Error importing or getting agent class '{agent_class_path}' for agent name '{registration_key}': {e}")
                    except Exception as e: # Catch other potential errors during import/getattr
                         logger.error(f"Unexpected error processing agent class for agent name '{registration_key}': {e}")
                else:
                    logger.warning(f"Missing 'class_path' in configuration for agent name '{registration_key}'")
            except Exception as e:
                # Catch errors processing the config item itself
                logger.error(f"Error processing configuration for agent name '{registration_key}': {e}")

        # Load version configurations
        cls._versions = {}
        config_path = Path(config_dir)

        if not config_path.exists():
            logger.warning(f"Configuration directory {config_dir} does not exist")
            return

        for file_path in config_path.glob("*.yaml"):
            try:
                # Only process version files (they have a dash in the filename)
                if "-" not in file_path.stem:
                    continue

                try:
                    # Try to open with UTF-8 encoding first
                    with open(file_path, "r", encoding="utf-8") as f:
                        config = yaml.safe_load(f)
                except UnicodeDecodeError:
                    # If UTF-8 fails, try with utf-8-sig (handles BOM)
                    try:
                        with open(file_path, "r", encoding="utf-8-sig") as f:
                            config = yaml.safe_load(f)
                    except UnicodeDecodeError:
                        # If that fails too, use latin-1 which can read any file
                        with open(file_path, "r", encoding="latin-1") as f:
                            config = yaml.safe_load(f)

                # Extract persona_id and version from filename
                # Format: {persona_id}-{version}.yaml
                parts = file_path.stem.split("-", 1)
                if len(parts) != 2:
                    logger.warning(f"Invalid version filename format: {file_path}")
                    continue

                persona_id, version = parts

                # Initialize versions dict for this persona if needed
                if persona_id not in cls._versions:
                    cls._versions[persona_id] = {}

                # Store the version configuration
                cls._versions[persona_id][version] = config
                logger.info(f"Loaded version {version} for persona ID '{persona_id}' from {file_path}")
            except Exception as e:
                logger.error(f"Error loading version file {file_path}: {e}")

    @classmethod
    def get_configuration(cls, persona_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the configuration for a persona ID.

        Args:
            persona_id: The ID of the persona

        Returns:
            The configuration dictionary if found, None otherwise
        """
        config = cls._configurations.get(persona_id)
        if config is None:
            logger.warning(f"No configuration found for persona ID '{persona_id}'")
        return config

    @classmethod
    def get_persona_versions(cls, persona_id: str) -> List[str]:
        """
        Get all available versions for a persona.

        Args:
            persona_id: The ID of the persona

        Returns:
            List of version strings
        """
        if persona_id not in cls._versions:
            return []

        return list(cls._versions[persona_id].keys())

    @classmethod
    def get_version_configuration(cls, persona_id: str, version: str) -> Optional[Dict[str, Any]]:
        """
        Get the configuration for a specific version of a persona.

        Args:
            persona_id: The ID of the persona
            version: The version string

        Returns:
            The version configuration dictionary if found, None otherwise
        """
        if persona_id not in cls._versions or version not in cls._versions[persona_id]:
            logger.warning(f"No configuration found for persona ID '{persona_id}' version '{version}'")
            return None

        return cls._versions[persona_id][version]

    @classmethod
    async def create_agent_instance(cls, persona_id: str, version: Optional[str] = None) -> Optional[BaseAgent]:
        """
        Create an instance of an agent for a persona ID and initialize it with configuration.

        Args:
            persona_id: The ID of the persona
            version: Optional specific version to use

        Returns:
            Initialized agent instance if found, None otherwise
        """
        # Log the persona ID being requested *before* getting the class
        logger.info(f"Creating agent instance for requested persona ID: '{persona_id}'")

        agent_class = cls.get_agent_class(persona_id)
        if agent_class is None:
            # Log added here too for clarity
            logger.error(f"Failed to create instance: No agent class found in registry for persona ID '{persona_id}'")
            return None

        # If a specific version is requested, use that configuration
        if version is not None:
            config = cls.get_version_configuration(persona_id, version)
            if config is None:
                logger.warning(f"No configuration found for persona ID '{persona_id}' version '{version}', falling back to main config")
                config = cls.get_configuration(persona_id)
        else:
            # Otherwise use the main configuration
            config = cls.get_configuration(persona_id)

        if config is None:
            logger.warning(f"No configuration found for persona ID '{persona_id}', using empty config")
            config = {}

        try:
            agent = agent_class()
            await agent.initialize(config)
            return agent
        except Exception as e:
            logger.error(f"Error initializing agent for persona ID '{persona_id}': {e}")
            return None
