/**
 * Utility functions for working with JWT tokens
 */

/**
 * Decode a JWT token to get its payload
 * @param token JWT token string
 * @returns Decoded token payload or null if invalid
 */
export const decodeToken = (token: string): any | null => {
  try {
    // JWT tokens are in the format: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.error('Invalid JWT token format (not 3 parts):', token.substring(0, 15) + '...');
      return null;
    }

    const base64Url = parts[1];
    if (!base64Url) {
      console.error('Invalid JWT token (no payload)');
      return null;
    }

    // Convert base64url to base64
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

    // Add padding if needed
    const padding = '='.repeat((4 - base64.length % 4) % 4);
    const base64Padded = base64 + padding;

    // Decode the base64 string
    try {
      const jsonPayload = decodeURIComponent(
        atob(base64Padded)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );

      const decoded = JSON.parse(jsonPayload);
      console.log('Decoded token:', {
        sub: decoded.sub,
        exp: decoded.exp ? new Date(decoded.exp * 1000).toLocaleString() : 'none',
        iat: decoded.iat ? new Date(decoded.iat * 1000).toLocaleString() : 'none',
        jti: decoded.jti ? decoded.jti.substring(0, 8) + '...' : 'none'
      });
      return decoded;
    } catch (e) {
      console.error('Error parsing JWT payload:', e);
      return null;
    }
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return null;
  }
};

/**
 * Check if a token is expired
 * @param token JWT token string
 * @returns true if token is expired or invalid, false otherwise
 */
export const isTokenExpired = (token: string): boolean => {
  console.log('Checking if token is expired...');
  const decodedToken = decodeToken(token);
  if (!decodedToken) {
    console.error('Token is invalid (could not decode)');
    return true;
  }

  if (!decodedToken.exp) {
    console.error('Token has no expiration claim');
    return true;
  }

  // exp claim is in seconds since epoch
  const expirationTime = decodedToken.exp * 1000; // Convert to milliseconds
  const currentTime = Date.now();

  const isExpired = currentTime >= expirationTime;
  const timeRemaining = expirationTime - currentTime;

  if (isExpired) {
    console.error(`Token is expired. Expired at: ${new Date(expirationTime).toLocaleString()}, Current time: ${new Date(currentTime).toLocaleString()}`);
  } else {
    console.log(`Token is valid. Expires at: ${new Date(expirationTime).toLocaleString()}, Time remaining: ${Math.round(timeRemaining / 1000)} seconds`);
  }

  return isExpired;
};

/**
 * Get the expiration time of a token in milliseconds
 * @param token JWT token string
 * @returns Expiration time in milliseconds or null if invalid
 */
export const getTokenExpirationTime = (token: string): number | null => {
  console.log('Getting token expiration time...');
  const decodedToken = decodeToken(token);
  if (!decodedToken) {
    console.error('Could not decode token to get expiration time');
    return null;
  }

  if (!decodedToken.exp) {
    console.error('Token does not contain an expiration claim');
    return null;
  }

  const expirationTime = decodedToken.exp * 1000; // Convert to milliseconds
  console.log(`Token expires at: ${new Date(expirationTime).toLocaleString()}`);
  return expirationTime;
};

/**
 * Calculate time remaining before token expires in milliseconds
 * @param token JWT token string
 * @returns Time remaining in milliseconds or 0 if expired/invalid
 */
export const getTokenTimeRemaining = (token: string): number => {
  console.log('Calculating time remaining for token...');
  const expirationTime = getTokenExpirationTime(token);
  if (!expirationTime) {
    console.error('Could not get token expiration time');
    return 0;
  }

  const currentTime = Date.now();
  const timeRemaining = expirationTime - currentTime;

  if (timeRemaining <= 0) {
    console.error(`Token is expired. Expired at: ${new Date(expirationTime).toLocaleString()}, Current time: ${new Date(currentTime).toLocaleString()}`);
    return 0;
  }

  console.log(`Token time remaining: ${Math.round(timeRemaining / 1000)} seconds (${Math.round(timeRemaining / 1000 / 60)} minutes)`);
  return timeRemaining;
};

/**
 * Check if a token should be refreshed (e.g., if it's close to expiring)
 * @param token JWT token string
 * @param refreshThresholdMs Time threshold in milliseconds (default: 5 minutes)
 * @returns true if token should be refreshed, false otherwise
 */
export const shouldRefreshToken = (token: string, refreshThresholdMs: number = 5 * 60 * 1000): boolean => {
  console.log('Checking if token should be refreshed...');
  const timeRemaining = getTokenTimeRemaining(token);

  if (timeRemaining <= 0) {
    console.log('Token is expired, should refresh');
    return true;
  }

  const shouldRefresh = timeRemaining < refreshThresholdMs;
  if (shouldRefresh) {
    console.log(`Token should be refreshed (${Math.round(timeRemaining / 1000)} seconds remaining, threshold: ${Math.round(refreshThresholdMs / 1000)} seconds)`);
  } else {
    console.log(`Token does not need refresh yet (${Math.round(timeRemaining / 1000)} seconds remaining, threshold: ${Math.round(refreshThresholdMs / 1000)} seconds)`);
  }

  return shouldRefresh;
};
