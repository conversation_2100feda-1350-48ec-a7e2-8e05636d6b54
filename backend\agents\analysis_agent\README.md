# Composable Analysis Agent

This directory contains the Composable Analysis Agent implementation for the Datagenius backend. The agent uses a component-based architecture for flexibility and modularity.

## Features

- **Data Cleaning**: Identify and handle missing values, duplicates, and inconsistencies
- **Data Visualization**: Create interactive charts and graphs
- **Data Querying**: Ask natural language questions about your data
- **Advanced Querying**: Perform complex data analysis with AI assistance
- **Data Filtering**: Filter and explore data interactively
- **Sentiment Analysis**: Analyze text data for sentiment (if applicable)

## Files

- `composable_agent.py`: The composable agent implementation
- `components.py`: Components used by the composable agent

## Components

The agent is built from the following components:

- **AnalysisParserComponent**: Parses user requests to determine the analysis task
- **AnalysisLLMComponent**: Handles interactions with language models
- **DataLoaderComponent**: Loads and processes data files
- **AnalysisExecutorComponent**: Executes data analysis operations

## Shared Tools

The core data analysis functionality is in the shared tools directory at `backend/agents/tools/data_analysis.py`. This allows other agents to use the same data analysis capabilities.

## Usage

To use the Composable Analysis Agent, you can interact with it through the API endpoints. The agent is registered with the agent registry as "composable-analysis-ai" and can be accessed through the personas API.

Example:

```python
from agents.registry import AgentRegistry
from agents.tools.data_analysis import clean_data, visualize_data, query_data

# Get the analysis agent
analysis_agent = await AgentRegistry.create_agent_instance("composable-analysis-ai")

# Process a message
response = await analysis_agent.process_message(
    user_id=1,
    message="Analyze the distribution of values in the sales column",
    conversation_id="conversation_123",
    context={"file_id": "file_uuid"}
)

# Print the response
print(response["message"])
```

## Configuration

### AI Models
The Analysis Agent supports two types of AI models:
1. **ChatGroq (Cloud)**: Requires a GROQ_API_KEY in the .env file
2. **Ollama (Local)**: Requires a local Ollama server running with the llama3 model

## Dependencies

- Python 3.10+
- FastAPI
- Pandas
- NumPy
- Matplotlib
- Seaborn
- Plotly
- LangChain
- NLTK (for sentiment analysis)
- python-dotenv