import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useProviders } from '@/hooks/useProviders';
import { providerApi } from '@/lib/api';
import { loadYamlSchemaAsZod } from '@/utils/schema-utils';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, Card<PERSON>it<PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { X, Plus, Info, Loader2 } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Define a placeholder schema to use until the YAML schema is loaded
const placeholderSchema = z.object({
  classification_type: z.enum(['llm', 'huggingface']),
  model_provider: z.string().optional(),
  model_name: z.string().optional(),
  hf_model_name: z.string().optional(),
  temperature: z.number().min(0).max(1).optional(),
  threshold: z.number().min(0).max(1).optional(),
  sample_size: z.number().int().positive().optional(),
  categories: z.array(z.string()).optional(),
  hierarchy_levels: z.array(z.string()).optional(),
});

// We'll use this as our form schema
let formSchema = placeholderSchema;

// Load the YAML schema
(async () => {
  try {
    // In a production environment, this would be a proper URL
    // For development, we're using a relative path
    formSchema = await loadYamlSchemaAsZod('/src/schemas/classification-config-form.yaml');
    console.log('Classification config form schema loaded from YAML');
  } catch (error) {
    console.error('Error loading YAML schema:', error);
    console.warn('Using placeholder schema instead');
    // Continue using the placeholder schema
  }
})();

export type ClassificationConfig = z.infer<typeof formSchema>;

interface ClassificationConfigFormProps {
  onSubmit: (data: ClassificationConfig) => void;
  defaultValues?: Partial<ClassificationConfig>;
  isLoading?: boolean;
}

export function ClassificationConfigForm({
  onSubmit,
  defaultValues,
  isLoading = false,
}: ClassificationConfigFormProps) {
  const { providers, isLoading: isLoadingProviders } = useProviders();
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<string | undefined>(
    defaultValues?.model_provider
  );
  const [newCategory, setNewCategory] = useState('');
  const [newHierarchyLevel, setNewHierarchyLevel] = useState('');

  // Initialize form with default values
  const form = useForm<ClassificationConfig>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      classification_type: defaultValues?.classification_type || 'llm',
      model_provider: defaultValues?.model_provider || 'groq',
      model_name: defaultValues?.model_name || '',
      hf_model_name: defaultValues?.hf_model_name || 'facebook/bart-large-mnli',
      temperature: defaultValues?.temperature || 0.1,
      threshold: defaultValues?.threshold || 0.5,
      sample_size: defaultValues?.sample_size || 100,
      categories: defaultValues?.categories || [],
      hierarchy_levels: defaultValues?.hierarchy_levels || ['theme', 'category'],
    },
  });

  const classificationType = form.watch('classification_type');

  // Load models when provider changes
  useEffect(() => {
    const loadModels = async () => {
      if (selectedProvider) {
        setIsLoadingModels(true);
        try {
          const response = await providerApi.getProviderModels(selectedProvider);
          setModels(response.models.map(model => ({
            id: model.id,
            name: model.name
          })));
        } catch (error) {
          console.error('Error loading models:', error);
          setModels([]);
        } finally {
          setIsLoadingModels(false);
        }
      } else {
        setModels([]);
      }
    };

    loadModels();
  }, [selectedProvider]);

  // Handle provider change
  const handleProviderChange = (value: string) => {
    setSelectedProvider(value);
    form.setValue('model_provider', value);
    form.setValue('model_name', ''); // Reset model when provider changes
  };

  // Handle adding a new category
  const handleAddCategory = () => {
    if (newCategory.trim() !== '') {
      const currentCategories = form.getValues('categories') || [];
      form.setValue('categories', [...currentCategories, newCategory.trim()]);
      setNewCategory('');
    }
  };

  // Handle removing a category
  const handleRemoveCategory = (index: number) => {
    const currentCategories = form.getValues('categories') || [];
    form.setValue('categories', currentCategories.filter((_, i) => i !== index));
  };

  // Handle adding a new hierarchy level
  const handleAddHierarchyLevel = () => {
    if (newHierarchyLevel.trim() !== '') {
      const currentLevels = form.getValues('hierarchy_levels') || [];
      form.setValue('hierarchy_levels', [...currentLevels, newHierarchyLevel.trim()]);
      setNewHierarchyLevel('');
    }
  };

  // Handle removing a hierarchy level
  const handleRemoveHierarchyLevel = (index: number) => {
    const currentLevels = form.getValues('hierarchy_levels') || [];
    form.setValue('hierarchy_levels', currentLevels.filter((_, i) => i !== index));
  };

  // Handle form submission
  const handleFormSubmit = (data: ClassificationConfig) => {
    onSubmit(data);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Classification Configuration</CardTitle>
        <CardDescription>
          Configure parameters for text classification
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
            <Tabs defaultValue={classificationType} onValueChange={(value) => form.setValue('classification_type', value as 'llm' | 'huggingface')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="llm">LLM Classification</TabsTrigger>
                <TabsTrigger value="huggingface">Hugging Face Classification</TabsTrigger>
              </TabsList>

              <TabsContent value="llm" className="space-y-4 mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="model_provider"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>AI Provider</FormLabel>
                        <Select
                          disabled={isLoadingProviders}
                          onValueChange={handleProviderChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select provider" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {providers.map((provider) => (
                              <SelectItem key={provider.id} value={provider.id}>
                                {provider.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the AI provider for classification
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="model_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Model</FormLabel>
                        <Select
                          disabled={isLoadingModels || !selectedProvider}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select model" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {models.map((model) => (
                              <SelectItem key={model.id} value={model.id}>
                                {model.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the model to use for classification
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="temperature"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Temperature: {field.value}</FormLabel>
                      <FormControl>
                        <Slider
                          min={0}
                          max={1}
                          step={0.01}
                          defaultValue={[field.value || 0.1]}
                          onValueChange={(values) => field.onChange(values[0])}
                        />
                      </FormControl>
                      <FormDescription>
                        Controls randomness: lower values are more deterministic
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sample_size"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sample Size</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={1}
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 100)}
                        />
                      </FormControl>
                      <FormDescription>
                        Number of samples to classify (for large datasets)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <FormLabel>Categories</FormLabel>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Add categories for classification. Leave empty for auto-detection.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="flex space-x-2">
                    <Input
                      value={newCategory}
                      onChange={(e) => setNewCategory(e.target.value)}
                      placeholder="Add a category"
                      className="flex-1"
                    />
                    <Button type="button" onClick={handleAddCategory} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {form.watch('categories')?.map((category, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {category}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => handleRemoveCategory(index)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <FormLabel>Hierarchy Levels</FormLabel>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Add hierarchy levels for classification (e.g., theme, category, subcategory)</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="flex space-x-2">
                    <Input
                      value={newHierarchyLevel}
                      onChange={(e) => setNewHierarchyLevel(e.target.value)}
                      placeholder="Add a hierarchy level"
                      className="flex-1"
                    />
                    <Button type="button" onClick={handleAddHierarchyLevel} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {form.watch('hierarchy_levels')?.map((level, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {level}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => handleRemoveHierarchyLevel(index)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="huggingface" className="space-y-4 mt-4">
                <FormField
                  control={form.control}
                  name="hf_model_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hugging Face Model</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="e.g., facebook/bart-large-mnli" />
                      </FormControl>
                      <FormDescription>
                        Enter the Hugging Face model name to use for classification
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="threshold"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confidence Threshold: {field.value}</FormLabel>
                      <FormControl>
                        <Slider
                          min={0}
                          max={1}
                          step={0.01}
                          defaultValue={[field.value || 0.5]}
                          onValueChange={(values) => field.onChange(values[0])}
                        />
                      </FormControl>
                      <FormDescription>
                        Minimum confidence score to accept a classification
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sample_size"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sample Size</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={1}
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 100)}
                        />
                      </FormControl>
                      <FormDescription>
                        Number of samples to classify (for large datasets)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <FormLabel>Categories</FormLabel>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Add categories for classification. Required for Hugging Face models.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="flex space-x-2">
                    <Input
                      value={newCategory}
                      onChange={(e) => setNewCategory(e.target.value)}
                      placeholder="Add a category"
                      className="flex-1"
                    />
                    <Button type="button" onClick={handleAddCategory} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {form.watch('categories')?.map((category, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {category}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => handleRemoveCategory(index)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => form.reset()}>
          Reset
        </Button>
        <Button
          onClick={form.handleSubmit(handleFormSubmit)}
          disabled={isLoading}
        >
          {isLoading ? 'Processing...' : 'Run Classification'}
        </Button>
      </CardFooter>
    </Card>
  );
}
