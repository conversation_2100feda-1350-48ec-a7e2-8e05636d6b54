
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, X, UserPlus } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface CustomersWidgetProps {
  onRemove?: () => void;
}

export function CustomersWidget({ onRemove }: CustomersWidgetProps) {
  const customerData = [
    { name: "New Customers", value: 125, change: 12 },
    { name: "Returning Customers", value: 430, change: 8 },
  ];

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Customer Overview</CardTitle>
        <div className="flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Refresh</DropdownMenuItem>
              <DropdownMenuItem>View details</DropdownMenuItem>
              {onRemove && (
                <DropdownMenuItem onClick={onRemove}>
                  Remove widget
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
          {onRemove && (
            <Button variant="ghost" size="icon" onClick={onRemove} className="h-8 w-8">
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {customerData.map((item) => (
            <div key={item.name} className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{item.name}</span>
              <span className="font-medium">{item.value}</span>
            </div>
          ))}
          <div className="flex items-center mt-6 text-blue-600">
            <UserPlus className="h-4 w-4 mr-1" />
            <span className="text-sm font-medium">125 new signups this week</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
