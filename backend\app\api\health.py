"""
Health check endpoints for the Datagenius backend.

This module provides API endpoints for health checks.
"""

import logging
import psutil
import os
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..database import get_db
from ..models.auth import User
from ..auth import get_current_active_user

# Import the WebSocket connection manager
from .chat import manager as websocket_manager

# Import the message queue
from ..queue import message_queue

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/health", tags=["Health"])


@router.get("/status")
async def get_health_status():
    """
    Get the health status of the backend.

    Returns:
        Health status information
    """
    # Get system information
    cpu_percent = psutil.cpu_percent()
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    # Get WebSocket connection information
    websocket_connections = {}
    total_connections = 0
    
    for conversation_id, connections in websocket_manager.active_connections.items():
        websocket_connections[conversation_id] = len(connections)
        total_connections += len(connections)
        
    # Get message queue information
    queue_length = len(message_queue.queue)
    processing_tasks = len(message_queue.processing)
    completed_tasks = len(message_queue.completed)
    failed_tasks = len(message_queue.failed)
    
    return {
        "status": "ok",
        "system": {
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "disk_percent": disk.percent,
            "process_id": os.getpid()
        },
        "websocket": {
            "total_connections": total_connections,
            "active_conversations": len(websocket_connections),
            "connections_by_conversation": websocket_connections
        },
        "message_queue": {
            "queue_length": queue_length,
            "processing_tasks": processing_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "is_running": message_queue.is_running
        }
    }


@router.get("/websocket")
async def get_websocket_status():
    """
    Get the status of the WebSocket service.

    Returns:
        WebSocket status information
    """
    # Get WebSocket connection information
    websocket_connections = {}
    total_connections = 0
    
    for conversation_id, connections in websocket_manager.active_connections.items():
        websocket_connections[conversation_id] = len(connections)
        total_connections += len(connections)
        
    return {
        "status": "ok",
        "total_connections": total_connections,
        "active_conversations": len(websocket_connections),
        "connections_by_conversation": websocket_connections
    }


@router.get("/queue")
async def get_queue_status():
    """
    Get the status of the message queue.

    Returns:
        Message queue status information
    """
    # Get message queue information
    queue_length = len(message_queue.queue)
    processing_tasks = len(message_queue.processing)
    completed_tasks = len(message_queue.completed)
    failed_tasks = len(message_queue.failed)
    
    return {
        "status": "ok" if message_queue.is_running else "error",
        "is_running": message_queue.is_running,
        "queue_length": queue_length,
        "processing_tasks": processing_tasks,
        "completed_tasks": completed_tasks,
        "failed_tasks": failed_tasks
    }


@router.get("/queue/tasks/{conversation_id}")
async def get_queue_tasks_for_conversation(
    conversation_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get all tasks for a conversation.

    Args:
        conversation_id: The conversation ID

    Returns:
        List of tasks for the conversation
    """
    # Get the conversation
    from ..database import get_conversation
    conversation = get_conversation(db, conversation_id)
    
    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.error(f"Conversation {conversation_id} not found or does not belong to user {current_user.id}")
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    # Get tasks for the conversation
    tasks = message_queue.get_tasks_for_conversation(conversation_id)
    
    return {
        "conversation_id": conversation_id,
        "tasks": [task.to_dict() for task in tasks]
    }


@router.post("/queue/restart")
async def restart_message_queue(
    current_user: User = Depends(get_current_active_user)
):
    """
    Restart the message queue.

    Returns:
        Success message
    """
    # Check if the user is an admin
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Only admins can restart the message queue")
    
    # Stop the message queue
    await message_queue.stop()
    
    # Start the message queue
    await message_queue.start()
    
    return {
        "status": "ok",
        "message": "Message queue restarted successfully"
    }
