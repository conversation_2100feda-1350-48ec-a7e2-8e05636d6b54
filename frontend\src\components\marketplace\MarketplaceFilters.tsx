
import { Filter, Star, X } from "lucide-react";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  <PERSON>alogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

interface MarketplaceFiltersProps {
  industries: string[];
  selectedIndustries: string[];
  onIndustriesChange: (industries: string[]) => void;
  selectedRating: number | null;
  onRatingChange: (rating: number | null) => void;
}

export function MarketplaceFilters({
  industries,
  selectedIndustries,
  onIndustriesChange,
  selectedRating,
  onRatingChange,
}: MarketplaceFiltersProps) {
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);

  const handleIndustryChange = (industry: string, checked: boolean) => {
    let newSelectedIndustries: string[];

    if (checked) {
      newSelectedIndustries = [...selectedIndustries, industry];
    } else {
      newSelectedIndustries = selectedIndustries.filter(i => i !== industry);
    }

    onIndustriesChange(newSelectedIndustries);
  };

  const handleClearFilters = () => {
    onIndustriesChange([]);
    onRatingChange(null);
  };

  const handleApplyFilters = () => {
    // Apply the filters and close the dialog
    setIsFiltersOpen(false);
  };

  const hasActiveFilters = selectedIndustries.length > 0 || selectedRating !== null;
  const activeFilterCount = selectedIndustries.length + (selectedRating ? 1 : 0);

  return (
    <div className="flex justify-end items-center space-x-2">
      {hasActiveFilters && (
        <Button variant="ghost" size="sm" onClick={handleClearFilters} className="h-9">
          Clear Filters
        </Button>
      )}

      <Dialog open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="h-9">
            <Filter className="h-4 w-4 mr-2" />
            Filter
            {hasActiveFilters && (
              <Badge variant="secondary" className="ml-2 h-5 px-1.5 bg-primary/10 text-primary">
                {activeFilterCount}
              </Badge>
            )}
          </Button>
        </DialogTrigger>

        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Filter AI Personas</DialogTitle>
          </DialogHeader>

          <div className="grid grid-cols-2 gap-6 py-4">
            {/* Industry Column */}
            <div className="space-y-4">
              <div className="font-medium">Industry</div>
              <ScrollArea className="h-[300px] pr-4">
                <div className="space-y-3">
                  {industries.map((industry) => (
                    <div key={industry} className="flex items-center space-x-2">
                      <Checkbox
                        id={`industry-${industry}`}
                        checked={selectedIndustries.includes(industry)}
                        onCheckedChange={(checked) =>
                          handleIndustryChange(industry, checked as boolean)
                        }
                      />
                      <label
                        htmlFor={`industry-${industry}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {industry}
                      </label>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>

            {/* Rating Column */}
            <div className="space-y-4">
              <div className="font-medium">Rating</div>
              <div className="space-y-3">
                <div
                  className={`flex items-center space-x-2 p-2 rounded-md cursor-pointer ${selectedRating === null ? 'bg-primary/10' : ''}`}
                  onClick={() => onRatingChange(null)}
                >
                  <div className="text-sm font-medium">Any Rating</div>
                </div>
                {[4, 3, 2, 1].map((rating) => (
                  <div
                    key={rating}
                    className={`flex items-center space-x-2 p-2 rounded-md cursor-pointer ${selectedRating === rating ? 'bg-primary/10' : ''}`}
                    onClick={() => onRatingChange(rating)}
                  >
                    <div className="flex items-center">
                      <span className="mr-2 text-sm font-medium">{rating}+</span>
                      <div className="flex">
                        {Array.from({ length: rating }).map((_, i) => (
                          <Star
                            key={i}
                            className="h-4 w-4 fill-yellow-400 text-yellow-400"
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleClearFilters}>Clear All</Button>
            <Button onClick={handleApplyFilters}>Apply Filters</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
