import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { RefreshCw } from 'lucide-react';

interface SecuritySettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

const SecuritySettings = ({ settings, onUpdate }: SecuritySettingsProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    // Authentication
    sessionTimeout: 60,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    requireEmailVerification: true,
    twoFactorAuthEnabled: false,
    twoFactorAuthRequired: false,
    
    // Password Policy
    passwordMinLength: 8,
    passwordRequireUppercase: true,
    passwordRequireLowercase: true,
    passwordRequireNumbers: true,
    passwordRequireSpecial: true,
    passwordExpiryDays: 90,
    preventPasswordReuse: true,
    passwordReuseLimit: 5,
    
    // OAuth
    googleOAuthEnabled: true,
    microsoftOAuthEnabled: false,
    githubOAuthEnabled: false,
    
    // API Security
    jwtSecret: '',
    jwtExpiryMinutes: 60,
    refreshTokenExpiryDays: 7,
    apiRateLimitEnabled: true,
    apiRateLimit: 100,
    apiRateLimitWindow: 60,
    
    // Content Security
    contentFilteringEnabled: true,
    contentFilteringLevel: 'medium',
    ageVerificationRequired: false,
    minimumAge: 18,
  });

  // Initialize form data when settings are loaded
  useEffect(() => {
    if (settings) {
      setFormData({
        // Authentication
        sessionTimeout: settings.sessionTimeout || 60,
        maxLoginAttempts: settings.maxLoginAttempts || 5,
        lockoutDuration: settings.lockoutDuration || 15,
        requireEmailVerification: settings.requireEmailVerification ?? true,
        twoFactorAuthEnabled: settings.twoFactorAuthEnabled ?? false,
        twoFactorAuthRequired: settings.twoFactorAuthRequired ?? false,
        
        // Password Policy
        passwordMinLength: settings.passwordMinLength || 8,
        passwordRequireUppercase: settings.passwordRequireUppercase ?? true,
        passwordRequireLowercase: settings.passwordRequireLowercase ?? true,
        passwordRequireNumbers: settings.passwordRequireNumbers ?? true,
        passwordRequireSpecial: settings.passwordRequireSpecial ?? true,
        passwordExpiryDays: settings.passwordExpiryDays || 90,
        preventPasswordReuse: settings.preventPasswordReuse ?? true,
        passwordReuseLimit: settings.passwordReuseLimit || 5,
        
        // OAuth
        googleOAuthEnabled: settings.googleOAuthEnabled ?? true,
        microsoftOAuthEnabled: settings.microsoftOAuthEnabled ?? false,
        githubOAuthEnabled: settings.githubOAuthEnabled ?? false,
        
        // API Security
        jwtSecret: settings.jwtSecret || '',
        jwtExpiryMinutes: settings.jwtExpiryMinutes || 60,
        refreshTokenExpiryDays: settings.refreshTokenExpiryDays || 7,
        apiRateLimitEnabled: settings.apiRateLimitEnabled ?? true,
        apiRateLimit: settings.apiRateLimit || 100,
        apiRateLimitWindow: settings.apiRateLimitWindow || 60,
        
        // Content Security
        contentFilteringEnabled: settings.contentFilteringEnabled ?? true,
        contentFilteringLevel: settings.contentFilteringLevel || 'medium',
        ageVerificationRequired: settings.ageVerificationRequired ?? false,
        minimumAge: settings.minimumAge || 18,
      });
    }
  }, [settings]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const updated = { ...prev, [name]: value };
      onUpdate(updated);
      return updated;
    });
  };

  // Handle number input change
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue)) {
      setFormData((prev) => {
        const updated = { ...prev, [name]: numValue };
        onUpdate(updated);
        return updated;
      });
    }
  };

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => {
      const updated = { ...prev, [name]: value };
      onUpdate(updated);
      return updated;
    });
  };

  // Handle checkbox change
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData((prev) => {
      const updated = { ...prev, [name]: checked };
      onUpdate(updated);
      return updated;
    });
  };

  // Handle slider change
  const handleSliderChange = (name: string, value: number[]) => {
    setFormData((prev) => {
      const updated = { ...prev, [name]: value[0] };
      onUpdate(updated);
      return updated;
    });
  };

  // Generate new JWT secret
  const generateJwtSecret = () => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+';
    let result = '';
    const length = 32;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    
    setFormData((prev) => {
      const updated = { ...prev, jwtSecret: result };
      onUpdate(updated);
      return updated;
    });
    
    toast({
      title: 'JWT Secret Generated',
      description: 'A new JWT secret has been generated.',
    });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Authentication</h3>
        <div className="grid gap-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sessionTimeout" className="text-right">
              Session Timeout (minutes)
            </Label>
            <Input
              id="sessionTimeout"
              name="sessionTimeout"
              type="number"
              min="5"
              max="1440"
              value={formData.sessionTimeout}
              onChange={handleNumberChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="maxLoginAttempts" className="text-right">
              Max Login Attempts
            </Label>
            <Input
              id="maxLoginAttempts"
              name="maxLoginAttempts"
              type="number"
              min="3"
              max="10"
              value={formData.maxLoginAttempts}
              onChange={handleNumberChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="lockoutDuration" className="text-right">
              Lockout Duration (minutes)
            </Label>
            <Input
              id="lockoutDuration"
              name="lockoutDuration"
              type="number"
              min="5"
              max="1440"
              value={formData.lockoutDuration}
              onChange={handleNumberChange}
              className="col-span-3"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="requireEmailVerification"
              checked={formData.requireEmailVerification}
              onCheckedChange={(checked) => handleCheckboxChange('requireEmailVerification', checked as boolean)}
            />
            <Label htmlFor="requireEmailVerification">Require Email Verification</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="twoFactorAuthEnabled"
              checked={formData.twoFactorAuthEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('twoFactorAuthEnabled', checked as boolean)}
            />
            <Label htmlFor="twoFactorAuthEnabled">Enable Two-Factor Authentication</Label>
          </div>
          {formData.twoFactorAuthEnabled && (
            <div className="flex items-center space-x-2 ml-6">
              <Checkbox
                id="twoFactorAuthRequired"
                checked={formData.twoFactorAuthRequired}
                onCheckedChange={(checked) => handleCheckboxChange('twoFactorAuthRequired', checked as boolean)}
              />
              <Label htmlFor="twoFactorAuthRequired">Require Two-Factor Authentication for All Users</Label>
            </div>
          )}
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Password Policy</h3>
        <div className="grid gap-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="passwordMinLength" className="text-right">
              Minimum Length
            </Label>
            <div className="col-span-3 space-y-2">
              <Slider
                id="passwordMinLength"
                min={6}
                max={16}
                step={1}
                value={[formData.passwordMinLength]}
                onValueChange={(value) => handleSliderChange('passwordMinLength', value)}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>6</span>
                <span>{formData.passwordMinLength}</span>
                <span>16</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="passwordRequireUppercase"
              checked={formData.passwordRequireUppercase}
              onCheckedChange={(checked) => handleCheckboxChange('passwordRequireUppercase', checked as boolean)}
            />
            <Label htmlFor="passwordRequireUppercase">Require Uppercase Letters</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="passwordRequireLowercase"
              checked={formData.passwordRequireLowercase}
              onCheckedChange={(checked) => handleCheckboxChange('passwordRequireLowercase', checked as boolean)}
            />
            <Label htmlFor="passwordRequireLowercase">Require Lowercase Letters</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="passwordRequireNumbers"
              checked={formData.passwordRequireNumbers}
              onCheckedChange={(checked) => handleCheckboxChange('passwordRequireNumbers', checked as boolean)}
            />
            <Label htmlFor="passwordRequireNumbers">Require Numbers</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="passwordRequireSpecial"
              checked={formData.passwordRequireSpecial}
              onCheckedChange={(checked) => handleCheckboxChange('passwordRequireSpecial', checked as boolean)}
            />
            <Label htmlFor="passwordRequireSpecial">Require Special Characters</Label>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="passwordExpiryDays" className="text-right">
              Password Expiry (days)
            </Label>
            <Input
              id="passwordExpiryDays"
              name="passwordExpiryDays"
              type="number"
              min="0"
              max="365"
              value={formData.passwordExpiryDays}
              onChange={handleNumberChange}
              className="col-span-3"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="preventPasswordReuse"
              checked={formData.preventPasswordReuse}
              onCheckedChange={(checked) => handleCheckboxChange('preventPasswordReuse', checked as boolean)}
            />
            <Label htmlFor="preventPasswordReuse">Prevent Password Reuse</Label>
          </div>
          {formData.preventPasswordReuse && (
            <div className="grid grid-cols-4 items-center gap-4 ml-6">
              <Label htmlFor="passwordReuseLimit" className="text-right">
                Password History
              </Label>
              <Input
                id="passwordReuseLimit"
                name="passwordReuseLimit"
                type="number"
                min="1"
                max="10"
                value={formData.passwordReuseLimit}
                onChange={handleNumberChange}
                className="col-span-3"
              />
            </div>
          )}
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">OAuth Providers</h3>
        <div className="grid gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="googleOAuthEnabled"
              checked={formData.googleOAuthEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('googleOAuthEnabled', checked as boolean)}
            />
            <Label htmlFor="googleOAuthEnabled">Enable Google OAuth</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="microsoftOAuthEnabled"
              checked={formData.microsoftOAuthEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('microsoftOAuthEnabled', checked as boolean)}
            />
            <Label htmlFor="microsoftOAuthEnabled">Enable Microsoft OAuth</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="githubOAuthEnabled"
              checked={formData.githubOAuthEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('githubOAuthEnabled', checked as boolean)}
            />
            <Label htmlFor="githubOAuthEnabled">Enable GitHub OAuth</Label>
          </div>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">API Security</h3>
        <div className="grid gap-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="jwtSecret" className="text-right">
              JWT Secret
            </Label>
            <div className="col-span-3 flex space-x-2">
              <Input
                id="jwtSecret"
                name="jwtSecret"
                type="password"
                value={formData.jwtSecret}
                onChange={handleInputChange}
                className="flex-1"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={generateJwtSecret}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Generate
              </Button>
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="jwtExpiryMinutes" className="text-right">
              JWT Expiry (minutes)
            </Label>
            <Input
              id="jwtExpiryMinutes"
              name="jwtExpiryMinutes"
              type="number"
              min="5"
              max="1440"
              value={formData.jwtExpiryMinutes}
              onChange={handleNumberChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="refreshTokenExpiryDays" className="text-right">
              Refresh Token Expiry (days)
            </Label>
            <Input
              id="refreshTokenExpiryDays"
              name="refreshTokenExpiryDays"
              type="number"
              min="1"
              max="30"
              value={formData.refreshTokenExpiryDays}
              onChange={handleNumberChange}
              className="col-span-3"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="apiRateLimitEnabled"
              checked={formData.apiRateLimitEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('apiRateLimitEnabled', checked as boolean)}
            />
            <Label htmlFor="apiRateLimitEnabled">Enable API Rate Limiting</Label>
          </div>
          {formData.apiRateLimitEnabled && (
            <>
              <div className="grid grid-cols-4 items-center gap-4 ml-6">
                <Label htmlFor="apiRateLimit" className="text-right">
                  Rate Limit (requests)
                </Label>
                <Input
                  id="apiRateLimit"
                  name="apiRateLimit"
                  type="number"
                  min="10"
                  max="1000"
                  value={formData.apiRateLimit}
                  onChange={handleNumberChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4 ml-6">
                <Label htmlFor="apiRateLimitWindow" className="text-right">
                  Window (seconds)
                </Label>
                <Input
                  id="apiRateLimitWindow"
                  name="apiRateLimitWindow"
                  type="number"
                  min="10"
                  max="3600"
                  value={formData.apiRateLimitWindow}
                  onChange={handleNumberChange}
                  className="col-span-3"
                />
              </div>
            </>
          )}
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Content Security</h3>
        <div className="grid gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="contentFilteringEnabled"
              checked={formData.contentFilteringEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('contentFilteringEnabled', checked as boolean)}
            />
            <Label htmlFor="contentFilteringEnabled">Enable Content Filtering</Label>
          </div>
          {formData.contentFilteringEnabled && (
            <div className="grid grid-cols-4 items-center gap-4 ml-6">
              <Label htmlFor="contentFilteringLevel" className="text-right">
                Filtering Level
              </Label>
              <Select
                value={formData.contentFilteringLevel}
                onValueChange={(value) => handleSelectChange('contentFilteringLevel', value)}
              >
                <SelectTrigger id="contentFilteringLevel" className="col-span-3">
                  <SelectValue placeholder="Select filtering level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="ageVerificationRequired"
              checked={formData.ageVerificationRequired}
              onCheckedChange={(checked) => handleCheckboxChange('ageVerificationRequired', checked as boolean)}
            />
            <Label htmlFor="ageVerificationRequired">Require Age Verification</Label>
          </div>
          {formData.ageVerificationRequired && (
            <div className="grid grid-cols-4 items-center gap-4 ml-6">
              <Label htmlFor="minimumAge" className="text-right">
                Minimum Age
              </Label>
              <Input
                id="minimumAge"
                name="minimumAge"
                type="number"
                min="13"
                max="21"
                value={formData.minimumAge}
                onChange={handleNumberChange}
                className="col-span-3"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SecuritySettings;
