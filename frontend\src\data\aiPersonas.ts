
export interface AIPersona {
  id: string;
  name: string;
  description: string;
  industry: string;
  skills: string[];
  rating: number;
  reviewCount: number;
  imageUrl: string;
  isAvailable?: boolean;
  capabilities?: string[];
}

// List of industries for filtering
export const industries = [
  "Healthcare",
  "Marketing",
  "Retail",
  "Finance",
  "Education",
  "Technology",
  "Legal",
  "Customer Service",
  "Data Science"
];

// Empty array - we'll fetch personas from the API instead of using fallback data
export const aiPersonas: AIPersona[] = [];
