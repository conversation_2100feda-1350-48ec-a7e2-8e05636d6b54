"""
Chat API endpoints for the Datagenius backend.

This module provides API endpoints for chat functionality.
"""

import logging
import uuid
from datetime import datetime
from typing import List, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect, Query
from sqlalchemy.orm import Session

from ..utils.json_utils import ensure_serializable, sanitize_metadata, sanitize_json

from ..models.chat import (
    ConversationCreate, ConversationResponse, ConversationListResponse,
    MessageCreate, MessageResponse, SendMessageRequest, SendMessageResponse
)
from ..models.auth import User
from ..database import (
    get_db, create_conversation, get_conversation, get_user_conversations,
    update_conversation, delete_conversation, create_message, get_conversation_messages,
    update_message, get_message, Message
)
from ..auth import get_current_active_user, get_current_user_from_token

# Import the agent registry using centralized import utility
from ..utils.import_utils import import_agents_registry, import_from_backend

AgentRegistry = import_agents_registry()
Orchestrator = import_from_backend('app.orchestration.orchestrator', 'Orchestrator')

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/chat", tags=["Chat"])

# Create Orchestrator instance
orchestrator = Orchestrator()


@router.post("/conversations", response_model=ConversationResponse)
async def create_new_conversation(
    conversation: ConversationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new conversation.

    Args:
        conversation: Conversation creation request

    Returns:
        Created conversation
    """
    logger.info(f"User {current_user.id} creating new conversation with persona {conversation.persona_id}")

    # Enhanced logging for debugging
    registered_personas = AgentRegistry.list_registered_personas()
    logger.info(f"Registered personas in AgentRegistry: {registered_personas}")

    # Check if the persona exists in the registry
    if conversation.persona_id not in registered_personas:
        logger.error(f"Persona {conversation.persona_id} not found in registry")

        # Check if the persona exists in the database
        from ..database import get_persona
        db_persona = get_persona(db, conversation.persona_id)
        if db_persona:
            logger.error(f"Persona {conversation.persona_id} exists in database but not in registry: {db_persona.name}")

        # Check if the user has purchased this persona
        from ..database import has_user_purchased_persona
        is_purchased = has_user_purchased_persona(db, current_user.id, conversation.persona_id)
        logger.error(f"Has user purchased persona {conversation.persona_id}: {is_purchased}")

        # Check agent registry configuration
        config = AgentRegistry.get_configuration(conversation.persona_id)
        logger.error(f"Agent configuration for {conversation.persona_id}: {config}")

        # Check agent class
        agent_class = AgentRegistry.get_agent_class(conversation.persona_id)
        logger.error(f"Agent class for {conversation.persona_id}: {agent_class}")

        raise HTTPException(status_code=404, detail=f"Persona {conversation.persona_id} not found")

    # Create the conversation
    logger.info(f"Attempting to create conversation '{conversation.title}' for user {current_user.id} with persona {conversation.persona_id}") # Log before create
    try: # Added missing try keyword
        db_conversation = create_conversation(
            db,
            user_id=current_user.id,
            title=conversation.title,
            persona_id=conversation.persona_id,
            metadata=conversation.metadata
        )
        logger.info(f"Successfully created conversation with ID: {db_conversation.id}") # Log after create
    except Exception as e:
        logger.error(f"Database error during create_conversation: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Database error creating conversation.")


    # Enqueue an initiation task to generate the first greeting message
    logger.info(f"Attempting to enqueue initiation task for conversation {db_conversation.id}") # Log before enqueue
    try: # Corrected try block for enqueue
        from ..queue import message_queue
        await message_queue.enqueue(
            user_id=current_user.id, # Pass user_id for context if needed by agent later
            conversation_id=db_conversation.id,
            message="", # No user message for initiation
            context={"task_type": "initiate", "persona_id": conversation.persona_id}, # Special context
            metadata={}
        )
        logger.info(f"Successfully enqueued initiation task for conversation {db_conversation.id}") # Log after enqueue
    except Exception as e:
        # Log the error but don't fail the conversation creation
        logger.error(f"Failed to enqueue initiation task for conversation {db_conversation.id}: {e}", exc_info=True)
        # Optionally, decide if this failure should prevent returning success
        # For now, we log but still return the created conversation


    return ConversationResponse(
        id=db_conversation.id,
        user_id=db_conversation.user_id,
        persona_id=db_conversation.persona_id,
        title=db_conversation.title,
        created_at=db_conversation.created_at,
        updated_at=db_conversation.updated_at,
        is_archived=db_conversation.is_archived,
        messages=[]
    )


@router.get("/conversations", response_model=ConversationListResponse)
async def list_conversations(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    List all conversations for the current user.

    Args:
        skip: Number of conversations to skip
        limit: Maximum number of conversations to return

    Returns:
        List of conversations
    """
    logger.info(f"User {current_user.id} listing conversations")

    # Get conversations for the user
    conversations = get_user_conversations(db, current_user.id, skip, limit)

    # Count total conversations for the user
    total = db.query(get_db().query_class.func.count()).filter_by(user_id=current_user.id).scalar()

    return ConversationListResponse(
        conversations=[
            ConversationResponse(
                id=conv.id,
                user_id=conv.user_id,
                persona_id=conv.persona_id,
                title=conv.title,
                created_at=conv.created_at,
                updated_at=conv.updated_at,
                is_archived=conv.is_archived,
                messages=[]
            ) for conv in conversations
        ],
        total=total
    )


@router.get("/conversations/{conversation_id}", response_model=ConversationResponse)
async def get_conversation_detail(
    conversation_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get details of a specific conversation.

    Args:
        conversation_id: ID of the conversation

    Returns:
        Conversation details
    """
    logger.info(f"User {current_user.id} getting conversation {conversation_id}")

    # Get the conversation
    conversation = get_conversation(db, conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.error(f"Conversation {conversation_id} not found or does not belong to user {current_user.id}")
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Get messages for the conversation
    messages = get_conversation_messages(db, conversation_id)

    return ConversationResponse(
        id=conversation.id,
        user_id=conversation.user_id,
        persona_id=conversation.persona_id,
        title=conversation.title,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at,
        is_archived=conversation.is_archived,
        messages=[
            MessageResponse(
                id=msg.id,
                conversation_id=msg.conversation_id,
                sender=msg.sender,
                content=msg.content,
                metadata=msg.message_metadata,
                created_at=msg.created_at
            ) for msg in messages
        ]
    )


@router.put("/conversations/{conversation_id}", response_model=ConversationResponse)
async def update_conversation_details(
    conversation_id: str,
    title: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Update a conversation's title.

    Args:
        conversation_id: ID of the conversation
        title: New title for the conversation

    Returns:
        Updated conversation
    """
    logger.info(f"User {current_user.id} updating conversation {conversation_id}")

    # Get the conversation
    conversation = get_conversation(db, conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.error(f"Conversation {conversation_id} not found or does not belong to user {current_user.id}")
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Update the conversation
    updated_conversation = update_conversation(db, conversation_id, update_data={"title": title})

    # Get messages for the conversation
    messages = get_conversation_messages(db, conversation_id)

    return ConversationResponse(
        id=updated_conversation.id,
        user_id=updated_conversation.user_id,
        persona_id=updated_conversation.persona_id,
        title=updated_conversation.title,
        created_at=updated_conversation.created_at,
        updated_at=updated_conversation.updated_at,
        is_archived=updated_conversation.is_archived,
        messages=[
            MessageResponse(
                id=msg.id,
                conversation_id=msg.conversation_id,
                sender=msg.sender,
                content=msg.content,
                metadata=msg.message_metadata,
                created_at=msg.created_at
            ) for msg in messages
        ]
    )


@router.delete("/conversations/{conversation_id}")
async def delete_conversation_endpoint(
    conversation_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a conversation.

    Args:
        conversation_id: ID of the conversation

    Returns:
        Success message
    """
    logger.info(f"User {current_user.id} deleting conversation {conversation_id}")

    # Get the conversation
    conversation = get_conversation(db, conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.error(f"Conversation {conversation_id} not found or does not belong to user {current_user.id}")
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Delete the conversation
    success = delete_conversation(db, conversation_id)

    if not success:
        logger.error(f"Failed to delete conversation {conversation_id}")
        raise HTTPException(status_code=500, detail="Failed to delete conversation")

    return {"message": "Conversation deleted successfully"}


@router.post("/messages", response_model=MessageResponse)
async def create_new_message(
    message: MessageCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new message.

    Args:
        message: Message creation request

    Returns:
        Created message
    """
    logger.info(f"User {current_user.id} creating new message in conversation {message.conversation_id}")

    # Get the conversation
    conversation = get_conversation(db, message.conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.error(f"Conversation {message.conversation_id} not found or does not belong to user {current_user.id}")
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Create the message
    message_data = {
        "id": str(uuid.uuid4()),
        "conversation_id": message.conversation_id,
        "sender": "user",  # Messages created via this endpoint are always from the user
        "content": message.content,
        "message_metadata": message.metadata
    }

    db_message = create_message(
        db,
        conversation_id=message_data["conversation_id"],
        sender=message_data["sender"],
        content=message_data["content"],
        metadata=message_data.get("message_metadata")
    )

    # Update the conversation's updated_at timestamp
    # Just trigger the updated_at timestamp without changing any data
    update_conversation(db, message.conversation_id)

    return MessageResponse(
        id=db_message.id,
        conversation_id=db_message.conversation_id,
        sender=db_message.sender,
        content=db_message.content,
        metadata=db_message.message_metadata,
        created_at=db_message.created_at
    )


@router.post("/send", response_model=SendMessageResponse)
async def send_message_to_agent(
    request: SendMessageRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Send a message to an agent and get a response.

    Args:
        request: Message sending request

    Returns:
        User message and agent response
    """
    logger.info(f"User {current_user.id} sending message to agent in conversation {request.conversation_id}")

    # Get the conversation
    conversation = get_conversation(db, request.conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.error(f"Conversation {request.conversation_id} not found or does not belong to user {current_user.id}")
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Create the user message
    user_message_id = str(uuid.uuid4())
    user_message_data = {
        "id": user_message_id,
        "conversation_id": request.conversation_id,
        "sender": "user",
        "content": request.message,
        "metadata": {}
    }

    db_user_message = create_message(
        db,
        conversation_id=user_message_data["conversation_id"],
        sender=user_message_data["sender"],
        content=user_message_data["content"],
        metadata=user_message_data.get("metadata")
    )

    # Create an initial empty AI message
    ai_message_id = str(uuid.uuid4())

    # Create initial metadata and sanitize it
    initial_metadata = {
        "status": "processing",
        "processing": True
    }

    # Sanitize metadata to ensure it's JSON serializable
    sanitized_initial_metadata = sanitize_json(initial_metadata)

    initial_ai_message_data = {
        "id": ai_message_id,
        "conversation_id": request.conversation_id,
        "sender": "ai",
        "content": "Processing your request...",  # Initial message
        "message_metadata": sanitized_initial_metadata
    }

    db_ai_message = create_message(
        db,
        conversation_id=initial_ai_message_data["conversation_id"],
        sender=initial_ai_message_data["sender"],
        content=initial_ai_message_data["content"],
        metadata=initial_ai_message_data.get("message_metadata")
    )

    # Update the conversation's updated_at timestamp
    update_conversation(db, request.conversation_id)

    # Call the orchestrator directly
    try:
        orchestrator_response = await orchestrator.handle_incoming_message(
            db=db, # Pass the db session
            user_id=current_user.id,
            conversation_id=request.conversation_id,
            message=request.message,
            context=request.context
        )

        # Update the AI message with the final response from the orchestrator
        final_content = orchestrator_response.get("message", "No response generated.")
        final_metadata = {
            "status": "completed",
            "processing": False,
            **orchestrator_response.get("metadata", {})
        }

        # Sanitize metadata to ensure it's JSON serializable
        sanitized_metadata = sanitize_json(final_metadata)

        logger.info(f"Sanitized metadata for message {ai_message_id}")

        updated_message = update_message(
            db,
            ai_message_id,
            content=final_content,
            metadata=sanitized_metadata
        )

        if not updated_message:
             # If update failed (e.g., message ID mismatch), log error and use original ID
             logger.error(f"Failed to update AI message {ai_message_id}, using original DB object.")
             updated_message = db_ai_message # Fallback to the initially created message object
             updated_message.content = final_content
             updated_message.message_metadata = sanitized_metadata


    except Exception as e:
        logger.error(f"Error during orchestrator processing: {str(e)}", exc_info=True)
        # Update the AI message with the error
        error_message = "An error occurred while processing your message. Please try again later."
        error_metadata = {
            "status": "error",
            "processing": False,
            "error": str(e),
            "error_type": e.__class__.__name__
        }

        # Sanitize metadata to ensure it's JSON serializable
        sanitized_error_metadata = sanitize_json(error_metadata)

        logger.info(f"Sanitized error metadata for message {ai_message_id}")

        updated_message = update_message(
            db,
            ai_message_id,
            content=error_message,
            metadata=sanitized_error_metadata
        )
        if not updated_message:
             logger.error(f"Failed to update AI message {ai_message_id} with error, using original DB object.")
             updated_message = db_ai_message
             updated_message.content = error_message
             updated_message.message_metadata = sanitized_error_metadata


    # Return the final response including the updated AI message
    return SendMessageResponse(
        conversation_id=request.conversation_id,
        user_message=MessageResponse(
            id=db_user_message.id,
            conversation_id=db_user_message.conversation_id,
            sender=db_user_message.sender,
            content=db_user_message.content,
            metadata=db_user_message.message_metadata,
            created_at=db_user_message.created_at
        ),
        ai_message=MessageResponse(
            id=updated_message.id,
            conversation_id=updated_message.conversation_id,
            sender=updated_message.sender,
            content=updated_message.content,
            metadata=updated_message.message_metadata,
            created_at=updated_message.created_at
        )
    )


# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: dict[str, List[WebSocket]] = {}
        self.connection_status: dict[str, dict[WebSocket, bool]] = {}  # Track connection status

    async def connect(self, websocket: WebSocket, conversation_id: str):
        try:
            logger.info(f"Accepting WebSocket connection for conversation {conversation_id}")
            await websocket.accept()

            # Initialize connection tracking for this conversation if needed
            if conversation_id not in self.active_connections:
                logger.info(f"Creating new connection tracking for conversation {conversation_id}")
                self.active_connections[conversation_id] = []
                self.connection_status[conversation_id] = {}

            # Add this connection to the active connections
            self.active_connections[conversation_id].append(websocket)
            self.connection_status[conversation_id][websocket] = True

            # Log connection count
            connection_count = len(self.active_connections[conversation_id])
            logger.info(f"Added WebSocket connection for conversation {conversation_id}. Total connections: {connection_count}")

            # Send connection confirmation
            await websocket.send_json({
                "type": "connection_status",
                "status": "connected",
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat()
            })

            logger.info(f"WebSocket connection established for conversation {conversation_id}")
        except Exception as e:
            logger.error(f"Error accepting WebSocket connection for conversation {conversation_id}: {str(e)}", exc_info=True)
            raise

    def disconnect(self, websocket: WebSocket, conversation_id: str):
        try:
            logger.info(f"Disconnecting WebSocket for conversation {conversation_id}")

            if conversation_id in self.active_connections:
                # Check if this WebSocket is in the active connections for this conversation
                if websocket in self.active_connections[conversation_id]:
                    logger.info(f"Removing WebSocket from active connections for conversation {conversation_id}")
                    self.active_connections[conversation_id].remove(websocket)

                    # Log remaining connection count
                    remaining_connections = len(self.active_connections[conversation_id])
                    logger.info(f"Remaining connections for conversation {conversation_id}: {remaining_connections}")

                    # Update connection status
                    if conversation_id in self.connection_status and websocket in self.connection_status[conversation_id]:
                        logger.info(f"Updating connection status for conversation {conversation_id}")
                        self.connection_status[conversation_id][websocket] = False
                        del self.connection_status[conversation_id][websocket]
                else:
                    logger.warning(f"WebSocket not found in active connections for conversation {conversation_id}")

                # Clean up empty conversation entries
                if not self.active_connections[conversation_id]:
                    logger.info(f"No more active connections for conversation {conversation_id}, cleaning up")
                    del self.active_connections[conversation_id]
                    if conversation_id in self.connection_status:
                        del self.connection_status[conversation_id]
            else:
                logger.warning(f"Conversation {conversation_id} not found in active connections")

            logger.info(f"WebSocket connection closed for conversation {conversation_id}")
        except Exception as e:
            logger.error(f"Error during WebSocket disconnection for conversation {conversation_id}: {str(e)}", exc_info=True)

    async def broadcast(self, message: Dict[str, Any], conversation_id: str):
        """
        Broadcast a message to all clients connected to a conversation.

        Args:
            message: The message to broadcast
            conversation_id: ID of the conversation
        """
        if conversation_id in self.active_connections:
            disconnected = []

            # Ensure the message is JSON serializable
            serializable_message = ensure_serializable(message)

            # If the message contains metadata, sanitize it
            if "message" in serializable_message and "metadata" in serializable_message["message"]:
                serializable_message["message"]["metadata"] = sanitize_metadata(
                    serializable_message["message"]["metadata"]
                )

            for connection in self.active_connections[conversation_id]:
                try:
                    await connection.send_json(serializable_message)
                except WebSocketDisconnect:
                    # Mark for removal
                    disconnected.append(connection)
                    logger.warning(f"WebSocket disconnected during broadcast for conversation {conversation_id}")
                except Exception as e:
                    # Mark for removal but log the specific error
                    disconnected.append(connection)
                    logger.error(f"Error broadcasting message: {str(e)}", exc_info=True)

            # Clean up disconnected connections
            for connection in disconnected:
                if connection in self.active_connections[conversation_id]:
                    self.active_connections[conversation_id].remove(connection)

                    # Update connection status
                    if conversation_id in self.connection_status and connection in self.connection_status[conversation_id]:
                        self.connection_status[conversation_id][connection] = False
                        del self.connection_status[conversation_id][connection]

            # Clean up empty conversation entries
            if not self.active_connections[conversation_id]:
                del self.active_connections[conversation_id]
                if conversation_id in self.connection_status:
                    del self.connection_status[conversation_id]

    async def send_typing_indicator(self, conversation_id: str, is_typing: bool = True):
        """Send typing indicator to all clients connected to a conversation."""
        if conversation_id in self.active_connections:
            await self.broadcast({
                "type": "typing_indicator",
                "is_typing": is_typing,
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat()
            }, conversation_id)

    def get_connection_count(self, conversation_id: str) -> int:
        """Get the number of active connections for a conversation."""
        if conversation_id in self.active_connections:
            return len(self.active_connections[conversation_id])
        return 0


manager = ConnectionManager()


@router.websocket("/ws/{conversation_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    conversation_id: str,
    token: str = Query(...),
    db: Session = Depends(get_db)
):
    """
    WebSocket endpoint for real-time chat.

    Args:
        websocket: WebSocket connection
        conversation_id: ID of the conversation
        token: JWT token for authentication
    """
    # Log connection attempt
    logger.info(f"WebSocket connection attempt for conversation {conversation_id}")

    # Authenticate the user
    try:
        current_user = await get_current_user_from_token(token, db)
        logger.info(f"WebSocket authentication successful for user {current_user.id}")
    except Exception as e:
        logger.error(f"WebSocket authentication failed for conversation {conversation_id}: {str(e)}", exc_info=True)
        await websocket.close(code=1008, reason="Authentication failed")
        return

    # Get the conversation
    conversation = get_conversation(db, conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.warning(f"WebSocket access denied: User {current_user.id} attempted to access conversation {conversation_id}")
        await websocket.close(code=1008, reason="Conversation not found or access denied")
        return

    # Connect to the WebSocket
    await manager.connect(websocket, conversation_id)

    try:
        while True:
            # Receive message from the client
            data = await websocket.receive_json()
            logger.info(f"RAW WebSocket received data: {data}") # Log raw incoming data
            logger.debug(f"WebSocket received data: {data}")

            # Process the message
            if "message" in data:
                # Send typing indicator
                await manager.send_typing_indicator(conversation_id, True)

                # Create the user message
                user_message_id = str(uuid.uuid4())
                user_message_data = {
                    "id": user_message_id,
                    "conversation_id": conversation_id,
                    "sender": "user",
                    "content": data["message"],
                    "metadata": data.get("metadata", {}) # Original metadata
                }

                # Extract temp_id from metadata if present
                temp_id = data.get("metadata", {}).pop("temp_id", None)
                logger.info(f"Received message with temp_id: {temp_id}")

                db_user_message = create_message(
                    db,
                    conversation_id=user_message_data["conversation_id"],
                    sender=user_message_data["sender"],
                    content=user_message_data["content"],
                    metadata=user_message_data.get("metadata", {})
                )
                logger.info(f"Created user message {user_message_id} in conversation {conversation_id}")

                # Assign the payload to the variable
                broadcast_payload = {
                    "type": "user_message",
                    "message": {
                        "id": db_user_message.id,
                        "conversation_id": db_user_message.conversation_id,
                        "sender": db_user_message.sender,
                        "content": db_user_message.content,
                        "metadata": db_user_message.message_metadata,
                        "created_at": db_user_message.created_at.isoformat(),
                        "temp_id": temp_id
                    }
                }
                logger.info(f"Broadcasting user_message payload: {broadcast_payload}") # Log the payload
                await manager.broadcast(broadcast_payload, conversation_id)

                # Get the agent class from the registry
                agent_class = AgentRegistry.get_agent_class(conversation.persona_id)
                if not agent_class:
                    logger.error(f"No agent found for persona: {conversation.persona_id}")

                    # Send an error message
                    error_message_id = str(uuid.uuid4())
                    error_message_data = {
                        "id": error_message_id,
                        "conversation_id": conversation_id,
                        "sender": "ai",
                        "content": f"No agent found for persona: {conversation.persona_id}",
                        "metadata": {"error": "agent_not_found"}
                    }

                    db_error_message = create_message(db, error_message_data)

                    # Turn off typing indicator
                    await manager.send_typing_indicator(conversation_id, False)

                    await manager.broadcast({
                        "type": "ai_message",
                        "message": {
                            "id": db_error_message.id,
                            "conversation_id": db_error_message.conversation_id,
                            "sender": db_error_message.sender,
                            "content": db_error_message.content,
                            "metadata": db_error_message.metadata,
                            "created_at": db_error_message.created_at.isoformat()
                        }
                    }, conversation_id)
                    continue

                # Call Orchestrator directly instead of using queue
                try:
                    # Prepare context
                    task_context = data.get("context", {})
                    metadata = data.get("metadata", {})

                    if "marketing_form_data" in metadata:
                        task_context["marketing_form_data"] = metadata["marketing_form_data"]
                    if "provider" in metadata:
                        task_context["provider"] = metadata["provider"]
                    if "model" in metadata:
                        task_context["model"] = metadata["model"]
                    if "data_source" in metadata:
                        task_context["data_source"] = metadata["data_source"]
                        logger.info(f"Found data_source in metadata: {metadata['data_source']}")

                    # Handle file input attachments (convert attachment to data_source format)
                    if "attachment" in metadata and "data_source" not in task_context:
                        attachment = metadata["attachment"]
                        # Convert attachment to data_source format
                        task_context["data_source"] = {
                            "id": f"attachment_{attachment.get('name', 'unknown')}",
                            "name": attachment.get("name", "attached_file"),
                            "type": "file",
                            "attachment_info": attachment  # Keep original attachment info
                        }
                        logger.info(f"Converted attachment to data_source: {task_context['data_source']}")

                    logger.info(f"Final task_context: {task_context}")

                    # Create initial AI message placeholder
                    ai_message_id = str(uuid.uuid4())
                    logger.info(f"Generated new AI message ID for orchestrator call: {ai_message_id}") # Keep the ID generation for logging/reference if needed elsewhere, but don't pass it to create_message

                    # Create initial metadata and sanitize it
                    initial_metadata = {
                        "status": "processing",
                        "processing": True,
                        "streaming": False, # Set to False as we process synchronously now
                        "complete": False
                    }

                    # Sanitize metadata to ensure it's JSON serializable
                    sanitized_initial_metadata = sanitize_json(initial_metadata)

                    initial_ai_message_data = {
                        # "id": ai_message_id, # Removed: ID is likely generated by the DB/create_message function
                        "conversation_id": conversation_id,
                        "sender": "ai",
                        "content": "Processing your request...",  # Initial message
                        "metadata": sanitized_initial_metadata
                    }
                    db_ai_message = create_message(db, **initial_ai_message_data)
                    logger.info(f"Created initial AI message {db_ai_message.id}")

                    # Send the initial message to indicate processing
                    await manager.broadcast({
                        "type": "ai_message_start", # Keep type for consistency? Or change?
                        "message": {
                            "id": db_ai_message.id,
                            "conversation_id": db_ai_message.conversation_id,
                            "sender": db_ai_message.sender,
                            "content": db_ai_message.content,
                            "metadata": db_ai_message.message_metadata,
                            "created_at": db_ai_message.created_at.isoformat()
                        }
                    }, conversation_id)

                    # Call the orchestrator
                    orchestrator_response = await orchestrator.handle_incoming_message(
                        db=db, # Pass the db session
                        user_id=current_user.id,
                        conversation_id=conversation_id,
                        message=data["message"],
                        context=task_context
                    )

                    # Turn off typing indicator
                    await manager.send_typing_indicator(conversation_id, False)

                    # Update the AI message with the final response
                    final_content = orchestrator_response.get("message", "No response generated.")
                    # Create metadata and sanitize it to handle any potential NaN values
                    final_metadata = {
                        "status": "completed",
                        "processing": False,
                        "streaming": False,
                        "complete": True,
                        **orchestrator_response.get("metadata", {})
                    }

                    # Sanitize metadata to ensure it's JSON serializable
                    sanitized_metadata = sanitize_json(final_metadata)

                    logger.info(f"Sanitized metadata for message {db_ai_message.id}")

                    updated_message = update_message(
                        db,
                        db_ai_message.id, # Use the ID from the created message
                        content=final_content,
                        metadata=sanitized_metadata
                    )

                    if updated_message:
                        logger.info(f"Updated AI message {updated_message.id} with orchestrator result")
                        # Broadcast the final message
                        await manager.broadcast({
                            "type": "ai_message_complete",
                            "message": {
                                "id": updated_message.id,
                                "conversation_id": updated_message.conversation_id,
                                "sender": updated_message.sender,
                                "content": updated_message.content,
                                "metadata": updated_message.message_metadata,
                                "created_at": updated_message.created_at.isoformat()
                            }
                        }, conversation_id)
                    else:
                         # Handle update failure (rare, but possible)
                         logger.error(f"Failed to update AI message {db_ai_message.id} after orchestrator call.")
                         # Optionally send an error message back
                         # Create error metadata and sanitize it
                         error_metadata = {
                             "error": "update_failed",
                             "status": "error",
                             "processing": False,
                             "streaming": False,
                             "complete": True
                         }

                         # Sanitize metadata to ensure it's JSON serializable
                         sanitized_error_metadata = sanitize_json(error_metadata)

                         await manager.broadcast({
                             "type": "ai_message_error",
                             "message": {
                                 "id": db_ai_message.id,
                                 "content": "Error updating final response.",
                                 "metadata": sanitized_error_metadata
                             }
                         }, conversation_id)

                    # Update conversation timestamp
                    update_conversation(db, conversation_id)

                except Exception as e:
                    logger.error(f"Error processing message: {str(e)}", exc_info=True)

                    # Turn off typing indicator
                    await manager.send_typing_indicator(conversation_id, False)

                    # Create an error message from the AI
                    error_message_id = str(uuid.uuid4())

                    # Create error metadata and sanitize it
                    error_metadata = {
                        "error": str(e),
                        "streaming": False,
                        "complete": True,
                        "error_type": e.__class__.__name__
                    }

                    # Sanitize metadata to ensure it's JSON serializable
                    sanitized_error_metadata = sanitize_json(error_metadata)

                    error_message_data = {
                        "id": error_message_id,
                        "conversation_id": conversation_id,
                        "sender": "ai",
                        "content": "An error occurred while processing your message. Please try again later.",
                        "metadata": sanitized_error_metadata
                    }

                    db_error_message = create_message(
                        db,
                        conversation_id=error_message_data["conversation_id"],
                        sender=error_message_data["sender"],
                        content=error_message_data["content"],
                        metadata=error_message_data.get("metadata", {})
                    )

                    # Send the error message to all connected clients
                    await manager.broadcast({
                        "type": "ai_message_error",
                        "message": {
                            "id": db_error_message.id,
                            "conversation_id": db_error_message.conversation_id,
                            "sender": db_error_message.sender,
                            "content": db_error_message.content,
                            "metadata": db_error_message.message_metadata,
                            "created_at": db_error_message.created_at.isoformat()
                        }
                    }, conversation_id)

                    # Update the conversation's updated_at timestamp
                    # Just trigger the updated_at timestamp without changing any data
                    update_conversation(db, conversation_id)
            elif "ping" in data:
                # Handle ping messages to keep the connection alive
                await websocket.send_json({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                })
            elif "reconnect" in data:
                # Handle reconnection requests
                await manager.broadcast({
                    "type": "reconnect_confirmation",
                    "conversation_id": conversation_id,
                    "timestamp": datetime.now().isoformat()
                }, conversation_id)
            elif "type" in data and data["type"] == "refresh_conversation":
                # Handle refresh conversation requests
                try:
                    # Get the latest messages from the database
                    messages = get_conversation_messages(db, conversation_id)

                    # Send the messages to the client
                    await websocket.send_json({
                        "type": "conversation_refreshed",
                        "conversation_id": conversation_id,
                        "messages": [
                            {
                                "id": message.id,
                                "conversation_id": message.conversation_id,
                                "sender": message.sender,
                                "content": message.content,
                                "metadata": message.message_metadata,
                                "created_at": message.created_at.isoformat()
                            }
                            for message in messages
                        ],
                        "timestamp": datetime.now().isoformat()
                    })

                    logger.info(f"Refreshed conversation {conversation_id} for user {current_user.id}")
                except Exception as e:
                    logger.error(f"Error refreshing conversation: {str(e)}", exc_info=True)
                    await websocket.send_json({
                        "type": "error",
                        "message": f"Error refreshing conversation: {str(e)}",
                        "timestamp": datetime.now().isoformat()
                    })
    except WebSocketDisconnect as e:
        logger.info(f"WebSocket disconnected for conversation {conversation_id} with code {e.code}")
        manager.disconnect(websocket, conversation_id)
    except Exception as e:
        logger.error(f"WebSocket error for conversation {conversation_id}: {str(e)}", exc_info=True)
        try:
            # Try to send an error message to the client before disconnecting
            await websocket.send_json({
                "type": "error",
                "message": f"Server error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            })
        except Exception:
            # If sending the error message fails, just log it
            logger.error(f"Failed to send error message to WebSocket for conversation {conversation_id}")

        # Disconnect the WebSocket
        manager.disconnect(websocket, conversation_id)
