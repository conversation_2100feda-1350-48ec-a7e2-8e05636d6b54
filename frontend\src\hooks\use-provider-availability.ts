import { useState, useEffect } from 'react';
import { providerApi } from '@/lib/api';

export interface ProviderAvailability {
  // User-specific provider availability
  userProviders: Record<string, boolean>;
  // Global provider availability (from .env file)
  globalProviders: Record<string, boolean>;
  // Combined availability (true if either user or global is available)
  combinedAvailability: Record<string, boolean>;
  // Loading state
  isLoading: boolean;
  // Error state
  error: string | null;
  // Refresh function
  refresh: () => Promise<void>;
}

/**
 * Hook to check for provider availability from both user-specific and global sources
 */
export function useProviderAvailability(): ProviderAvailability {
  const [userProviders, setUserProviders] = useState<Record<string, boolean>>({});
  const [globalProviders, setGlobalProviders] = useState<Record<string, boolean>>({});
  const [combinedAvailability, setCombinedAvailability] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProviderAvailability = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Fetching provider availability...');

      // Fetch user-specific provider availability
      console.log('Fetching user-specific provider availability...');
      const userResponse = await providerApi.getProviders();
      console.log('User provider response:', userResponse);

      const userAvailability: Record<string, boolean> = {};

      userResponse.providers.forEach(provider => {
        userAvailability[provider.id] = provider.is_available;
      });

      console.log('User provider availability:', userAvailability);
      setUserProviders(userAvailability);

      // Fetch global provider availability
      console.log('Fetching global provider availability...');
      let globalAvailability: Record<string, boolean> = {};
      try {
        globalAvailability = await providerApi.getGlobalAvailability();
        console.log('Global provider availability:', globalAvailability);
        setGlobalProviders(globalAvailability);
      } catch (globalError) {
        console.error('Error fetching global provider availability:', globalError);
        // Continue with empty global availability
        setGlobalProviders({});
      }

      // Combine the two sources
      const combined: Record<string, boolean> = {};

      // Include all provider IDs from both sources
      const allProviderIds = new Set([
        ...Object.keys(userAvailability),
        ...Object.keys(globalAvailability)
      ]);

      console.log('All provider IDs:', Array.from(allProviderIds));

      // A provider is available if it's available in either source
      allProviderIds.forEach(providerId => {
        combined[providerId] =
          (userAvailability[providerId] || false) ||
          (globalAvailability[providerId] || false);
      });

      console.log('Combined provider availability:', combined);
      setCombinedAvailability(combined);
    } catch (err) {
      console.error('Error fetching provider availability:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');

      // Set empty objects as fallback
      setUserProviders({});
      setGlobalProviders({});
      setCombinedAvailability({});
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch provider availability on mount
  useEffect(() => {
    fetchProviderAvailability();
  }, []);

  return {
    userProviders,
    globalProviders,
    combinedAvailability,
    isLoading,
    error,
    refresh: fetchProviderAvailability
  };
}
