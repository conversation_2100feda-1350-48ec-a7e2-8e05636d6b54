import React from 'react';
import { Loader2 } from 'lucide-react';
import { ProviderModel } from '@/lib/providerApi';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ModelSelectProps {
  isLoadingModels: boolean;
  providerModels: ProviderModel[];
  value: string;
  onChange: (value: string) => void;
  inputId?: string;
  placeholder?: string;
}

const ModelSelect: React.FC<ModelSelectProps> = ({
  isLoadingModels,
  providerModels,
  value,
  onChange,
  inputId = 'model',
  placeholder = 'e.g., llama3-8b-8192',
}) => {
  if (isLoadingModels) {
    return (
      <div className="flex items-center space-x-2 h-10 px-3 border rounded-md bg-blue-50 border-blue-200 text-blue-700">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm">Loading available models...</span>
      </div>
    );
  }

  if (providerModels.length > 0) {
    return (
      <Select
        name="model"
        value={value || ""}
        onValueChange={onChange}
      >
        <SelectTrigger className="bg-white border-gray-300 hover:bg-gray-50 transition-colors shadow-sm">
          {value ? (
            <div className="flex items-center">
              <div className="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-700">
                  <path d="M12 2c1.7 0 3.3.2 4.6.6a12 12 0 0 1 7 7c.3 1.1.4 2.3.4 3.4 0 5-1.3 8-4.3 9.7A12 12 0 0 1 12 22a12 12 0 0 1-9.6-4.6 12 12 0 0 1 0-14.8A12 12 0 0 1 12 2Z"></path>
                  <path d="M12 22c-1.7 0-3.3-.2-4.6-.6a12 12 0 0 1-7-7 12 12 0 0 1 0-4.8c.9.5 2.3.8 3.6.9"></path>
                  <path d="m9 10 3-1 3 1"></path>
                  <path d="M12 9v4"></path>
                  <path d="m11 14 2 2 4-4"></path>
                </svg>
              </div>
              <div className="flex flex-col">
                <span className="font-medium text-sm">
                  {(() => {
                    const modelName = providerModels.find(m => m.id === value)?.name || value;
                    // Remove the (Unknown) label if present
                    return modelName.replace(" (Unknown)", "");
                  })()}
                </span>
              </div>
            </div>
          ) : (
            <SelectValue placeholder="Select model" />
          )}
        </SelectTrigger>
        <SelectContent>
          {(() => {
            // Group models by category if available
            const hasCategories = providerModels.some(model => model.category);

            if (hasCategories) {
              // Get unique categories
              const categories = [...new Set(providerModels.map(model => model.category || 'other'))];

              // Sort categories in a logical order
              const categoryOrder = ['chat', 'embedding', 'image', 'audio', 'other'];
              categories.sort((a, b) => {
                const indexA = categoryOrder.indexOf(a);
                const indexB = categoryOrder.indexOf(b);
                return (indexA === -1 ? 999 : indexA) - (indexB === -1 ? 999 : indexB);
              });

              // Render models grouped by category
              return categories.map(category => (
                <div key={category}>
                  <SelectLabel className="px-2 py-1.5 text-xs font-semibold bg-muted/50 w-full">
                    {category.charAt(0).toUpperCase() + category.slice(1)} Models
                  </SelectLabel>
                  {providerModels
                    .filter(model => (model.category || 'other') === category)
                    .map(model => (
                      <SelectItem key={model.id} value={model.id}>
                        <div className="flex flex-col py-1">
                          <span className="font-medium">{model.name.replace(" (Unknown)", "")}</span>
                          {model.description && (
                            <span className="text-xs text-muted-foreground truncate max-w-[300px] mt-0.5">
                              {model.description}
                            </span>
                          )}
                          <div className="flex flex-wrap gap-2 mt-1">
                            {model.pricing && model.pricing.input && (
                              <span className="text-xs bg-green-50 text-green-700 px-2 py-0.5 rounded-full">
                                Input: ${(model.pricing.input.prompt * 1000).toFixed(2)}/1K
                              </span>
                            )}
                            {model.pricing && model.pricing.output && (
                              <span className="text-xs bg-amber-50 text-amber-700 px-2 py-0.5 rounded-full">
                                Output: ${(model.pricing.output.completion * 1000).toFixed(2)}/1K
                              </span>
                            )}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                </div>
              ));
            } else {
              // Render models without categories
              return providerModels.map(model => (
                <SelectItem key={model.id} value={model.id}>
                  <div className="flex flex-col py-1">
                    <span className="font-medium">{model.name.replace(" (Unknown)", "")}</span>
                    {model.description && (
                      <span className="text-xs text-muted-foreground truncate max-w-[300px] mt-0.5">
                        {model.description}
                      </span>
                    )}
                    <div className="flex flex-wrap gap-2 mt-1">
                      {model.pricing && model.pricing.input && (
                        <span className="text-xs bg-green-50 text-green-700 px-2 py-0.5 rounded-full">
                          Input: ${(model.pricing.input.prompt * 1000).toFixed(2)}/1K
                        </span>
                      )}
                      {model.pricing && model.pricing.output && (
                        <span className="text-xs bg-amber-50 text-amber-700 px-2 py-0.5 rounded-full">
                          Output: ${(model.pricing.output.completion * 1000).toFixed(2)}/1K
                        </span>
                      )}
                    </div>
                  </div>
                </SelectItem>
              ));
            }
          })()}
        </SelectContent>
      </Select>
    );
  } else {
    // If no models are available but we're not loading, show a message
    if (!isLoadingModels) {
      return (
        <div className="flex items-center space-x-2 h-10 px-3 border rounded-md bg-amber-50 border-amber-200 text-amber-800">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-alert-circle">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
          <span className="text-sm">
            No models available. Please check your API key configuration.
          </span>
        </div>
      );
    }
  }

  return (
    <Input
      id={inputId}
      name="model"
      placeholder={placeholder}
      value={value || ""}
      onChange={(e) => onChange(e.target.value)}
    />
  );
};

export default ModelSelect;
