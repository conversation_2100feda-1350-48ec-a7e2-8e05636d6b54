"""
Component for providing welcome messages and initial guidance for the concierge agent.
"""

import logging
from typing import Dict, Any, List, Optional # Added Optional

from .base import AgentComponent
from backend.schemas.agent_config_schemas import AgentProcessingContext # Added

logger = logging.getLogger(__name__)


class ConciergeWelcomeComponent(AgentComponent):
    """
    Provides initial greeting and explains available personas to users.
    """

    def __init__(self):
        """Initialize the ConciergeWelcomeComponent."""
        super().__init__()

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"ConciergeWelcomeComponent '{self.name}' initialized.")
        self.greeting_message = config.get("greeting_message", 
            "Hello! I'm the Datagenius Concierge. How can I help you today? "
            "I can help you find the right AI persona for your task or assist with your data.")

    async def process(self, context: AgentProcessingContext) -> AgentProcessingContext:
        """
        Process the context to provide a welcome message if this is the first message in a conversation.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object with welcome message if appropriate.
        """
        # Check if this is the first message in a conversation
        # Conversation history might be in context.initial_context or context.component_data
        # Assuming it's passed in initial_context for now.
        conversation_history = context.initial_context.get("conversation_history", [])
        is_first_message = not conversation_history # True if empty or None
        
        # Also check if a response has already been set by a previous component,
        # or if this component has already run (e.g. by checking a flag in context.metadata)
        if context.response or context.metadata.get("welcome_message_sent"):
            return context

        if is_first_message:
            logger.info("Providing welcome message for new conversation")
            
            # Get the user's name if available from initial_context
            user_info = context.initial_context.get("user_info", {})
            user_name = user_info.get("name", "")
            
            greeting = f"Hello {user_name}! " if user_name else "Hello! "
            welcome_message = greeting + self.greeting_message
            
            personas_info = self._get_personas_info(context) # Pass the full AgentProcessingContext
            if personas_info:
                welcome_message += f"\n\n{personas_info}"
            
            context.response = welcome_message
            context.metadata["is_welcome_message"] = True
            context.metadata["welcome_message_sent"] = True # Flag to prevent re-sending
        
        return context

    def _get_personas_info(self, context: AgentProcessingContext) -> str:
        """
        Get information about available personas for the user.

        Args:
            context: The current AgentProcessingContext.

        Returns:
            A string with information about available personas.
        """
        # user_id is directly available in AgentProcessingContext
        user_id = context.user_id 
        logger.debug(f"Getting persona info for user_id: {user_id}")
        
        # This is a placeholder - in a real implementation, we would fetch the user's
        # purchased personas from a service or database using user_id.
        return (
            "I can help you work with these AI personas:\n\n"
            "- **Composable Analyst**: For data analysis, visualization, and insights\n"
            "- **Composable Marketer**: For creating marketing content and strategies\n"
            "- **Composable Classifier**: For categorizing and organizing content\n\n"
            "What would you like help with today?"
        )

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["welcome", "guidance"])
