"""
Component for providing welcome messages and initial guidance for the concierge agent.
"""

import logging
from typing import Dict, Any, List

from .base import AgentComponent

logger = logging.getLogger(__name__)


class ConciergeWelcomeComponent(AgentComponent):
    """
    Provides initial greeting and explains available personas to users.
    """

    def __init__(self):
        """Initialize the ConciergeWelcomeComponent."""
        super().__init__()

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"ConciergeWelcomeComponent '{self.name}' initialized.")
        self.greeting_message = config.get("greeting_message", 
            "Hello! I'm the Datagenius Concierge. How can I help you today? "
            "I can help you find the right AI persona for your task or assist with your data.")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the context to provide a welcome message if this is the first message in a conversation.

        Args:
            context: Context dictionary containing request data.

        Returns:
            Updated context dictionary with welcome message if appropriate.
        """
        # Check if this is the first message in a conversation
        conversation_history = context.get("conversation_history", [])
        is_first_message = len(conversation_history) == 0
        
        # If this is the first message, provide a welcome
        if is_first_message:
            logger.info("Providing welcome message for new conversation")
            
            # Get the user's name if available
            user_info = context.get("user_info", {})
            user_name = user_info.get("name", "")
            
            # Personalize greeting if we have the user's name
            greeting = f"Hello {user_name}! " if user_name else "Hello! "
            
            # Construct the full welcome message
            welcome_message = greeting + self.greeting_message
            
            # Add information about available personas
            personas_info = self._get_personas_info(context)
            if personas_info:
                welcome_message += f"\n\n{personas_info}"
            
            # Set the response
            context["response"] = welcome_message
            
            # Add metadata
            context["metadata"] = context.get("metadata", {})
            context["metadata"]["is_welcome_message"] = True
        
        return context

    def _get_personas_info(self, context: Dict[str, Any]) -> str:
        """
        Get information about available personas for the user.

        Args:
            context: The current context.

        Returns:
            A string with information about available personas.
        """
        # In a real implementation, this would get the user's purchased personas
        # For now, we'll use a placeholder
        user_id = context.get("user_id")
        
        # This is a placeholder - in a real implementation, we would fetch the user's
        # purchased personas from a service or database
        return (
            "I can help you work with these AI personas:\n\n"
            "- **Composable Analyst**: For data analysis, visualization, and insights\n"
            "- **Composable Marketer**: For creating marketing content and strategies\n"
            "- **Composable Classifier**: For categorizing and organizing content\n\n"
            "What would you like help with today?"
        )

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["welcome", "guidance"])
