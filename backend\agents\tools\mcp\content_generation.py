"""
Content generation MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for generating marketing content.
"""

import logging
import os
import json
from typing import Dict, Any, List, Optional

# Import HumanMessage
from langchain_core.messages import HumanMessage

from .base import BaseMCPTool
from agents.utils.model_providers.utils import get_model
from agents.utils.prompt_template import PromptTemplate
from agents.utils.template_processor import format_data_profile

logger = logging.getLogger(__name__)


class ContentGenerationTool(BaseMCPTool):
    """Tool for generating marketing content."""

    def __init__(self):
        """Initialize the content generation tool."""
        super().__init__(
            name="generate_content",
            description="Generate marketing content based on specified parameters",
            input_schema={
                "type": "object",
                "properties": {
                    "content_type": {
                        "type": "string",
                        "enum": [
                            "marketing_strategy",
                            "campaign_strategy",
                            "social_media_content",
                            "seo_optimization",
                            "post_composer",
                            "greeting", # Added for initial message
                            "analysis_response", # Added for analysis agent responses
                            "data_profile" # Added for data profile generation
                        ],
                        "description": "Type of content to generate"
                    },
                    "brand_description": {
                        "type": "string",
                        "description": "Description of the brand"
                    },
                    "target_audience": {
                        "type": "string",
                        "description": "Description of the target audience"
                    },
                    "products_services": {
                        "type": "string",
                        "description": "Description of products or services"
                    },
                    "marketing_goals": {
                        "type": "string",
                        "description": "Marketing goals"
                    },
                    "existing_content": {
                        "type": "string",
                        "description": "Existing marketing content"
                    },
                    "keywords": {
                        "type": "string",
                        "description": "Keywords to target"
                    },
                    "suggested_topics": {
                        "type": "string",
                        "description": "Suggested topics"
                    },
                    "tone": {
                        "type": "string",
                        "description": "Tone of the content"
                    },
                    "provider": {
                        "type": "string",
                        "description": "AI provider to use (e.g., groq, openai, anthropic)"
                    },
                    "model": {
                        "type": "string",
                        "description": "Model name to use"
                    },
                    "temperature": {
                        "type": "number",
                        "description": "Temperature for content generation"
                    },
                    "is_first_conversation": {
                        "type": "boolean",
                        "description": "Whether this is the first conversation with the user"
                    },
                    "has_data_source": {
                        "type": "boolean",
                        "description": "Whether the user has provided a data source"
                    },
                    "prompt_override": {
                        "type": "string",
                        "description": "Optional raw prompt to use instead of templates"
                    }
                },
                "required": ["content_type"] # Keep content_type required for structure, even if overridden
            },
            annotations={
                "title": "Generate Content",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.prompt_templates = {}

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # Load prompt templates if provided
        if "prompt_templates" in config:
            self.prompt_templates = config["prompt_templates"]
            logger.info(f"Loaded {len(self.prompt_templates)} prompt templates")

        # Ensure we have default templates for essential content types
        if "default" not in self.prompt_templates:
            self.prompt_templates["default"] = "You are a helpful AI assistant. Please provide a thoughtful response to the user's request."
            logger.info("Added default prompt template")

        if "greeting" not in self.prompt_templates:
            self.prompt_templates["greeting"] = "Hello! I'm your AI assistant. How can I help you today?"
            logger.info("Added greeting prompt template")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            content_type = arguments["content_type"]
            provider_id = arguments.get("provider", "groq")
            model_name = arguments.get("model", "llama3-70b-8192")
            temperature = arguments.get("temperature", 0.7)

            # Extract content parameters (provide defaults)
            brand_description = arguments.get("brand_description", "")
            target_audience = arguments.get("target_audience", "")
            products_services = arguments.get("products_services", "")
            marketing_goals = arguments.get("marketing_goals", "")
            existing_content = arguments.get("existing_content", "")
            keywords = arguments.get("keywords", "")
            suggested_topics = arguments.get("suggested_topics", "")
            tone = arguments.get("tone", "Professional")
            is_first_conversation = arguments.get("is_first_conversation", False)
            has_data_source = arguments.get("has_data_source", False)

            # Check if this is a regeneration request
            is_regeneration = arguments.get("is_regeneration", False)
            if is_regeneration:
                logger.info("This is a regeneration request in ContentGenerationTool")
                # For regeneration, we might want to adjust the prompt slightly
                # to encourage more variation in the output

            # Debug log all arguments to see what's being passed
            logger.info(f"CONTENT GENERATION ARGUMENTS: {json.dumps(arguments, indent=2)}")
            logger.info(f"EXTRACTED PARAMETERS:")
            logger.info(f"- brand_description: {brand_description}")
            logger.info(f"- target_audience: {target_audience}")
            logger.info(f"- products_services: {products_services}")
            logger.info(f"- marketing_goals: {marketing_goals}")
            logger.info(f"- existing_content: {existing_content}")
            logger.info(f"- keywords: {keywords}")
            logger.info(f"- suggested_topics: {suggested_topics}")
            logger.info(f"- tone: {tone}")
            logger.info(f"- is_first_conversation: {is_first_conversation}")
            logger.info(f"- has_data_source: {has_data_source}")

            # Initialize LLM client using the model provider system
            try:
                llm = await get_model(provider_id, model_name, {"temperature": temperature})
                logger.info(f"Successfully initialized model from provider '{provider_id}'")
            except Exception as e:
                logger.error(f"Error initializing model: {str(e)}", exc_info=True)
                raise ValueError(f"Failed to initialize model: {str(e)}")

            # Get the appropriate prompt template or use override
            prompt_text = await self._get_prompt_template(
                content_type,
                brand_description,
                target_audience,
                products_services,
                marketing_goals,
                existing_content,
                keywords,
                suggested_topics,
                tone,
                is_first_conversation,
                has_data_source,
                is_regeneration
            )

            # Check for prompt override
            final_prompt = arguments.get("prompt_override", prompt_text)
            if arguments.get("prompt_override"):
                logger.info("Using provided prompt_override instead of template.")

            # Generate content
            try:
                # Wrap the prompt string in a HumanMessage object for LangChain compatibility
                input_message = HumanMessage(content=final_prompt) # Use final_prompt

                # Log the model type and available methods
                logger.info(f"MODEL TYPE: {type(llm).__name__}")
                logger.info(f"MODEL METHODS: {[method for method in dir(llm) if not method.startswith('_') and callable(getattr(llm, method))]}")

                # Log the input message being sent to the model
                logger.info(f"INPUT MESSAGE TYPE: {type(input_message).__name__}")
                logger.info(f"INPUT MESSAGE CONTENT (first 200 chars): {str(input_message.content)[:200]}...")

                # Determine which method to call based on the model type
                # Prefer ainvoke as it's the standard LCEL interface
                try:
                    if hasattr(llm, 'ainvoke'):
                        logger.info("USING ainvoke METHOD")
                        response = await llm.ainvoke([input_message])
                        # Extract content, handling potential variations in response structure
                        generated_content = response.content if hasattr(response, 'content') else str(response)
                        logger.info(f"RESPONSE TYPE FROM ainvoke: {type(response).__name__}")
                        logger.info(f"RESPONSE HAS CONTENT ATTRIBUTE: {hasattr(response, 'content')}")
                    elif hasattr(llm, 'agenerate'):
                        # Fallback for older models
                        logger.info("USING agenerate METHOD")
                        response = await llm.agenerate([[input_message]]) # Note: agenerate expects List[List[Message]]
                        generated_content = response.generations[0][0].text
                        logger.info(f"RESPONSE TYPE FROM agenerate: {type(response).__name__}")
                    elif hasattr(llm, 'agenerate_prompt'):
                        # Some models use agenerate_prompt
                        logger.info("USING agenerate_prompt METHOD")
                        response = await llm.agenerate_prompt([input_message])
                        generated_content = response.generations[0][0].text
                        logger.info(f"RESPONSE TYPE FROM agenerate_prompt: {type(response).__name__}")
                    elif hasattr(llm, 'agenerate_text'):
                         # Less common, but handle if present
                         logger.info("USING agenerate_text METHOD")
                         # Try with the message content as a string
                         generated_content = await llm.agenerate_text(final_prompt)
                         logger.info(f"RESPONSE TYPE FROM agenerate_text: {type(generated_content).__name__}")
                    else:
                        # If no standard async method, try to inspect the model more deeply
                        logger.error(f"Model {llm.__class__.__name__} does not have a standard async generation method")
                        logger.error(f"Available methods: {dir(llm)}")
                        raise NotImplementedError("Unsupported model interface for async generation.")
                except Exception as e:
                    logger.error(f"Error during model invocation: {str(e)}", exc_info=True)

                    # Try to get more detailed error information
                    error_details = ""
                    if hasattr(e, "__cause__") and e.__cause__:
                        error_details += f" Caused by: {str(e.__cause__)}"

                    # Check for common connection errors
                    if "connection" in str(e).lower() or "timeout" in str(e).lower() or "socket" in str(e).lower() or "websocket" in str(e).lower():
                        error_message = "Connection error while contacting the AI provider. Please try again in a moment."
                        raise ValueError(f"{error_message} Technical details: {str(e)}{error_details}")

                    # Re-raise with more context
                    raise ValueError(f"Error generating content: {str(e)}{error_details}")

                logger.info(f"Generated content for type: {content_type}")
            except Exception as e:
                logger.error(f"Error generating content: {str(e)}", exc_info=True)

                # Check for common connection errors in the outer exception handler as well
                if "connection" in str(e).lower() or "timeout" in str(e).lower() or "socket" in str(e).lower() or "websocket" in str(e).lower():
                    raise ValueError("Connection error while contacting the AI provider. Please try again in a moment.")
                else:
                    raise ValueError(f"Failed to generate content: {str(e)}")

            # Post-process data profile content if needed
            if content_type == "data_profile" and "metadata" in arguments:
                # Extract metadata from arguments
                data_metadata = arguments.get("metadata", {})
                logger.info(f"Post-processing data profile with metadata: {data_metadata}")

                # Format the data profile with actual values from metadata
                try:
                    # Use the template processor to replace placeholders with actual values
                    processed_content = format_data_profile(generated_content, data_metadata)
                    logger.info("Successfully processed data profile with actual values")
                    generated_content = processed_content
                except Exception as e:
                    logger.error(f"Error processing data profile: {str(e)}", exc_info=True)
                    # Continue with the original content if processing fails

            return {
                "content": [
                    {
                        "type": "text",
                        "text": generated_content
                    }
                ],
                "metadata": {
                    "content_type": content_type,
                    "parameters": {
                        "brand_description": brand_description[:100] + "..." if len(brand_description) > 100 else brand_description,
                        "target_audience": target_audience[:100] + "..." if len(target_audience) > 100 else target_audience,
                        "products_services": products_services[:100] + "..." if len(products_services) > 100 else products_services,
                        "marketing_goals": marketing_goals[:100] + "..." if len(marketing_goals) > 100 else marketing_goals,
                        "tone": tone
                    }
                }
            }

        except Exception as e:
            logger.error(f"Error in ContentGenerationTool execute: {str(e)}", exc_info=True)
            error_message = f"Error generating content: {str(e)}"

            # Add more detailed error information for debugging
            if hasattr(e, "__cause__") and e.__cause__:
                error_message += f" Caused by: {str(e.__cause__)}"

            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": error_message
                    }
                ],
                "metadata": {
                    "error_type": e.__class__.__name__,
                    "error_details": str(e),
                    "component": "ContentGenerationTool"
                }
            }

    async def _get_prompt_template(
        self,
        content_type: str,
        brand_description: str,
        target_audience: str,
        products_services: str,
        marketing_goals: str,
        existing_content: str,
        keywords: str,
        suggested_topics: str,
        tone: str,
        is_first_conversation: bool,
        has_data_source: bool,
        is_regeneration: bool = False
    ) -> str:
        """
        Get the appropriate prompt template for the content type.

        Args:
            content_type: Type of content to generate
            brand_description: Description of the brand
            target_audience: Description of the target audience
            products_services: Description of products or services
            marketing_goals: Marketing goals
            existing_content: Existing marketing content
            keywords: Keywords to target
            suggested_topics: Suggested topics
            tone: Tone of the content
            is_first_conversation: Whether this is the first conversation
            has_data_source: Whether the user has provided a data source

        Returns:
            Formatted prompt text
        """
        # Map content types to prompt template names
        prompt_mapping = {
            "marketing_strategy": "marketing_strategy",
            "campaign_strategy": "campaign_strategy",
            "social_media_content": "social_media",
            "seo_optimization": "seo_optimization",
            "post_composer": "post_composer",
            "greeting": "default", # Map greeting type to default prompt
            "analysis_response": "default", # Map analysis response to default prompt
            "data_profile": "data_profile" # Map data profile to data_profile template
        }

        prompt_name = prompt_mapping.get(content_type)
        is_fallback = False

        # Check if we have a template for this content type
        if prompt_name and prompt_name in self.prompt_templates:
            # Format the prompt template with parameters
            try:
                # Debug log the values being used for formatting
                logger.info(f"FORMATTING PROMPT TEMPLATE '{prompt_name}' WITH VALUES:")
                logger.info(f"- brand_description: '{brand_description}'")
                logger.info(f"- target_audience: '{target_audience}'")
                logger.info(f"- products_services: '{products_services}'")
                logger.info(f"- marketing_goals: '{marketing_goals}'")
                logger.info(f"- existing_content: '{existing_content}'")
                logger.info(f"- keywords: '{keywords}'")
                logger.info(f"- suggested_topics: '{suggested_topics}'")
                logger.info(f"- tone: '{tone}'")
                logger.info(f"- is_regeneration: {is_regeneration}")

                # For regeneration, add a note to the prompt to encourage variation
                regeneration_note = ""
                if is_regeneration:
                    regeneration_note = "\n\nNOTE: This is a request to regenerate content. Please provide a fresh perspective or alternative approach while maintaining the core requirements."

                # Use the values as-is without adding defaults
                # This ensures we use the actual form data
                if not brand_description:
                    logger.warning("Empty brand_description received, using as-is")
                if not target_audience:
                    logger.warning("Empty target_audience received, using as-is")
                if not products_services:
                    logger.warning("Empty products_services received, using as-is")
                if not marketing_goals:
                    logger.warning("Empty marketing_goals received, using as-is")
                if not existing_content:
                    logger.warning("Empty existing_content received, using as-is")
                if not keywords:
                    logger.warning("Empty keywords received, using as-is")
                if not suggested_topics:
                    logger.warning("Empty suggested_topics received, using as-is")
                if not tone:
                    logger.warning("Empty tone received, using default: Professional")
                    tone = "Professional"  # Only set default for tone as it's required

                # Log the values being used
                logger.info(f"USING VALUES AS-IS:")
                logger.info(f"- brand_description: '{brand_description}'")
                logger.info(f"- target_audience: '{target_audience}'")
                logger.info(f"- products_services: '{products_services}'")
                logger.info(f"- marketing_goals: '{marketing_goals}'")
                logger.info(f"- existing_content: '{existing_content}'")
                logger.info(f"- keywords: '{keywords}'")
                logger.info(f"- suggested_topics: '{suggested_topics}'")
                logger.info(f"- tone: '{tone}'")

                template = PromptTemplate(self.prompt_templates[prompt_name])
                prompt_text = template.format(
                    brand_description=brand_description,
                    target_audience=target_audience,
                    products_services=products_services,
                    marketing_goals=marketing_goals,
                    existing_content=existing_content,
                    keywords=keywords,
                    suggested_topics=suggested_topics,
                    tone=tone,
                    is_first_conversation=is_first_conversation,
                    has_data_source=has_data_source
                )

                # Add regeneration note if this is a regeneration request
                if is_regeneration:
                    prompt_text += regeneration_note

                # Log the formatted prompt for debugging
                logger.info(f"FORMATTED PROMPT (first 200 chars): {prompt_text[:200]}...")
            except Exception as e:
                logger.error(f"Error formatting prompt template '{prompt_name}': {str(e)}", exc_info=True)
                is_fallback = True
        else:
            logger.warning(f"Prompt template '{prompt_name}' not found for content type '{content_type}'. Falling back.")
            is_fallback = True

        if is_fallback:
            # Fall back to hardcoded templates (or a generic default)
            # Using a simple default for fallback if specific type isn't found
            default_fallback_prompt = "Please generate content based on the provided details."
            templates = {
                "marketing_strategy": """
                    You are a strategic marketing consultant tasked with developing a comprehensive marketing strategy.

                    BRAND INFORMATION:
                    - Brand Description: {brand_description}
                    - Target Audience: {target_audience}
                    - Products/Services: {products_services}
                    - Marketing Goals: {marketing_goals}
                    - Existing Content: {existing_content}
                    - Keywords: {keywords}
                    - Suggested Topics: {suggested_topics}
                    - Tone: {tone}

                    Create a comprehensive marketing strategy that includes:
                    1. Executive Summary
                    2. Market Analysis
                    3. Target Audience Analysis
                    4. Competitive Analysis
                    5. Marketing Objectives
                    6. Marketing Tactics and Channels
                    7. Content Strategy
                    8. Budget Considerations
                    9. Timeline and Implementation Plan
                    10. KPIs and Success Metrics

                    Ensure the strategy is specific to the brand, practical, and actionable.
                """,
                "campaign_strategy": """
                    You are a creative campaign director tasked with developing innovative marketing campaigns.

                    BRAND INFORMATION:
                    - Brand Description: {brand_description}
                    - Target Audience: {target_audience}
                    - Products/Services: {products_services}
                    - Marketing Goals: {marketing_goals}
                    - Existing Content: {existing_content}
                    - Keywords: {keywords}
                    - Suggested Topics: {suggested_topics}
                    - Tone: {tone}

                    Generate 5 distinct, creative campaign concepts that align with the brand identity and will resonate with the target audience.
                    Each campaign should be achievable with realistic resources and have clear business impact.

                    For each of the 5 campaigns, provide:

                    ### Campaign [Number]: [Creative Name]
                    * Concept: Brief explanation of the campaign idea and creative angle
                    * Target Segment: Specific audience segment this will appeal to most
                    * Core Message: The primary takeaway for the audience
                    * Campaign Elements: List of deliverables (videos, posts, emails, etc.)
                    * Channels: Primary platforms for distribution
                    * Timeline: Suggested duration and key milestones
                    * Success Metrics: How to measure campaign effectiveness
                    * Estimated Impact: Expected outcomes tied to marketing goals
                """,
                "seo_optimization": """
                    You are an SEO specialist developing a comprehensive search optimization strategy.

                    BRAND INFORMATION:
                    - Brand Description: {brand_description}
                    - Target Audience: {target_audience}
                    - Products/Services: {products_services}
                    - Marketing Goals: {marketing_goals}
                    - Keywords: {keywords}
                    - Existing Content: {existing_content}
                    - Suggested Topics: {suggested_topics}
                    - Tone: {tone}

                    Create a detailed SEO strategy that will improve organic visibility and drive qualified traffic.
                    Focus on both quick wins and long-term sustainable growth.
                    Provide specific, actionable recommendations rather than general advice.

                    1. Keyword Strategy
                       * Primary Keywords (5-7 high-priority terms with search volume estimates)
                       * Secondary Keywords (10-15 supporting terms)
                       * Long-tail Opportunities (7-10 specific phrases)
                       * Semantic/Topic Clusters (group related terms by topic)

                    2. On-Page Optimization
                       * Title Tag Templates
                       * Meta Description Frameworks
                       * Heading Structure Recommendations
                       * Content Length and Formatting Guidelines
                       * Internal Linking Strategy

                    3. Technical SEO Checklist
                       * Site Speed Optimization
                       * Mobile Usability
                       * Schema Markup Recommendations
                       * Indexation Controls
                       * URL Structure Guidelines

                    4. Content Strategy
                       * Content Gaps Analysis
                       * Content Update Priorities
                       * New Content Recommendations (5-7 specific pieces)
                       * Content Calendar Framework

                    5. Off-Page Strategy
                       * Link Building Tactics (specific to industry)
                       * Digital PR Opportunities
                       * Local Citation Opportunities (if applicable)

                    6. Measurement Plan
                       * Key Performance Indicators
                """,
                "analysis_response": """
                    You are Composable Analyst, a specialized AI for data analysis and visualization.

                    Respond to the user's request about data analysis. Be helpful, informative, and provide specific insights.

                    If the user has attached a data source, mention that you'll analyze it and provide insights based on the data.
                    If no data source is attached, suggest that the user attach one using the 'Attach Data' button.

                    Provide a structured response that includes:
                    1. Acknowledgment of the user's request
                    2. Initial observations or approach to the analysis
                    3. Specific steps you'll take to analyze the data
                    4. Potential insights or visualizations you might generate
                    5. Any clarifying questions you might have

                    Keep your response concise, professional, and focused on helping the user understand their data better.
                """,
                 "post_composer": """
                    You are a content creator tasked with writing social media posts.

                    BRAND INFORMATION:
                    - Brand Description: {brand_description}
                    - Target Audience: {target_audience}
                    - Products/Services: {products_services}
                    - Marketing Goals: {marketing_goals}
                    - Existing Content: {existing_content}
                    - Keywords: {keywords}
                    - Suggested Topics: {suggested_topics}
                    - Tone: {tone}

                    Create 5 engaging social media posts that include:
                    1. Post text (appropriate length for the platform)
                    2. Hashtags (where appropriate)
                    3. Call to action
                    4. Image/video description suggestion

                    Create posts for these platforms: Instagram, Facebook, Twitter, LinkedIn

                    Ensure the posts are engaging, on-brand, and designed to drive engagement and conversions.
                """
            }

            # Get the template for the content type or use a default fallback
            template_text = templates.get(content_type, default_fallback_prompt)

            # Format the template (handle potential missing keys gracefully)
            try:
                # Debug log the values being used for formatting
                logger.info(f"FORMATTING TEMPLATE WITH VALUES:")
                logger.info(f"- brand_description: '{brand_description}'")
                logger.info(f"- target_audience: '{target_audience}'")
                logger.info(f"- products_services: '{products_services}'")
                logger.info(f"- marketing_goals: '{marketing_goals}'")
                logger.info(f"- existing_content: '{existing_content}'")
                logger.info(f"- keywords: '{keywords}'")
                logger.info(f"- suggested_topics: '{suggested_topics}'")
                logger.info(f"- tone: '{tone}'")

                # Use the values as-is without adding defaults
                # This ensures we use the actual form data

                # Format the template with the values
                prompt_text = template_text.format(
                    brand_description=brand_description,
                    target_audience=target_audience,
                    products_services=products_services,
                    marketing_goals=marketing_goals,
                    existing_content=existing_content,
                    keywords=keywords,
                    suggested_topics=suggested_topics,
                    tone=tone,
                    # Add defaults for potentially missing keys in fallback
                    is_first_conversation=is_first_conversation,
                    has_data_source=has_data_source
                )

                # Add regeneration note if this is a regeneration request
                if is_regeneration:
                    prompt_text += regeneration_note

                # Log the formatted prompt for debugging
                logger.info(f"FORMATTED PROMPT (first 200 chars): {prompt_text[:200]}...")
            except KeyError as e:
                 logger.error(f"KeyError formatting fallback template for {content_type}: {e}. Using raw template.", exc_info=True)
                 prompt_text = template_text # Use raw template if formatting fails

                 # Add regeneration note if this is a regeneration request
                 if is_regeneration:
                     prompt_text += regeneration_note

        return prompt_text
