"""
Task selector component for the Datagenius agent system.

This module provides a component for guiding users through task selection.
"""

import logging
import re
from typing import Dict, Any, List, Optional

from .base import AgentComponent

logger = logging.getLogger(__name__)


class TaskSelectorComponent(AgentComponent):
    """Component for guiding users through task selection."""

    def __init__(self):
        """Initialize the task selector component."""
        super().__init__()
        self.tasks = {}
        self.task_descriptions = {}

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        # Register tasks from configuration
        if "tasks" in config:
            for task_config in config["tasks"]:
                task_id = task_config.get("id")
                task_name = task_config.get("name", task_id)
                task_description = task_config.get("description", "")
                
                if not task_id:
                    logger.warning("Task configuration missing 'id'")
                    continue
                
                # Register the task
                self.tasks[task_id] = {
                    "name": task_name,
                    "description": task_description,
                    "prompt_template": task_config.get("prompt_template"),
                    "required_data": task_config.get("required_data", []),
                    "metadata": task_config.get("metadata", {})
                }
                
                # Add to task descriptions for display
                self.task_descriptions[task_id] = f"{task_name}: {task_description}"
                
                logger.info(f"Registered task '{task_id}': {task_name}")
        else:
            # Default marketing tasks if none provided
            default_tasks = [
                {
                    "id": "marketing_strategy",
                    "name": "Marketing Strategy",
                    "description": "Create a comprehensive marketing strategy for your business"
                },
                {
                    "id": "campaign_strategy",
                    "name": "Campaign Strategy",
                    "description": "Develop campaign concepts and plans for specific initiatives"
                },
                {
                    "id": "social_media",
                    "name": "Social Media Content Strategy",
                    "description": "Create a social media content strategy and posting plan"
                },
                {
                    "id": "seo_optimization",
                    "name": "SEO Optimization Strategy",
                    "description": "Optimize your content and website for search engines"
                },
                {
                    "id": "post_composer",
                    "name": "Post Composer",
                    "description": "Generate engaging social media posts for your brand"
                }
            ]
            
            for task in default_tasks:
                task_id = task["id"]
                self.tasks[task_id] = task
                self.task_descriptions[task_id] = f"{task['name']}: {task['description']}"
                logger.info(f"Registered default task '{task_id}': {task['name']}")

        logger.info(f"Initialized task selector with {len(self.tasks)} tasks")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        message = context.get("message", "").lower().strip()
        
        # Check if this is the first message in the conversation
        is_first_message = context.get("context", {}).get("is_first_message", False)
        
        # If this is the first message, present task options
        if is_first_message:
            task_list = "\n".join([f"{i+1}. {desc}" for i, desc in enumerate(self.task_descriptions.values())])
            
            response = f"""
Welcome to the Marketing AI! I'm here to help you with your marketing needs.

Please select one of the following tasks:

{task_list}

You can select a task by number or name. For example, "1" or "Marketing Strategy".
"""
            context["response"] = response
            context["metadata"]["awaiting_task_selection"] = True
            return context
        
        # Check if we're awaiting task selection
        if context.get("metadata", {}).get("awaiting_task_selection", False):
            # Try to match the message to a task
            selected_task_id = self._match_task(message)
            
            if selected_task_id:
                task = self.tasks[selected_task_id]
                
                # Store the selected task in the context
                context["selected_task"] = {
                    "id": selected_task_id,
                    "name": task["name"],
                    "description": task["description"],
                    "required_data": task.get("required_data", [])
                }
                
                # Check if we need to prompt for data
                if task.get("required_data"):
                    data_requirements = ", ".join(task["required_data"])
                    response = f"""
Great! You've selected: {task["name"]}

For this task, I'll need some information about your business. Please provide:
- {data_requirements}

You can also attach relevant files that contain this information, and I'll analyze them for you.
"""
                    context["response"] = response
                    context["metadata"]["awaiting_data"] = True
                    context["metadata"]["awaiting_task_selection"] = False
                else:
                    response = f"""
Great! You've selected: {task["name"]}

Let me help you with that. If you have any relevant files or data, please attach them now, and I'll analyze them to provide better recommendations.

Otherwise, please tell me about your business, target audience, and goals so I can tailor my recommendations.
"""
                    context["response"] = response
                    context["metadata"]["awaiting_data"] = True
                    context["metadata"]["awaiting_task_selection"] = False
                
                return context
            else:
                # No matching task found, ask again
                task_list = "\n".join([f"{i+1}. {desc}" for i, desc in enumerate(self.task_descriptions.values())])
                
                response = f"""
I'm not sure which task you're selecting. Please choose one of the following:

{task_list}

You can select a task by number or name.
"""
                context["response"] = response
                context["metadata"]["awaiting_task_selection"] = True
                return context
        
        # Check if we're awaiting data but the user has already selected a task
        if context.get("metadata", {}).get("awaiting_data", False) and "selected_task" in context:
            # The user has provided some information, but we'll let the data retriever component handle it
            # Just pass through without modifying the response
            return context
        
        # If we get here, check if the message contains a task selection
        selected_task_id = self._match_task(message)
        if selected_task_id:
            task = self.tasks[selected_task_id]
            
            # Store the selected task in the context
            context["selected_task"] = {
                "id": selected_task_id,
                "name": task["name"],
                "description": task["description"],
                "required_data": task.get("required_data", [])
            }
            
            # Update the response to acknowledge the task selection
            response = f"""
I'll help you with: {task["name"]}

Let me analyze your request and any attached data to provide the best recommendations.
"""
            context["response"] = response
            return context
        
        # If no task selection is detected, just pass through
        return context

    def _match_task(self, message: str) -> Optional[str]:
        """
        Match a message to a task.

        Args:
            message: The message to match

        Returns:
            Task ID if matched, None otherwise
        """
        # Check if the message is a number
        if message.isdigit():
            task_index = int(message) - 1
            if 0 <= task_index < len(self.tasks):
                return list(self.tasks.keys())[task_index]
        
        # Check if the message contains a task name
        message_lower = message.lower()
        for task_id, task in self.tasks.items():
            task_name_lower = task["name"].lower()
            if task_name_lower in message_lower or task_id.lower() in message_lower:
                return task_id
        
        # No match found
        return None

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.

        Returns:
            List of capability strings
        """
        return ["task_selection"] + [f"task:{task_id}" for task_id in self.tasks.keys()]
