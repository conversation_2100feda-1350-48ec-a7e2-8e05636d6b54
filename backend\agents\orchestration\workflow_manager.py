"""
Advanced Multi-Agent Workflow Manager for Datagenius.

This module provides sophisticated workflow orchestration, task decomposition,
and quality assurance for complex multi-agent operations, with state persisted
to a database.
"""

import logging
import asyncio
import uuid
from typing import Dict, Any, List, Optional, Callable, Union, Type
from datetime import datetime, timedelta, timezone

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from ..registry import AgentRegistry
from ..utils.memory_service import MemoryService
from backend.schemas.db_schemas import (
    WorkflowCreate, WorkflowUpdate, WorkflowInDB,
    TaskCreate, TaskUpdate, TaskInDB,
    WorkflowStatusEnum, TaskStatusEnum
)
from backend.app.crud import workflow_crud

logger = logging.getLogger(__name__)

# Use Enums from db_schemas directly
TaskStatus = TaskStatusEnum
WorkflowStatus = WorkflowStatusEnum

class QualityAssuranceAgent:
    """
    Quality assurance agent for validating task results and workflow integrity.
    Operates on TaskInDB Pydantic models (representing DB data).
    """
    def __init__(self):
        self.validation_rules = {
            "data_analysis": self._validate_analysis_result,
            "content_generation": self._validate_content_result,
            "classification": self._validate_classification_result,
            "general": self._validate_general_result
        }

    async def validate_task_result(self, task: TaskInDB) -> Dict[str, Any]:
        try:
            validator = self.validation_rules.get(task.agent_type, self.validation_rules["general"])
            validation_result = await validator(task)
            logger.debug(f"QA validation completed for task {task.id} with score {validation_result.get('score', 0)}")
            return validation_result
        except Exception as e:
            logger.error(f"Error validating task {task.id}: {e}", exc_info=True)
            return {"valid": False, "score": 0.0, "feedback": f"Validation error: {str(e)}", "suggestions": ["Please retry the task"]}

    async def _validate_analysis_result(self, task: TaskInDB) -> Dict[str, Any]:
        result = task.result or {}
        required_fields = ["insights", "visualizations", "summary"]
        missing_fields = [field for field in required_fields if field not in result]
        if missing_fields:
            return {"valid": False, "score": 0.3, "feedback": f"Missing required fields: {', '.join(missing_fields)}", "suggestions": [f"Add {field} to the analysis result" for field in missing_fields]}
        insights = result.get("insights", [])
        if len(insights) < 3:
            return {"valid": False, "score": 0.6, "feedback": "Analysis should provide at least 3 meaningful insights", "suggestions": ["Expand the analysis to include more insights"]}
        return {"valid": True, "score": 0.9, "feedback": "Analysis result meets quality standards", "suggestions": []}

    async def _validate_content_result(self, task: TaskInDB) -> Dict[str, Any]:
        result = task.result or {}
        content = result.get("content", "")
        if len(content) < 100:
            return {"valid": False, "score": 0.4, "feedback": "Generated content is too short", "suggestions": ["Expand the content to be more comprehensive"]}
        sentences = content.split('.')
        if len(sentences) < 3:
            return {"valid": False, "score": 0.5, "feedback": "Content lacks sufficient detail", "suggestions": ["Add more detailed explanations"]}
        return {"valid": True, "score": 0.85, "feedback": "Content meets quality standards", "suggestions": []}

    async def _validate_classification_result(self, task: TaskInDB) -> Dict[str, Any]:
        result = task.result or {}
        if "classifications" not in result:
            return {"valid": False, "score": 0.2, "feedback": "Missing classification results", "suggestions": ["Ensure classification results are included"]}
        classifications = result["classifications"]
        if not isinstance(classifications, list) or not classifications:
            return {"valid": False, "score": 0.3, "feedback": "No classifications found", "suggestions": ["Verify input data and classification logic"]}
        return {"valid": True, "score": 0.8, "feedback": "Classification results are valid", "suggestions": []}

    async def _validate_general_result(self, task: TaskInDB) -> Dict[str, Any]:
        if not task.result:
            return {"valid": False, "score": 0.0, "feedback": "No result produced", "suggestions": ["Ensure the task produces a valid result"]}
        return {"valid": True, "score": 0.7, "feedback": "Task completed successfully", "suggestions": []}


class WorkflowManager:
    def __init__(self, db_session_factory: Optional[Callable[[], Session]] = None):
        self.agent_registry = AgentRegistry
        self.memory_service = MemoryService()
        self.qa_agent = QualityAssuranceAgent()
        self.task_queue = asyncio.Queue()
        self.max_concurrent_tasks = 5
        self.worker_tasks: List[asyncio.Task] = []
        
        if db_session_factory is None:
            logger.error("WorkflowManager initialized without a database session factory. Persistence will not work.")
        self.db_session_factory = db_session_factory
        self._start_workers()
        logger.info("Workflow manager initialized")

    def _get_db(self) -> Session:
        if not self.db_session_factory:
            logger.critical("Database session factory not configured.")
            raise RuntimeError("Database session factory not configured.")
        db = self.db_session_factory()
        if db is None:
            logger.critical("Database session factory returned None.")
            raise RuntimeError("Failed to obtain a database session from the factory.")
        return db

    def _start_workers(self):
        for i in range(self.max_concurrent_tasks):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.worker_tasks.append(worker)

    async def _worker(self, worker_name: str):
        logger.info(f"Worker {worker_name} started")
        while True:
            try:
                workflow_id, task_id = await self.task_queue.get()
                await self._execute_task(workflow_id, task_id)
                self.task_queue.task_done()
            except asyncio.CancelledError:
                logger.info(f"Worker {worker_name} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker {worker_name} encountered an error: {e}", exc_info=True)
                await asyncio.sleep(1)

    async def create_workflow(self, name: str, description: str, user_id: str, session_id: str, context: Optional[Dict[str, Any]] = None) -> uuid.UUID:
        workflow_data = WorkflowCreate(name=name, description=description, user_id=user_id, session_id=session_id, context=context or {}, status=WorkflowStatus.CREATED)
        db: Session = self._get_db()
        try:
            created_workflow = workflow_crud.create_workflow_execution(db=db, workflow_data=workflow_data)
            logger.info(f"Created workflow {created_workflow.id} in DB: {name}")
            return created_workflow.id
        finally:
            db.close()

    async def add_task(self, workflow_id: uuid.UUID, name: str, agent_type: str, input_data: Dict[str, Any], dependencies: Optional[List[str]] = None, metadata: Optional[Dict[str, Any]] = None) -> uuid.UUID:
        db: Session = self._get_db()
        try:
            workflow_db = workflow_crud.get_workflow_execution(db=db, workflow_id=workflow_id)
            if not workflow_db:
                raise ValueError(f"Workflow {workflow_id} not found in database")
            task_create_data = TaskCreate(name=name, agent_type=agent_type, input_data=input_data, dependencies=dependencies or [], metadata=metadata or {}, workflow_id=workflow_id, status=TaskStatus.PENDING)
            created_task = workflow_crud.create_workflow_task_execution(db=db, task_data=task_create_data, workflow_id=workflow_id)
            logger.info(f"Added task {created_task.id} to workflow {workflow_id} in DB")
            return created_task.id
        finally:
            db.close()

    async def execute_workflow(self, workflow_id: uuid.UUID) -> Dict[str, Any]:
        db: Session = self._get_db()
        try:
            workflow_db_model = workflow_crud.get_workflow_execution_with_tasks(db=db, workflow_id=workflow_id)
            if not workflow_db_model:
                raise ValueError(f"Workflow {workflow_id} not found in database")

            if workflow_db_model.status not in [WorkflowStatus.CREATED, WorkflowStatus.PENDING, WorkflowStatus.PAUSED]:
                logger.warning(f"Workflow {workflow_id} is in status {workflow_db_model.status}, not starting execution.")
                return {"workflow_id": str(workflow_db_model.id), "status": workflow_db_model.status.value, "message": f"Workflow already in status {workflow_db_model.status.value}"}

            workflow_crud.update_workflow_execution(db=db, workflow_id=workflow_id, workflow_update_data=WorkflowUpdate(status=WorkflowStatus.RUNNING, start_time=datetime.now(timezone.utc)))
            
            logger.info(f"Starting workflow execution: {workflow_id}")
            ready_tasks = [task for task in workflow_db_model.tasks if not task.dependencies and task.status == TaskStatus.PENDING]
            for task_db in ready_tasks:
                await self.task_queue.put((workflow_db_model.id, task_db.id))

            await self._wait_for_workflow_completion(workflow_id)
            
            # Re-fetch workflow for final state
            db.refresh(workflow_db_model, attribute_names=['tasks']) # Refresh tasks relationship
            # workflow_db_model = workflow_crud.get_workflow_execution_with_tasks(db=db, workflow_id=workflow_id) # Alternative full re-fetch

            validation_results = await self._validate_workflow(workflow_db_model)

            final_status = WorkflowStatus.COMPLETED
            if any(t.status == TaskStatus.FAILED and t.retry_count >= t.max_retries for t in workflow_db_model.tasks):
                final_status = WorkflowStatus.FAILED
            
            updated_workflow = workflow_crud.update_workflow_execution(db=db, workflow_id=workflow_id, workflow_update_data=WorkflowUpdate(status=final_status, end_time=datetime.now(timezone.utc)))
            if not updated_workflow: # Should ideally not happen
                 updated_workflow = workflow_db_model 
                 updated_workflow.status = final_status # Manual set for response if update failed

            result = {
                "workflow_id": str(updated_workflow.id), "status": updated_workflow.status.value,
                "duration": (updated_workflow.end_time - updated_workflow.start_time).total_seconds() if updated_workflow.start_time and updated_workflow.end_time else 0,
                "task_results": {str(task.id): task.result for task in updated_workflow.tasks if task.result is not None},
                "validation": validation_results
            }
            logger.info(f"Workflow {workflow_id} finished with status: {updated_workflow.status.value}")
            return result
        except Exception as e:
            logger.error(f"Workflow {workflow_id} execution failed: {e}", exc_info=True)
            try:
                workflow_crud.update_workflow_execution(db=db, workflow_id=workflow_id, workflow_update_data=WorkflowUpdate(status=WorkflowStatus.FAILED, end_time=datetime.now(timezone.utc)))
            except Exception as db_err:
                logger.error(f"Failed to update workflow {workflow_id} status to FAILED in DB: {db_err}", exc_info=True)
            raise
        finally:
            db.close()

    async def _execute_task(self, workflow_id: uuid.UUID, task_id: uuid.UUID):
        db: Session = self._get_db()
        task_db_model: Optional[WorkflowTaskExecution] = None
        try:
            task_db_model = workflow_crud.get_workflow_task_execution(db, task_id)
            if not task_db_model:
                logger.error(f"Task {task_id} not found in DB for workflow {workflow_id}")
                return

            workflow_db_model = workflow_crud.get_workflow_execution(db, task_db_model.workflow_id)
            if not workflow_db_model:
                logger.error(f"Workflow {task_db_model.workflow_id} for task {task_id} not found.")
                workflow_crud.update_workflow_task_execution(db, task_id, TaskUpdate(status=TaskStatus.FAILED, error="Parent workflow not found", end_time=datetime.now(timezone.utc)))
                return

            if not await self._check_dependencies_db(db, workflow_id, task_db_model):
                logger.debug(f"Task {task_id} dependencies not met.")
                return

            workflow_crud.update_workflow_task_execution(db, task_id, TaskUpdate(status=TaskStatus.RUNNING, start_time=datetime.now(timezone.utc)))
            
            logger.info(f"Executing task {task_id}: {task_db_model.name}")
            agent = await self.agent_registry.create_agent_instance(task_db_model.agent_type)
            if not agent:
                raise RuntimeError(f"Failed to create agent instance: {task_db_model.agent_type} for task {task_id}")

            agent_input_context = task_db_model.input_data or {}
            user_id_for_agent = str(workflow_db_model.user_id) if workflow_db_model.user_id is not None else ""
            session_id_for_agent = str(workflow_db_model.session_id) if workflow_db_model.session_id is not None else ""

            task_result_data = await asyncio.wait_for(
                agent.process_message(user_id=user_id_for_agent, message=agent_input_context.get("message", ""), conversation_id=session_id_for_agent, context=agent_input_context),
                timeout=task_db_model.timeout_seconds
            )
            
            workflow_crud.update_workflow_task_execution(db, task_id, TaskUpdate(result=task_result_data, status=TaskStatus.COMPLETED, end_time=datetime.now(timezone.utc)))
            logger.info(f"Task {task_id} completed successfully")
            await self._queue_dependent_tasks_db(db, workflow_id, task_id)

        except asyncio.TimeoutError:
            logger.error(f"Task {task_id} timed out")
            if task_db_model:
                workflow_crud.update_workflow_task_execution(db, task_id, TaskUpdate(status=TaskStatus.FAILED, error="Task timeout", end_time=datetime.now(timezone.utc)))
        except Exception as e:
            logger.error(f"Error executing task {task_id}: {e}", exc_info=True)
            if task_db_model:
                current_retry_count = task_db_model.retry_count or 0
                max_retries = task_db_model.max_retries if task_db_model.max_retries is not None else 3
                if current_retry_count < max_retries:
                    workflow_crud.update_workflow_task_execution(db, task_id, TaskUpdate(status=TaskStatus.PENDING, error=str(e), retry_count=current_retry_count + 1))
                    await asyncio.sleep(2 ** (current_retry_count + 1))
                    await self.task_queue.put((workflow_id, task_id))
                    logger.info(f"Retrying task {task_id} (attempt {current_retry_count + 1})")
                else:
                    workflow_crud.update_workflow_task_execution(db, task_id, TaskUpdate(status=TaskStatus.FAILED, error=str(e), end_time=datetime.now(timezone.utc)))
                    logger.error(f"Task {task_id} failed after {max_retries} retries: {e}")
        finally:
            if db.is_active:
                db.close()

    async def _check_dependencies_db(self, db: Session, workflow_id: uuid.UUID, task: TaskInDB) -> bool:
        if not task.dependencies: return True
        for dep_id_str in task.dependencies:
            try:
                dep_id = uuid.UUID(dep_id_str)
                dep_task_db = workflow_crud.get_workflow_task_execution(db=db, task_id=dep_id)
                if not dep_task_db or dep_task_db.workflow_id != workflow_id or dep_task_db.status != TaskStatus.COMPLETED:
                    return False
            except ValueError:
                logger.error(f"Invalid dependency ID format '{dep_id_str}' for task {task.id}")
                return False
            except SQLAlchemyError as e:
                logger.error(f"DB error checking dependency {dep_id_str} for task {task.id}: {e}", exc_info=True)
                return False
        return True

    async def _queue_dependent_tasks_db(self, db: Session, workflow_id: uuid.UUID, completed_task_id: uuid.UUID):
        all_tasks_in_workflow = workflow_crud.get_tasks_for_workflow(db=db, workflow_id=workflow_id)
        str_completed_task_id = str(completed_task_id)
        for potential_task_db in all_tasks_in_workflow:
            if potential_task_db.dependencies and str_completed_task_id in potential_task_db.dependencies and potential_task_db.status == TaskStatus.PENDING:
                if await self._check_dependencies_db(db, workflow_id, potential_task_db): # Re-check all deps for the potential task
                    await self.task_queue.put((workflow_id, potential_task_db.id))

    async def _wait_for_workflow_completion(self, workflow_id: uuid.UUID):
        while True:
            db: Session = self._get_db()
            try:
                active_tasks_count = workflow_crud.count_active_tasks_for_workflow(db=db, workflow_id=workflow_id)
                if active_tasks_count == 0:
                    logger.info(f"No more active tasks for workflow {workflow_id}. Completion check passed.")
                    break
                
                terminally_failed_count = workflow_crud.count_terminally_failed_tasks_for_workflow(db=db, workflow_id=workflow_id)
                if terminally_failed_count > 0:
                    failed_task_ids = [str(t.id) for t in db.query(WorkflowTaskExecution.id).filter(WorkflowTaskExecution.workflow_id == workflow_id, WorkflowTaskExecution.status == TaskStatus.FAILED, WorkflowTaskExecution.retry_count >= WorkflowTaskExecution.max_retries).all()]
                    logger.error(f"Workflow {workflow_id} failed due to terminally failed tasks: {failed_task_ids}")
                    raise RuntimeError(f"Workflow {workflow_id} failed due to terminally failed tasks: {failed_task_ids}")
            finally:
                db.close()
            await asyncio.sleep(1)

    async def _validate_workflow(self, workflow_db_model: WorkflowInDB) -> Dict[str, Any]: # Expects WorkflowInDB (or compatible ORM model)
        validation_results = {}
        # Ensure tasks are loaded; get_workflow_execution_with_tasks should do this.
        # If not, tasks might need to be fetched separately or relationship reloaded.
        if not workflow_db_model.tasks: # Check if tasks were loaded
             db = self._get_db()
             try:
                 loaded_tasks = workflow_crud.get_tasks_for_workflow(db, workflow_db_model.id)
                 tasks_to_validate = [TaskInDB.model_validate(task_orm) for task_orm in loaded_tasks] # Convert ORM to Pydantic
             finally:
                 db.close()
        else:
            tasks_to_validate = [TaskInDB.model_validate(task_orm) for task_orm in workflow_db_model.tasks]


        for task_schema in tasks_to_validate: # task_schema is now TaskInDB
            if task_schema.status == TaskStatus.COMPLETED:
                validation = await self.qa_agent.validate_task_result(task_schema) # QA agent expects TaskInDB
                validation_results[str(task_schema.id)] = validation
        return validation_results

    async def get_workflow_status(self, workflow_id: uuid.UUID) -> Dict[str, Any]:
        db: Session = self._get_db()
        try:
            workflow_db = workflow_crud.get_workflow_execution_with_tasks(db=db, workflow_id=workflow_id)
            if not workflow_db:
                raise ValueError(f"Workflow {workflow_id} not found in database")
            
            # Convert ORM model to Pydantic schema for consistent output if needed, or construct dict directly
            workflow_pydantic = WorkflowInDB.model_validate(workflow_db) # Includes tasks if loaded correctly

            return {
                "workflow_id": str(workflow_pydantic.id),
                "name": workflow_pydantic.name,
                "status": workflow_pydantic.status.value,
                "start_time": workflow_pydantic.start_time.isoformat() if workflow_pydantic.start_time else None,
                "end_time": workflow_pydantic.end_time.isoformat() if workflow_pydantic.end_time else None,
                "tasks": [
                    {
                        "id": str(task.id), "name": task.name, "status": task.status.value,
                        "agent_type": task.agent_type, "retry_count": task.retry_count
                    }
                    for task in workflow_pydantic.tasks
                ]
            }
        finally:
            db.close()

    async def cancel_workflow(self, workflow_id: uuid.UUID):
        db: Session = self._get_db()
        try:
            workflow_db = workflow_crud.get_workflow_execution_with_tasks(db=db, workflow_id=workflow_id)
            if not workflow_db:
                raise ValueError(f"Workflow {workflow_id} not found in database")

            workflow_crud.update_workflow_execution(db=db, workflow_id=workflow_id, workflow_update_data=WorkflowUpdate(status=WorkflowStatus.CANCELLED))
            
            for task_db_item in workflow_db.tasks:
                if task_db_item.status in [TaskStatus.RUNNING, TaskStatus.PENDING, TaskStatus.WAITING]:
                    workflow_crud.update_workflow_task_execution(db=db, task_id=task_db_item.id, task_update_data=TaskUpdate(status=TaskStatus.CANCELLED))
            # db.commit() # CRUD should handle commits individually or a final one here if grouped
            logger.info(f"Workflow {workflow_id} cancelled in DB")
        except SQLAlchemyError as e:
            # db.rollback() # Handled in CRUD
            logger.error(f"Database error cancelling workflow {workflow_id}: {e}", exc_info=True)
            raise
        finally:
            db.close()

    async def cleanup_completed_workflows(self, max_age_hours: int = 24):
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=max_age_hours) # Ensure timezone aware
        db: Session = self._get_db()
        try:
            # Fetching IDs first to avoid issues with deleting from an iterated list if cascade isn't perfect
            old_workflows = workflow_crud.get_old_workflows_for_cleanup(db=db, cutoff_time=cutoff_time)
            num_deleted = 0
            for wf in old_workflows:
                if workflow_crud.delete_workflow_execution(db=db, workflow_id=wf.id): # This will cascade delete tasks
                    num_deleted +=1
            
            if num_deleted > 0:
                logger.info(f"Cleaned up {num_deleted} old workflows from database")
        except SQLAlchemyError as e:
            # db.rollback() # Handled in CRUD
            logger.error(f"Database error cleaning up old workflows: {e}", exc_info=True)
        finally:
            db.close()

# Global workflow manager instance
# A proper db_session_factory needs to be injected here from the main application setup.
# For example, in a FastAPI app, this could be done in a startup event.
# from backend.app.database import SessionLocal # Assuming SessionLocal is the factory
# workflow_manager = WorkflowManager(db_session_factory=SessionLocal)
workflow_manager = WorkflowManager(db_session_factory=None)
