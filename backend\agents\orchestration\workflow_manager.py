"""
Advanced Multi-Agent Workflow Manager for Datagenius.

This module provides sophisticated workflow orchestration, task decomposition,
and quality assurance for complex multi-agent operations.
"""

import logging
import asyncio
import uuid
from typing import Dict, Any, List, Optional, Callable, Union
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field

from ..registry import AgentRegistry
from ..utils.memory_service import MemoryService

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    WAITING = "waiting"


class WorkflowStatus(Enum):
    """Workflow execution status."""
    CREATED = "created"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


@dataclass
class Task:
    """Represents a single task in a workflow."""
    id: str
    name: str
    agent_type: str
    input_data: Dict[str, Any]
    dependencies: List[str] = field(default_factory=list)
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    timeout_seconds: int = 300
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Workflow:
    """Represents a complete workflow with multiple tasks."""
    id: str
    name: str
    description: str
    tasks: List[Task]
    status: WorkflowStatus = WorkflowStatus.CREATED
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    user_id: str = ""
    session_id: str = ""
    context: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


class QualityAssuranceAgent:
    """
    Quality assurance agent for validating task results and workflow integrity.
    """

    def __init__(self):
        """Initialize the QA agent."""
        self.validation_rules = {
            "data_analysis": self._validate_analysis_result,
            "content_generation": self._validate_content_result,
            "classification": self._validate_classification_result,
            "general": self._validate_general_result
        }

    async def validate_task_result(self, task: Task) -> Dict[str, Any]:
        """
        Validate a task result for quality and completeness.

        Args:
            task: The completed task to validate

        Returns:
            Validation result with score and feedback
        """
        try:
            # Get appropriate validation function
            validator = self.validation_rules.get(
                task.agent_type,
                self.validation_rules["general"]
            )

            # Run validation
            validation_result = await validator(task)

            # Log validation (metrics integration can be added later)
            logger.debug(f"QA validation completed for task {task.id} with score {validation_result.get('score', 0)}")

            return validation_result

        except Exception as e:
            logger.error(f"Error validating task {task.id}: {e}")
            return {
                "valid": False,
                "score": 0.0,
                "feedback": f"Validation error: {str(e)}",
                "suggestions": ["Please retry the task"]
            }

    async def _validate_analysis_result(self, task: Task) -> Dict[str, Any]:
        """Validate data analysis task results."""
        result = task.result or {}

        # Check for required components
        required_fields = ["insights", "visualizations", "summary"]
        missing_fields = [field for field in required_fields if field not in result]

        if missing_fields:
            return {
                "valid": False,
                "score": 0.3,
                "feedback": f"Missing required fields: {', '.join(missing_fields)}",
                "suggestions": [f"Add {field} to the analysis result" for field in missing_fields]
            }

        # Check data quality
        insights = result.get("insights", [])
        if len(insights) < 3:
            return {
                "valid": False,
                "score": 0.6,
                "feedback": "Analysis should provide at least 3 meaningful insights",
                "suggestions": ["Expand the analysis to include more insights"]
            }

        return {
            "valid": True,
            "score": 0.9,
            "feedback": "Analysis result meets quality standards",
            "suggestions": []
        }

    async def _validate_content_result(self, task: Task) -> Dict[str, Any]:
        """Validate content generation task results."""
        result = task.result or {}
        content = result.get("content", "")

        if len(content) < 100:
            return {
                "valid": False,
                "score": 0.4,
                "feedback": "Generated content is too short",
                "suggestions": ["Expand the content to be more comprehensive"]
            }

        # Check for basic content quality indicators
        sentences = content.split('.')
        if len(sentences) < 3:
            return {
                "valid": False,
                "score": 0.5,
                "feedback": "Content lacks sufficient detail",
                "suggestions": ["Add more detailed explanations"]
            }

        return {
            "valid": True,
            "score": 0.85,
            "feedback": "Content meets quality standards",
            "suggestions": []
        }

    async def _validate_classification_result(self, task: Task) -> Dict[str, Any]:
        """Validate classification task results."""
        result = task.result or {}

        if "classifications" not in result:
            return {
                "valid": False,
                "score": 0.2,
                "feedback": "Missing classification results",
                "suggestions": ["Ensure classification results are included"]
            }

        classifications = result["classifications"]
        if not isinstance(classifications, list) or len(classifications) == 0:
            return {
                "valid": False,
                "score": 0.3,
                "feedback": "No classifications found",
                "suggestions": ["Verify input data and classification logic"]
            }

        return {
            "valid": True,
            "score": 0.8,
            "feedback": "Classification results are valid",
            "suggestions": []
        }

    async def _validate_general_result(self, task: Task) -> Dict[str, Any]:
        """Validate general task results."""
        if not task.result:
            return {
                "valid": False,
                "score": 0.0,
                "feedback": "No result produced",
                "suggestions": ["Ensure the task produces a valid result"]
            }

        return {
            "valid": True,
            "score": 0.7,
            "feedback": "Task completed successfully",
            "suggestions": []
        }


class WorkflowManager:
    """
    Advanced workflow manager for orchestrating multi-agent tasks.

    Features:
    - Task dependency management
    - Parallel execution
    - Error handling and retries
    - Quality assurance integration
    - Workflow state persistence
    """

    def __init__(self):
        """Initialize the workflow manager."""
        self.agent_registry = AgentRegistry
        self.memory_service = MemoryService()
        self.qa_agent = QualityAssuranceAgent()
        self.active_workflows: Dict[str, Workflow] = {}
        self.task_queue = asyncio.Queue()
        self.max_concurrent_tasks = 5
        self.worker_tasks: List[asyncio.Task] = []

        # Start worker tasks
        self._start_workers()

        logger.info("Workflow manager initialized")

    def _start_workers(self):
        """Start background worker tasks."""
        for i in range(self.max_concurrent_tasks):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.worker_tasks.append(worker)

    async def _worker(self, worker_name: str):
        """Background worker for processing tasks."""
        logger.info(f"Worker {worker_name} started")

        while True:
            try:
                # Get next task from queue
                workflow_id, task_id = await self.task_queue.get()

                # Process the task
                await self._execute_task(workflow_id, task_id)

                # Mark task as done
                self.task_queue.task_done()

            except asyncio.CancelledError:
                logger.info(f"Worker {worker_name} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {e}")
                await asyncio.sleep(1)  # Brief pause on error

    async def create_workflow(
        self,
        name: str,
        description: str,
        user_id: str,
        session_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create a new workflow.

        Args:
            name: Workflow name
            description: Workflow description
            user_id: User ID
            session_id: Session ID
            context: Additional context

        Returns:
            Workflow ID
        """
        workflow_id = str(uuid.uuid4())

        workflow = Workflow(
            id=workflow_id,
            name=name,
            description=description,
            tasks=[],
            user_id=user_id,
            session_id=session_id,
            context=context or {}
        )

        self.active_workflows[workflow_id] = workflow

        logger.info(f"Created workflow {workflow_id}: {name}")
        return workflow_id

    async def add_task(
        self,
        workflow_id: str,
        name: str,
        agent_type: str,
        input_data: Dict[str, Any],
        dependencies: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Add a task to a workflow.

        Args:
            workflow_id: Workflow ID
            name: Task name
            agent_type: Type of agent to execute the task
            input_data: Input data for the task
            dependencies: List of task IDs this task depends on
            metadata: Additional metadata

        Returns:
            Task ID
        """
        if workflow_id not in self.active_workflows:
            raise ValueError(f"Workflow {workflow_id} not found")

        task_id = str(uuid.uuid4())

        task = Task(
            id=task_id,
            name=name,
            agent_type=agent_type,
            input_data=input_data,
            dependencies=dependencies or [],
            metadata=metadata or {}
        )

        workflow = self.active_workflows[workflow_id]
        workflow.tasks.append(task)

        logger.info(f"Added task {task_id} to workflow {workflow_id}")
        return task_id

    async def execute_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Execute a workflow.

        Args:
            workflow_id: Workflow ID

        Returns:
            Workflow execution result
        """
        if workflow_id not in self.active_workflows:
            raise ValueError(f"Workflow {workflow_id} not found")

        workflow = self.active_workflows[workflow_id]
        workflow.status = WorkflowStatus.RUNNING
        workflow.start_time = datetime.now()

        try:
            logger.info(f"Starting workflow execution: {workflow_id}")

            # Queue tasks that have no dependencies
            ready_tasks = [task for task in workflow.tasks if not task.dependencies]
            for task in ready_tasks:
                await self.task_queue.put((workflow_id, task.id))

            # Wait for all tasks to complete
            await self._wait_for_workflow_completion(workflow_id)

            # Validate workflow results
            validation_results = await self._validate_workflow(workflow_id)

            workflow.status = WorkflowStatus.COMPLETED
            workflow.end_time = datetime.now()

            result = {
                "workflow_id": workflow_id,
                "status": workflow.status.value,
                "duration": (workflow.end_time - workflow.start_time).total_seconds(),
                "task_results": {task.id: task.result for task in workflow.tasks},
                "validation": validation_results
            }

            logger.info(f"Workflow {workflow_id} completed successfully")
            return result

        except Exception as e:
            workflow.status = WorkflowStatus.FAILED
            workflow.end_time = datetime.now()

            logger.error(f"Workflow {workflow_id} failed: {e}")
            raise

    async def _execute_task(self, workflow_id: str, task_id: str):
        """Execute a single task."""
        workflow = self.active_workflows[workflow_id]
        task = next((t for t in workflow.tasks if t.id == task_id), None)

        if not task:
            logger.error(f"Task {task_id} not found in workflow {workflow_id}")
            return

        # Check if dependencies are satisfied
        if not await self._check_dependencies(workflow, task):
            # Re-queue the task for later
            await asyncio.sleep(1)
            await self.task_queue.put((workflow_id, task_id))
            return

        task.status = TaskStatus.RUNNING
        task.start_time = datetime.now()

        try:
            logger.info(f"Executing task {task_id}: {task.name}")

            # Create agent instance
            agent = await self.agent_registry.create_agent_instance(task.agent_type)
            if not agent:
                raise RuntimeError(f"Failed to create agent instance: {task.agent_type}")

            # Execute task with timeout
            result = await asyncio.wait_for(
                agent.process_message(
                    user_id=workflow.user_id,
                    message=task.input_data.get("message", ""),
                    conversation_id=workflow.session_id,
                    context=task.input_data
                ),
                timeout=task.timeout_seconds
            )

            task.result = result
            task.status = TaskStatus.COMPLETED
            task.end_time = datetime.now()

            # Queue dependent tasks
            await self._queue_dependent_tasks(workflow, task_id)

            logger.info(f"Task {task_id} completed successfully")

        except asyncio.TimeoutError:
            task.status = TaskStatus.FAILED
            task.error = "Task timeout"
            task.end_time = datetime.now()
            logger.error(f"Task {task_id} timed out")

        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.end_time = datetime.now()

            # Retry logic
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.PENDING
                await asyncio.sleep(2 ** task.retry_count)  # Exponential backoff
                await self.task_queue.put((workflow_id, task_id))
                logger.info(f"Retrying task {task_id} (attempt {task.retry_count})")
            else:
                logger.error(f"Task {task_id} failed after {task.max_retries} retries: {e}")

    async def _check_dependencies(self, workflow: Workflow, task: Task) -> bool:
        """Check if task dependencies are satisfied."""
        for dep_id in task.dependencies:
            dep_task = next((t for t in workflow.tasks if t.id == dep_id), None)
            if not dep_task or dep_task.status != TaskStatus.COMPLETED:
                return False
        return True

    async def _queue_dependent_tasks(self, workflow: Workflow, completed_task_id: str):
        """Queue tasks that depend on the completed task."""
        for task in workflow.tasks:
            if (completed_task_id in task.dependencies and
                task.status == TaskStatus.PENDING and
                await self._check_dependencies(workflow, task)):
                await self.task_queue.put((workflow.id, task.id))

    async def _wait_for_workflow_completion(self, workflow_id: str):
        """Wait for all tasks in a workflow to complete."""
        workflow = self.active_workflows[workflow_id]

        while True:
            pending_tasks = [t for t in workflow.tasks if t.status in [TaskStatus.PENDING, TaskStatus.RUNNING]]
            if not pending_tasks:
                break

            await asyncio.sleep(1)

            # Check for failed tasks that can't be retried
            failed_tasks = [t for t in workflow.tasks if t.status == TaskStatus.FAILED and t.retry_count >= t.max_retries]
            if failed_tasks:
                raise RuntimeError(f"Workflow failed due to failed tasks: {[t.id for t in failed_tasks]}")

    async def _validate_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Validate workflow results using QA agent."""
        workflow = self.active_workflows[workflow_id]
        validation_results = {}

        for task in workflow.tasks:
            if task.status == TaskStatus.COMPLETED:
                validation = await self.qa_agent.validate_task_result(task)
                validation_results[task.id] = validation

        return validation_results

    async def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get current workflow status."""
        if workflow_id not in self.active_workflows:
            raise ValueError(f"Workflow {workflow_id} not found")

        workflow = self.active_workflows[workflow_id]

        return {
            "workflow_id": workflow_id,
            "name": workflow.name,
            "status": workflow.status.value,
            "start_time": workflow.start_time.isoformat() if workflow.start_time else None,
            "end_time": workflow.end_time.isoformat() if workflow.end_time else None,
            "tasks": [
                {
                    "id": task.id,
                    "name": task.name,
                    "status": task.status.value,
                    "agent_type": task.agent_type,
                    "retry_count": task.retry_count
                }
                for task in workflow.tasks
            ]
        }

    async def cancel_workflow(self, workflow_id: str):
        """Cancel a running workflow."""
        if workflow_id not in self.active_workflows:
            raise ValueError(f"Workflow {workflow_id} not found")

        workflow = self.active_workflows[workflow_id]
        workflow.status = WorkflowStatus.CANCELLED

        # Cancel running tasks
        for task in workflow.tasks:
            if task.status == TaskStatus.RUNNING:
                task.status = TaskStatus.CANCELLED

        logger.info(f"Workflow {workflow_id} cancelled")

    async def cleanup_completed_workflows(self, max_age_hours: int = 24):
        """Clean up old completed workflows."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        workflows_to_remove = []
        for workflow_id, workflow in self.active_workflows.items():
            if (workflow.status in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED, WorkflowStatus.CANCELLED] and
                workflow.end_time and workflow.end_time < cutoff_time):
                workflows_to_remove.append(workflow_id)

        for workflow_id in workflows_to_remove:
            del self.active_workflows[workflow_id]

        if workflows_to_remove:
            logger.info(f"Cleaned up {len(workflows_to_remove)} old workflows")


# Global workflow manager instance
workflow_manager = WorkflowManager()
