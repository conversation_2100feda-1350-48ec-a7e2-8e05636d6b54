"""
Authentication models for the Datagenius backend.

This module provides Pydantic models for authentication.
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, Field


class UserBase(BaseModel):
    """Base model for user data."""
    email: EmailStr
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool = True
    is_verified: bool = False
    is_superuser: bool = False


class UserCreate(UserBase):
    """Model for creating a new user."""
    password: str = Field(..., min_length=8)


class UserUpdate(BaseModel):
    """Model for updating a user."""
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    is_superuser: Optional[bool] = None


class UserInDB(UserBase):
    """Model for user data in the database."""
    id: int
    hashed_password: Optional[str] = None
    oauth_provider: Optional[str] = None
    oauth_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class User(UserBase):
    """Model for user data returned to the client."""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class Token(BaseModel):
    """Model for JWT token."""
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int


class TokenData(BaseModel):
    """Model for JWT token data."""
    sub: Optional[int] = None
    exp: Optional[int] = None
    jti: Optional[str] = None


class DeviceInfo(BaseModel):
    """Model for device information."""
    user_agent: str
    platform: str
    screen_resolution: str
    timezone: str
    language: str
    ip_address: Optional[str] = None


class LoginRequest(BaseModel):
    """Model for login request."""
    email: EmailStr
    password: str
    device_fingerprint: Optional[str] = None
    device_info: Optional[DeviceInfo] = None


class RefreshTokenRequest(BaseModel):
    """Model for refresh token request."""
    refresh_token: str
    device_fingerprint: Optional[str] = None
    device_info: Optional[DeviceInfo] = None


class PasswordChangeRequest(BaseModel):
    """Model for password change request."""
    current_password: str
    new_password: str = Field(..., min_length=8)


class PasswordResetRequest(BaseModel):
    """Model for password reset request."""
    email: EmailStr


class PasswordResetConfirmRequest(BaseModel):
    """Model for password reset confirmation request."""
    token: str
    new_password: str = Field(..., min_length=8)


class EmailVerificationRequest(BaseModel):
    """Model for email verification request."""
    token: str


class OAuthRequest(BaseModel):
    """Model for OAuth authorization request."""
    provider: str
    redirect_uri: str
    state: Optional[str] = None


class GoogleAuthRequest(BaseModel):
    """Model for Google OAuth authorization request."""
    code: str
    state: Optional[str] = None


class OAuthUserInfo(BaseModel):
    """Model for OAuth user information."""
    provider: str
    provider_user_id: str
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    picture: Optional[str] = None
    access_token: str
    refresh_token: Optional[str] = None

