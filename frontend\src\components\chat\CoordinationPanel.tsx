import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  ArrowRight, 
  MessageSquare, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { useConcierge } from '@/contexts/ConciergeContext';

interface CoordinationPanelProps {
  className?: string;
}

export const CoordinationPanel: React.FC<CoordinationPanelProps> = ({
  className = ''
}) => {
  const { conciergeState } = useConcierge();

  if (!conciergeState) return null;

  const { 
    coordinationChain, 
    activeCollaborations, 
    pendingCallbacks, 
    handoffs 
  } = conciergeState;

  const hasCoordinationData = 
    coordinationChain.length > 0 || 
    activeCollaborations.length > 0 || 
    pendingCallbacks.length > 0 || 
    handoffs.length > 0;

  if (!hasCoordinationData) {
    return (
      <Card className={`${className} border-dashed border-gray-300`}>
        <CardContent className="p-4">
          <div className="flex flex-col items-center justify-center text-center p-4 text-gray-500">
            <Users className="h-8 w-8 mb-2 text-gray-400" />
            <h3 className="text-sm font-medium mb-1">No Coordination Activity</h3>
            <p className="text-xs">
              Coordination information will appear here when you collaborate with multiple personas.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className}`}>
      <CardContent className="p-4">
        <h3 className="text-sm font-medium mb-3 flex items-center">
          <Users className="h-4 w-4 mr-1 text-brand-500" />
          Coordination Activity
        </h3>

        {/* Coordination Chain */}
        {coordinationChain.length > 0 && (
          <div className="mb-4">
            <h4 className="text-xs font-medium text-gray-500 mb-2">Coordination Path</h4>
            <div className="flex flex-wrap items-center gap-1 text-xs">
              {coordinationChain.map((persona, index) => (
                <React.Fragment key={`${persona}-${index}`}>
                  <Badge variant="outline" className="bg-gray-50">
                    {persona}
                  </Badge>
                  {index < coordinationChain.length - 1 && (
                    <ArrowRight className="h-3 w-3 text-gray-400" />
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>
        )}

        {/* Active Collaborations */}
        {activeCollaborations.length > 0 && (
          <div className="mb-4">
            <h4 className="text-xs font-medium text-gray-500 mb-2">Active Collaborations</h4>
            <div className="space-y-2">
              {activeCollaborations.map((collab, index) => (
                <motion.div 
                  key={`collab-${index}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-blue-50 p-2 rounded-md text-xs"
                >
                  <div className="flex justify-between items-start mb-1">
                    <div className="font-medium text-blue-700 flex items-center">
                      <Users className="h-3 w-3 mr-1" />
                      {collab.personas.join(', ')}
                    </div>
                    <Badge 
                      variant="outline" 
                      className={`
                        ${collab.status === 'pending' ? 'bg-yellow-100 text-yellow-700 border-yellow-200' : ''}
                        ${collab.status === 'active' ? 'bg-green-100 text-green-700 border-green-200' : ''}
                        ${collab.status === 'completed' ? 'bg-gray-100 text-gray-700 border-gray-200' : ''}
                      `}
                    >
                      {collab.status}
                    </Badge>
                  </div>
                  <p className="text-blue-600">{collab.task}</p>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Pending Callbacks */}
        {pendingCallbacks.length > 0 && (
          <div className="mb-4">
            <h4 className="text-xs font-medium text-gray-500 mb-2">Pending Callbacks</h4>
            <div className="space-y-2">
              {pendingCallbacks.map((callback, index) => (
                <motion.div 
                  key={`callback-${index}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-purple-50 p-2 rounded-md text-xs"
                >
                  <div className="flex justify-between items-start mb-1">
                    <div className="font-medium text-purple-700 flex items-center">
                      <MessageSquare className="h-3 w-3 mr-1" />
                      {callback.source} → {callback.target}
                    </div>
                    <Badge 
                      variant="outline" 
                      className={`
                        ${callback.status === 'pending' ? 'bg-yellow-100 text-yellow-700 border-yellow-200' : ''}
                        ${callback.status === 'seen' ? 'bg-blue-100 text-blue-700 border-blue-200' : ''}
                        ${callback.status === 'responded' ? 'bg-green-100 text-green-700 border-green-200' : ''}
                      `}
                    >
                      {callback.status}
                    </Badge>
                  </div>
                  <p className="text-purple-600">{callback.reason}</p>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Handoffs */}
        {handoffs.length > 0 && (
          <div>
            <h4 className="text-xs font-medium text-gray-500 mb-2">Handoffs</h4>
            <div className="space-y-2">
              {handoffs.map((handoff, index) => (
                <motion.div 
                  key={`handoff-${index}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-amber-50 p-2 rounded-md text-xs"
                >
                  <div className="flex justify-between items-start mb-1">
                    <div className="font-medium text-amber-700 flex items-center">
                      <ArrowRight className="h-3 w-3 mr-1" />
                      {handoff.source} → {handoff.target}
                    </div>
                    <Badge 
                      variant="outline" 
                      className={`
                        ${handoff.status === 'pending' ? 'bg-yellow-100 text-yellow-700 border-yellow-200' : ''}
                        ${handoff.status === 'completed' ? 'bg-green-100 text-green-700 border-green-200' : ''}
                        ${handoff.status === 'rejected' ? 'bg-red-100 text-red-700 border-red-200' : ''}
                      `}
                    >
                      {handoff.status}
                    </Badge>
                  </div>
                  <p className="text-amber-600">{handoff.reason}</p>
                </motion.div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
