# YAML Transition Guide

## Introduction

This document provides guidelines for transitioning from JSON to YAM<PERSON> in the Datagenius project. It covers best practices, style guidelines, and examples to help developers understand and implement the transition.

## Why YAML?

YAML (YAML Ain't Markup Language) offers several advantages over JSON for configuration and schema definition:

1. **Better Readability**: YAML's indentation-based structure makes complex configurations easier to read and understand.
2. **Comments**: YAML supports comments, allowing developers to document configuration options inline.
3. **Multiline Strings**: YAM<PERSON> handles multiline strings elegantly, which is particularly useful for system prompts and templates.
4. **Less Syntax Noise**: YAML doesn't require quotes around keys, commas between items, or as many brackets and braces.
5. **More Flexible Syntax**: YAML supports references, anchors, and other advanced features for complex configurations.

## YAML Style Guide

### 1. Indentation

Use 2 spaces for indentation (not tabs):

```yaml
# Good
parent:
  child: value
  another_child: value

# Avoid
parent:
    child: value  # 4 spaces
```

### 2. Key Naming

Use kebab-case for file names and snake_case for keys:

```yaml
# Good
user_settings:
  display_name: "<PERSON>"
  preferred_language: "en"

# Avoid
userSettings:
  displayName: "<PERSON>"
  preferredLanguage: "en"
```

### 3. Comments

Add comments to explain non-obvious configuration options:

```yaml
# Database configuration
database:
  host: localhost
  port: 5432  # Default PostgreSQL port
  name: datagenius_db
  # Set to true in production for SSL connections
  use_ssl: false
```

### 4. Multiline Strings

Use the `|` character for multiline strings that should preserve newlines:

```yaml
description: |
  This is a multiline description.
  It preserves line breaks.
  Each line will end with a newline.
```

Use the `>` character for multiline strings that should fold newlines:

```yaml
description: >
  This is a multiline description.
  It folds newlines into spaces.
  This will be rendered as a single paragraph.
```

### 5. Lists

Use the dash-space (`- `) format for lists:

```yaml
fruits:
  - apple
  - banana
  - orange

# For complex items
users:
  - name: John
    role: admin
  - name: Jane
    role: user
```

### 6. Boolean Values

Use `true` and `false` for boolean values (not strings):

```yaml
# Good
is_enabled: true
debug_mode: false

# Avoid
is_enabled: "true"
debug_mode: "false"
```

### 7. Empty Values

Use `null` or `~` for null values, and `[]` for empty lists:

```yaml
# Null values
optional_setting: null
another_optional: ~

# Empty list
tags: []
```

## File Organization

### 1. Configuration Files

Place configuration files in the appropriate directories:

- **Persona Configurations**: `backend/personas/*.yaml`
- **Agent Configurations**: `backend/agents/configs/*.yaml`
- **Tool Schemas**: `backend/schemas/tools/*.yaml`
- **Form Schemas**: `frontend/src/schemas/*.yaml`

### 2. File Naming

Use kebab-case for file names:

```
# Good
marketing-content-form.yaml
data-visualization-tool.yaml

# Avoid
marketingContentForm.yaml
DataVisualizationTool.yaml
```

## Working with YAML in the Codebase

### 1. Loading YAML Files

Use the `load_yaml` function from `app.utils.yaml_utils`:

```python
from app.utils.yaml_utils import load_yaml

# Load a YAML configuration file
config = load_yaml("path/to/config.yaml")

# Access configuration values
database_host = config.get("database", {}).get("host", "localhost")
```

### 2. Saving YAML Files

Use the `save_yaml` function from `app.utils.yaml_utils`:

```python
from app.utils.yaml_utils import save_yaml

# Create a configuration dictionary
config = {
    "database": {
        "host": "localhost",
        "port": 5432,
        "name": "datagenius_db"
    },
    "logging": {
        "level": "INFO",
        "file": "app.log"
    }
}

# Save the configuration to a YAML file
save_yaml(config, "path/to/config.yaml")
```

### 3. Converting JSON to YAML

Use the `convert_file` function from `app.utils.yaml_utils`:

```python
from app.utils.yaml_utils import convert_file

# Convert a JSON file to YAML
convert_file("path/to/config.json", "path/to/config.yaml")
```

### 4. Loading YAML Schemas in Frontend

Use the `loadYamlSchemaAsZod` function from `utils/schema-utils.ts`:

```typescript
import { loadYamlSchemaAsZod } from '@/utils/schema-utils';

// Load a YAML schema as a Zod schema
const formSchema = await loadYamlSchemaAsZod('/src/schemas/form-schema.yaml');

// Use the schema with react-hook-form
const form = useForm({
  resolver: zodResolver(formSchema),
  defaultValues: {
    // ...
  }
});
```

## Completed Transition

The codebase now exclusively uses YAML format for configuration and data files:

1. **File Detection**: The system only accepts files with .yaml or .yml extensions.
2. **No JSON Support**: JSON files are no longer supported for configuration or data storage.
3. **API Communication**: JSON is still used for API communication, as this is the standard for web APIs.

## Migration Complete

The transition from JSON to YAML has been completed:

1. **Phase 1**: Both formats supported, YAML preferred (Completed)
2. **Phase 2**: JSON format marked as deprecated in documentation (Completed)
3. **Phase 3**: Warning logs when JSON format is used (Completed)
4. **Phase 4**: JSON support removed for configuration (Completed)

## Converting Existing Files

If you find any remaining JSON files that need to be converted to YAML, use the conversion script:

```bash
python scripts/convert_json_to_yaml.py --directory path/to/directory --pattern "*.json" --backup --delete-original
```

This script:
1. Finds all JSON files matching the pattern in the specified directory
2. Creates YAML versions of these files
3. Backs up the original JSON files with a `.bak` extension
4. Deletes the original JSON files after successful conversion

## Common Pitfalls

### 1. YAML is Sensitive to Indentation

Ensure consistent indentation throughout your YAML files. Mixing tabs and spaces or inconsistent indentation will cause parsing errors.

### 2. Quotes Around Special Characters

While YAML doesn't require quotes around strings in most cases, you should use quotes when strings contain special characters:

```yaml
# Needs quotes because it contains a colon
message: "Error: File not found"

# Needs quotes because it starts with a special character
command: "* Run this command"
```

### 3. Boolean Values vs. Strings

Be careful with boolean values. YAML interprets `yes`, `no`, `true`, `false`, `on`, and `off` as boolean values, not strings:

```yaml
# These are boolean values
debug: true
enabled: yes

# These are strings (use quotes)
status: "true"
command: "yes"
```

### 4. Multiline String Indentation

When using multiline strings, be careful with indentation:

```yaml
description: |
  This is a multiline description.
    This line will have extra indentation.
  This line will be at the normal indentation.
```

## Additional Resources

- [Official YAML Specification](https://yaml.org/spec/1.2.2/)
- [YAML Lint](http://www.yamllint.com/) - Online YAML validator
- [PyYAML Documentation](https://pyyaml.org/wiki/PyYAMLDocumentation)
- [js-yaml Documentation](https://github.com/nodeca/js-yaml)

## Getting Help

If you encounter issues with the YAML transition, please:

1. Check the logs for specific error messages
2. Validate your YAML files using a YAML linter
3. Refer to this guide for best practices
4. Contact the development team for assistance
