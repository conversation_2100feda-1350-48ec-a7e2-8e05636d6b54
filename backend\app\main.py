"""
Main FastAPI application entry point.

This module initializes the FastAPI application and includes all the routers.
"""

import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Import config
from . import config

# Import routers
from .api.chat import router as chat_router
from .api.agents import router as agents_router
from .api.files import router as files_router
from .api.auth import router as auth_router
from .api.personas import router as personas_router
from .api.providers import router as providers_router
from .api.data_sources import router as data_sources_router
from .api.cart import router as cart_router
from .api.purchases import router as purchases_router
from .api.admin import router as admin_router
from .api.health import router as health_router
from .api.models import router as models_router
from .api.document_query import router as document_query_router
from .api.docx import router as docx_router

# Import database
from .database import init_db, SessionLocal # Added SessionLocal

# Import WorkflowManager
from backend.agents.orchestration.workflow_manager import workflow_manager as global_workflow_manager_instance # Import the instance
from backend.agents.orchestration.workflow_manager import WorkflowManager # Import the class for re-initialization


# Configure logging
logging.basicConfig(
    level=logging.DEBUG if config.DEBUG else logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize database and personas on startup
@asynccontextmanager
async def lifespan(_: FastAPI):
    """Lifespan event handler for database and personas initialization."""
    # Initialize database
    logger.info("Initializing database...")
    init_db()
    logger.info("Database initialized successfully.")

    # Initialize personas
    try:
        logger.info("Initializing personas...")

        # Initialize WorkflowManager with DB session factory
        logger.info("Initializing WorkflowManager with DB session factory...")
        try:
            # Re-assign the global instance with a properly configured one.
            # This assumes workflow_manager is a module-level variable in orchestration.workflow_manager
            # If it's not, this approach needs to change (e.g., by making WorkflowManager a singleton class
            # that can be configured, or by passing the instance via FastAPI's app.state or dependency injection)
            # For now, directly re-initializing the imported instance's factory.
            if hasattr(global_workflow_manager_instance, 'db_session_factory'):
                 global_workflow_manager_instance.db_session_factory = SessionLocal
                 logger.info("Successfully injected db_session_factory into global WorkflowManager instance.")
            else:
                 logger.error("Global WorkflowManager instance does not have db_session_factory attribute. Cannot inject.")
            # Alternatively, if we need to replace the instance itself:
            # from backend.agents.orchestration import workflow_manager as wm_module
            # wm_module.workflow_manager = WorkflowManager(db_session_factory=SessionLocal)
            # logger.info("Re-initialized global WorkflowManager instance with DB session factory.")

        except Exception as e:
            logger.error(f"Error initializing WorkflowManager: {e}", exc_info=True)


        # Import here to avoid circular imports for persona sync
        import sys
        import os

        # Add the parent directory to sys.path to allow importing from scripts
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)

        # Import the persona manager
        from agents.persona_manager import persona_manager

        # Get database session
        from .database import get_db
        db = next(get_db())

        # Synchronize personas with database
        synchronized_personas = persona_manager.sync_personas_with_database(db)
        logger.info(f"Synchronized {len(synchronized_personas)} personas with database: {synchronized_personas}")

        # Initialize agent registry
        from agents.registry import AgentRegistry
        personas_dir = os.path.join(parent_dir, "personas")

        try:
            # Try to load configurations from YAML files
            AgentRegistry.load_configurations(personas_dir)
            logger.info(f"Loaded {len(AgentRegistry.list_registered_personas())} personas into registry")
        except Exception as e:
            logger.error(f"Error loading agent configurations: {e}")
            logger.info("Will ensure critical agents are registered manually")

        # Ensure critical agents are registered regardless of YAML loading
        try:
            # Import and run the ensure_agents script
            from scripts.ensure_agents import ensure_critical_agents
            ensure_critical_agents()
            logger.info(f"Ensured critical agents are registered. Current registry: {AgentRegistry.list_registered_personas()}")
        except Exception as e:
            logger.error(f"Error ensuring critical agents: {e}")

            # Last resort: manually register the composable analysis agent
            try:
                from agents.analysis_agent.composable_agent import ComposableAnalysisAgent
                if "composable-analysis-ai" not in AgentRegistry.list_registered_personas():
                    AgentRegistry.register("composable-analysis-ai", ComposableAnalysisAgent)
                    logger.info("Manually registered ComposableAnalysisAgent as last resort")
            except Exception as manual_error:
                logger.error(f"Error manually registering ComposableAnalysisAgent: {manual_error}")

        # Ensure all analysis components are registered
        try:
            # Import and run the ensure_analysis_components script
            from scripts.ensure_analysis_components import ensure_analysis_components
            ensure_analysis_components()
            logger.info("Ensured all analysis components are registered")
        except Exception as e:
            logger.error(f"Error ensuring analysis components: {e}")

        logger.info("Personas initialized successfully.")
    except Exception as e:
        logger.error(f"Error initializing personas: {e}", exc_info=True)

    yield

# Create FastAPI app
app = FastAPI(
    title="Datagenius API",
    description="API for the Datagenius application",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
logger.info(f"Configuring CORS with origins: {config.CORS_ORIGINS}")
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.CORS_ORIGINS if config.CORS_ORIGINS != ["*"] else ["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Disposition"],
    max_age=600,  # 10 minutes
)

# Include routers
app.include_router(auth_router)
app.include_router(chat_router)
app.include_router(agents_router)
app.include_router(files_router)

# Create a special route for purchased personas
from fastapi import Depends
from sqlalchemy.orm import Session
from typing import List
from .database import get_db
from .auth import get_current_active_user
from .models.auth import User
from .services import persona_service

@app.get("/personas/purchased", response_model=List[str], tags=["Personas"])
async def get_purchased_personas(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a list of persona IDs that the user has purchased.
    """
    logger.info(f"User {current_user.id} requested purchased personas")
    try:
        # Get purchased personas using the service
        persona_ids = persona_service.get_user_purchased_personas(db, current_user.id)
        logger.info(f"Found {len(persona_ids)} purchased personas for user {current_user.id}: {persona_ids}")
        return persona_ids
    except Exception as e:
        logger.error(f"Error getting purchased personas for user {current_user.id}: {str(e)}", exc_info=True)
        # Return empty list instead of failing
        return []

# Include the rest of the routers
app.include_router(personas_router)
app.include_router(providers_router)
app.include_router(data_sources_router)
app.include_router(cart_router)
app.include_router(purchases_router)
app.include_router(admin_router)
app.include_router(health_router)
app.include_router(models_router)
app.include_router(document_query_router)
app.include_router(docx_router)

# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """Root endpoint."""
    return {
        "message": "Welcome to the Datagenius API",
        "version": "1.0.0",
        "docs_url": "/docs",
    }

# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

# Error handlers
@app.exception_handler(Exception)
async def global_exception_handler(_, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "An unexpected error occurred. Please try again later."},
    )
