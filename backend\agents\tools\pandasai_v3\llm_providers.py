"""
LLM provider factory for PandasAI v3.

This module provides a factory for creating PandasAI LLM providers for different
AI providers like OpenAI, Groq, Google Gemini, OpenRouter, and Requesty.
"""

import logging
from typing import Any, Optional

from .custom_providers import (
    OpenAILLM,
    GroqLLM,
    GeminiLLM,
    OpenRouterLLM,
    RequestyLLM
)

logger = logging.getLogger(__name__)

class LLMProviderFactory:
    """Factory for creating PandasAI LLM providers."""

    @staticmethod
    def create_provider(provider: str, api_key: str, model: Optional[str] = None) -> Any:
        """Create a PandasAI LLM provider."""
        provider = provider.lower()

        try:
            if provider == "openai":
                return OpenAILLM(api_key=api_key, model=model or "gpt-3.5-turbo")
            elif provider == "groq":
                return GroqLLM(api_key=api_key, model=model or "llama3-70b-8192")
            elif provider == "google" or provider == "gemini":
                return GeminiLLM(api_key=api_key, model=model or "gemini-pro")
            elif provider == "openrouter":
                return OpenRouterLLM(api_key=api_key, model=model or "openai/gpt-3.5-turbo")
            elif provider == "requesty":
                return RequestyLLM(api_key=api_key, model=model or "openai/gpt-3.5-turbo")
            else:
                logger.error(f"Unsupported LLM provider: {provider}")
                raise ValueError(f"Unsupported LLM provider: {provider}")
        except ImportError as e:
            logger.error(f"Missing dependency for {provider}: {e}")
            raise ValueError(f"Missing dependency for {provider}: {e}")
        except Exception as e:
            logger.error(f"Error creating LLM provider {provider}: {e}", exc_info=True)
            raise ValueError(f"Error creating LLM provider {provider}: {e}")
