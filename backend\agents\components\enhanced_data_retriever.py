"""
Enhanced data retriever component for the Datagenius agent system.

This module provides an enhanced implementation of the data retriever component,
with improved error handling, file processing, and data extraction.
"""

import logging
import os
import pandas as pd
import numpy as np
import json # Keep json for loading json files
import yaml # Added
import sys # Added for path manipulation
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path

from backend.schemas.agent_config_schemas import AgentProcessingContext # Added
from .base import AgentComponent
from app.database import get_db, get_file

# Add yaml_utils import relative to backend directory
try:
    # Adjust path relative to this file's location (components -> app/utils)
    current_dir = os.path.dirname(os.path.abspath(__file__))
    utils_dir = os.path.abspath(os.path.join(current_dir, '..', '..', 'app', 'utils'))
    if utils_dir not in sys.path:
         # Add the 'app' directory to the path
        sys.path.insert(0, os.path.dirname(utils_dir))
    from utils.yaml_utils import load_yaml, save_yaml
except ImportError as e:
    print(f"Error importing yaml_utils in enhanced_data_retriever: {e}. Ensure backend/app/utils is accessible.")
    # Define dummy functions
    def load_yaml(*args, **kwargs): raise NotImplementedError("yaml_utils not loaded")
    def save_yaml(*args, **kwargs): raise NotImplementedError("yaml_utils not loaded")


logger = logging.getLogger(__name__)


class EnhancedDataRetrieverComponent(AgentComponent):
    """Enhanced component for retrieving and processing data files."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the data retriever component.

        Args:
            config: Configuration dictionary for the component
        """
        # Get database session
        self.db = next(get_db())

        # Set supported file types
        self.supported_extensions = config.get("supported_extensions", [".csv", ".xlsx", ".xls", ".json", ".txt", ".pdf", ".docx"])

        # Set maximum file size (in bytes)
        self.max_file_size = config.get("max_file_size", 100 * 1024 * 1024)  # 100 MB default

        # Set data processing options
        self.auto_clean = config.get("auto_clean", True)
        self.sample_size = config.get("sample_size", 1000)

        # Initialize vector store (enabled by default)
        self.use_vector_store = config.get("use_vector_store", True)
        if self.use_vector_store:
            try:
                from langchain_huggingface import HuggingFaceEmbeddings
                from langchain_community.vectorstores import FAISS

                # Initialize embeddings model
                self.embeddings = HuggingFaceEmbeddings(
                    model_name=config.get("embeddings_model", "all-MiniLM-L6-v2")
                )

                # Set vector database directory
                self.vector_db_dir = config.get("vector_db_dir", "vector_db")
                os.makedirs(self.vector_db_dir, exist_ok=True)

                logger.info(f"Initialized embeddings model for data retriever component: {self.name}")
            except ImportError as e:
                logger.warning(f"Could not initialize vector store: {e}")
                self.use_vector_store = False

    async def process(self, context: AgentProcessingContext) -> AgentProcessingContext:
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object.
        """
        # Check if this component should be skipped
        if context.metadata.get("skip_data_retriever", False):
            logger.debug(f"Skipping data retriever component: {self.name}")
            return context

        # Check if there's a file ID in the context
        file_id = context.initial_context.get("file_id")
        if not file_id:
            file_id = context.metadata.get("file_id")

        if not file_id:
            logger.debug("No file ID found in context (checked initial_context and metadata)")
            context.metadata["has_data"] = False
            # context.response = "No file ID provided for data retrieval." # Optional
            return context

        component_data_store = context.component_data.setdefault(self.name, {})

        try:
            # Get file from database
            file = get_file(self.db, file_id)
            if not file:
                logger.warning(f"File not found in DB: {file_id}")
                context.add_error(self.name, "db_file_not_found", {"file_id": file_id})
                context.response = f"File with ID '{file_id}' not found in the database."
                return context

            # Check if file exists on disk
            file_path = file.file_path
            if not os.path.exists(file_path):
                logger.warning(f"File not found on disk: {file_path}")
                context.add_error(self.name, "disk_file_not_found", {"file_id": file_id, "path": file_path})
                context.response = f"File '{file.filename}' (ID: {file_id}) not found on disk at the expected location."
                return context

            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size > self.max_file_size:
                logger.warning(f"File too large: {file_size} bytes for file {file.filename}")
                context.add_error(self.name, "file_too_large", {"file_id": file_id, "filename": file.filename, "size": file_size, "max_size": self.max_file_size})
                context.response = f"File '{file.filename}' is too large ({file_size} bytes) to process. Maximum allowed size is {self.max_file_size} bytes."
                return context

            # Process file based on extension
            file_extension = Path(file_path).suffix.lower()

            if file_extension not in self.supported_extensions:
                logger.warning(f"Unsupported file extension: {file_extension} for file {file.filename}")
                context.add_error(self.name, "unsupported_file_extension", {"file_id": file_id, "filename": file.filename, "extension": file_extension})
                context.response = f"File type '{file_extension}' for '{file.filename}' is not supported."
                return context

            # Process file based on extension
            if file_extension in [".csv", ".xlsx", ".xls"]:
                data, file_info = await self._process_tabular_file(file_path, file_extension)
                component_data_store["data"] = data
                component_data_store["file_info"] = file_info
                context.metadata["data_type"] = "tabular"

            elif file_extension in [".txt", ".pdf", ".docx"]:
                if self.use_vector_store:
                    vector_store, file_info = await self._process_text_file(file_path, file_extension)
                    component_data_store["vector_store"] = vector_store
                    component_data_store["file_info"] = file_info
                else:
                    text, file_info = await self._read_text_file(file_path, file_extension)
                    component_data_store["text_data"] = text
                    component_data_store["file_info"] = file_info
                context.metadata["data_type"] = "text"

            elif file_extension == ".json":
                with open(file_path, 'r') as f:
                    json_data = json.load(f)
                component_data_store["json_data"] = json_data
                component_data_store["file_info"] = {
                    "file_name": file.filename,
                    "file_size": file_size,
                    "file_type": "json",
                    "file_path": file_path
                }
                context.metadata["data_type"] = "json"
            
            context.metadata["has_data"] = True
            context.metadata["file_name_processed"] = file.filename # Use a distinct key
            context.metadata["file_id_processed"] = file_id

            logger.info(f"Successfully processed file: {file.filename} (ID: {file_id})")
            # context.response = f"Successfully processed file: {file.filename}" # Optional response
            return context

        except Exception as e:
            logger.error(f"Error processing file ID {file_id}: {str(e)}", exc_info=True)
            context.add_error(self.name, "file_processing_error", {"file_id": file_id, "error": str(e)})
            context.response = f"An error occurred while processing file ID '{file_id}'. Please check the logs."
            return context

    async def _process_tabular_file(self, file_path: str, file_extension: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Process a tabular data file.

        Args:
            file_path: Path to the file
            file_extension: File extension

        Returns:
            Tuple of (DataFrame, file_info)
        """
        # Read the file based on extension
        if file_extension == ".csv":
            # Try different encodings
            try:
                df = pd.read_csv(file_path)
            except UnicodeDecodeError:
                df = pd.read_csv(file_path, encoding="latin1")
        elif file_extension in [".xlsx", ".xls"]:
            df = pd.read_excel(file_path)
        else:
            raise ValueError(f"Unsupported file extension for tabular data: {file_extension}")

        # Clean data if enabled
        if self.auto_clean:
            df = self._clean_dataframe(df)

        # Sample data if needed
        if len(df) > self.sample_size:
            df_sample = df.sample(self.sample_size, random_state=42)
        else:
            df_sample = df

        # Create file info
        file_info = {
            "file_path": file_path,
            "file_type": file_extension.lstrip("."),
            "num_rows": len(df),
            "num_columns": len(df.columns),
            "columns": df.columns.tolist(),
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "missing_values": df.isnull().sum().to_dict(),
            "sample_rows": self.sample_size
        }

        return df_sample, file_info

    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Clean a DataFrame by handling common issues.

        Args:
            df: DataFrame to clean

        Returns:
            Cleaned DataFrame
        """
        # Make a copy to avoid modifying the original
        df_clean = df.copy()

        # Convert column names to strings
        df_clean.columns = df_clean.columns.astype(str)

        # Strip whitespace from column names
        df_clean.columns = [col.strip() for col in df_clean.columns]

        # Replace invalid column names
        df_clean.columns = [col.replace(" ", "_").replace(".", "_") for col in df_clean.columns]

        # Handle duplicate column names
        if len(df_clean.columns) != len(set(df_clean.columns)):
            # Add suffix to duplicate columns
            cols = pd.Series(df_clean.columns)
            for col in cols[cols.duplicated()].unique():
                cols[cols == col] = [f"{col}_{i}" for i in range(sum(cols == col))]
            df_clean.columns = cols

        # Handle missing values in numeric columns
        numeric_cols = df_clean.select_dtypes(include=["number"]).columns
        df_clean[numeric_cols] = df_clean[numeric_cols].fillna(df_clean[numeric_cols].median())

        # Handle missing values in string columns
        string_cols = df_clean.select_dtypes(include=["object"]).columns
        df_clean[string_cols] = df_clean[string_cols].fillna("")

        # Handle infinite values
        df_clean = df_clean.replace([np.inf, -np.inf], np.nan)

        return df_clean

    async def _process_text_file(self, file_path: str, file_extension: str) -> Tuple[Any, Dict[str, Any]]:
        """
        Process a text file and create a vector store.

        Args:
            file_path: Path to the file
            file_extension: File extension

        Returns:
            Tuple of (vector_store, file_info)
        """
        from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader, TextLoader
        from langchain.text_splitter import RecursiveCharacterTextSplitter

        # Load the document based on extension
        if file_extension == ".pdf":
            loader = PyPDFLoader(file_path)
        elif file_extension == ".docx":
            loader = Docx2txtLoader(file_path)
        elif file_extension == ".txt":
            loader = TextLoader(file_path)
        else:
            raise ValueError(f"Unsupported file extension for text data: {file_extension}")

        # Load the document
        documents = loader.load()

        # Split the document into chunks
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len
        )
        chunks = text_splitter.split_documents(documents)

        # Create vector store
        from langchain_community.vectorstores import FAISS
        vector_store = FAISS.from_documents(chunks, self.embeddings)

        # Generate a unique ID for the vector store
        import uuid
        vector_store_id = str(uuid.uuid4())

        # Save the vector store for later use
        vector_store_path = os.path.join(self.vector_db_dir, vector_store_id)
        vector_store.save_local(vector_store_path)

        # Create file info
        file_info = {
            "file_path": file_path,
            "file_type": file_extension.lstrip("."),
            "num_chunks": len(chunks),
            "chunk_size": 1000,
            "chunk_overlap": 200,
            "vector_store_id": vector_store_id,
            "vector_store_path": vector_store_path
        }

        # Save file info as YAML
        info_yaml_path = f"{vector_store_path}_info.yaml"
        logger.info(f"Saving vector store metadata to: {info_yaml_path}")
        if not save_yaml(file_info, info_yaml_path):
            logger.error(f"Failed to save metadata file: {info_yaml_path}")
            # Log and continue, metadata won't be saved.

        return vector_store, file_info

    async def _read_text_file(self, file_path: str, file_extension: str) -> Tuple[str, Dict[str, Any]]:
        """
        Read a text file without creating a vector store.

        Args:
            file_path: Path to the file
            file_extension: File extension

        Returns:
            Tuple of (text, file_info)
        """
        # Read the file based on extension
        if file_extension == ".pdf":
            import PyPDF2
            with open(file_path, "rb") as f:
                reader = PyPDF2.PdfReader(f)
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        elif file_extension == ".docx":
            import docx2txt
            text = docx2txt.process(file_path)
        elif file_extension == ".txt":
            with open(file_path, "r", encoding="utf-8") as f:
                text = f.read()
        else:
            raise ValueError(f"Unsupported file extension for text data: {file_extension}")

        # Create file info
        file_info = {
            "file_path": file_path,
            "file_type": file_extension.lstrip("."),
            "text_length": len(text)
        }

        return text, file_info
