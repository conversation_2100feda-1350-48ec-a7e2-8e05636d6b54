"""
Base component interface for the Datagenius agent system.

This module defines the base component interface that all agent components must implement.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class AgentComponent(ABC):
    """Base class for agent components."""

    def __init__(self):
        """Initialize the base component."""
        self.config = {}
        self.name = self.__class__.__name__

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        self.config = config
        if "name" in config:
            self.name = config["name"]
        
        # Additional initialization should be implemented by subclasses
        await self._initialize(config)
        logger.info(f"Initialized component: {self.name}")

    @abstractmethod
    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Additional initialization for subclasses.

        Args:
            config: Configuration dictionary for the component
        """
        pass

    @abstractmethod
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        pass

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value by key.

        Args:
            key: Configuration key
            default: Default value if key is not found

        Returns:
            Configuration value
        """
        return self.config.get(key, default)

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.

        Returns:
            List of capability strings
        """
        return self.config.get("capabilities", [])
