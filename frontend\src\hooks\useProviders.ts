import { useState, useEffect } from 'react';
import { providerApi } from '@/lib/api';

interface Provider {
  id: string;
  name: string;
  models?: Array<{ id: string; name: string }>;
}

export function useProviders() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchProviders = async () => {
      setIsLoading(true);
      try {
        const response = await providerApi.getProviders();
        setProviders(response.providers);
        setError(null);
      } catch (err) {
        console.error('Error fetching providers:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch providers'));

        // Display error message instead of using fallback data
        setProviders([]);
        console.error('Failed to load AI providers. Please check your connection and try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProviders();
  }, []);

  return { providers, isLoading, error };
}
